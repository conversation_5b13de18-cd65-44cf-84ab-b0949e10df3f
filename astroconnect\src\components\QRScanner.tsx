'use client';

import { useEffect, useRef, useState } from 'react';
import { Html5QrcodeScanner, Html5QrcodeScannerConfig, Html5QrcodeResult, Html5Qrcode, Html5QrcodeScanType } from 'html5-qrcode';

import { QRScannerProps } from '@/types';
import { Camera, Upload, X } from 'lucide-react';

export default function QRScanner({
  onScanSuccess,
  onScanError,
  width = 300,
  height = 300
}: QRScannerProps) {
  const scannerRef = useRef<Html5Qrcode | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scanMode, setScanMode] = useState<'camera' | 'file' | null>(null);
  const [cameras, setCameras] = useState<any[]>([]);
  const [permissionGranted, setPermissionGranted] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const config: Html5QrcodeScannerConfig = {
    fps: 10,
    qrbox: { width: 250, height: 250 },
    aspectRatio: 1.0,
    disableFlip: false,
    supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],
    showTorchButtonIfSupported: true,
    showZoomSliderIfSupported: true,
  };

  const handleScanSuccess = (decodedText: string, decodedResult: Html5QrcodeResult) => {
    console.log('QR Code scanned:', decodedText);
    onScanSuccess(decodedText);
    stopScanning();
  };

  const handleScanError = (error: string) => {
    // Only log actual errors, not the constant "No QR code found" messages
    if (!error.includes('No QR code found')) {
      console.error('QR scan error:', error);
      onScanError?.(error);
    }
  };

  const requestCameraPermission = async () => {
    try {
      console.log('🎥 Starting camera permission request...');

      // Check browser support
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera API not supported in this browser');
      }

      // Check if we're in a secure context (HTTPS or localhost)
      if (!window.isSecureContext) {
        console.warn('⚠️ Not in secure context, camera may not work');
      }

      console.log('🔐 Requesting camera access...');

      // Request camera permission with multiple fallback strategies
      let stream: MediaStream | null = null;

      try {
        // Strategy 1: Try with environment camera (back camera)
        stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: { ideal: 'environment' },
            width: { ideal: 1280 },
            height: { ideal: 720 }
          }
        });
        console.log('✅ Got camera stream with environment facing mode');
      } catch (envError) {
        console.log('⚠️ Environment camera failed, trying user camera:', envError);

        try {
          // Strategy 2: Try with user camera (front camera)
          stream = await navigator.mediaDevices.getUserMedia({
            video: {
              facingMode: 'user',
              width: { ideal: 1280 },
              height: { ideal: 720 }
            }
          });
          console.log('✅ Got camera stream with user facing mode');
        } catch (userError) {
          console.log('⚠️ User camera failed, trying basic video:', userError);

          // Strategy 3: Try with basic video constraints
          stream = await navigator.mediaDevices.getUserMedia({
            video: true
          });
          console.log('✅ Got camera stream with basic constraints');
        }
      }

      if (!stream) {
        throw new Error('Failed to get camera stream');
      }

      // Stop the stream immediately - we just needed permission
      stream.getTracks().forEach(track => {
        track.stop();
        console.log('🛑 Stopped track:', track.label);
      });

      console.log('✅ Camera permission granted, getting available cameras...');

      // Small delay to ensure camera is released
      await new Promise(resolve => setTimeout(resolve, 100));

      // Now get the list of cameras using Html5Qrcode
      const cameras = await Html5Qrcode.getCameras();
      console.log('📷 Available cameras:', cameras);

      if (!cameras || cameras.length === 0) {
        throw new Error('No cameras found on this device');
      }

      setCameras(cameras);
      setPermissionGranted(true);

      // Select the best camera for QR scanning
      const backCamera = cameras.find(camera => {
        const label = camera.label.toLowerCase();
        return label.includes('back') ||
               label.includes('rear') ||
               label.includes('environment') ||
               label.includes('facing back');
      });

      const selectedCamera = backCamera || cameras[0];
      console.log('🎯 Selected camera:', selectedCamera);

      await startCameraScanning(selectedCamera.id);

    } catch (error: any) {
      console.error('❌ Camera permission error:', error);
      setPermissionGranted(false);

      let errorMessage = 'Camera access failed. ';

      switch (error.name) {
        case 'NotAllowedError':
          errorMessage += 'Please allow camera access in your browser and try again.';
          break;
        case 'NotFoundError':
          errorMessage += 'No camera found on this device.';
          break;
        case 'NotSupportedError':
          errorMessage += 'Camera not supported in this browser. Try Chrome, Firefox, or Safari.';
          break;
        case 'NotReadableError':
          errorMessage += 'Camera is being used by another application. Please close other apps using the camera.';
          break;
        case 'OverconstrainedError':
          errorMessage += 'Camera constraints not supported. Trying with basic settings...';
          // Try again with basic constraints
          setTimeout(() => requestBasicCameraPermission(), 1000);
          return;
        case 'SecurityError':
          errorMessage += 'Camera access blocked by security policy. Please check your browser settings.';
          break;
        default:
          errorMessage += error.message || 'Unknown error occurred.';
      }

      onScanError?.(errorMessage);
    }
  };

  // Fallback method with minimal constraints
  const requestBasicCameraPermission = async () => {
    try {
      console.log('🔄 Trying basic camera permission...');

      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      stream.getTracks().forEach(track => track.stop());

      const cameras = await Html5Qrcode.getCameras();
      if (cameras && cameras.length > 0) {
        setCameras(cameras);
        setPermissionGranted(true);
        await startCameraScanning(cameras[0].id);
      } else {
        throw new Error('No cameras available');
      }
    } catch (error) {
      console.error('❌ Basic camera permission failed:', error);
      onScanError?.('Camera access failed completely. Please check your camera permissions and try refreshing the page.');
    }
  };

  const startCameraScanning = async (cameraId: string) => {
    console.log('🚀 Starting camera scanning with camera ID:', cameraId);

    // Clean up any existing scanner
    if (scannerRef.current) {
      try {
        await scannerRef.current.stop();
        console.log('🛑 Stopped existing scanner');
      } catch (error) {
        console.log('ℹ️ Scanner was already stopped');
      }
      scannerRef.current = null;
    }

    setScanMode('camera');
    setIsScanning(true);

    try {
      const element = document.getElementById('qr-scanner-container');
      if (!element) {
        throw new Error('QR scanner container element not found in DOM');
      }

      console.log('📦 Creating new Html5Qrcode instance...');
      scannerRef.current = new Html5Qrcode('qr-scanner-container');

      // Progressive configuration - start with optimal settings
      const configs = [
        // Config 1: Optimal for QR scanning
        {
          fps: 10,
          qrbox: { width: 250, height: 250 },
          aspectRatio: 1.0,
          disableFlip: false,
          videoConstraints: {
            facingMode: 'environment',
            width: { ideal: 1280, min: 640 },
            height: { ideal: 720, min: 480 }
          }
        },
        // Config 2: Fallback with basic constraints
        {
          fps: 8,
          qrbox: { width: 200, height: 200 },
          aspectRatio: 1.0,
          disableFlip: false
        },
        // Config 3: Minimal configuration
        {
          fps: 5,
          qrbox: { width: 150, height: 150 }
        }
      ];

      let configIndex = 0;
      let lastError: any = null;

      const tryConfig = async (config: any): Promise<void> => {
        console.log(`🔧 Trying camera config ${configIndex + 1}:`, config);

        try {
          await scannerRef.current!.start(
            cameraId,
            config,
            handleScanSuccess,
            handleScanError
          );

          console.log('✅ Camera scanner started successfully with config', configIndex + 1);
          return;
        } catch (error: any) {
          console.warn(`⚠️ Config ${configIndex + 1} failed:`, error);
          lastError = error;

          if (configIndex < configs.length - 1) {
            configIndex++;
            await tryConfig(configs[configIndex]);
          } else {
            throw lastError;
          }
        }
      };

      await tryConfig(configs[0]);

    } catch (error: any) {
      console.error('❌ All camera configurations failed:', error);
      setIsScanning(false);
      setScanMode(null);

      let errorMessage = 'Failed to start camera scanner. ';

      switch (error.name) {
        case 'NotAllowedError':
          errorMessage += 'Camera permission was revoked. Please refresh the page and allow camera access.';
          break;
        case 'NotFoundError':
          errorMessage += 'Camera not found or disconnected. Please check your camera connection.';
          break;
        case 'NotReadableError':
          errorMessage += 'Camera is being used by another application. Please close other camera apps.';
          break;
        case 'OverconstrainedError':
          errorMessage += 'Camera settings not supported. Please try a different camera if available.';
          break;
        case 'AbortError':
          errorMessage += 'Camera operation was interrupted. Please try again.';
          break;
        default:
          errorMessage += error.message || 'Unknown camera error occurred.';
      }

      onScanError?.(errorMessage);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setScanMode('file');
    setIsScanning(true);

    try {
      // Use Html5Qrcode for file scanning
      const html5QrCode = new Html5Qrcode("qr-reader-file");

      const result = await html5QrCode.scanFile(file, true);
      console.log('QR Code from file:', result);
      onScanSuccess(result);

      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: any) {
      console.error('File scan error:', error);
      onScanError?.('Failed to scan QR code from image. Please ensure the image contains a valid QR code.');
    } finally {
      setIsScanning(false);
      setScanMode(null);
    }
  };

  const stopScanning = async () => {
    if (scannerRef.current) {
      try {
        await scannerRef.current.stop();
        scannerRef.current = null;
      } catch (error) {
        console.error('Error stopping scanner:', error);
      }
    }
    setIsScanning(false);
    setScanMode(null);
  };

  // Check camera permission status on mount
  const checkCameraPermission = async () => {
    try {
      if (navigator.permissions && navigator.permissions.query) {
        const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });
        console.log('Camera permission status:', permission.state);

        if (permission.state === 'granted') {
          setPermissionGranted(true);
        } else if (permission.state === 'denied') {
          setPermissionGranted(false);
        }

        // Listen for permission changes
        permission.onchange = () => {
          console.log('Camera permission changed to:', permission.state);
          if (permission.state === 'granted') {
            setPermissionGranted(true);
          } else if (permission.state === 'denied') {
            setPermissionGranted(false);
            if (scannerRef.current) {
              stopScanning();
            }
          }
        };
      }
    } catch (error) {
      console.log('Permission API not supported:', error);
    }
  };

  useEffect(() => {
    checkCameraPermission();

    return () => {
      if (scannerRef.current) {
        scannerRef.current.stop().catch(console.error);
      }
    };
  }, []);

  if (!scanMode) {
    return (
      <div className="flex flex-col items-center space-y-4 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Scan Your QR Code</h3>

        {/* Permission Status Indicator */}
        {permissionGranted === false && (
          <div className="w-full max-w-md bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4">
            <div className="flex items-center gap-2 text-red-300 mb-2">
              <X size={16} />
              <span className="text-sm font-medium">Camera Permission Denied</span>
            </div>
            <p className="text-red-200 text-xs mb-3">
              Please allow camera access in your browser settings and try again.
            </p>

            {/* Troubleshooting Steps */}
            <div className="text-xs text-red-200 space-y-2">
              <p className="font-medium">Troubleshooting steps:</p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>Click the camera icon in your browser's address bar</li>
                <li>Select "Allow" for camera access</li>
                <li>Refresh the page and try again</li>
                <li>Check if another app is using your camera</li>
              </ul>

              <button
                onClick={() => {
                  setPermissionGranted(null);
                  requestCameraPermission();
                }}
                className="mt-2 px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
          <button
            onClick={requestCameraPermission}
            disabled={isScanning}
            className={`flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-colors ${
              isScanning
                ? 'bg-gray-600 cursor-not-allowed text-gray-400'
                : 'bg-purple-600 hover:bg-purple-700 text-white'
            }`}
          >
            <Camera size={20} />
            {isScanning ? 'Starting Camera...' : 'Use Camera'}
          </button>

          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={isScanning}
            className={`flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-colors ${
              isScanning
                ? 'bg-gray-600 cursor-not-allowed text-gray-400'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            <Upload size={20} />
            Upload Image
          </button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
          className="hidden"
        />

        <div className="text-center mt-4">
          <p className="text-gray-300 text-sm">
            Point your camera at the QR code or upload an image containing the QR code
          </p>

          {/* Browser Compatibility Info */}
          <div className="mt-3 text-xs text-gray-400">
            <p>📱 For best results, use your device's back camera</p>
            <p>🔒 Camera access is required for QR scanning</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center space-y-4 p-6">
      <div className="flex items-center justify-between w-full max-w-md">
        <h3 className="text-lg font-semibold text-white">
          {scanMode === 'camera' ? 'Camera Scanner' : 'Processing Image...'}
        </h3>
        <button
          onClick={stopScanning}
          className="text-gray-400 hover:text-white transition-colors"
        >
          <X size={24} />
        </button>
      </div>

      {scanMode === 'camera' && (
        <div className="w-full max-w-md">
          <div
            id="qr-scanner-container"
            className="w-full rounded-lg overflow-hidden border-2 border-purple-400"
            style={{ width, height }}
          />
          {cameras.length > 1 && (
            <div className="mt-4">
              <label className="block text-white text-sm mb-2">Select Camera:</label>
              <select
                onChange={(e) => startCameraScanning(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                style={{ colorScheme: 'dark' }}
              >
                {cameras.map((camera, index) => (
                  <option key={camera.id} value={camera.id} className="bg-gray-800 text-white">
                    {camera.label || `Camera ${index + 1}`}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      )}

      {scanMode === 'file' && (
        <div className="flex items-center justify-center w-64 h-64 border-2 border-dashed border-gray-400 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p className="text-gray-300">Scanning image...</p>
          </div>
        </div>
      )}

      {/* Hidden div for file scanner */}
      <div id="qr-reader-file" style={{ display: 'none' }}></div>
    </div>
  );
}
