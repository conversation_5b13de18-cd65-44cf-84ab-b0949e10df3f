'use client';

import { useEffect, useRef, useState } from 'react';
import { Html5Qrcode, Html5QrcodeResult } from 'html5-qrcode';
import { QRScannerProps } from '@/types';
import { Camera, Upload, X } from 'lucide-react';

export default function QRScanner({
  onScanSuccess,
  onScanError,
  width = 300,
  height = 300
}: QRScannerProps) {
  const scannerRef = useRef<Html5Qrcode | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scanMode, setScanMode] = useState<'camera' | 'file' | null>(null);
  const [cameras, setCameras] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const scannerElementId = 'qr-scanner-container';

  const handleScanSuccess = (decodedText: string, decodedResult: Html5QrcodeResult) => {
    console.log('✅ QR Code scanned successfully:', decodedText);
    onScanSuccess(decodedText);
    stopScanning();
  };

  const handleScanError = (error: string) => {
    // Only log actual errors, not the constant "No QR code found" messages
    if (!error.includes('No QR code found') && !error.includes('QR code parse error')) {
      console.error('❌ QR scan error:', error);
      onScanError?.(error);
    }
  };

  const startCameraScanning = async () => {
    try {
      setError(null);
      console.log('🎥 Starting camera scanning...');

      // Check browser support
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera not supported in this browser');
      }

      // Initialize Html5Qrcode
      if (!scannerRef.current) {
        scannerRef.current = new Html5Qrcode(scannerElementId);
      }

      // Get available cameras
      const devices = await Html5Qrcode.getCameras();
      if (devices && devices.length > 0) {
        setCameras(devices);
        console.log('📱 Available cameras:', devices.length);

        // Prefer back camera for mobile
        const backCamera = devices.find(device => 
          device.label.toLowerCase().includes('back') || 
          device.label.toLowerCase().includes('environment')
        );
        const cameraId = backCamera ? backCamera.id : devices[0].id;

        // Start scanning with mobile-optimized config
        await scannerRef.current.start(
          cameraId,
          {
            fps: 10,
            qrbox: function(viewfinderWidth, viewfinderHeight) {
              // Make QR box responsive to screen size
              const minEdgePercentage = 0.7;
              const minEdgeSize = Math.min(viewfinderWidth, viewfinderHeight);
              const qrboxSize = Math.floor(minEdgeSize * minEdgePercentage);
              return {
                width: qrboxSize,
                height: qrboxSize
              };
            },
            aspectRatio: 1.0,
            disableFlip: false,
          },
          handleScanSuccess,
          handleScanError
        );

        setIsScanning(true);
        setScanMode('camera');
        console.log('✅ Camera scanning started');
      } else {
        throw new Error('No cameras found');
      }
    } catch (error) {
      console.error('❌ Camera scanning failed:', error);
      setError(error instanceof Error ? error.message : 'Camera access failed');
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setError(null);
      setScanMode('file');
      console.log('📁 Processing uploaded file:', file.name);

      // Use Html5Qrcode static method for file scanning (no DOM element needed)
      const result = await Html5Qrcode.scanFile(file, true);
      console.log('✅ File scan successful:', result);
      handleScanSuccess(result, {} as Html5QrcodeResult);
      
    } catch (error) {
      console.error('❌ File scan failed:', error);
      setError('Could not read QR code from image. Please try a clearer image.');
      onScanError?.('File scan failed');
    } finally {
      setScanMode(null);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const stopScanning = async () => {
    try {
      if (scannerRef.current && isScanning) {
        await scannerRef.current.stop();
        console.log('🛑 Scanning stopped');
      }
    } catch (error) {
      console.error('Error stopping scanner:', error);
    } finally {
      setIsScanning(false);
      setScanMode(null);
    }
  };

  // Cleanup function
  useEffect(() => {
    return () => {
      stopScanning();
    };
  }, []);

  if (!scanMode) {
    return (
      <div className="flex flex-col items-center space-y-4 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Scan Your QR Code</h3>

        {/* Error Display */}
        {error && (
          <div className="w-full max-w-md bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4">
            <div className="flex items-center gap-2 text-red-300 mb-2">
              <X size={16} />
              <span className="text-sm font-medium">Error</span>
            </div>
            <p className="text-red-200 text-xs mb-3">{error}</p>
            <button
              onClick={() => setError(null)}
              className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
            >
              Dismiss
            </button>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
          <button
            onClick={startCameraScanning}
            disabled={isScanning}
            className={`flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-colors ${
              isScanning
                ? 'bg-gray-600 cursor-not-allowed text-gray-400'
                : 'bg-purple-600 hover:bg-purple-700 text-white'
            }`}
          >
            <Camera size={20} />
            {isScanning ? 'Starting Camera...' : 'Use Camera'}
          </button>

          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={isScanning}
            className={`flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-colors ${
              isScanning
                ? 'bg-gray-600 cursor-not-allowed text-gray-400'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            <Upload size={20} />
            Upload Image
          </button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
          className="hidden"
        />

        <div className="text-center mt-4">
          <p className="text-gray-300 text-sm">
            Point your camera at the QR code or upload an image containing the QR code
          </p>

          {/* Browser Compatibility Info */}
          <div className="mt-3 text-xs text-gray-400">
            <p>📱 For best results, use your device's back camera</p>
            <p>🔒 Camera access is required for QR scanning</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center space-y-4 p-6">
      <div className="flex items-center justify-between w-full max-w-md">
        <h3 className="text-lg font-semibold text-white">
          {scanMode === 'camera' ? 'Camera Scanner' : 'Processing Image...'}
        </h3>
        <button
          onClick={stopScanning}
          className="text-gray-400 hover:text-white transition-colors"
        >
          <X size={24} />
        </button>
      </div>

      {scanMode === 'camera' && (
        <div className="w-full max-w-md">
          <div
            id="qr-scanner-container"
            className="w-full rounded-lg overflow-hidden border-2 border-purple-400"
            style={{ width, height }}
          />
        </div>
      )}

      {scanMode === 'file' && (
        <div className="flex items-center justify-center w-64 h-64 border-2 border-dashed border-gray-400 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p className="text-gray-300">Scanning image...</p>
          </div>
        </div>
      )}
    </div>
  );
}
