'use client';

import { useEffect, useRef, useState } from 'react';
import { Html5QrcodeScanner, Html5QrcodeScannerConfig, Html5QrcodeResult, Html5Qrcode } from 'html5-qrcode';
import { QRScannerProps } from '@/types';
import { Camera, Upload, X } from 'lucide-react';

export default function QRScanner({ 
  onScanSuccess, 
  onScanError, 
  width = 300, 
  height = 300 
}: QRScannerProps) {
  const scannerRef = useRef<Html5QrcodeScanner | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scanMode, setScanMode] = useState<'camera' | 'file' | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const config: Html5QrcodeScannerConfig = {
    fps: 10,
    qrbox: { width: 250, height: 250 },
    aspectRatio: 1.0,
    disableFlip: false,
    supportedScanTypes: [],
  };

  const handleScanSuccess = (decodedText: string, decodedResult: Html5QrcodeResult) => {
    console.log('QR Code scanned:', decodedText);
    onScanSuccess(decodedText);
    stopScanning();
  };

  const handleScanError = (error: string) => {
    // Only log actual errors, not the constant "No QR code found" messages
    if (!error.includes('No QR code found')) {
      console.error('QR scan error:', error);
      onScanError?.(error);
    }
  };

  const startCameraScanning = () => {
    if (scannerRef.current) {
      scannerRef.current.clear();
    }

    setScanMode('camera');
    setIsScanning(true);

    scannerRef.current = new Html5QrcodeScanner(
      'qr-scanner-container',
      config,
      false
    );

    scannerRef.current.render(handleScanSuccess, handleScanError);
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setScanMode('file');
    setIsScanning(true);

    try {
      // Use static method for file scanning - no DOM element needed
      const decodedText = await Html5Qrcode.scanFile(file, true);
      console.log('QR Code from file:', decodedText);
      onScanSuccess(decodedText);

      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: any) {
      console.error('File scan error:', error);
      onScanError?.('Failed to scan QR code from image. Please ensure the image contains a valid QR code.');
    } finally {
      setIsScanning(false);
      setScanMode(null);
    }
  };

  const stopScanning = () => {
    if (scannerRef.current) {
      scannerRef.current.clear().catch(console.error);
      scannerRef.current = null;
    }
    setIsScanning(false);
    setScanMode(null);
  };

  useEffect(() => {
    return () => {
      if (scannerRef.current) {
        scannerRef.current.clear().catch(console.error);
      }
    };
  }, []);

  if (!scanMode) {
    return (
      <div className="flex flex-col items-center space-y-4 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Scan Your QR Code</h3>
        
        <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
          <button
            onClick={startCameraScanning}
            className="flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            <Camera size={20} />
            Use Camera
          </button>
          
          <button
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            <Upload size={20} />
            Upload Image
          </button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
          className="hidden"
        />

        <p className="text-gray-300 text-sm text-center mt-4">
          Point your camera at the QR code or upload an image containing the QR code
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center space-y-4 p-6">
      <div className="flex items-center justify-between w-full max-w-md">
        <h3 className="text-lg font-semibold text-white">
          {scanMode === 'camera' ? 'Camera Scanner' : 'Processing Image...'}
        </h3>
        <button
          onClick={stopScanning}
          className="text-gray-400 hover:text-white transition-colors"
        >
          <X size={24} />
        </button>
      </div>

      {scanMode === 'camera' && (
        <div 
          id="qr-scanner-container" 
          className="w-full max-w-md"
          style={{ width, height }}
        />
      )}

      {scanMode === 'file' && (
        <div className="flex items-center justify-center w-64 h-64 border-2 border-dashed border-gray-400 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p className="text-gray-300">Scanning image...</p>
          </div>
        </div>
      )}
    </div>
  );
}
