'use client';

import { useEffect, useRef, useState } from 'react';
import { Html5QrcodeScanner, Html5QrcodeScannerConfig, Html5QrcodeResult, Html5Qrcode, Html5QrcodeScanType } from 'html5-qrcode';
import jsQR from 'jsqr';
import { QRScannerProps } from '@/types';
import { Camera, Upload, X } from 'lucide-react';

export default function QRScanner({
  onScanSuccess,
  onScanError,
  width = 300,
  height = 300
}: QRScannerProps) {
  const scannerRef = useRef<Html5Qrcode | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scanMode, setScanMode] = useState<'camera' | 'file' | null>(null);
  const [cameras, setCameras] = useState<any[]>([]);
  const [permissionGranted, setPermissionGranted] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const config: Html5QrcodeScannerConfig = {
    fps: 10,
    qrbox: { width: 250, height: 250 },
    aspectRatio: 1.0,
    disableFlip: false,
    supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],
    showTorchButtonIfSupported: true,
    showZoomSliderIfSupported: true,
  };

  const handleScanSuccess = (decodedText: string, decodedResult: Html5QrcodeResult) => {
    console.log('QR Code scanned:', decodedText);
    onScanSuccess(decodedText);
    stopScanning();
  };

  const handleScanError = (error: string) => {
    // Only log actual errors, not the constant "No QR code found" messages
    if (!error.includes('No QR code found')) {
      console.error('QR scan error:', error);
      onScanError?.(error);
    }
  };

  const requestCameraPermission = async () => {
    try {
      console.log('Requesting camera permissions...');
      const cameras = await Html5Qrcode.getCameras();
      console.log('Available cameras:', cameras);
      setCameras(cameras);
      setPermissionGranted(true);

      if (cameras && cameras.length > 0) {
        await startCameraScanning(cameras[0].id);
      } else {
        onScanError?.('No cameras found on this device');
      }
    } catch (error) {
      console.error('Camera permission error:', error);
      setPermissionGranted(false);
      onScanError?.('Camera permission denied or not available. Please allow camera access and try again.');
    }
  };

  const startCameraScanning = async (cameraId: string) => {
    if (scannerRef.current) {
      try {
        await scannerRef.current.stop();
      } catch (error) {
        console.log('Scanner already stopped');
      }
    }

    setScanMode('camera');
    setIsScanning(true);

    try {
      const element = document.getElementById('qr-scanner-container');
      if (!element) {
        throw new Error('QR scanner container not found');
      }

      scannerRef.current = new Html5Qrcode('qr-scanner-container');

      const qrCodeConfig = {
        fps: 10,
        qrbox: { width: 250, height: 250 },
        aspectRatio: 1.0,
        disableFlip: false,
      };

      await scannerRef.current.start(
        cameraId,
        qrCodeConfig,
        handleScanSuccess,
        handleScanError
      );

      console.log('Camera scanner started successfully');
    } catch (error) {
      console.error('Scanner initialization error:', error);
      setIsScanning(false);
      setScanMode(null);
      onScanError?.('Failed to start camera scanner. Please check camera permissions.');
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setScanMode('file');
    setIsScanning(true);

    try {
      // Convert file to data URL
      const dataUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });

      // Create an image element to load the file
      const img = new Image();
      await new Promise<void>((resolve, reject) => {
        img.onload = () => resolve();
        img.onerror = reject;
        img.src = dataUrl;
      });

      // Create a canvas to draw the image
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Could not get canvas context');

      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      // Get image data for jsQR
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

      // Use jsQR to decode the QR code
      const code = jsQR(imageData.data, imageData.width, imageData.height);

      if (code) {
        console.log('QR Code from file:', code.data);
        onScanSuccess(code.data);
      } else {
        throw new Error('No QR code found in image');
      }

      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: any) {
      console.error('File scan error:', error);
      onScanError?.('Failed to scan QR code from image. Please ensure the image contains a valid QR code.');
    } finally {
      setIsScanning(false);
      setScanMode(null);
    }
  };

  const stopScanning = async () => {
    if (scannerRef.current) {
      try {
        await scannerRef.current.stop();
        scannerRef.current = null;
      } catch (error) {
        console.error('Error stopping scanner:', error);
      }
    }
    setIsScanning(false);
    setScanMode(null);
  };

  useEffect(() => {
    return () => {
      if (scannerRef.current) {
        scannerRef.current.stop().catch(console.error);
      }
    };
  }, []);

  if (!scanMode) {
    return (
      <div className="flex flex-col items-center space-y-4 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Scan Your QR Code</h3>
        
        <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
          <button
            onClick={requestCameraPermission}
            className="flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            <Camera size={20} />
            Use Camera
          </button>
          
          <button
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            <Upload size={20} />
            Upload Image
          </button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
          className="hidden"
        />

        <p className="text-gray-300 text-sm text-center mt-4">
          Point your camera at the QR code or upload an image containing the QR code
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center space-y-4 p-6">
      <div className="flex items-center justify-between w-full max-w-md">
        <h3 className="text-lg font-semibold text-white">
          {scanMode === 'camera' ? 'Camera Scanner' : 'Processing Image...'}
        </h3>
        <button
          onClick={stopScanning}
          className="text-gray-400 hover:text-white transition-colors"
        >
          <X size={24} />
        </button>
      </div>

      {scanMode === 'camera' && (
        <div className="w-full max-w-md">
          <div
            id="qr-scanner-container"
            className="w-full rounded-lg overflow-hidden border-2 border-purple-400"
            style={{ width, height }}
          />
          {cameras.length > 1 && (
            <div className="mt-4">
              <label className="block text-white text-sm mb-2">Select Camera:</label>
              <select
                onChange={(e) => startCameraScanning(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                style={{ colorScheme: 'dark' }}
              >
                {cameras.map((camera, index) => (
                  <option key={camera.id} value={camera.id} className="bg-gray-800 text-white">
                    {camera.label || `Camera ${index + 1}`}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      )}

      {scanMode === 'file' && (
        <div className="flex items-center justify-center w-64 h-64 border-2 border-dashed border-gray-400 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p className="text-gray-300">Scanning image...</p>
          </div>
        </div>
      )}
    </div>
  );
}
