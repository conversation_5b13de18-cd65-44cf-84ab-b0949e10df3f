'use client';

import { useEffect, useRef, useState } from 'react';
import { QRScannerProps } from '@/types';
import { Camera, Upload, X } from 'lucide-react';

export default function QRScanner({
  onScanSuccess,
  onScanError,
  width = 300,
  height = 300
}: QRScannerProps) {
  const [Html5Qrcode, setHtml5Qrcode] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [scanMode, setScanMode] = useState<'camera' | 'file' | null>(null);
  const scannerRef = useRef<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load Html5Qrcode library
  useEffect(() => {
    const loadLibrary = async () => {
      try {
        console.log('🔄 Loading Html5Qrcode library...');
        const module = await import('html5-qrcode');
        setHtml5Qrcode(module.Html5Qrcode);
        setIsLoading(false);
        console.log('✅ Html5Qrcode library loaded successfully');
      } catch (err) {
        console.error('❌ Failed to load Html5Qrcode:', err);
        setError('Failed to load QR scanner library');
        setIsLoading(false);
      }
    };

    loadLibrary();
  }, []);

  const handleScanSuccess = (decodedText: string) => {
    console.log('✅ QR Code scanned:', decodedText);
    onScanSuccess(decodedText);
    stopScanning();
  };

  const handleScanError = (errorMessage: string) => {
    // Only log actual errors, not the constant scanning messages
    if (!errorMessage.includes('No QR code found')) {
      console.error('❌ QR scan error:', errorMessage);
    }
  };

  const startCameraScanning = async () => {
    if (!Html5Qrcode) {
      setError('QR scanner library not loaded');
      return;
    }

    try {
      setError(null);
      setScanMode('camera');
      setIsScanning(true);

      // Get cameras
      const cameras = await Html5Qrcode.getCameras();
      if (!cameras || cameras.length === 0) {
        throw new Error('No cameras found');
      }

      // Initialize scanner
      scannerRef.current = new Html5Qrcode('qr-reader');

      // Start scanning
      await scannerRef.current.start(
        cameras[0].id,
        {
          fps: 10,
          qrbox: { width: 250, height: 250 }
        },
        handleScanSuccess,
        handleScanError
      );

      console.log('✅ Camera scanning started');
    } catch (err) {
      console.error('❌ Camera scanning failed:', err);
      setError(err instanceof Error ? err.message : 'Camera scanning failed');
      setScanMode(null);
      setIsScanning(false);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !Html5Qrcode) return;

    try {
      setError(null);
      setScanMode('file');
      console.log('📁 Scanning file:', file.name);

      const result = await Html5Qrcode.scanFile(file, true);
      handleScanSuccess(result);
    } catch (err) {
      console.error('❌ File scanning failed:', err);
      setError('Could not read QR code from image');
      onScanError?.('File scan failed');
    } finally {
      setScanMode(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const stopScanning = async () => {
    if (scannerRef.current) {
      try {
        await scannerRef.current.stop();
        scannerRef.current = null;
      } catch (err) {
        console.error('Error stopping scanner:', err);
      }
    }
    setIsScanning(false);
    setScanMode(null);
  };

  // Cleanup
  useEffect(() => {
    return () => {
      stopScanning();
    };
  }, []);



  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mb-4"></div>
        <p className="text-white">Loading QR Scanner...</p>
      </div>
    );
  }

  if (!scanMode) {
    return (
      <div className="flex flex-col items-center space-y-4 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Scan Your QR Code</h3>

        {error && (
          <div className="w-full max-w-md bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4">
            <div className="flex items-center gap-2 text-red-300 mb-2">
              <X size={16} />
              <span className="text-sm font-medium">Error</span>
            </div>
            <p className="text-red-200 text-xs mb-3">{error}</p>
            <button
              onClick={() => setError(null)}
              className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
            >
              Dismiss
            </button>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
          <button
            onClick={startCameraScanning}
            disabled={isScanning}
            className={`flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-colors ${
              isScanning
                ? 'bg-gray-600 cursor-not-allowed text-gray-400'
                : 'bg-purple-600 hover:bg-purple-700 text-white'
            }`}
          >
            <Camera size={20} />
            {isScanning ? 'Starting...' : 'Use Camera'}
          </button>

          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={isScanning}
            className={`flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-colors ${
              isScanning
                ? 'bg-gray-600 cursor-not-allowed text-gray-400'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            <Upload size={20} />
            Upload Image
          </button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
          className="hidden"
        />

        <div className="text-center mt-4">
          <p className="text-gray-300 text-sm">
            Point your camera at the QR code or upload an image
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center space-y-4 p-6">
      <div className="flex items-center justify-between w-full max-w-md">
        <h3 className="text-lg font-semibold text-white">
          {scanMode === 'camera' ? 'Camera Scanner' : 'Processing Image...'}
        </h3>
        <button
          onClick={stopScanning}
          className="text-gray-400 hover:text-white transition-colors"
        >
          <X size={24} />
        </button>
      </div>

      {scanMode === 'camera' && (
        <div
          id="qr-reader"
          className="w-full max-w-md rounded-lg overflow-hidden border-2 border-purple-400"
        />
      )}

      {scanMode === 'file' && (
        <div className="flex items-center justify-center w-64 h-64 border-2 border-dashed border-gray-400 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p className="text-gray-300">Scanning image...</p>
          </div>
        </div>
      )}
    </div>
  );
}
