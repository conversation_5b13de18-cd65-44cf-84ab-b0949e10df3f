{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Helper function to disconnect Prisma (useful for serverless)\nexport async function disconnectPrisma() {\n  await prisma.$disconnect();\n}\n\n// Helper function to check database connection\nexport async function checkDatabaseConnection() {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/test-qr/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { prisma } from '@/lib/prisma';\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Get all users with their QR tokens\n    const users = await prisma.user.findMany({\n      select: {\n        id: true,\n        name: true,\n        qrToken: true,\n        zodiacSign: true\n      }\n    });\n\n    // Get QR mappings\n    const qrMappings = await prisma.qrCodeMapping.findMany({\n      select: {\n        qrToken: true,\n        userId: true,\n        scanCount: true,\n        lastScanned: true\n      }\n    });\n\n    // Create test URLs for each user\n    const testData = users.map(user => {\n      const mapping = qrMappings.find(m => m.userId === user.id);\n      return {\n        user: {\n          id: user.id,\n          name: user.name,\n          zodiacSign: user.zodiacSign\n        },\n        qrToken: user.qrToken,\n        qrUrl: `http://localhost:3000/qr/${user.qrToken}`,\n        mapping: mapping ? {\n          scanCount: mapping.scanCount,\n          lastScanned: mapping.lastScanned\n        } : null\n      };\n    });\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        users: testData,\n        totalUsers: users.length,\n        totalMappings: qrMappings.length\n      },\n      message: 'QR test data retrieved successfully'\n    });\n\n  } catch (error) {\n    console.error('QR test data error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,qCAAqC;QACrC,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,SAAS;gBACT,YAAY;YACd;QACF;QAEA,kBAAkB;QAClB,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;YACrD,QAAQ;gBACN,SAAS;gBACT,QAAQ;gBACR,WAAW;gBACX,aAAa;YACf;QACF;QAEA,iCAAiC;QACjC,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA;YACzB,MAAM,UAAU,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,KAAK,EAAE;YACzD,OAAO;gBACL,MAAM;oBACJ,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,YAAY,KAAK,UAAU;gBAC7B;gBACA,SAAS,KAAK,OAAO;gBACrB,OAAO,CAAC,yBAAyB,EAAE,KAAK,OAAO,EAAE;gBACjD,SAAS,UAAU;oBACjB,WAAW,QAAQ,SAAS;oBAC5B,aAAa,QAAQ,WAAW;gBAClC,IAAI;YACN;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP,YAAY,MAAM,MAAM;gBACxB,eAAe,WAAW,MAAM;YAClC;YACA,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}