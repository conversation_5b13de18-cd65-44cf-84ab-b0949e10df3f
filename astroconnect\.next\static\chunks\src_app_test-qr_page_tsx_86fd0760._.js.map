{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/test-qr/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport QRScanner from '@/components/QRScanner';\n\nexport default function TestQRPage() {\n  const [scannedResult, setScannedResult] = useState<string | null>(null);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleScanSuccess = (result: string) => {\n    console.log('✅ QR Scan Success:', result);\n    setScannedResult(result);\n    setError(null);\n  };\n\n  const handleScanError = (error: string) => {\n    console.error('❌ QR Scan Error:', error);\n    setError(error);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4\">\n      <div className=\"max-w-2xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-white text-center mb-8\">\n          QR Scanner Test\n        </h1>\n\n        {/* Results Display */}\n        {scannedResult && (\n          <div className=\"mb-6 p-4 bg-green-500/20 border border-green-500/50 rounded-lg\">\n            <h3 className=\"text-green-300 font-semibold mb-2\">✅ Scan Successful!</h3>\n            <p className=\"text-green-100 break-all\">{scannedResult}</p>\n            <button\n              onClick={() => setScannedResult(null)}\n              className=\"mt-2 px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded transition-colors\"\n            >\n              Clear Result\n            </button>\n          </div>\n        )}\n\n        {error && (\n          <div className=\"mb-6 p-4 bg-red-500/20 border border-red-500/50 rounded-lg\">\n            <h3 className=\"text-red-300 font-semibold mb-2\">❌ Error</h3>\n            <p className=\"text-red-100\">{error}</p>\n            <button\n              onClick={() => setError(null)}\n              className=\"mt-2 px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors\"\n            >\n              Clear Error\n            </button>\n          </div>\n        )}\n\n        {/* QR Scanner Component */}\n        <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6\">\n          <SimpleQRScanner\n            onScanSuccess={handleScanSuccess}\n            onScanError={handleScanError}\n            width={400}\n            height={400}\n          />\n        </div>\n\n        {/* Instructions */}\n        <div className=\"mt-8 p-4 bg-blue-500/20 border border-blue-500/50 rounded-lg\">\n          <h3 className=\"text-blue-300 font-semibold mb-2\">📋 Test Instructions</h3>\n          <ul className=\"text-blue-100 text-sm space-y-1\">\n            <li>• Click \"Use Camera\" to test camera scanning</li>\n            <li>• Click \"Upload Image\" to test file scanning</li>\n            <li>• Generate a test QR code at: qr-code-generator.com</li>\n            <li>• Try scanning text like \"Hello World\" or a URL</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAKe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,oBAAoB,CAAC;QACzB,QAAQ,GAAG,CAAC,sBAAsB;QAClC,iBAAiB;QACjB,SAAS;IACX;IAEA,MAAM,kBAAkB,CAAC;QACvB,QAAQ,KAAK,CAAC,oBAAoB;QAClC,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAiD;;;;;;gBAK9D,+BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,6LAAC;4BAAE,WAAU;sCAA4B;;;;;;sCACzC,6LAAC;4BACC,SAAS,IAAM,iBAAiB;4BAChC,WAAU;sCACX;;;;;;;;;;;;gBAMJ,uBACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAkC;;;;;;sCAChD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,6LAAC;4BACC,SAAS,IAAM,SAAS;4BACxB,WAAU;sCACX;;;;;;;;;;;;8BAOL,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,eAAe;wBACf,aAAa;wBACb,OAAO;wBACP,QAAQ;;;;;;;;;;;8BAKZ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB;GAxEwB;KAAA", "debugId": null}}]}