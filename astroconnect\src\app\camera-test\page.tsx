import CameraDiagnostic from '@/components/CameraDiagnostic';

export default function CameraTestPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Camera Test & Diagnostics</h1>
          <p className="text-gray-300">
            Use this tool to diagnose camera permission and access issues
          </p>
        </div>
        
        <CameraDiagnostic />
        
        <div className="mt-8 text-center">
          <a 
            href="/" 
            className="inline-block px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            Back to Home
          </a>
        </div>
      </div>
    </div>
  );
}
