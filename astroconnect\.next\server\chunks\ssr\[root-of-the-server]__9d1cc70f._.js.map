{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { User, UserWithSession } from '@/types';\n\nexport function useAuth() {\n  const [user, setUser] = useState<UserWithSession | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check if session is expired\n  const isSessionExpired = (user: UserWithSession | null): boolean => {\n    if (!user || !user.sessionExpiry) return true;\n    return new Date() > new Date(user.sessionExpiry);\n  };\n\n  useEffect(() => {\n    // Check for stored user data on component mount\n    const storedUser = localStorage.getItem('astroconnect_user');\n    if (storedUser) {\n      try {\n        const parsedUser = JSON.parse(storedUser) as UserWithSession;\n\n        // Check if session is expired\n        if (isSessionExpired(parsedUser)) {\n          console.log('Session expired, removing stored user data');\n          localStorage.removeItem('astroconnect_user');\n          setUser(null);\n        } else {\n          setUser(parsedUser);\n        }\n      } catch (error) {\n        console.error('Error parsing stored user data:', error);\n        localStorage.removeItem('astroconnect_user');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  // Auto-logout when session expires\n  useEffect(() => {\n    if (!user || !user.sessionExpiry) return;\n\n    const checkSessionExpiry = () => {\n      if (isSessionExpired(user)) {\n        console.log('Session expired, logging out user');\n        logout();\n      }\n    };\n\n    // Check every minute\n    const interval = setInterval(checkSessionExpiry, 60000);\n\n    // Also set a timeout for the exact expiry time\n    const timeUntilExpiry = new Date(user.sessionExpiry).getTime() - Date.now();\n    const timeout = setTimeout(() => {\n      console.log('Session expired, logging out user');\n      logout();\n    }, timeUntilExpiry);\n\n    return () => {\n      clearInterval(interval);\n      clearTimeout(timeout);\n    };\n  }, [user]);\n\n  const login = async (token: string): Promise<{ success: boolean; error?: string }> => {\n    try {\n      const response = await fetch('/api/auth/qr', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ token }),\n      });\n\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const userWithSession = data.data as UserWithSession;\n\n        // Verify session is not expired\n        if (isSessionExpired(userWithSession)) {\n          return { success: false, error: 'Session expired during authentication' };\n        }\n\n        setUser(userWithSession);\n        localStorage.setItem('astroconnect_user', JSON.stringify(userWithSession));\n        console.log(`User logged in with session expiring at: ${userWithSession.sessionExpiry}`);\n        return { success: true };\n      } else {\n        return { success: false, error: data.error || 'Authentication failed' };\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      return { success: false, error: 'Network error occurred' };\n    }\n  };\n\n  const logout = () => {\n    console.log('User logged out');\n    setUser(null);\n    localStorage.removeItem('astroconnect_user');\n  };\n\n  const updateUser = (updatedUser: UserWithSession) => {\n    setUser(updatedUser);\n    localStorage.setItem('astroconnect_user', JSON.stringify(updatedUser));\n  };\n\n  const isAuthenticated = !!user && !isSessionExpired(user);\n\n  // Get session time remaining in minutes\n  const getSessionTimeRemaining = (): number => {\n    if (!user || !user.sessionExpiry) return 0;\n    const remaining = new Date(user.sessionExpiry).getTime() - Date.now();\n    return Math.max(0, Math.floor(remaining / (1000 * 60))); // Convert to minutes\n  };\n\n  return {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser,\n    getSessionTimeRemaining,\n    isSessionExpired: () => isSessionExpired(user)\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAKO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,8BAA8B;IAC9B,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE,OAAO;QACzC,OAAO,IAAI,SAAS,IAAI,KAAK,KAAK,aAAa;IACjD;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gDAAgD;QAChD,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,YAAY;YACd,IAAI;gBACF,MAAM,aAAa,KAAK,KAAK,CAAC;gBAE9B,8BAA8B;gBAC9B,IAAI,iBAAiB,aAAa;oBAChC,QAAQ,GAAG,CAAC;oBACZ,aAAa,UAAU,CAAC;oBACxB,QAAQ;gBACV,OAAO;oBACL,QAAQ;gBACV;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,aAAa,UAAU,CAAC;YAC1B;QACF;QACA,WAAW;IACb,GAAG,EAAE;IAEL,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE;QAElC,MAAM,qBAAqB;YACzB,IAAI,iBAAiB,OAAO;gBAC1B,QAAQ,GAAG,CAAC;gBACZ;YACF;QACF;QAEA,qBAAqB;QACrB,MAAM,WAAW,YAAY,oBAAoB;QAEjD,+CAA+C;QAC/C,MAAM,kBAAkB,IAAI,KAAK,KAAK,aAAa,EAAE,OAAO,KAAK,KAAK,GAAG;QACzE,MAAM,UAAU,WAAW;YACzB,QAAQ,GAAG,CAAC;YACZ;QACF,GAAG;QAEH,OAAO;YACL,cAAc;YACd,aAAa;QACf;IACF,GAAG;QAAC;KAAK;IAET,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;gBAC7B,MAAM,kBAAkB,KAAK,IAAI;gBAEjC,gCAAgC;gBAChC,IAAI,iBAAiB,kBAAkB;oBACrC,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAwC;gBAC1E;gBAEA,QAAQ;gBACR,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;gBACzD,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,gBAAgB,aAAa,EAAE;gBACvF,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK,IAAI;gBAAwB;YACxE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;IACF;IAEA,MAAM,SAAS;QACb,QAAQ,GAAG,CAAC;QACZ,QAAQ;QACR,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,aAAa,CAAC;QAClB,QAAQ;QACR,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D;IAEA,MAAM,kBAAkB,CAAC,CAAC,QAAQ,CAAC,iBAAiB;IAEpD,wCAAwC;IACxC,MAAM,0BAA0B;QAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE,OAAO;QACzC,MAAM,YAAY,IAAI,KAAK,KAAK,aAAa,EAAE,OAAO,KAAK,KAAK,GAAG;QACnE,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,qBAAqB;IAChF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA,kBAAkB,IAAM,iBAAiB;IAC3C;AACF", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/zodiac.ts"], "sourcesContent": ["import { ZodiacSign } from '@prisma/client';\n\nexport const ZODIAC_SIGNS: ZodiacSign[] = [\n  'aries', 'taurus', 'gemini', 'cancer',\n  'leo', 'virgo', 'libra', 'scorpio',\n  'sagittarius', 'capricorn', 'aquarius', 'pisces'\n];\n\nexport const ZODIAC_INFO = {\n  aries: { name: '<PERSON><PERSON>', symbol: '♈', dates: 'Mar 21 - Apr 19', element: 'Fire' },\n  taurus: { name: 'Taurus', symbol: '♉', dates: 'Apr 20 - May 20', element: 'Earth' },\n  gemini: { name: 'Gemini', symbol: '♊', dates: 'May 21 - Jun 20', element: 'Air' },\n  cancer: { name: 'Cancer', symbol: '♋', dates: 'Jun 21 - Jul 22', element: 'Water' },\n  leo: { name: '<PERSON>', symbol: '♌', dates: 'Jul 23 - Aug 22', element: 'Fire' },\n  virgo: { name: '<PERSON>ir<PERSON>', symbol: '♍', dates: 'Aug 23 - Sep 22', element: 'Earth' },\n  libra: { name: '<PERSON><PERSON>', symbol: '♎', dates: 'Sep 23 - Oct 22', element: 'Air' },\n  scorpio: { name: '<PERSON><PERSON><PERSON>', symbol: '♏', dates: 'Oct 23 - Nov 21', element: 'Water' },\n  sagittarius: { name: 'Sagittarius', symbol: '♐', dates: 'Nov 22 - Dec 21', element: 'Fire' },\n  capricorn: { name: 'Capricorn', symbol: '♑', dates: 'Dec 22 - Jan 19', element: 'Earth' },\n  aquarius: { name: 'Aquarius', symbol: '♒', dates: 'Jan 20 - Feb 18', element: 'Air' },\n  pisces: { name: 'Pisces', symbol: '♓', dates: 'Feb 19 - Mar 20', element: 'Water' }\n};\n\nexport function getZodiacFromDate(birthDate: string): ZodiacSign {\n  const date = new Date(birthDate);\n  const month = date.getMonth() + 1; // 1-12\n  const day = date.getDate();\n\n  if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) return 'aries';\n  if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) return 'taurus';\n  if ((month === 5 && day >= 21) || (month === 6 && day <= 20)) return 'gemini';\n  if ((month === 6 && day >= 21) || (month === 7 && day <= 22)) return 'cancer';\n  if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) return 'leo';\n  if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) return 'virgo';\n  if ((month === 9 && day >= 23) || (month === 10 && day <= 22)) return 'libra';\n  if ((month === 10 && day >= 23) || (month === 11 && day <= 21)) return 'scorpio';\n  if ((month === 11 && day >= 22) || (month === 12 && day <= 21)) return 'sagittarius';\n  if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) return 'capricorn';\n  if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) return 'aquarius';\n  return 'pisces';\n}\n\nexport function getZodiacColors(sign: ZodiacSign): string[] {\n  const colorMap = {\n    aries: ['#FF6B6B', '#FF4757'],\n    taurus: ['#2ECC71', '#27AE60'],\n    gemini: ['#F39C12', '#E67E22'],\n    cancer: ['#3498DB', '#2980B9'],\n    leo: ['#E74C3C', '#C0392B'],\n    virgo: ['#1ABC9C', '#16A085'],\n    libra: ['#9B59B6', '#8E44AD'],\n    scorpio: ['#34495E', '#2C3E50'],\n    sagittarius: ['#E67E22', '#D35400'],\n    capricorn: ['#95A5A6', '#7F8C8D'],\n    aquarius: ['#3498DB', '#2980B9'],\n    pisces: ['#1ABC9C', '#16A085']\n  };\n  return colorMap[sign];\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,eAA6B;IACxC;IAAS;IAAU;IAAU;IAC7B;IAAO;IAAS;IAAS;IACzB;IAAe;IAAa;IAAY;CACzC;AAEM,MAAM,cAAc;IACzB,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC/E,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAClF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IAChF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAClF,KAAK;QAAE,MAAM;QAAO,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC3E,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAChF,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IAC9E,SAAS;QAAE,MAAM;QAAW,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IACpF,aAAa;QAAE,MAAM;QAAe,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC3F,WAAW;QAAE,MAAM;QAAa,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IACxF,UAAU;QAAE,MAAM;QAAY,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IACpF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;AACpF;AAEO,SAAS,kBAAkB,SAAiB;IACjD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,QAAQ,KAAK,QAAQ,KAAK,GAAG,OAAO;IAC1C,MAAM,MAAM,KAAK,OAAO;IAExB,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACtE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACvE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACvE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACtE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,OAAO;AACT;AAEO,SAAS,gBAAgB,IAAgB;IAC9C,MAAM,WAAW;QACf,OAAO;YAAC;YAAW;SAAU;QAC7B,QAAQ;YAAC;YAAW;SAAU;QAC9B,QAAQ;YAAC;YAAW;SAAU;QAC9B,QAAQ;YAAC;YAAW;SAAU;QAC9B,KAAK;YAAC;YAAW;SAAU;QAC3B,OAAO;YAAC;YAAW;SAAU;QAC7B,OAAO;YAAC;YAAW;SAAU;QAC7B,SAAS;YAAC;YAAW;SAAU;QAC/B,aAAa;YAAC;YAAW;SAAU;QACnC,WAAW;YAAC;YAAW;SAAU;QACjC,UAAU;YAAC;YAAW;SAAU;QAChC,QAAQ;YAAC;YAAW;SAAU;IAChC;IACA,OAAO,QAAQ,CAAC,KAAK;AACvB", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\nimport { Loader2 } from 'lucide-react';\n\ninterface LoadingSpinnerProps {\n  message?: string;\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ \n  message = 'Loading...', \n  size = 'md',\n  className = '' \n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-6 h-6',\n    md: 'w-12 h-12',\n    lg: 'w-16 h-16'\n  };\n\n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <Loader2 className={`${sizeClasses[size]} text-white animate-spin mb-4`} />\n      {message && (\n        <p className=\"text-white text-center\">{message}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,eAAe,EACrC,UAAU,YAAY,EACtB,OAAO,IAAI,EACX,YAAY,EAAE,EACM;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACtE,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,6BAA6B,CAAC;;;;;;YACtE,yBACC,8OAAC;gBAAE,WAAU;0BAA0B;;;;;;;;;;;;AAI/C", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, RefreshCw } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  title?: string;\n  message: string;\n  onRetry?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ \n  title = 'Something went wrong',\n  message,\n  onRetry,\n  className = ''\n}: ErrorMessageProps) {\n  return (\n    <div className={`text-center max-w-md mx-auto p-6 ${className}`}>\n      <AlertCircle className=\"w-16 h-16 text-red-400 mx-auto mb-4\" />\n      <h2 className=\"text-xl font-bold text-white mb-4\">{title}</h2>\n      <p className=\"text-gray-300 mb-6\">{message}</p>\n      {onRetry && (\n        <button\n          onClick={onRetry}\n          className=\"flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors mx-auto\"\n        >\n          <RefreshCw size={16} />\n          Try Again\n        </button>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAWe,SAAS,aAAa,EACnC,QAAQ,sBAAsB,EAC9B,OAAO,EACP,OAAO,EACP,YAAY,EAAE,EACI;IAClB,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;;0BAC7D,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,8OAAC;gBAAG,WAAU;0BAAqC;;;;;;0BACnD,8OAAC;gBAAE,WAAU;0BAAsB;;;;;;YAClC,yBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,8OAAC,gNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;oBAAM;;;;;;;;;;;;;AAMjC", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/TranslatedText.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useLanguage } from '@/hooks/useLanguage';\nimport { Loader2 } from 'lucide-react';\n\ninterface TranslatedTextProps {\n  text: string;\n  className?: string;\n  fallback?: string;\n  showLoader?: boolean;\n}\n\nexport default function TranslatedText({ \n  text, \n  className = '', \n  fallback,\n  showLoader = true \n}: TranslatedTextProps) {\n  const { language, translate, isTranslating } = useLanguage();\n  const [translatedText, setTranslatedText] = useState<string>(text);\n  const [isLoading, setIsLoading] = useState(false);\n\n  useEffect(() => {\n    const translateContent = async () => {\n      if (language === 'en') {\n        setTranslatedText(text);\n        return;\n      }\n\n      setIsLoading(true);\n      try {\n        const translated = await translate(text);\n        setTranslatedText(translated);\n      } catch (error) {\n        console.error('Translation error:', error);\n        setTranslatedText(fallback || text);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    translateContent();\n  }, [text, language, translate, fallback]);\n\n  if (isLoading && showLoader) {\n    return (\n      <div className={`flex items-center gap-2 ${className}`}>\n        <Loader2 className=\"w-4 h-4 animate-spin\" />\n        <span className=\"opacity-70\">{fallback || text}</span>\n      </div>\n    );\n  }\n\n  return <span className={className}>{translatedText}</span>;\n}\n\n// Hook for batch translation\nexport function useTranslatedContent(content: Record<string, string>) {\n  const { language, translate } = useLanguage();\n  const [translatedContent, setTranslatedContent] = useState<Record<string, string>>(content);\n  const [isTranslating, setIsTranslating] = useState(false);\n\n  useEffect(() => {\n    const translateAll = async () => {\n      if (language === 'en') {\n        setTranslatedContent(content);\n        return;\n      }\n\n      setIsTranslating(true);\n      try {\n        const translations: Record<string, string> = {};\n        \n        for (const [key, text] of Object.entries(content)) {\n          translations[key] = await translate(text);\n        }\n        \n        setTranslatedContent(translations);\n      } catch (error) {\n        console.error('Batch translation error:', error);\n        setTranslatedContent(content);\n      } finally {\n        setIsTranslating(false);\n      }\n    };\n\n    translateAll();\n  }, [content, language, translate]);\n\n  return { translatedContent, isTranslating };\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAae,SAAS,eAAe,EACrC,IAAI,EACJ,YAAY,EAAE,EACd,QAAQ,EACR,aAAa,IAAI,EACG;IACpB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,aAAa,MAAM;gBACrB,kBAAkB;gBAClB;YACF;YAEA,aAAa;YACb,IAAI;gBACF,MAAM,aAAa,MAAM,UAAU;gBACnC,kBAAkB;YACpB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,kBAAkB,YAAY;YAChC,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;QAAM;QAAU;QAAW;KAAS;IAExC,IAAI,aAAa,YAAY;QAC3B,qBACE,8OAAC;YAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;8BACpD,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,8OAAC;oBAAK,WAAU;8BAAc,YAAY;;;;;;;;;;;;IAGhD;IAEA,qBAAO,8OAAC;QAAK,WAAW;kBAAY;;;;;;AACtC;AAGO,SAAS,qBAAqB,OAA+B;IAClE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAC1C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACnF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,aAAa,MAAM;gBACrB,qBAAqB;gBACrB;YACF;YAEA,iBAAiB;YACjB,IAAI;gBACF,MAAM,eAAuC,CAAC;gBAE9C,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,OAAO,OAAO,CAAC,SAAU;oBACjD,YAAY,CAAC,IAAI,GAAG,MAAM,UAAU;gBACtC;gBAEA,qBAAqB;YACvB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,qBAAqB;YACvB,SAAU;gBACR,iBAAiB;YACnB;QACF;QAEA;IACF,GAAG;QAAC;QAAS;QAAU;KAAU;IAEjC,OAAO;QAAE;QAAmB;IAAc;AAC5C", "debugId": null}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useLanguage } from '@/hooks/useLanguage';\nimport { Globe, Check, Loader2 } from 'lucide-react';\nimport { LANGUAGE_NAMES } from '@/utils/translation';\n\ninterface LanguageSwitcherProps {\n  onLanguageChange?: (language: 'en' | 'si') => void;\n  className?: string;\n}\n\nexport default function LanguageSwitcher({ onLanguageChange, className = '' }: LanguageSwitcherProps) {\n  const { language, setLanguage, isTranslating } = useLanguage();\n  const [isOpen, setIsOpen] = useState(false);\n\n  const handleLanguageChange = async (newLanguage: 'en' | 'si') => {\n    if (newLanguage === language) return;\n    \n    setLanguage(newLanguage);\n    setIsOpen(false);\n    \n    if (onLanguageChange) {\n      onLanguageChange(newLanguage);\n    }\n  };\n\n  return (\n    <div className={`relative ${className}`}>\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 bg-white/10 hover:bg-white/20 text-white px-3 py-2 rounded-lg transition-colors\"\n        disabled={isTranslating}\n      >\n        {isTranslating ? (\n          <Loader2 size={16} className=\"animate-spin\" />\n        ) : (\n          <Globe size={16} />\n        )}\n        <span>{LANGUAGE_NAMES[language]}</span>\n        <svg \n          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} \n          fill=\"none\" \n          stroke=\"currentColor\" \n          viewBox=\"0 0 24 24\"\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n        </svg>\n      </button>\n\n      {isOpen && (\n        <div className=\"absolute top-full right-0 mt-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg shadow-lg z-50 min-w-[120px]\">\n          {Object.entries(LANGUAGE_NAMES).map(([code, name]) => (\n            <button\n              key={code}\n              onClick={() => handleLanguageChange(code as 'en' | 'si')}\n              className=\"flex items-center justify-between w-full px-4 py-2 text-white hover:bg-white/10 transition-colors first:rounded-t-lg last:rounded-b-lg\"\n            >\n              <span>{name}</span>\n              {language === code && <Check size={16} className=\"text-green-400\" />}\n            </button>\n          ))}\n        </div>\n      )}\n\n      {/* Overlay to close dropdown */}\n      {isOpen && (\n        <div \n          className=\"fixed inset-0 z-40\" \n          onClick={() => setIsOpen(false)}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAYe,SAAS,iBAAiB,EAAE,gBAAgB,EAAE,YAAY,EAAE,EAAyB;IAClG,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAC3D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,uBAAuB,OAAO;QAClC,IAAI,gBAAgB,UAAU;QAE9B,YAAY;QACZ,UAAU;QAEV,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACrC,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,UAAU;;oBAET,8BACC,8OAAC,iNAAA,CAAA,UAAO;wBAAC,MAAM;wBAAI,WAAU;;;;;6CAE7B,8OAAC,oMAAA,CAAA,QAAK;wBAAC,MAAM;;;;;;kCAEf,8OAAC;kCAAM,2HAAA,CAAA,iBAAc,CAAC,SAAS;;;;;;kCAC/B,8OAAC;wBACC,WAAW,CAAC,6BAA6B,EAAE,SAAS,eAAe,IAAI;wBACvE,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAIxE,wBACC,8OAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,2HAAA,CAAA,iBAAc,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAC/C,8OAAC;wBAEC,SAAS,IAAM,qBAAqB;wBACpC,WAAU;;0CAEV,8OAAC;0CAAM;;;;;;4BACN,aAAa,sBAAQ,8OAAC,oMAAA,CAAA,QAAK;gCAAC,MAAM;gCAAI,WAAU;;;;;;;uBAL5C;;;;;;;;;;YAYZ,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;;;;;;;AAKnC", "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/PWAInstaller.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Download, X } from 'lucide-react';\n\ninterface BeforeInstallPromptEvent extends Event {\n  readonly platforms: string[];\n  readonly userChoice: Promise<{\n    outcome: 'accepted' | 'dismissed';\n    platform: string;\n  }>;\n  prompt(): Promise<void>;\n}\n\nexport default function PWAInstaller() {\n  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);\n  const [showInstallPrompt, setShowInstallPrompt] = useState(false);\n  const [isInstalled, setIsInstalled] = useState(false);\n\n  useEffect(() => {\n    // Check if app is already installed\n    if (window.matchMedia('(display-mode: standalone)').matches) {\n      setIsInstalled(true);\n      return;\n    }\n\n    // Listen for the beforeinstallprompt event\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault();\n      setDeferredPrompt(e as BeforeInstallPromptEvent);\n      setShowInstallPrompt(true);\n    };\n\n    // Listen for app installed event\n    const handleAppInstalled = () => {\n      setIsInstalled(true);\n      setShowInstallPrompt(false);\n      setDeferredPrompt(null);\n    };\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n    window.addEventListener('appinstalled', handleAppInstalled);\n\n    // Register service worker\n    if ('serviceWorker' in navigator) {\n      navigator.serviceWorker.register('/sw.js')\n        .then((registration) => {\n          console.log('Service Worker registered:', registration);\n        })\n        .catch((error) => {\n          console.error('Service Worker registration failed:', error);\n        });\n    }\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n      window.removeEventListener('appinstalled', handleAppInstalled);\n    };\n  }, []);\n\n  const handleInstallClick = async () => {\n    if (!deferredPrompt) return;\n\n    try {\n      await deferredPrompt.prompt();\n      const { outcome } = await deferredPrompt.userChoice;\n      \n      if (outcome === 'accepted') {\n        console.log('User accepted the install prompt');\n      } else {\n        console.log('User dismissed the install prompt');\n      }\n      \n      setDeferredPrompt(null);\n      setShowInstallPrompt(false);\n    } catch (error) {\n      console.error('Error during installation:', error);\n    }\n  };\n\n  const handleDismiss = () => {\n    setShowInstallPrompt(false);\n    // Don't show again for this session\n    sessionStorage.setItem('pwa-install-dismissed', 'true');\n  };\n\n  // Don't show if already installed or dismissed\n  if (isInstalled || !showInstallPrompt || sessionStorage.getItem('pwa-install-dismissed')) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4 shadow-lg z-50\">\n      <div className=\"flex items-start justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          <Download className=\"w-5 h-5 text-purple-400\" />\n          <h3 className=\"text-white font-semibold\">Install AstroConnect</h3>\n        </div>\n        <button\n          onClick={handleDismiss}\n          className=\"text-gray-400 hover:text-white transition-colors\"\n        >\n          <X size={20} />\n        </button>\n      </div>\n      \n      <p className=\"text-gray-300 text-sm mb-4\">\n        Install our app for quick access to your daily horoscope and cosmic insights!\n      </p>\n      \n      <div className=\"flex space-x-2\">\n        <button\n          onClick={handleInstallClick}\n          className=\"flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n        >\n          Install\n        </button>\n        <button\n          onClick={handleDismiss}\n          className=\"px-4 py-2 text-gray-300 hover:text-white text-sm transition-colors\"\n        >\n          Not now\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAce,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IACtF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oCAAoC;QACpC,IAAI,OAAO,UAAU,CAAC,8BAA8B,OAAO,EAAE;YAC3D,eAAe;YACf;QACF;QAEA,2CAA2C;QAC3C,MAAM,4BAA4B,CAAC;YACjC,EAAE,cAAc;YAChB,kBAAkB;YAClB,qBAAqB;QACvB;QAEA,iCAAiC;QACjC,MAAM,qBAAqB;YACzB,eAAe;YACf,qBAAqB;YACrB,kBAAkB;QACpB;QAEA,OAAO,gBAAgB,CAAC,uBAAuB;QAC/C,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,0BAA0B;QAC1B,IAAI,mBAAmB,WAAW;YAChC,UAAU,aAAa,CAAC,QAAQ,CAAC,UAC9B,IAAI,CAAC,CAAC;gBACL,QAAQ,GAAG,CAAC,8BAA8B;YAC5C,GACC,KAAK,CAAC,CAAC;gBACN,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACJ;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,uBAAuB;YAClD,OAAO,mBAAmB,CAAC,gBAAgB;QAC7C;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,eAAe,MAAM;YAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;YAEnD,IAAI,YAAY,YAAY;gBAC1B,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,kBAAkB;YAClB,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,gBAAgB;QACpB,qBAAqB;QACrB,oCAAoC;QACpC,eAAe,OAAO,CAAC,yBAAyB;IAClD;IAEA,+CAA+C;IAC/C,IAAI,eAAe,CAAC,qBAAqB,eAAe,OAAO,CAAC,0BAA0B;QACxF,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;;;;;;;kCAE3C,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAIb,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;0BAI1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/MobileNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Menu, X, Home, Star, Calendar, Clock, Settings, BookOpen } from 'lucide-react';\nimport TranslatedText from './TranslatedText';\n\ninterface MobileNavigationProps {\n  activeTab: string;\n  onTabChange: (tab: string) => void;\n  className?: string;\n}\n\nexport default function MobileNavigation({ activeTab, onTabChange, className = '' }: MobileNavigationProps) {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const navigationItems = [\n    { id: 'horoscope', label: 'Horoscope', icon: BookOpen },\n    { id: 'guide', label: 'Daily Guide', icon: Clock }\n  ];\n\n  const handleTabChange = (tabId: string) => {\n    onTabChange(tabId);\n    setIsOpen(false);\n  };\n\n  return (\n    <>\n      {/* Mobile Menu Button */}\n      <div className={`md:hidden ${className}`}>\n        <button\n          onClick={() => setIsOpen(!isOpen)}\n          className=\"flex items-center justify-center w-10 h-10 bg-white/10 hover:bg-white/20 rounded-lg transition-colors\"\n        >\n          {isOpen ? <X size={20} className=\"text-white\" /> : <Menu size={20} className=\"text-white\" />}\n        </button>\n      </div>\n\n      {/* Mobile Menu Overlay */}\n      {isOpen && (\n        <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 md:hidden\">\n          <div className=\"absolute top-0 right-0 w-64 h-full bg-gradient-to-b from-purple-900 to-indigo-900 shadow-xl\">\n            <div className=\"p-4\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className=\"text-white font-semibold\">\n                  <TranslatedText text=\"Navigation\" />\n                </h2>\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className=\"text-gray-400 hover:text-white transition-colors\"\n                >\n                  <X size={20} />\n                </button>\n              </div>\n\n              <nav className=\"space-y-2\">\n                {navigationItems.map(({ id, label, icon: Icon }) => (\n                  <button\n                    key={id}\n                    onClick={() => handleTabChange(id)}\n                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${\n                      activeTab === id\n                        ? 'bg-white/20 text-white'\n                        : 'text-gray-300 hover:bg-white/10 hover:text-white'\n                    }`}\n                  >\n                    <Icon size={20} />\n                    <span><TranslatedText text={label} /></span>\n                  </button>\n                ))}\n              </nav>\n\n              <div className=\"mt-8 pt-6 border-t border-white/10\">\n                <button className=\"w-full flex items-center space-x-3 px-4 py-3 text-gray-300 hover:bg-white/10 hover:text-white rounded-lg transition-colors\">\n                  <Settings size={20} />\n                  <span><TranslatedText text=\"Settings\" /></span>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Bottom Navigation for Mobile */}\n      <div className=\"fixed bottom-0 left-0 right-0 bg-black/20 backdrop-blur-sm border-t border-white/10 md:hidden z-40\">\n        <div className=\"flex items-center justify-around py-2\">\n          {navigationItems.slice(0, 4).map(({ id, label, icon: Icon }) => (\n            <button\n              key={id}\n              onClick={() => handleTabChange(id)}\n              className={`flex flex-col items-center space-y-1 px-3 py-2 rounded-lg transition-colors ${\n                activeTab === id\n                  ? 'text-purple-400'\n                  : 'text-gray-400 hover:text-white'\n              }`}\n            >\n              <Icon size={20} />\n              <span className=\"text-xs\">\n                <TranslatedText text={label.split(' ')[0]} />\n              </span>\n            </button>\n          ))}\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAYe,SAAS,iBAAiB,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,EAAyB;IACxG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,kBAAkB;QACtB;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,8MAAA,CAAA,WAAQ;QAAC;QACtD;YAAE,IAAI;YAAS,OAAO;YAAe,MAAM,oMAAA,CAAA,QAAK;QAAC;KAClD;IAED,MAAM,kBAAkB,CAAC;QACvB,YAAY;QACZ,UAAU;IACZ;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAW,CAAC,UAAU,EAAE,WAAW;0BACtC,cAAA,8OAAC;oBACC,SAAS,IAAM,UAAU,CAAC;oBAC1B,WAAU;8BAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;wBAAC,MAAM;wBAAI,WAAU;;;;;6CAAkB,8OAAC,kMAAA,CAAA,OAAI;wBAAC,MAAM;wBAAI,WAAU;;;;;;;;;;;;;;;;YAKhF,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACZ,cAAA,8OAAC,oIAAA,CAAA,UAAc;4CAAC,MAAK;;;;;;;;;;;kDAEvB,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAIb,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,iBAC7C,8OAAC;wCAEC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,CAAC,0EAA0E,EACpF,cAAc,KACV,2BACA,oDACJ;;0DAEF,8OAAC;gDAAK,MAAM;;;;;;0DACZ,8OAAC;0DAAK,cAAA,8OAAC,oIAAA,CAAA,UAAc;oDAAC,MAAM;;;;;;;;;;;;uCATvB;;;;;;;;;;0CAcX,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;sDAChB,8OAAC;sDAAK,cAAA,8OAAC,oIAAA,CAAA,UAAc;gDAAC,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,iBACzD,8OAAC;4BAEC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,4EAA4E,EACtF,cAAc,KACV,oBACA,kCACJ;;8CAEF,8OAAC;oCAAK,MAAM;;;;;;8CACZ,8OAAC;oCAAK,WAAU;8CACd,cAAA,8OAAC,oIAAA,CAAA,UAAc;wCAAC,MAAM,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;2BAVtC;;;;;;;;;;;;;;;;;AAkBnB", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/hooks/useAuth';\nimport { useLanguage } from '@/hooks/useLanguage';\nimport { DashboardData } from '@/types';\nimport { ZODIAC_INFO } from '@/utils/zodiac';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport ZodiacCard from '@/components/ZodiacCard';\nimport TranslatedText from '@/components/TranslatedText';\nimport LanguageSwitcher from '@/components/LanguageSwitcher';\nimport PWAInstaller from '@/components/PWAInstaller';\nimport MobileNavigation from '@/components/MobileNavigation';\nimport { Settings, Star, Calendar, Clock, Palette, Hash, BookOpen } from 'lucide-react';\n\nexport default function Dashboard() {\n  const { user, isAuthenticated, loading: authLoading, getSessionTimeRemaining, isSessionExpired } = useAuth();\n  const { language, setLanguage } = useLanguage();\n  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState<'horoscope' | 'guide'>('horoscope');\n  const [sessionTimeRemaining, setSessionTimeRemaining] = useState<number>(0);\n  const router = useRouter();\n\n  // Update session time remaining every minute\n  useEffect(() => {\n    if (!isAuthenticated) return;\n\n    const updateSessionTime = () => {\n      const remaining = getSessionTimeRemaining();\n      setSessionTimeRemaining(remaining);\n\n      // If session expired, redirect to home\n      if (remaining <= 0 || isSessionExpired()) {\n        console.log('Session expired, redirecting to home');\n        router.push('/');\n        return;\n      }\n    };\n\n    // Update immediately\n    updateSessionTime();\n\n    // Update every minute\n    const interval = setInterval(updateSessionTime, 60000);\n\n    return () => clearInterval(interval);\n  }, [isAuthenticated, getSessionTimeRemaining, isSessionExpired, router]);\n\n  useEffect(() => {\n    if (!authLoading && !isAuthenticated) {\n      router.push('/');\n      return;\n    }\n\n    if (user) {\n      fetchDashboardData();\n    }\n  }, [user, isAuthenticated, authLoading, router, language]);\n\n  const fetchDashboardData = async () => {\n    if (!user) return;\n\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/dashboard?userId=${user.id}&language=${language}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setDashboardData(data.data);\n      } else {\n        setError(data.error || 'Failed to load dashboard data');\n      }\n    } catch (err) {\n      console.error('Dashboard fetch error:', err);\n      setError('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLanguageChange = async (newLanguage: 'en' | 'si') => {\n    // Update user preference in backend\n    if (user) {\n      try {\n        await fetch('/api/dashboard', {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({ userId: user.id, language: newLanguage })\n        });\n      } catch (error) {\n        console.error('Failed to update language preference:', error);\n      }\n    }\n  };\n\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\">\n        <LoadingSpinner message=\"Loading your cosmic insights...\" />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\">\n        <ErrorMessage\n          title=\"Error Loading Dashboard\"\n          message={error}\n          onRetry={fetchDashboardData}\n        />\n      </div>\n    );\n  }\n\n  if (!dashboardData || !user) {\n    return null;\n  }\n\n  const zodiacInfo = ZODIAC_INFO[user.zodiacSign];\n\n  // Fallback if zodiac info is not found\n  if (!zodiacInfo) {\n    console.error('Zodiac info not found for sign:', user.zodiacSign);\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\">\n        <ErrorMessage\n          title=\"Configuration Error\"\n          message=\"Unable to load zodiac information. Please contact support.\"\n          onRetry={fetchDashboardData}\n        />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\">\n      {/* Session Warning */}\n      {sessionTimeRemaining <= 5 && sessionTimeRemaining > 0 && (\n        <div className=\"bg-red-600/90 backdrop-blur-sm text-white px-4 py-2 text-center text-sm font-medium\">\n          ⚠️ Your session will expire in {sessionTimeRemaining} minute{sessionTimeRemaining !== 1 ? 's' : ''}. Please scan your QR code again to continue.\n        </div>\n      )}\n\n      {/* Header */}\n      <header className=\"bg-black/20 backdrop-blur-sm border-b border-white/10\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"text-3xl\">{zodiacInfo.symbol}</div>\n              <div>\n                <h1 className=\"text-xl font-bold text-white\">Welcome, {user.name}</h1>\n                <p className=\"text-gray-300 text-sm\">{zodiacInfo.name} • {zodiacInfo.dates}</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              {/* Session Timer */}\n              <div className=\"hidden md:flex items-center space-x-2 text-sm\">\n                <Clock size={16} className=\"text-yellow-400\" />\n                <span className={`font-medium ${sessionTimeRemaining <= 5 ? 'text-red-400' : sessionTimeRemaining <= 10 ? 'text-yellow-400' : 'text-green-400'}`}>\n                  {sessionTimeRemaining}m\n                </span>\n              </div>\n\n              <LanguageSwitcher onLanguageChange={handleLanguageChange} />\n\n              <button className=\"hidden md:block text-gray-300 hover:text-white transition-colors\">\n                <Settings size={20} />\n              </button>\n\n              <MobileNavigation\n                activeTab={activeTab}\n                onTabChange={setActiveTab}\n              />\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation Tabs */}\n      <nav className=\"bg-black/10 backdrop-blur-sm border-b border-white/10\">\n        <div className=\"max-w-6xl mx-auto px-4\">\n          <div className=\"flex space-x-8\">\n            {[\n              { id: 'horoscope', label: 'Horoscope', icon: BookOpen },\n              { id: 'guide', label: 'Daily Guide', icon: Clock }\n            ].map(({ id, label, icon: Icon }) => (\n              <button\n                key={id}\n                onClick={() => setActiveTab(id as any)}\n                className={`flex items-center space-x-2 py-4 px-2 border-b-2 transition-colors ${\n                  activeTab === id\n                    ? 'border-purple-400 text-white'\n                    : 'border-transparent text-gray-300 hover:text-white'\n                }`}\n              >\n                <Icon size={16} />\n                <span><TranslatedText text={label} /></span>\n              </button>\n            ))}\n          </div>\n        </div>\n      </nav>\n\n      {/* Content */}\n      <main className=\"max-w-6xl mx-auto px-4 py-8 pb-20 md:pb-8\">\n        {activeTab === 'horoscope' && (\n          <div className=\"space-y-6\">\n            {/* Today's Horoscope */}\n            {dashboardData.todayHoroscope && (\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <h2 className=\"text-2xl font-bold text-white mb-4 flex items-center\">\n                  <Star className=\"mr-2 text-yellow-400\" />\n                  <TranslatedText text=\"Today's Horoscope\" />\n                </h2>\n                <p className=\"text-gray-200 leading-relaxed text-lg\">\n                  <TranslatedText text={dashboardData.todayHoroscope.content} />\n                </p>\n              </div>\n            )}\n\n            {/* Weekly Horoscope */}\n            {dashboardData.weeklyHoroscope && (\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <h2 className=\"text-2xl font-bold text-white mb-4 flex items-center\">\n                  <Calendar className=\"mr-2 text-blue-400\" />\n                  <TranslatedText text=\"Weekly Horoscope\" />\n                </h2>\n                <p className=\"text-gray-200 leading-relaxed text-lg\">\n                  <TranslatedText text={dashboardData.weeklyHoroscope.content} />\n                </p>\n              </div>\n            )}\n\n            {/* Monthly Horoscope */}\n            {dashboardData.monthlyHoroscope && (\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <h2 className=\"text-2xl font-bold text-white mb-4 flex items-center\">\n                  <Calendar className=\"mr-2 text-purple-400\" />\n                  <TranslatedText text=\"Monthly Horoscope\" />\n                </h2>\n                <p className=\"text-gray-200 leading-relaxed text-lg\">\n                  <TranslatedText text={dashboardData.monthlyHoroscope.content} />\n                </p>\n              </div>\n            )}\n\n            {/* No Horoscope Content */}\n            {!dashboardData.todayHoroscope && !dashboardData.weeklyHoroscope && !dashboardData.monthlyHoroscope && (\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center\">\n                <BookOpen className=\"mx-auto mb-4 text-gray-400\" size={48} />\n                <h3 className=\"text-xl font-semibold text-white mb-2\">\n                  <TranslatedText text=\"No Horoscope Available\" />\n                </h3>\n                <p className=\"text-gray-300\">\n                  <TranslatedText text=\"Your personalized horoscope content will appear here once added by the admin.\" />\n                </p>\n              </div>\n            )}\n          </div>\n        )}\n\n        {activeTab === 'guide' && (\n          dashboardData.dailyGuide ? (\n          <div className=\"space-y-6\">\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n              <h2 className=\"text-2xl font-bold text-white mb-6 flex items-center\">\n                <Clock className=\"mr-2 text-green-400\" />\n                <TranslatedText text=\"Today's Guidance\" />\n              </h2>\n\n              <div className=\"grid md:grid-cols-2 gap-6 mb-6\">\n                <div className=\"bg-white/5 rounded-lg p-4\">\n                  <div className=\"flex items-center mb-2\">\n                    <Hash className=\"text-yellow-400 mr-2\" size={20} />\n                    <h3 className=\"text-white font-semibold\">\n                      <TranslatedText text=\"Lucky Number\" />\n                    </h3>\n                  </div>\n                  <p className=\"text-2xl font-bold text-yellow-400\">\n                    {dashboardData.dailyGuide.lucky_number}\n                  </p>\n                </div>\n\n                <div className=\"bg-white/5 rounded-lg p-4\">\n                  <div className=\"flex items-center mb-2\">\n                    <Palette className=\"text-pink-400 mr-2\" size={20} />\n                    <h3 className=\"text-white font-semibold\">\n                      <TranslatedText text=\"Lucky Color\" />\n                    </h3>\n                  </div>\n                  <p className=\"text-lg font-semibold text-pink-400\">\n                    <TranslatedText text={dashboardData.dailyGuide.lucky_color} />\n                  </p>\n                </div>\n\n                <div className=\"bg-white/5 rounded-lg p-4 md:col-span-2\">\n                  <div className=\"flex items-center mb-2\">\n                    <Clock className=\"text-blue-400 mr-2\" size={20} />\n                    <h3 className=\"text-white font-semibold\">\n                      <TranslatedText text=\"Lucky Time\" />\n                    </h3>\n                  </div>\n                  <p className=\"text-lg font-semibold text-blue-400\">\n                    <TranslatedText text={dashboardData.dailyGuide.lucky_time} />\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"bg-white/5 rounded-lg p-4\">\n                <h3 className=\"text-white font-semibold mb-3\">\n                  <TranslatedText text=\"Daily Advice\" />\n                </h3>\n                <p className=\"text-gray-200 leading-relaxed\">\n                  <TranslatedText text={dashboardData.dailyGuide.advice} />\n                </p>\n              </div>\n            </div>\n          </div>\n          ) : (\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center\">\n              <Clock className=\"mx-auto mb-4 text-gray-400\" size={48} />\n              <h3 className=\"text-xl font-semibold text-white mb-2\">\n                <TranslatedText text=\"No Daily Guide Available\" />\n              </h3>\n              <p className=\"text-gray-300\">\n                <TranslatedText text=\"Your personalized daily guide will appear here once added by the admin.\" />\n              </p>\n            </div>\n          )\n        )}\n      </main>\n\n      {/* PWA Installer */}\n      <PWAInstaller />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAfA;;;;;;;;;;;;;;AAiBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,uBAAuB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACzG,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAC5C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAClE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,iBAAiB;QAEtB,MAAM,oBAAoB;YACxB,MAAM,YAAY;YAClB,wBAAwB;YAExB,uCAAuC;YACvC,IAAI,aAAa,KAAK,oBAAoB;gBACxC,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;QAEA,qBAAqB;QACrB;QAEA,sBAAsB;QACtB,MAAM,WAAW,YAAY,mBAAmB;QAEhD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAiB;QAAyB;QAAkB;KAAO;IAEvE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;YACpC,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;QAAM;QAAiB;QAAa;QAAQ;KAAS;IAEzD,MAAM,qBAAqB;QACzB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,UAAU;YACpF,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,KAAK,IAAI;YAC5B,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,oCAAoC;QACpC,IAAI,MAAM;YACR,IAAI;gBACF,MAAM,MAAM,kBAAkB;oBAC5B,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBAAE,QAAQ,KAAK,EAAE;wBAAE,UAAU;oBAAY;gBAChE;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;YACzD;QACF;IACF;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;gBAAC,SAAQ;;;;;;;;;;;IAG9B;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;gBACX,OAAM;gBACN,SAAS;gBACT,SAAS;;;;;;;;;;;IAIjB;IAEA,IAAI,CAAC,iBAAiB,CAAC,MAAM;QAC3B,OAAO;IACT;IAEA,MAAM,aAAa,sHAAA,CAAA,cAAW,CAAC,KAAK,UAAU,CAAC;IAE/C,uCAAuC;IACvC,IAAI,CAAC,YAAY;QACf,QAAQ,KAAK,CAAC,mCAAmC,KAAK,UAAU;QAChE,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;gBACX,OAAM;gBACN,SAAQ;gBACR,SAAS;;;;;;;;;;;IAIjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,wBAAwB,KAAK,uBAAuB,mBACnD,8OAAC;gBAAI,WAAU;;oBAAsF;oBACnE;oBAAqB;oBAAQ,yBAAyB,IAAI,MAAM;oBAAG;;;;;;;0BAKvG,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAY,WAAW,MAAM;;;;;;kDAC5C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAA+B;oDAAU,KAAK,IAAI;;;;;;;0DAChE,8OAAC;gDAAE,WAAU;;oDAAyB,WAAW,IAAI;oDAAC;oDAAI,WAAW,KAAK;;;;;;;;;;;;;;;;;;;0CAI9E,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC3B,8OAAC;gDAAK,WAAW,CAAC,YAAY,EAAE,wBAAwB,IAAI,iBAAiB,wBAAwB,KAAK,oBAAoB,kBAAkB;;oDAC7I;oDAAqB;;;;;;;;;;;;;kDAI1B,8OAAC,sIAAA,CAAA,UAAgB;wCAAC,kBAAkB;;;;;;kDAEpC,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;;;;;;kDAGlB,8OAAC,sIAAA,CAAA,UAAgB;wCACf,WAAW;wCACX,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,IAAI;gCAAa,OAAO;gCAAa,MAAM,8MAAA,CAAA,WAAQ;4BAAC;4BACtD;gCAAE,IAAI;gCAAS,OAAO;gCAAe,MAAM,oMAAA,CAAA,QAAK;4BAAC;yBAClD,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,iBAC9B,8OAAC;gCAEC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,mEAAmE,EAC7E,cAAc,KACV,iCACA,qDACJ;;kDAEF,8OAAC;wCAAK,MAAM;;;;;;kDACZ,8OAAC;kDAAK,cAAA,8OAAC,oIAAA,CAAA,UAAc;4CAAC,MAAM;;;;;;;;;;;;+BATvB;;;;;;;;;;;;;;;;;;;;0BAiBf,8OAAC;gBAAK,WAAU;;oBACb,cAAc,6BACb,8OAAC;wBAAI,WAAU;;4BAEZ,cAAc,cAAc,kBAC3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC,oIAAA,CAAA,UAAc;gDAAC,MAAK;;;;;;;;;;;;kDAEvB,8OAAC;wCAAE,WAAU;kDACX,cAAA,8OAAC,oIAAA,CAAA,UAAc;4CAAC,MAAM,cAAc,cAAc,CAAC,OAAO;;;;;;;;;;;;;;;;;4BAM/D,cAAc,eAAe,kBAC5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,UAAc;gDAAC,MAAK;;;;;;;;;;;;kDAEvB,8OAAC;wCAAE,WAAU;kDACX,cAAA,8OAAC,oIAAA,CAAA,UAAc;4CAAC,MAAM,cAAc,eAAe,CAAC,OAAO;;;;;;;;;;;;;;;;;4BAMhE,cAAc,gBAAgB,kBAC7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,UAAc;gDAAC,MAAK;;;;;;;;;;;;kDAEvB,8OAAC;wCAAE,WAAU;kDACX,cAAA,8OAAC,oIAAA,CAAA,UAAc;4CAAC,MAAM,cAAc,gBAAgB,CAAC,OAAO;;;;;;;;;;;;;;;;;4BAMjE,CAAC,cAAc,cAAc,IAAI,CAAC,cAAc,eAAe,IAAI,CAAC,cAAc,gBAAgB,kBACjG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAA6B,MAAM;;;;;;kDACvD,8OAAC;wCAAG,WAAU;kDACZ,cAAA,8OAAC,oIAAA,CAAA,UAAc;4CAAC,MAAK;;;;;;;;;;;kDAEvB,8OAAC;wCAAE,WAAU;kDACX,cAAA,8OAAC,oIAAA,CAAA,UAAc;4CAAC,MAAK;;;;;;;;;;;;;;;;;;;;;;;oBAO9B,cAAc,WAAW,CACxB,cAAc,UAAU,iBACxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC,oIAAA,CAAA,UAAc;4CAAC,MAAK;;;;;;;;;;;;8CAGvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;4DAAuB,MAAM;;;;;;sEAC7C,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,oIAAA,CAAA,UAAc;gEAAC,MAAK;;;;;;;;;;;;;;;;;8DAGzB,8OAAC;oDAAE,WAAU;8DACV,cAAc,UAAU,CAAC,YAAY;;;;;;;;;;;;sDAI1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;4DAAqB,MAAM;;;;;;sEAC9C,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,oIAAA,CAAA,UAAc;gEAAC,MAAK;;;;;;;;;;;;;;;;;8DAGzB,8OAAC;oDAAE,WAAU;8DACX,cAAA,8OAAC,oIAAA,CAAA,UAAc;wDAAC,MAAM,cAAc,UAAU,CAAC,WAAW;;;;;;;;;;;;;;;;;sDAI9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;4DAAqB,MAAM;;;;;;sEAC5C,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC,oIAAA,CAAA,UAAc;gEAAC,MAAK;;;;;;;;;;;;;;;;;8DAGzB,8OAAC;oDAAE,WAAU;8DACX,cAAA,8OAAC,oIAAA,CAAA,UAAc;wDAAC,MAAM,cAAc,UAAU,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;8CAK/D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,oIAAA,CAAA,UAAc;gDAAC,MAAK;;;;;;;;;;;sDAEvB,8OAAC;4CAAE,WAAU;sDACX,cAAA,8OAAC,oIAAA,CAAA,UAAc;gDAAC,MAAM,cAAc,UAAU,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAM3D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;gCAA6B,MAAM;;;;;;0CACpD,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC,oIAAA,CAAA,UAAc;oCAAC,MAAK;;;;;;;;;;;0CAEvB,8OAAC;gCAAE,WAAU;0CACX,cAAA,8OAAC,oIAAA,CAAA,UAAc;oCAAC,MAAK;;;;;;;;;;;;;;;;4BAI7B;;;;;;;0BAIF,8OAAC,kIAAA,CAAA,UAAY;;;;;;;;;;;AAGnB", "debugId": null}}]}