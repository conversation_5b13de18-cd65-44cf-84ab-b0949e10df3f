{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Helper function to disconnect Prisma (useful for serverless)\nexport async function disconnectPrisma() {\n  await prisma.$disconnect();\n}\n\n// Helper function to check database connection\nexport async function checkDatabaseConnection() {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/qr.ts"], "sourcesContent": ["import { v4 as uuidv4 } from 'uuid';\nimport QRCode from 'qrcode';\n\nexport function generateQRToken(): string {\n  return uuidv4();\n}\n\nexport function generateQRUrl(token: string, baseUrl: string = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'): string {\n  return `${baseUrl}/qr/${token}`;\n}\n\nexport async function generateQRCodeImage(token: string, baseUrl?: string): Promise<string> {\n  const url = generateQRUrl(token, baseUrl);\n  try {\n    const qrCodeDataUrl = await QRCode.toDataURL(url, {\n      width: 300,\n      margin: 2,\n      color: {\n        dark: '#000000',\n        light: '#FFFFFF'\n      }\n    });\n    return qrCodeDataUrl;\n  } catch (error) {\n    console.error('Error generating QR code:', error);\n    throw new Error('Failed to generate QR code');\n  }\n}\n\nexport function extractTokenFromUrl(url: string): string | null {\n  try {\n    const urlObj = new URL(url);\n    const pathParts = urlObj.pathname.split('/');\n    const qrIndex = pathParts.indexOf('qr');\n    \n    if (qrIndex !== -1 && qrIndex < pathParts.length - 1) {\n      return pathParts[qrIndex + 1];\n    }\n    \n    return null;\n  } catch (error) {\n    console.error('Error extracting token from URL:', error);\n    return null;\n  }\n}\n\nexport function isValidQRToken(token: string): boolean {\n  // UUID v4 regex pattern\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(token);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;AACd;AAEO,SAAS,cAAc,KAAa,EAAE,UAAkB,6DAAmC,uBAAuB;IACvH,OAAO,GAAG,QAAQ,IAAI,EAAE,OAAO;AACjC;AAEO,eAAe,oBAAoB,KAAa,EAAE,OAAgB;IACvE,MAAM,MAAM,cAAc,OAAO;IACjC,IAAI;QACF,MAAM,gBAAgB,MAAM,wIAAA,CAAA,UAAM,CAAC,SAAS,CAAC,KAAK;YAChD,OAAO;YACP,QAAQ;YACR,OAAO;gBACL,MAAM;gBACN,OAAO;YACT;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,SAAS,oBAAoB,GAAW;IAC7C,IAAI;QACF,MAAM,SAAS,IAAI,IAAI;QACvB,MAAM,YAAY,OAAO,QAAQ,CAAC,KAAK,CAAC;QACxC,MAAM,UAAU,UAAU,OAAO,CAAC;QAElC,IAAI,YAAY,CAAC,KAAK,UAAU,UAAU,MAAM,GAAG,GAAG;YACpD,OAAO,SAAS,CAAC,UAAU,EAAE;QAC/B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAEO,SAAS,eAAe,KAAa;IAC1C,wBAAwB;IACxB,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/zodiac.ts"], "sourcesContent": ["import { ZodiacSign } from '@prisma/client';\n\nexport const ZODIAC_SIGNS: ZodiacSign[] = [\n  'aries', 'taurus', 'gemini', 'cancer',\n  'leo', 'virgo', 'libra', 'scorpio',\n  'sagittarius', 'capricorn', 'aquarius', 'pisces'\n];\n\nexport const ZODIAC_INFO = {\n  aries: { name: '<PERSON><PERSON>', symbol: '♈', dates: 'Mar 21 - Apr 19', element: 'Fire' },\n  taurus: { name: 'Taurus', symbol: '♉', dates: 'Apr 20 - May 20', element: 'Earth' },\n  gemini: { name: 'Gemini', symbol: '♊', dates: 'May 21 - Jun 20', element: 'Air' },\n  cancer: { name: 'Cancer', symbol: '♋', dates: 'Jun 21 - Jul 22', element: 'Water' },\n  leo: { name: '<PERSON>', symbol: '♌', dates: 'Jul 23 - Aug 22', element: 'Fire' },\n  virgo: { name: '<PERSON>ir<PERSON>', symbol: '♍', dates: 'Aug 23 - Sep 22', element: 'Earth' },\n  libra: { name: '<PERSON><PERSON>', symbol: '♎', dates: 'Sep 23 - Oct 22', element: 'Air' },\n  scorpio: { name: '<PERSON><PERSON><PERSON>', symbol: '♏', dates: 'Oct 23 - Nov 21', element: 'Water' },\n  sagittarius: { name: 'Sagittarius', symbol: '♐', dates: 'Nov 22 - Dec 21', element: 'Fire' },\n  capricorn: { name: 'Capricorn', symbol: '♑', dates: 'Dec 22 - Jan 19', element: 'Earth' },\n  aquarius: { name: 'Aquarius', symbol: '♒', dates: 'Jan 20 - Feb 18', element: 'Air' },\n  pisces: { name: 'Pisces', symbol: '♓', dates: 'Feb 19 - Mar 20', element: 'Water' }\n};\n\nexport function getZodiacFromDate(birthDate: string): ZodiacSign {\n  const date = new Date(birthDate);\n  const month = date.getMonth() + 1; // 1-12\n  const day = date.getDate();\n\n  if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) return 'aries';\n  if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) return 'taurus';\n  if ((month === 5 && day >= 21) || (month === 6 && day <= 20)) return 'gemini';\n  if ((month === 6 && day >= 21) || (month === 7 && day <= 22)) return 'cancer';\n  if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) return 'leo';\n  if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) return 'virgo';\n  if ((month === 9 && day >= 23) || (month === 10 && day <= 22)) return 'libra';\n  if ((month === 10 && day >= 23) || (month === 11 && day <= 21)) return 'scorpio';\n  if ((month === 11 && day >= 22) || (month === 12 && day <= 21)) return 'sagittarius';\n  if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) return 'capricorn';\n  if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) return 'aquarius';\n  return 'pisces';\n}\n\nexport function getZodiacColors(sign: ZodiacSign): string[] {\n  const colorMap = {\n    aries: ['#FF6B6B', '#FF4757'],\n    taurus: ['#2ECC71', '#27AE60'],\n    gemini: ['#F39C12', '#E67E22'],\n    cancer: ['#3498DB', '#2980B9'],\n    leo: ['#E74C3C', '#C0392B'],\n    virgo: ['#1ABC9C', '#16A085'],\n    libra: ['#9B59B6', '#8E44AD'],\n    scorpio: ['#34495E', '#2C3E50'],\n    sagittarius: ['#E67E22', '#D35400'],\n    capricorn: ['#95A5A6', '#7F8C8D'],\n    aquarius: ['#3498DB', '#2980B9'],\n    pisces: ['#1ABC9C', '#16A085']\n  };\n  return colorMap[sign];\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,eAA6B;IACxC;IAAS;IAAU;IAAU;IAC7B;IAAO;IAAS;IAAS;IACzB;IAAe;IAAa;IAAY;CACzC;AAEM,MAAM,cAAc;IACzB,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC/E,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAClF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IAChF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAClF,KAAK;QAAE,MAAM;QAAO,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC3E,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAChF,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IAC9E,SAAS;QAAE,MAAM;QAAW,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IACpF,aAAa;QAAE,MAAM;QAAe,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC3F,WAAW;QAAE,MAAM;QAAa,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IACxF,UAAU;QAAE,MAAM;QAAY,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IACpF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;AACpF;AAEO,SAAS,kBAAkB,SAAiB;IACjD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,QAAQ,KAAK,QAAQ,KAAK,GAAG,OAAO;IAC1C,MAAM,MAAM,KAAK,OAAO;IAExB,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACtE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACvE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACvE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACtE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,OAAO;AACT;AAEO,SAAS,gBAAgB,IAAgB;IAC9C,MAAM,WAAW;QACf,OAAO;YAAC;YAAW;SAAU;QAC7B,QAAQ;YAAC;YAAW;SAAU;QAC9B,QAAQ;YAAC;YAAW;SAAU;QAC9B,QAAQ;YAAC;YAAW;SAAU;QAC9B,KAAK;YAAC;YAAW;SAAU;QAC3B,OAAO;YAAC;YAAW;SAAU;QAC7B,OAAO;YAAC;YAAW;SAAU;QAC7B,SAAS;YAAC;YAAW;SAAU;QAC/B,aAAa;YAAC;YAAW;SAAU;QACnC,WAAW;YAAC;YAAW;SAAU;QACjC,UAAU;YAAC;YAAW;SAAU;QAChC,QAAQ;YAAC;YAAW;SAAU;IAChC;IACA,OAAO,QAAQ,CAAC,KAAK;AACvB", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/validation.ts"], "sourcesContent": ["import { ZodiacSign, LanguageCode, HoroscopeType } from '@prisma/client';\nimport { ZODIAC_SIGNS } from './zodiac';\n\n// Email validation\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email) && email.length <= 254;\n}\n\n// Name validation\nexport function isValidName(name: string): boolean {\n  if (!name || typeof name !== 'string') return false;\n  \n  // Remove extra whitespace and check length\n  const trimmedName = name.trim();\n  if (trimmedName.length < 2 || trimmedName.length > 100) return false;\n  \n  // Check for valid characters (letters, spaces, hyphens, apostrophes)\n  const nameRegex = /^[a-zA-Z\\s\\-']+$/;\n  return nameRegex.test(trimmedName);\n}\n\n// Date validation\nexport function isValidDate(dateString: string): boolean {\n  if (!dateString || typeof dateString !== 'string') return false;\n  \n  const date = new Date(dateString);\n  if (isNaN(date.getTime())) return false;\n  \n  // Check if date is reasonable (not in future, not too far in past)\n  const now = new Date();\n  const minDate = new Date('1900-01-01');\n  \n  return date <= now && date >= minDate;\n}\n\n// Zodiac sign validation\nexport function isValidZodiacSign(sign: string): sign is ZodiacSign {\n  return ZODIAC_SIGNS.includes(sign as ZodiacSign);\n}\n\n// Language validation\nexport function isValidLanguage(lang: string): lang is LanguageCode {\n  return lang === 'en' || lang === 'si';\n}\n\n// Horoscope type validation\nexport function isValidHoroscopeType(type: string): type is HoroscopeType {\n  return ['daily', 'weekly', 'monthly'].includes(type);\n}\n\n// Content validation (for horoscopes, advice, etc.)\nexport function isValidContent(content: string): boolean {\n  if (!content || typeof content !== 'string') return false;\n  \n  const trimmedContent = content.trim();\n  if (trimmedContent.length < 10 || trimmedContent.length > 5000) return false;\n  \n  // Check for potentially malicious content\n  const suspiciousPatterns = [\n    /<script/i,\n    /javascript:/i,\n    /on\\w+\\s*=/i,\n    /<iframe/i,\n    /<object/i,\n    /<embed/i\n  ];\n  \n  return !suspiciousPatterns.some(pattern => pattern.test(content));\n}\n\n// UUID validation\nexport function isValidUUID(uuid: string): boolean {\n  if (!uuid || typeof uuid !== 'string') return false;\n  \n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(uuid);\n}\n\n// Sanitize string input\nexport function sanitizeString(input: string): string {\n  if (!input || typeof input !== 'string') return '';\n  \n  return input\n    .trim()\n    .replace(/[<>]/g, '') // Remove angle brackets\n    .replace(/javascript:/gi, '') // Remove javascript: protocol\n    .replace(/on\\w+\\s*=/gi, '') // Remove event handlers\n    .substring(0, 1000); // Limit length\n}\n\n// Sanitize HTML content (basic)\nexport function sanitizeHTML(html: string): string {\n  if (!html || typeof html !== 'string') return '';\n  \n  // Allow only basic formatting tags\n  const allowedTags = ['p', 'br', 'strong', 'em', 'u'];\n  const tagRegex = /<\\/?(\\w+)[^>]*>/g;\n  \n  return html.replace(tagRegex, (match, tagName) => {\n    if (allowedTags.includes(tagName.toLowerCase())) {\n      return match;\n    }\n    return '';\n  });\n}\n\n// Validate pagination parameters\nexport function validatePagination(page?: string, limit?: string): { page: number; limit: number } {\n  const parsedPage = parseInt(page || '1', 10);\n  const parsedLimit = parseInt(limit || '10', 10);\n  \n  return {\n    page: Math.max(1, Math.min(parsedPage, 1000)), // Max 1000 pages\n    limit: Math.max(1, Math.min(parsedLimit, 100)) // Max 100 items per page\n  };\n}\n\n// Validate search query\nexport function isValidSearchQuery(query: string): boolean {\n  if (!query || typeof query !== 'string') return false;\n  \n  const trimmedQuery = query.trim();\n  if (trimmedQuery.length < 1 || trimmedQuery.length > 100) return false;\n  \n  // Check for SQL injection patterns\n  const sqlPatterns = [\n    /union\\s+select/i,\n    /drop\\s+table/i,\n    /delete\\s+from/i,\n    /insert\\s+into/i,\n    /update\\s+set/i,\n    /--/,\n    /\\/\\*/,\n    /\\*\\//\n  ];\n  \n  return !sqlPatterns.some(pattern => pattern.test(query));\n}\n\n// Rate limiting key generation\nexport function generateRateLimitKey(ip: string, endpoint: string, userId?: string): string {\n  const baseKey = `${ip}:${endpoint}`;\n  return userId ? `${baseKey}:${userId}` : baseKey;\n}\n\n// Password strength validation (if implementing password auth)\nexport function isStrongPassword(password: string): boolean {\n  if (!password || typeof password !== 'string') return false;\n  \n  // At least 8 characters, contains uppercase, lowercase, number, and special character\n  const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/;\n  return strongPasswordRegex.test(password) && password.length <= 128;\n}\n\n// Validate API request body\nexport function validateRequestBody(body: any, requiredFields: string[]): { isValid: boolean; errors: string[] } {\n  const errors: string[] = [];\n  \n  if (!body || typeof body !== 'object') {\n    return { isValid: false, errors: ['Invalid request body'] };\n  }\n  \n  // Check required fields\n  for (const field of requiredFields) {\n    if (!(field in body) || body[field] === null || body[field] === undefined) {\n      errors.push(`Missing required field: ${field}`);\n    }\n  }\n  \n  return { isValid: errors.length === 0, errors };\n}\n\n// Escape special characters for database queries\nexport function escapeSpecialChars(input: string): string {\n  if (!input || typeof input !== 'string') return '';\n  \n  return input\n    .replace(/'/g, \"''\") // Escape single quotes\n    .replace(/\\\\/g, '\\\\\\\\') // Escape backslashes\n    .replace(/\\0/g, '\\\\0') // Escape null bytes\n    .replace(/\\n/g, '\\\\n') // Escape newlines\n    .replace(/\\r/g, '\\\\r') // Escape carriage returns\n    .replace(/\\x1a/g, '\\\\Z'); // Escape ctrl+Z\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AACA;;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,MAAM,IAAI;AACnD;AAGO,SAAS,YAAY,IAAY;IACtC,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;IAE9C,2CAA2C;IAC3C,MAAM,cAAc,KAAK,IAAI;IAC7B,IAAI,YAAY,MAAM,GAAG,KAAK,YAAY,MAAM,GAAG,KAAK,OAAO;IAE/D,qEAAqE;IACrE,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAGO,SAAS,YAAY,UAAkB;IAC5C,IAAI,CAAC,cAAc,OAAO,eAAe,UAAU,OAAO;IAE1D,MAAM,OAAO,IAAI,KAAK;IACtB,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,mEAAmE;IACnE,MAAM,MAAM,IAAI;IAChB,MAAM,UAAU,IAAI,KAAK;IAEzB,OAAO,QAAQ,OAAO,QAAQ;AAChC;AAGO,SAAS,kBAAkB,IAAY;IAC5C,OAAO,wHAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;AAC/B;AAGO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,SAAS,QAAQ,SAAS;AACnC;AAGO,SAAS,qBAAqB,IAAY;IAC/C,OAAO;QAAC;QAAS;QAAU;KAAU,CAAC,QAAQ,CAAC;AACjD;AAGO,SAAS,eAAe,OAAe;IAC5C,IAAI,CAAC,WAAW,OAAO,YAAY,UAAU,OAAO;IAEpD,MAAM,iBAAiB,QAAQ,IAAI;IACnC,IAAI,eAAe,MAAM,GAAG,MAAM,eAAe,MAAM,GAAG,MAAM,OAAO;IAEvE,0CAA0C;IAC1C,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,CAAC,mBAAmB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;AAC1D;AAGO,SAAS,YAAY,IAAY;IACtC,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;IAE9C,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU,OAAO;IAEhD,OAAO,MACJ,IAAI,GACJ,OAAO,CAAC,SAAS,IAAI,wBAAwB;KAC7C,OAAO,CAAC,iBAAiB,IAAI,8BAA8B;KAC3D,OAAO,CAAC,eAAe,IAAI,wBAAwB;KACnD,SAAS,CAAC,GAAG,OAAO,eAAe;AACxC;AAGO,SAAS,aAAa,IAAY;IACvC,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;IAE9C,mCAAmC;IACnC,MAAM,cAAc;QAAC;QAAK;QAAM;QAAU;QAAM;KAAI;IACpD,MAAM,WAAW;IAEjB,OAAO,KAAK,OAAO,CAAC,UAAU,CAAC,OAAO;QACpC,IAAI,YAAY,QAAQ,CAAC,QAAQ,WAAW,KAAK;YAC/C,OAAO;QACT;QACA,OAAO;IACT;AACF;AAGO,SAAS,mBAAmB,IAAa,EAAE,KAAc;IAC9D,MAAM,aAAa,SAAS,QAAQ,KAAK;IACzC,MAAM,cAAc,SAAS,SAAS,MAAM;IAE5C,OAAO;QACL,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,YAAY;QACvC,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,aAAa,MAAM,yBAAyB;IAC1E;AACF;AAGO,SAAS,mBAAmB,KAAa;IAC9C,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU,OAAO;IAEhD,MAAM,eAAe,MAAM,IAAI;IAC/B,IAAI,aAAa,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,KAAK,OAAO;IAEjE,mCAAmC;IACnC,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,CAAC,YAAY,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;AACnD;AAGO,SAAS,qBAAqB,EAAU,EAAE,QAAgB,EAAE,MAAe;IAChF,MAAM,UAAU,GAAG,GAAG,CAAC,EAAE,UAAU;IACnC,OAAO,SAAS,GAAG,QAAQ,CAAC,EAAE,QAAQ,GAAG;AAC3C;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,IAAI,CAAC,YAAY,OAAO,aAAa,UAAU,OAAO;IAEtD,sFAAsF;IACtF,MAAM,sBAAsB;IAC5B,OAAO,oBAAoB,IAAI,CAAC,aAAa,SAAS,MAAM,IAAI;AAClE;AAGO,SAAS,oBAAoB,IAAS,EAAE,cAAwB;IACrE,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;QACrC,OAAO;YAAE,SAAS;YAAO,QAAQ;gBAAC;aAAuB;QAAC;IAC5D;IAEA,wBAAwB;IACxB,KAAK,MAAM,SAAS,eAAgB;QAClC,IAAI,CAAC,CAAC,SAAS,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,KAAK,WAAW;YACzE,OAAO,IAAI,CAAC,CAAC,wBAAwB,EAAE,OAAO;QAChD;IACF;IAEA,OAAO;QAAE,SAAS,OAAO,MAAM,KAAK;QAAG;IAAO;AAChD;AAGO,SAAS,mBAAmB,KAAa;IAC9C,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU,OAAO;IAEhD,OAAO,MACJ,OAAO,CAAC,MAAM,MAAM,uBAAuB;KAC3C,OAAO,CAAC,OAAO,QAAQ,qBAAqB;KAC5C,OAAO,CAAC,OAAO,OAAO,oBAAoB;KAC1C,OAAO,CAAC,OAAO,OAAO,kBAAkB;KACxC,OAAO,CAAC,OAAO,OAAO,0BAA0B;KAChD,OAAO,CAAC,SAAS,QAAQ,gBAAgB;AAC9C", "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/security.ts"], "sourcesContent": ["import crypto from 'crypto';\n\n// Generate cryptographically secure random token\nexport function generateS<PERSON>ureToken(length: number = 32): string {\n  return crypto.randomBytes(length).toString('hex');\n}\n\n// Generate secure QR token (UUID v4)\nexport function generateSecureQRToken(): string {\n  return crypto.randomUUID();\n}\n\n// Hash sensitive data\nexport function hashData(data: string, salt?: string): { hash: string; salt: string } {\n  const actualSalt = salt || crypto.randomBytes(16).toString('hex');\n  const hash = crypto.pbkdf2Sync(data, actualSalt, 10000, 64, 'sha512').toString('hex');\n  return { hash, salt: actualSalt };\n}\n\n// Verify hashed data\nexport function verifyHash(data: string, hash: string, salt: string): boolean {\n  const verifyHash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512').toString('hex');\n  return crypto.timingSafeEqual(Buffer.from(hash, 'hex'), Buffer.from(verifyHash, 'hex'));\n}\n\n// Encrypt sensitive data\nexport function encryptData(data: string, key?: string): { encrypted: string; iv: string; key: string } {\n  const actualKey = key || crypto.randomBytes(32).toString('hex');\n  const iv = crypto.randomBytes(16);\n  const cipher = crypto.createCipher('aes-256-cbc', actualKey);\n  \n  let encrypted = cipher.update(data, 'utf8', 'hex');\n  encrypted += cipher.final('hex');\n  \n  return {\n    encrypted,\n    iv: iv.toString('hex'),\n    key: actualKey\n  };\n}\n\n// Decrypt sensitive data\nexport function decryptData(encryptedData: string, key: string, iv: string): string {\n  const decipher = crypto.createDecipher('aes-256-cbc', key);\n  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');\n  decrypted += decipher.final('utf8');\n  return decrypted;\n}\n\n// Generate HMAC signature\nexport function generateHMAC(data: string, secret: string): string {\n  return crypto.createHmac('sha256', secret).update(data).digest('hex');\n}\n\n// Verify HMAC signature\nexport function verifyHMAC(data: string, signature: string, secret: string): boolean {\n  const expectedSignature = generateHMAC(data, secret);\n  return crypto.timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(expectedSignature, 'hex'));\n}\n\n// Generate secure session token\nexport function generateSessionToken(): string {\n  const timestamp = Date.now().toString();\n  const randomBytes = crypto.randomBytes(16).toString('hex');\n  return `${timestamp}.${randomBytes}`;\n}\n\n// Validate session token\nexport function validateSessionToken(token: string, maxAge: number = 24 * 60 * 60 * 1000): boolean {\n  try {\n    const [timestamp, randomPart] = token.split('.');\n    if (!timestamp || !randomPart) return false;\n    \n    const tokenTime = parseInt(timestamp, 10);\n    const now = Date.now();\n    \n    return (now - tokenTime) <= maxAge;\n  } catch {\n    return false;\n  }\n}\n\n// Rate limiting with sliding window\nexport class SlidingWindowRateLimit {\n  private windows: Map<string, number[]> = new Map();\n  \n  constructor(\n    private maxRequests: number,\n    private windowSizeMs: number\n  ) {}\n  \n  isAllowed(key: string): boolean {\n    const now = Date.now();\n    const windowStart = now - this.windowSizeMs;\n    \n    // Get or create window for this key\n    let requests = this.windows.get(key) || [];\n    \n    // Remove old requests outside the window\n    requests = requests.filter(timestamp => timestamp > windowStart);\n    \n    // Check if we're within the limit\n    if (requests.length >= this.maxRequests) {\n      return false;\n    }\n    \n    // Add current request\n    requests.push(now);\n    this.windows.set(key, requests);\n    \n    return true;\n  }\n  \n  getRemainingRequests(key: string): number {\n    const now = Date.now();\n    const windowStart = now - this.windowSizeMs;\n    const requests = this.windows.get(key) || [];\n    const validRequests = requests.filter(timestamp => timestamp > windowStart);\n    \n    return Math.max(0, this.maxRequests - validRequests.length);\n  }\n  \n  getResetTime(key: string): number {\n    const requests = this.windows.get(key) || [];\n    if (requests.length === 0) return 0;\n    \n    const oldestRequest = Math.min(...requests);\n    return oldestRequest + this.windowSizeMs;\n  }\n  \n  cleanup(): void {\n    const now = Date.now();\n    \n    for (const [key, requests] of this.windows.entries()) {\n      const windowStart = now - this.windowSizeMs;\n      const validRequests = requests.filter(timestamp => timestamp > windowStart);\n      \n      if (validRequests.length === 0) {\n        this.windows.delete(key);\n      } else {\n        this.windows.set(key, validRequests);\n      }\n    }\n  }\n}\n\n// IP address validation and normalization\nexport function normalizeIP(ip: string): string {\n  // Handle IPv6 mapped IPv4 addresses\n  if (ip.startsWith('::ffff:')) {\n    return ip.substring(7);\n  }\n  \n  // Handle localhost variations\n  if (ip === '::1' || ip === '127.0.0.1') {\n    return 'localhost';\n  }\n  \n  return ip;\n}\n\n// Check if IP is in allowed range (for admin access)\nexport function isIPAllowed(ip: string, allowedRanges: string[]): boolean {\n  const normalizedIP = normalizeIP(ip);\n  \n  // For development, allow localhost\n  if (process.env.NODE_ENV === 'development' && normalizedIP === 'localhost') {\n    return true;\n  }\n  \n  // Check against allowed ranges\n  return allowedRanges.some(range => {\n    if (range === normalizedIP) return true;\n    \n    // Simple CIDR check (basic implementation)\n    if (range.includes('/')) {\n      const [network, prefixLength] = range.split('/');\n      // This is a simplified check - in production, use a proper CIDR library\n      return normalizedIP.startsWith(network.split('.').slice(0, parseInt(prefixLength) / 8).join('.'));\n    }\n    \n    return false;\n  });\n}\n\n// Secure random string generation for various purposes\nexport function generateSecureRandomString(length: number, charset?: string): string {\n  const defaultCharset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  const actualCharset = charset || defaultCharset;\n  \n  let result = '';\n  const bytes = crypto.randomBytes(length);\n  \n  for (let i = 0; i < length; i++) {\n    result += actualCharset[bytes[i] % actualCharset.length];\n  }\n  \n  return result;\n}\n\n// Content Security Policy nonce generation\nexport function generateCSPNonce(): string {\n  return crypto.randomBytes(16).toString('base64');\n}\n\n// Secure headers configuration\nexport const SECURITY_HEADERS = {\n  'X-Content-Type-Options': 'nosniff',\n  'X-Frame-Options': 'DENY',\n  'X-XSS-Protection': '1; mode=block',\n  'Referrer-Policy': 'strict-origin-when-cross-origin',\n  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',\n  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'\n};\n\n// Input sanitization for different contexts\nexport function sanitizeForHTML(input: string): string {\n  return input\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;')\n    .replace(/\\//g, '&#x2F;');\n}\n\nexport function sanitizeForSQL(input: string): string {\n  return input.replace(/'/g, \"''\");\n}\n\nexport function sanitizeForJS(input: string): string {\n  return input\n    .replace(/\\\\/g, '\\\\\\\\')\n    .replace(/'/g, \"\\\\'\")\n    .replace(/\"/g, '\\\\\"')\n    .replace(/\\n/g, '\\\\n')\n    .replace(/\\r/g, '\\\\r')\n    .replace(/\\t/g, '\\\\t');\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,SAAS,oBAAoB,SAAiB,EAAE;IACrD,OAAO,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,QAAQ,QAAQ,CAAC;AAC7C;AAGO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAM,CAAC,UAAU;AAC1B;AAGO,SAAS,SAAS,IAAY,EAAE,IAAa;IAClD,MAAM,aAAa,QAAQ,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC;IAC3D,MAAM,OAAO,qGAAA,CAAA,UAAM,CAAC,UAAU,CAAC,MAAM,YAAY,OAAO,IAAI,UAAU,QAAQ,CAAC;IAC/E,OAAO;QAAE;QAAM,MAAM;IAAW;AAClC;AAGO,SAAS,WAAW,IAAY,EAAE,IAAY,EAAE,IAAY;IACjE,MAAM,aAAa,qGAAA,CAAA,UAAM,CAAC,UAAU,CAAC,MAAM,MAAM,OAAO,IAAI,UAAU,QAAQ,CAAC;IAC/E,OAAO,qGAAA,CAAA,UAAM,CAAC,eAAe,CAAC,OAAO,IAAI,CAAC,MAAM,QAAQ,OAAO,IAAI,CAAC,YAAY;AAClF;AAGO,SAAS,YAAY,IAAY,EAAE,GAAY;IACpD,MAAM,YAAY,OAAO,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC;IACzD,MAAM,KAAK,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC;IAC9B,MAAM,SAAS,qGAAA,CAAA,UAAM,CAAC,YAAY,CAAC,eAAe;IAElD,IAAI,YAAY,OAAO,MAAM,CAAC,MAAM,QAAQ;IAC5C,aAAa,OAAO,KAAK,CAAC;IAE1B,OAAO;QACL;QACA,IAAI,GAAG,QAAQ,CAAC;QAChB,KAAK;IACP;AACF;AAGO,SAAS,YAAY,aAAqB,EAAE,GAAW,EAAE,EAAU;IACxE,MAAM,WAAW,qGAAA,CAAA,UAAM,CAAC,cAAc,CAAC,eAAe;IACtD,IAAI,YAAY,SAAS,MAAM,CAAC,eAAe,OAAO;IACtD,aAAa,SAAS,KAAK,CAAC;IAC5B,OAAO;AACT;AAGO,SAAS,aAAa,IAAY,EAAE,MAAc;IACvD,OAAO,qGAAA,CAAA,UAAM,CAAC,UAAU,CAAC,UAAU,QAAQ,MAAM,CAAC,MAAM,MAAM,CAAC;AACjE;AAGO,SAAS,WAAW,IAAY,EAAE,SAAiB,EAAE,MAAc;IACxE,MAAM,oBAAoB,aAAa,MAAM;IAC7C,OAAO,qGAAA,CAAA,UAAM,CAAC,eAAe,CAAC,OAAO,IAAI,CAAC,WAAW,QAAQ,OAAO,IAAI,CAAC,mBAAmB;AAC9F;AAGO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ;IACrC,MAAM,cAAc,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC;IACpD,OAAO,GAAG,UAAU,CAAC,EAAE,aAAa;AACtC;AAGO,SAAS,qBAAqB,KAAa,EAAE,SAAiB,KAAK,KAAK,KAAK,IAAI;IACtF,IAAI;QACF,MAAM,CAAC,WAAW,WAAW,GAAG,MAAM,KAAK,CAAC;QAC5C,IAAI,CAAC,aAAa,CAAC,YAAY,OAAO;QAEtC,MAAM,YAAY,SAAS,WAAW;QACtC,MAAM,MAAM,KAAK,GAAG;QAEpB,OAAO,AAAC,MAAM,aAAc;IAC9B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,MAAM;;;IACH,QAA2C;IAEnD,YACE,AAAQ,WAAmB,EAC3B,AAAQ,YAAoB,CAC5B;aAFQ,cAAA;aACA,eAAA;aAJF,UAAiC,IAAI;IAK1C;IAEH,UAAU,GAAW,EAAW;QAC9B,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,cAAc,MAAM,IAAI,CAAC,YAAY;QAE3C,oCAAoC;QACpC,IAAI,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;QAE1C,yCAAyC;QACzC,WAAW,SAAS,MAAM,CAAC,CAAA,YAAa,YAAY;QAEpD,kCAAkC;QAClC,IAAI,SAAS,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACvC,OAAO;QACT;QAEA,sBAAsB;QACtB,SAAS,IAAI,CAAC;QACd,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK;QAEtB,OAAO;IACT;IAEA,qBAAqB,GAAW,EAAU;QACxC,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,cAAc,MAAM,IAAI,CAAC,YAAY;QAC3C,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;QAC5C,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,YAAa,YAAY;QAE/D,OAAO,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,cAAc,MAAM;IAC5D;IAEA,aAAa,GAAW,EAAU;QAChC,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;QAC5C,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;QAElC,MAAM,gBAAgB,KAAK,GAAG,IAAI;QAClC,OAAO,gBAAgB,IAAI,CAAC,YAAY;IAC1C;IAEA,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QAEpB,KAAK,MAAM,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,GAAI;YACpD,MAAM,cAAc,MAAM,IAAI,CAAC,YAAY;YAC3C,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,YAAa,YAAY;YAE/D,IAAI,cAAc,MAAM,KAAK,GAAG;gBAC9B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACtB,OAAO;gBACL,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK;YACxB;QACF;IACF;AACF;AAGO,SAAS,YAAY,EAAU;IACpC,oCAAoC;IACpC,IAAI,GAAG,UAAU,CAAC,YAAY;QAC5B,OAAO,GAAG,SAAS,CAAC;IACtB;IAEA,8BAA8B;IAC9B,IAAI,OAAO,SAAS,OAAO,aAAa;QACtC,OAAO;IACT;IAEA,OAAO;AACT;AAGO,SAAS,YAAY,EAAU,EAAE,aAAuB;IAC7D,MAAM,eAAe,YAAY;IAEjC,mCAAmC;IACnC,IAAI,oDAAyB,iBAAiB,iBAAiB,aAAa;QAC1E,OAAO;IACT;IAEA,+BAA+B;IAC/B,OAAO,cAAc,IAAI,CAAC,CAAA;QACxB,IAAI,UAAU,cAAc,OAAO;QAEnC,2CAA2C;QAC3C,IAAI,MAAM,QAAQ,CAAC,MAAM;YACvB,MAAM,CAAC,SAAS,aAAa,GAAG,MAAM,KAAK,CAAC;YAC5C,wEAAwE;YACxE,OAAO,aAAa,UAAU,CAAC,QAAQ,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,SAAS,gBAAgB,GAAG,IAAI,CAAC;QAC9F;QAEA,OAAO;IACT;AACF;AAGO,SAAS,2BAA2B,MAAc,EAAE,OAAgB;IACzE,MAAM,iBAAiB;IACvB,MAAM,gBAAgB,WAAW;IAEjC,IAAI,SAAS;IACb,MAAM,QAAQ,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC;IAEjC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,aAAa,CAAC,KAAK,CAAC,EAAE,GAAG,cAAc,MAAM,CAAC;IAC1D;IAEA,OAAO;AACT;AAGO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC;AACzC;AAGO,MAAM,mBAAmB;IAC9B,0BAA0B;IAC1B,mBAAmB;IACnB,oBAAoB;IACpB,mBAAmB;IACnB,sBAAsB;IACtB,6BAA6B;AAC/B;AAGO,SAAS,gBAAgB,KAAa;IAC3C,OAAO,MACJ,OAAO,CAAC,MAAM,SACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,eAAe,KAAa;IAC1C,OAAO,MAAM,OAAO,CAAC,MAAM;AAC7B;AAEO,SAAS,cAAc,KAAa;IACzC,OAAO,MACJ,OAAO,CAAC,OAAO,QACf,OAAO,CAAC,MAAM,OACd,OAAO,CAAC,MAAM,OACd,OAAO,CAAC,OAAO,OACf,OAAO,CAAC,OAAO,OACf,OAAO,CAAC,OAAO;AACpB", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/auth/qr/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { prisma } from '@/lib/prisma';\nimport { isValidQRToken } from '@/utils/qr';\nimport { isValidUUID, sanitizeString } from '@/utils/validation';\nimport { normalizeIP } from '@/utils/security';\nimport { ApiResponse, User } from '@/types';\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Get client IP for logging\n    const clientIP = normalizeIP(request.headers.get('x-forwarded-for') || 'unknown');\n\n    const body = await request.json();\n    const { token } = body;\n\n    // Input validation\n    if (!token || typeof token !== 'string') {\n      console.warn(`Invalid token format from IP: ${clientIP}`);\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Invalid QR token format'\n      }, { status: 400 });\n    }\n\n    const sanitizedToken = sanitizeString(token);\n\n    if (!isValidUUID(sanitizedToken) || !isValidQRToken(sanitizedToken)) {\n      console.warn(`Invalid QR token from IP: ${clientIP}, token: ${sanitizedToken.substring(0, 8)}...`);\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Invalid QR token format'\n      }, { status: 400 });\n    }\n\n    // Find user by QR token\n    const mapping = await prisma.qrCodeMapping.findUnique({\n      where: { qrToken: sanitizedToken },\n      include: {\n        user: true\n      }\n    });\n\n    if (!mapping) {\n      console.warn(`QR token not found from IP: ${clientIP}, token: ${sanitizedToken.substring(0, 8)}...`);\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'QR code not found or invalid'\n      }, { status: 404 });\n    }\n\n    // Update scan count and last scanned time\n    await prisma.qrCodeMapping.update({\n      where: { qrToken: sanitizedToken },\n      data: {\n        scanCount: mapping.scanCount + 1,\n        lastScanned: new Date()\n      }\n    });\n\n    const user = mapping.user as User;\n\n    // Log successful authentication\n    console.log(`Successful QR authentication for user: ${user.id} from IP: ${clientIP}`);\n\n    return NextResponse.json<ApiResponse<User>>({\n      success: true,\n      data: user,\n      message: 'Authentication successful'\n    });\n\n  } catch (error) {\n    console.error('QR authentication error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  const { searchParams } = new URL(request.url);\n  const token = searchParams.get('token');\n\n  if (!token || !isValidQRToken(token)) {\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Invalid or missing QR token'\n    }, { status: 400 });\n  }\n\n  try {\n    // Check if QR token exists\n    const mapping = await prisma.qrCodeMapping.findUnique({\n      where: { qrToken: token }\n    });\n\n    if (!mapping) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'QR code not found'\n      }, { status: 404 });\n    }\n\n    return NextResponse.json<ApiResponse<{ exists: boolean }>>({\n      success: true,\n      data: { exists: true },\n      message: 'QR token is valid'\n    });\n\n  } catch (error) {\n    console.error('QR validation error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,4BAA4B;QAC5B,MAAM,WAAW,CAAA,GAAA,0HAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;QAEvE,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,GAAG;QAElB,mBAAmB;QACnB,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;YACvC,QAAQ,IAAI,CAAC,CAAC,8BAA8B,EAAE,UAAU;YACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,iBAAiB,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;QAEtC,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD,EAAE,mBAAmB,CAAC,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB;YACnE,QAAQ,IAAI,CAAC,CAAC,0BAA0B,EAAE,SAAS,SAAS,EAAE,eAAe,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC;YACjG,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,wBAAwB;QACxB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YACpD,OAAO;gBAAE,SAAS;YAAe;YACjC,SAAS;gBACP,MAAM;YACR;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,SAAS,SAAS,EAAE,eAAe,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC;YACnG,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,0CAA0C;QAC1C,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YAChC,OAAO;gBAAE,SAAS;YAAe;YACjC,MAAM;gBACJ,WAAW,QAAQ,SAAS,GAAG;gBAC/B,aAAa,IAAI;YACnB;QACF;QAEA,MAAM,OAAO,QAAQ,IAAI;QAEzB,gCAAgC;QAChC,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,UAAU;QAEpF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC;IAE/B,IAAI,CAAC,SAAS,CAAC,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;IAEA,IAAI;QACF,2BAA2B;QAC3B,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YACpD,OAAO;gBAAE,SAAS;YAAM;QAC1B;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAmC;YACzD,SAAS;YACT,MAAM;gBAAE,QAAQ;YAAK;YACrB,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}