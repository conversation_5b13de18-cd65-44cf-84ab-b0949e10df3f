{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Helper function to disconnect Prisma (useful for serverless)\nexport async function disconnectPrisma() {\n  await prisma.$disconnect();\n}\n\n// Helper function to check database connection\nexport async function checkDatabaseConnection() {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI;AAEpD,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\n\nexport interface AdminTokenPayload {\n  adminId: string;\n  email: string;\n  name: string;\n  role: string;\n}\n\n// Hash password\nexport async function hashPassword(password: string): Promise<string> {\n  const saltRounds = 12;\n  return bcrypt.hash(password, saltRounds);\n}\n\n// Verify password\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Generate JWT token for admin\nexport function generateAdminToken(payload: AdminTokenPayload): string {\n  return jwt.sign(payload, JWT_SECRET, {\n    expiresIn: '24h',\n    issuer: 'astroconnect-admin'\n  });\n}\n\n// Verify JWT token\nexport function verifyAdminToken(token: string): AdminTokenPayload | null {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET, {\n      issuer: 'astroconnect-admin'\n    }) as AdminTokenPayload;\n    return decoded;\n  } catch (error) {\n    console.error('Token verification failed:', error);\n    return null;\n  }\n}\n\n// Extract admin token from request\nexport function getAdminFromRequest(request: NextRequest): AdminTokenPayload | null {\n  try {\n    // Check Authorization header\n    const authHeader = request.headers.get('authorization');\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n      const token = authHeader.substring(7);\n      return verifyAdminToken(token);\n    }\n\n    // Check cookie\n    const tokenCookie = request.cookies.get('admin-token');\n    if (tokenCookie) {\n      return verifyAdminToken(tokenCookie.value);\n    }\n\n    return null;\n  } catch (error) {\n    console.error('Error extracting admin from request:', error);\n    return null;\n  }\n}\n\n// Validate admin permissions\nexport function requireAdminAuth(admin: AdminTokenPayload | null): boolean {\n  return admin !== null && admin.role === 'admin';\n}\n\n// Generate secure random password for demo\nexport function generateSecurePassword(length: number = 12): string {\n  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';\n  let password = '';\n  for (let i = 0; i < length; i++) {\n    password += charset.charAt(Math.floor(Math.random() * charset.length));\n  }\n  return password;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAIA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAUtC,eAAe,aAAa,QAAgB;IACjD,MAAM,aAAa;IACnB,OAAO,OAAO,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,OAAO,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,mBAAmB,OAA0B;IAC3D,OAAO,IAAI,IAAI,CAAC,SAAS,YAAY;QACnC,WAAW;QACX,QAAQ;IACV;AACF;AAGO,SAAS,iBAAiB,KAAa;IAC5C,IAAI;QACF,MAAM,UAAU,IAAI,MAAM,CAAC,OAAO,YAAY;YAC5C,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,OAAoB;IACtD,IAAI;QACF,6BAA6B;QAC7B,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;YAClD,MAAM,QAAQ,WAAW,SAAS,CAAC;YACnC,OAAO,iBAAiB;QAC1B;QAEA,eAAe;QACf,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QACxC,IAAI,aAAa;YACf,OAAO,iBAAiB,YAAY,KAAK;QAC3C;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;IACT;AACF;AAGO,SAAS,iBAAiB,KAA+B;IAC9D,OAAO,UAAU,QAAQ,MAAM,IAAI,KAAK;AAC1C;AAGO,SAAS,uBAAuB,SAAiB,EAAE;IACxD,MAAM,UAAU;IAChB,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,YAAY,QAAQ,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM;IACtE;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/admin/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { prisma } from '@/lib/prisma';\nimport { verifyPassword, generateAdminToken } from '@/lib/auth';\nimport { ApiResponse } from '@/types';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { email, password } = await request.json();\n\n    // Validate input\n    if (!email || !password) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Email and password are required'\n      }, { status: 400 });\n    }\n\n    // Find admin by email\n    const admin = await prisma.admin.findUnique({\n      where: { email: email.toLowerCase() }\n    });\n\n    if (!admin) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Invalid credentials'\n      }, { status: 401 });\n    }\n\n    // Check if admin is active\n    if (!admin.isActive) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Account is deactivated'\n      }, { status: 401 });\n    }\n\n    // Verify password\n    const isValidPassword = await verifyPassword(password, admin.password);\n    if (!isValidPassword) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Invalid credentials'\n      }, { status: 401 });\n    }\n\n    // Update last login\n    await prisma.admin.update({\n      where: { id: admin.id },\n      data: { lastLogin: new Date() }\n    });\n\n    // Generate JWT token\n    const token = generateAdminToken({\n      adminId: admin.id,\n      email: admin.email,\n      name: admin.name,\n      role: admin.role\n    });\n\n    // Create response with token\n    const response = NextResponse.json<ApiResponse<{\n      admin: {\n        id: string;\n        email: string;\n        name: string;\n        role: string;\n      };\n      token: string;\n    }>>({\n      success: true,\n      data: {\n        admin: {\n          id: admin.id,\n          email: admin.email,\n          name: admin.name,\n          role: admin.role\n        },\n        token\n      },\n      message: 'Login successful'\n    });\n\n    // Set HTTP-only cookie for additional security\n    response.cookies.set('admin-token', token, {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === 'production',\n      sameSite: 'strict',\n      maxAge: 24 * 60 * 60 // 24 hours\n    });\n\n    return response;\n\n  } catch (error) {\n    console.error('Admin login error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE9C,iBAAiB;QACjB,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,sBAAsB;QACtB,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YAC1C,OAAO;gBAAE,OAAO,MAAM,WAAW;YAAG;QACtC;QAEA,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,QAAQ,EAAE;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,kBAAkB;QAClB,MAAM,kBAAkB,MAAM,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,MAAM,QAAQ;QACrE,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,oBAAoB;QACpB,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YACxB,OAAO;gBAAE,IAAI,MAAM,EAAE;YAAC;YACtB,MAAM;gBAAE,WAAW,IAAI;YAAO;QAChC;QAEA,qBAAqB;QACrB,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,EAAE;YAC/B,SAAS,MAAM,EAAE;YACjB,OAAO,MAAM,KAAK;YAClB,MAAM,MAAM,IAAI;YAChB,MAAM,MAAM,IAAI;QAClB;QAEA,6BAA6B;QAC7B,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAQ9B;YACF,SAAS;YACT,MAAM;gBACJ,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,OAAO,MAAM,KAAK;oBAClB,MAAM,MAAM,IAAI;oBAChB,MAAM,MAAM,IAAI;gBAClB;gBACA;YACF;YACA,SAAS;QACX;QAEA,+CAA+C;QAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,eAAe,OAAO;YACzC,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,KAAK,KAAK,GAAG,WAAW;QAClC;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}