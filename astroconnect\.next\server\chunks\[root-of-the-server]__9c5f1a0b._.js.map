{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Helper function to disconnect Prisma (useful for serverless)\nexport async function disconnectPrisma() {\n  await prisma.$disconnect();\n}\n\n// Helper function to check database connection\nexport async function checkDatabaseConnection() {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\n\nexport interface AdminTokenPayload {\n  adminId: string;\n  email: string;\n  name: string;\n  role: string;\n}\n\n// Hash password\nexport async function hashPassword(password: string): Promise<string> {\n  const saltRounds = 12;\n  return bcrypt.hash(password, saltRounds);\n}\n\n// Verify password\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Generate JWT token for admin\nexport function generateAdminToken(payload: AdminTokenPayload): string {\n  return jwt.sign(payload, JWT_SECRET, {\n    expiresIn: '24h',\n    issuer: 'astroconnect-admin'\n  });\n}\n\n// Verify JWT token\nexport function verifyAdminToken(token: string): AdminTokenPayload | null {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET, {\n      issuer: 'astroconnect-admin'\n    }) as AdminTokenPayload;\n    return decoded;\n  } catch (error) {\n    console.error('Token verification failed:', error);\n    return null;\n  }\n}\n\n// Extract admin token from request\nexport function getAdminFromRequest(request: NextRequest): AdminTokenPayload | null {\n  try {\n    // Check Authorization header\n    const authHeader = request.headers.get('authorization');\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n      const token = authHeader.substring(7);\n      return verifyAdminToken(token);\n    }\n\n    // Check cookie\n    const tokenCookie = request.cookies.get('admin-token');\n    if (tokenCookie) {\n      return verifyAdminToken(tokenCookie.value);\n    }\n\n    return null;\n  } catch (error) {\n    console.error('Error extracting admin from request:', error);\n    return null;\n  }\n}\n\n// Validate admin permissions\nexport function requireAdminAuth(admin: AdminTokenPayload | null): boolean {\n  return admin !== null && admin.role === 'admin';\n}\n\n// Generate secure random password for demo\nexport function generateSecurePassword(length: number = 12): string {\n  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';\n  let password = '';\n  for (let i = 0; i < length; i++) {\n    password += charset.charAt(Math.floor(Math.random() * charset.length));\n  }\n  return password;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAUtC,eAAe,aAAa,QAAgB;IACjD,MAAM,aAAa;IACnB,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,mBAAmB,OAA0B;IAC3D,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QACnC,WAAW;QACX,QAAQ;IACV;AACF;AAGO,SAAS,iBAAiB,KAAa;IAC5C,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,YAAY;YAC5C,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,OAAoB;IACtD,IAAI;QACF,6BAA6B;QAC7B,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;YAClD,MAAM,QAAQ,WAAW,SAAS,CAAC;YACnC,OAAO,iBAAiB;QAC1B;QAEA,eAAe;QACf,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QACxC,IAAI,aAAa;YACf,OAAO,iBAAiB,YAAY,KAAK;QAC3C;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;IACT;AACF;AAGO,SAAS,iBAAiB,KAA+B;IAC9D,OAAO,UAAU,QAAQ,MAAM,IAAI,KAAK;AAC1C;AAGO,SAAS,uBAAuB,SAAiB,EAAE;IACxD,MAAM,UAAU;IAChB,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,YAAY,QAAQ,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM;IACtE;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/admin/stats/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { prisma } from '@/lib/prisma';\nimport { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';\nimport { ApiResponse } from '@/types';\n\ninterface AdminStats {\n  totalUsers: number;\n  totalHoroscopes: number;\n  totalScans: number;\n  activeUsers: number;\n  recentUsers: number;\n  totalDailyGuides: number;\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Check admin authentication\n    const admin = getAdminFromRequest(request);\n    if (!requireAdminAuth(admin)) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Unauthorized - Admin access required'\n      }, { status: 401 });\n    }\n\n    // Get total users count\n    const totalUsers = await prisma.user.count();\n\n    // Get total horoscopes count\n    const totalHoroscopes = await prisma.horoscope.count();\n\n    // Get total daily guides count\n    const totalDailyGuides = await prisma.dailyGuide.count();\n\n    // Get total scans (sum of all scan counts)\n    const scanStats = await prisma.qrCodeMapping.aggregate({\n      _sum: {\n        scanCount: true\n      }\n    });\n    const totalScans = scanStats._sum.scanCount || 0;\n\n    // Get active users (users who have scanned their QR code at least once)\n    const activeUsers = await prisma.qrCodeMapping.count({\n      where: {\n        scanCount: {\n          gt: 0\n        }\n      }\n    });\n\n    // Get recent users (users created in the last 30 days)\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    \n    const recentUsers = await prisma.user.count({\n      where: {\n        createdAt: {\n          gte: thirtyDaysAgo\n        }\n      }\n    });\n\n    const stats: AdminStats = {\n      totalUsers,\n      totalHoroscopes,\n      totalScans,\n      activeUsers,\n      recentUsers,\n      totalDailyGuides\n    };\n\n    return NextResponse.json<ApiResponse<AdminStats>>({\n      success: true,\n      data: stats,\n      message: 'Admin statistics retrieved successfully'\n    });\n\n  } catch (error) {\n    console.error('Admin stats error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAYO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,6BAA6B;QAC7B,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;QAClC,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,wBAAwB;QACxB,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK;QAE1C,6BAA6B;QAC7B,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,KAAK;QAEpD,+BAA+B;QAC/B,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,KAAK;QAEtD,2CAA2C;QAC3C,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,SAAS,CAAC;YACrD,MAAM;gBACJ,WAAW;YACb;QACF;QACA,MAAM,aAAa,UAAU,IAAI,CAAC,SAAS,IAAI;QAE/C,wEAAwE;QACxE,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,KAAK,CAAC;YACnD,OAAO;gBACL,WAAW;oBACT,IAAI;gBACN;YACF;QACF;QAEA,uDAAuD;QACvD,MAAM,gBAAgB,IAAI;QAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK;QAEhD,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC1C,OAAO;gBACL,WAAW;oBACT,KAAK;gBACP;YACF;QACF;QAEA,MAAM,QAAoB;YACxB;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAA0B;YAChD,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}