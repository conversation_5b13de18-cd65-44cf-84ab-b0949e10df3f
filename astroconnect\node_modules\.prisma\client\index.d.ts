
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model Admin
 * 
 */
export type Admin = $Result.DefaultSelection<Prisma.$AdminPayload>
/**
 * Model User
 * 
 */
export type User = $Result.DefaultSelection<Prisma.$UserPayload>
/**
 * Model QrCodeMapping
 * 
 */
export type QrCodeMapping = $Result.DefaultSelection<Prisma.$QrCodeMappingPayload>
/**
 * Model Horoscope
 * 
 */
export type Horoscope = $Result.DefaultSelection<Prisma.$HoroscopePayload>
/**
 * Model DailyZodiacReading
 * 
 */
export type DailyZodiacReading = $Result.DefaultSelection<Prisma.$DailyZodiacReadingPayload>
/**
 * Model PersonalHoroscope
 * 
 */
export type PersonalHoroscope = $Result.DefaultSelection<Prisma.$PersonalHoroscopePayload>
/**
 * Model TranslationCache
 * 
 */
export type TranslationCache = $Result.DefaultSelection<Prisma.$TranslationCachePayload>

/**
 * Enums
 */
export namespace $Enums {
  export const ZodiacSign: {
  aries: 'aries',
  taurus: 'taurus',
  gemini: 'gemini',
  cancer: 'cancer',
  leo: 'leo',
  virgo: 'virgo',
  libra: 'libra',
  scorpio: 'scorpio',
  sagittarius: 'sagittarius',
  capricorn: 'capricorn',
  aquarius: 'aquarius',
  pisces: 'pisces'
};

export type ZodiacSign = (typeof ZodiacSign)[keyof typeof ZodiacSign]


export const LanguageCode: {
  en: 'en',
  si: 'si'
};

export type LanguageCode = (typeof LanguageCode)[keyof typeof LanguageCode]


export const HoroscopeType: {
  daily: 'daily',
  weekly: 'weekly',
  monthly: 'monthly'
};

export type HoroscopeType = (typeof HoroscopeType)[keyof typeof HoroscopeType]


export const UserRole: {
  admin: 'admin',
  user: 'user'
};

export type UserRole = (typeof UserRole)[keyof typeof UserRole]

}

export type ZodiacSign = $Enums.ZodiacSign

export const ZodiacSign: typeof $Enums.ZodiacSign

export type LanguageCode = $Enums.LanguageCode

export const LanguageCode: typeof $Enums.LanguageCode

export type HoroscopeType = $Enums.HoroscopeType

export const HoroscopeType: typeof $Enums.HoroscopeType

export type UserRole = $Enums.UserRole

export const UserRole: typeof $Enums.UserRole

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Admins
 * const admins = await prisma.admin.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Admins
   * const admins = await prisma.admin.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.admin`: Exposes CRUD operations for the **Admin** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Admins
    * const admins = await prisma.admin.findMany()
    * ```
    */
  get admin(): Prisma.AdminDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.user`: Exposes CRUD operations for the **User** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.UserDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.qrCodeMapping`: Exposes CRUD operations for the **QrCodeMapping** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more QrCodeMappings
    * const qrCodeMappings = await prisma.qrCodeMapping.findMany()
    * ```
    */
  get qrCodeMapping(): Prisma.QrCodeMappingDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.horoscope`: Exposes CRUD operations for the **Horoscope** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Horoscopes
    * const horoscopes = await prisma.horoscope.findMany()
    * ```
    */
  get horoscope(): Prisma.HoroscopeDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.dailyZodiacReading`: Exposes CRUD operations for the **DailyZodiacReading** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more DailyZodiacReadings
    * const dailyZodiacReadings = await prisma.dailyZodiacReading.findMany()
    * ```
    */
  get dailyZodiacReading(): Prisma.DailyZodiacReadingDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.personalHoroscope`: Exposes CRUD operations for the **PersonalHoroscope** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more PersonalHoroscopes
    * const personalHoroscopes = await prisma.personalHoroscope.findMany()
    * ```
    */
  get personalHoroscope(): Prisma.PersonalHoroscopeDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.translationCache`: Exposes CRUD operations for the **TranslationCache** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more TranslationCaches
    * const translationCaches = await prisma.translationCache.findMany()
    * ```
    */
  get translationCache(): Prisma.TranslationCacheDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.11.1
   * Query Engine version: f40f79ec31188888a2e33acda0ecc8fd10a853a9
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    Admin: 'Admin',
    User: 'User',
    QrCodeMapping: 'QrCodeMapping',
    Horoscope: 'Horoscope',
    DailyZodiacReading: 'DailyZodiacReading',
    PersonalHoroscope: 'PersonalHoroscope',
    TranslationCache: 'TranslationCache'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "admin" | "user" | "qrCodeMapping" | "horoscope" | "dailyZodiacReading" | "personalHoroscope" | "translationCache"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      Admin: {
        payload: Prisma.$AdminPayload<ExtArgs>
        fields: Prisma.AdminFieldRefs
        operations: {
          findUnique: {
            args: Prisma.AdminFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AdminPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.AdminFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AdminPayload>
          }
          findFirst: {
            args: Prisma.AdminFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AdminPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.AdminFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AdminPayload>
          }
          findMany: {
            args: Prisma.AdminFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AdminPayload>[]
          }
          create: {
            args: Prisma.AdminCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AdminPayload>
          }
          createMany: {
            args: Prisma.AdminCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.AdminCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AdminPayload>[]
          }
          delete: {
            args: Prisma.AdminDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AdminPayload>
          }
          update: {
            args: Prisma.AdminUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AdminPayload>
          }
          deleteMany: {
            args: Prisma.AdminDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.AdminUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.AdminUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AdminPayload>[]
          }
          upsert: {
            args: Prisma.AdminUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AdminPayload>
          }
          aggregate: {
            args: Prisma.AdminAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateAdmin>
          }
          groupBy: {
            args: Prisma.AdminGroupByArgs<ExtArgs>
            result: $Utils.Optional<AdminGroupByOutputType>[]
          }
          count: {
            args: Prisma.AdminCountArgs<ExtArgs>
            result: $Utils.Optional<AdminCountAggregateOutputType> | number
          }
        }
      }
      User: {
        payload: Prisma.$UserPayload<ExtArgs>
        fields: Prisma.UserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findFirst: {
            args: Prisma.UserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findMany: {
            args: Prisma.UserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          create: {
            args: Prisma.UserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          createMany: {
            args: Prisma.UserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          delete: {
            args: Prisma.UserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          update: {
            args: Prisma.UserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          deleteMany: {
            args: Prisma.UserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.UserUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          upsert: {
            args: Prisma.UserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          aggregate: {
            args: Prisma.UserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser>
          }
          groupBy: {
            args: Prisma.UserGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserCountArgs<ExtArgs>
            result: $Utils.Optional<UserCountAggregateOutputType> | number
          }
        }
      }
      QrCodeMapping: {
        payload: Prisma.$QrCodeMappingPayload<ExtArgs>
        fields: Prisma.QrCodeMappingFieldRefs
        operations: {
          findUnique: {
            args: Prisma.QrCodeMappingFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$QrCodeMappingPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.QrCodeMappingFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$QrCodeMappingPayload>
          }
          findFirst: {
            args: Prisma.QrCodeMappingFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$QrCodeMappingPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.QrCodeMappingFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$QrCodeMappingPayload>
          }
          findMany: {
            args: Prisma.QrCodeMappingFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$QrCodeMappingPayload>[]
          }
          create: {
            args: Prisma.QrCodeMappingCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$QrCodeMappingPayload>
          }
          createMany: {
            args: Prisma.QrCodeMappingCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.QrCodeMappingCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$QrCodeMappingPayload>[]
          }
          delete: {
            args: Prisma.QrCodeMappingDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$QrCodeMappingPayload>
          }
          update: {
            args: Prisma.QrCodeMappingUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$QrCodeMappingPayload>
          }
          deleteMany: {
            args: Prisma.QrCodeMappingDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.QrCodeMappingUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.QrCodeMappingUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$QrCodeMappingPayload>[]
          }
          upsert: {
            args: Prisma.QrCodeMappingUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$QrCodeMappingPayload>
          }
          aggregate: {
            args: Prisma.QrCodeMappingAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateQrCodeMapping>
          }
          groupBy: {
            args: Prisma.QrCodeMappingGroupByArgs<ExtArgs>
            result: $Utils.Optional<QrCodeMappingGroupByOutputType>[]
          }
          count: {
            args: Prisma.QrCodeMappingCountArgs<ExtArgs>
            result: $Utils.Optional<QrCodeMappingCountAggregateOutputType> | number
          }
        }
      }
      Horoscope: {
        payload: Prisma.$HoroscopePayload<ExtArgs>
        fields: Prisma.HoroscopeFieldRefs
        operations: {
          findUnique: {
            args: Prisma.HoroscopeFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$HoroscopePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.HoroscopeFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$HoroscopePayload>
          }
          findFirst: {
            args: Prisma.HoroscopeFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$HoroscopePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.HoroscopeFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$HoroscopePayload>
          }
          findMany: {
            args: Prisma.HoroscopeFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$HoroscopePayload>[]
          }
          create: {
            args: Prisma.HoroscopeCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$HoroscopePayload>
          }
          createMany: {
            args: Prisma.HoroscopeCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.HoroscopeCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$HoroscopePayload>[]
          }
          delete: {
            args: Prisma.HoroscopeDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$HoroscopePayload>
          }
          update: {
            args: Prisma.HoroscopeUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$HoroscopePayload>
          }
          deleteMany: {
            args: Prisma.HoroscopeDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.HoroscopeUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.HoroscopeUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$HoroscopePayload>[]
          }
          upsert: {
            args: Prisma.HoroscopeUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$HoroscopePayload>
          }
          aggregate: {
            args: Prisma.HoroscopeAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateHoroscope>
          }
          groupBy: {
            args: Prisma.HoroscopeGroupByArgs<ExtArgs>
            result: $Utils.Optional<HoroscopeGroupByOutputType>[]
          }
          count: {
            args: Prisma.HoroscopeCountArgs<ExtArgs>
            result: $Utils.Optional<HoroscopeCountAggregateOutputType> | number
          }
        }
      }
      DailyZodiacReading: {
        payload: Prisma.$DailyZodiacReadingPayload<ExtArgs>
        fields: Prisma.DailyZodiacReadingFieldRefs
        operations: {
          findUnique: {
            args: Prisma.DailyZodiacReadingFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DailyZodiacReadingPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.DailyZodiacReadingFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DailyZodiacReadingPayload>
          }
          findFirst: {
            args: Prisma.DailyZodiacReadingFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DailyZodiacReadingPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.DailyZodiacReadingFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DailyZodiacReadingPayload>
          }
          findMany: {
            args: Prisma.DailyZodiacReadingFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DailyZodiacReadingPayload>[]
          }
          create: {
            args: Prisma.DailyZodiacReadingCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DailyZodiacReadingPayload>
          }
          createMany: {
            args: Prisma.DailyZodiacReadingCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.DailyZodiacReadingCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DailyZodiacReadingPayload>[]
          }
          delete: {
            args: Prisma.DailyZodiacReadingDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DailyZodiacReadingPayload>
          }
          update: {
            args: Prisma.DailyZodiacReadingUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DailyZodiacReadingPayload>
          }
          deleteMany: {
            args: Prisma.DailyZodiacReadingDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.DailyZodiacReadingUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.DailyZodiacReadingUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DailyZodiacReadingPayload>[]
          }
          upsert: {
            args: Prisma.DailyZodiacReadingUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$DailyZodiacReadingPayload>
          }
          aggregate: {
            args: Prisma.DailyZodiacReadingAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateDailyZodiacReading>
          }
          groupBy: {
            args: Prisma.DailyZodiacReadingGroupByArgs<ExtArgs>
            result: $Utils.Optional<DailyZodiacReadingGroupByOutputType>[]
          }
          count: {
            args: Prisma.DailyZodiacReadingCountArgs<ExtArgs>
            result: $Utils.Optional<DailyZodiacReadingCountAggregateOutputType> | number
          }
        }
      }
      PersonalHoroscope: {
        payload: Prisma.$PersonalHoroscopePayload<ExtArgs>
        fields: Prisma.PersonalHoroscopeFieldRefs
        operations: {
          findUnique: {
            args: Prisma.PersonalHoroscopeFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PersonalHoroscopePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.PersonalHoroscopeFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PersonalHoroscopePayload>
          }
          findFirst: {
            args: Prisma.PersonalHoroscopeFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PersonalHoroscopePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.PersonalHoroscopeFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PersonalHoroscopePayload>
          }
          findMany: {
            args: Prisma.PersonalHoroscopeFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PersonalHoroscopePayload>[]
          }
          create: {
            args: Prisma.PersonalHoroscopeCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PersonalHoroscopePayload>
          }
          createMany: {
            args: Prisma.PersonalHoroscopeCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.PersonalHoroscopeCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PersonalHoroscopePayload>[]
          }
          delete: {
            args: Prisma.PersonalHoroscopeDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PersonalHoroscopePayload>
          }
          update: {
            args: Prisma.PersonalHoroscopeUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PersonalHoroscopePayload>
          }
          deleteMany: {
            args: Prisma.PersonalHoroscopeDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.PersonalHoroscopeUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.PersonalHoroscopeUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PersonalHoroscopePayload>[]
          }
          upsert: {
            args: Prisma.PersonalHoroscopeUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PersonalHoroscopePayload>
          }
          aggregate: {
            args: Prisma.PersonalHoroscopeAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregatePersonalHoroscope>
          }
          groupBy: {
            args: Prisma.PersonalHoroscopeGroupByArgs<ExtArgs>
            result: $Utils.Optional<PersonalHoroscopeGroupByOutputType>[]
          }
          count: {
            args: Prisma.PersonalHoroscopeCountArgs<ExtArgs>
            result: $Utils.Optional<PersonalHoroscopeCountAggregateOutputType> | number
          }
        }
      }
      TranslationCache: {
        payload: Prisma.$TranslationCachePayload<ExtArgs>
        fields: Prisma.TranslationCacheFieldRefs
        operations: {
          findUnique: {
            args: Prisma.TranslationCacheFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TranslationCachePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.TranslationCacheFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TranslationCachePayload>
          }
          findFirst: {
            args: Prisma.TranslationCacheFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TranslationCachePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.TranslationCacheFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TranslationCachePayload>
          }
          findMany: {
            args: Prisma.TranslationCacheFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TranslationCachePayload>[]
          }
          create: {
            args: Prisma.TranslationCacheCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TranslationCachePayload>
          }
          createMany: {
            args: Prisma.TranslationCacheCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.TranslationCacheCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TranslationCachePayload>[]
          }
          delete: {
            args: Prisma.TranslationCacheDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TranslationCachePayload>
          }
          update: {
            args: Prisma.TranslationCacheUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TranslationCachePayload>
          }
          deleteMany: {
            args: Prisma.TranslationCacheDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.TranslationCacheUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.TranslationCacheUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TranslationCachePayload>[]
          }
          upsert: {
            args: Prisma.TranslationCacheUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TranslationCachePayload>
          }
          aggregate: {
            args: Prisma.TranslationCacheAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateTranslationCache>
          }
          groupBy: {
            args: Prisma.TranslationCacheGroupByArgs<ExtArgs>
            result: $Utils.Optional<TranslationCacheGroupByOutputType>[]
          }
          count: {
            args: Prisma.TranslationCacheCountArgs<ExtArgs>
            result: $Utils.Optional<TranslationCacheCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    admin?: AdminOmit
    user?: UserOmit
    qrCodeMapping?: QrCodeMappingOmit
    horoscope?: HoroscopeOmit
    dailyZodiacReading?: DailyZodiacReadingOmit
    personalHoroscope?: PersonalHoroscopeOmit
    translationCache?: TranslationCacheOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type UserCountOutputType
   */

  export type UserCountOutputType = {
    qrCodeMappings: number
    personalHoroscopes: number
  }

  export type UserCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    qrCodeMappings?: boolean | UserCountOutputTypeCountQrCodeMappingsArgs
    personalHoroscopes?: boolean | UserCountOutputTypeCountPersonalHoroscopesArgs
  }

  // Custom InputTypes
  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserCountOutputType
     */
    select?: UserCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountQrCodeMappingsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: QrCodeMappingWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountPersonalHoroscopesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PersonalHoroscopeWhereInput
  }


  /**
   * Models
   */

  /**
   * Model Admin
   */

  export type AggregateAdmin = {
    _count: AdminCountAggregateOutputType | null
    _min: AdminMinAggregateOutputType | null
    _max: AdminMaxAggregateOutputType | null
  }

  export type AdminMinAggregateOutputType = {
    id: string | null
    email: string | null
    password: string | null
    name: string | null
    role: $Enums.UserRole | null
    isActive: boolean | null
    lastLogin: Date | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type AdminMaxAggregateOutputType = {
    id: string | null
    email: string | null
    password: string | null
    name: string | null
    role: $Enums.UserRole | null
    isActive: boolean | null
    lastLogin: Date | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type AdminCountAggregateOutputType = {
    id: number
    email: number
    password: number
    name: number
    role: number
    isActive: number
    lastLogin: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type AdminMinAggregateInputType = {
    id?: true
    email?: true
    password?: true
    name?: true
    role?: true
    isActive?: true
    lastLogin?: true
    createdAt?: true
    updatedAt?: true
  }

  export type AdminMaxAggregateInputType = {
    id?: true
    email?: true
    password?: true
    name?: true
    role?: true
    isActive?: true
    lastLogin?: true
    createdAt?: true
    updatedAt?: true
  }

  export type AdminCountAggregateInputType = {
    id?: true
    email?: true
    password?: true
    name?: true
    role?: true
    isActive?: true
    lastLogin?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type AdminAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Admin to aggregate.
     */
    where?: AdminWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Admins to fetch.
     */
    orderBy?: AdminOrderByWithRelationInput | AdminOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: AdminWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Admins from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Admins.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Admins
    **/
    _count?: true | AdminCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: AdminMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: AdminMaxAggregateInputType
  }

  export type GetAdminAggregateType<T extends AdminAggregateArgs> = {
        [P in keyof T & keyof AggregateAdmin]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateAdmin[P]>
      : GetScalarType<T[P], AggregateAdmin[P]>
  }




  export type AdminGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AdminWhereInput
    orderBy?: AdminOrderByWithAggregationInput | AdminOrderByWithAggregationInput[]
    by: AdminScalarFieldEnum[] | AdminScalarFieldEnum
    having?: AdminScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: AdminCountAggregateInputType | true
    _min?: AdminMinAggregateInputType
    _max?: AdminMaxAggregateInputType
  }

  export type AdminGroupByOutputType = {
    id: string
    email: string
    password: string
    name: string
    role: $Enums.UserRole
    isActive: boolean
    lastLogin: Date | null
    createdAt: Date
    updatedAt: Date
    _count: AdminCountAggregateOutputType | null
    _min: AdminMinAggregateOutputType | null
    _max: AdminMaxAggregateOutputType | null
  }

  type GetAdminGroupByPayload<T extends AdminGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<AdminGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof AdminGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], AdminGroupByOutputType[P]>
            : GetScalarType<T[P], AdminGroupByOutputType[P]>
        }
      >
    >


  export type AdminSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    password?: boolean
    name?: boolean
    role?: boolean
    isActive?: boolean
    lastLogin?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["admin"]>

  export type AdminSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    password?: boolean
    name?: boolean
    role?: boolean
    isActive?: boolean
    lastLogin?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["admin"]>

  export type AdminSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    password?: boolean
    name?: boolean
    role?: boolean
    isActive?: boolean
    lastLogin?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["admin"]>

  export type AdminSelectScalar = {
    id?: boolean
    email?: boolean
    password?: boolean
    name?: boolean
    role?: boolean
    isActive?: boolean
    lastLogin?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type AdminOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "email" | "password" | "name" | "role" | "isActive" | "lastLogin" | "createdAt" | "updatedAt", ExtArgs["result"]["admin"]>

  export type $AdminPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Admin"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      email: string
      password: string
      name: string
      role: $Enums.UserRole
      isActive: boolean
      lastLogin: Date | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["admin"]>
    composites: {}
  }

  type AdminGetPayload<S extends boolean | null | undefined | AdminDefaultArgs> = $Result.GetResult<Prisma.$AdminPayload, S>

  type AdminCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<AdminFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: AdminCountAggregateInputType | true
    }

  export interface AdminDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Admin'], meta: { name: 'Admin' } }
    /**
     * Find zero or one Admin that matches the filter.
     * @param {AdminFindUniqueArgs} args - Arguments to find a Admin
     * @example
     * // Get one Admin
     * const admin = await prisma.admin.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends AdminFindUniqueArgs>(args: SelectSubset<T, AdminFindUniqueArgs<ExtArgs>>): Prisma__AdminClient<$Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Admin that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {AdminFindUniqueOrThrowArgs} args - Arguments to find a Admin
     * @example
     * // Get one Admin
     * const admin = await prisma.admin.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends AdminFindUniqueOrThrowArgs>(args: SelectSubset<T, AdminFindUniqueOrThrowArgs<ExtArgs>>): Prisma__AdminClient<$Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Admin that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AdminFindFirstArgs} args - Arguments to find a Admin
     * @example
     * // Get one Admin
     * const admin = await prisma.admin.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends AdminFindFirstArgs>(args?: SelectSubset<T, AdminFindFirstArgs<ExtArgs>>): Prisma__AdminClient<$Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Admin that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AdminFindFirstOrThrowArgs} args - Arguments to find a Admin
     * @example
     * // Get one Admin
     * const admin = await prisma.admin.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends AdminFindFirstOrThrowArgs>(args?: SelectSubset<T, AdminFindFirstOrThrowArgs<ExtArgs>>): Prisma__AdminClient<$Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Admins that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AdminFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Admins
     * const admins = await prisma.admin.findMany()
     * 
     * // Get first 10 Admins
     * const admins = await prisma.admin.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const adminWithIdOnly = await prisma.admin.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends AdminFindManyArgs>(args?: SelectSubset<T, AdminFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Admin.
     * @param {AdminCreateArgs} args - Arguments to create a Admin.
     * @example
     * // Create one Admin
     * const Admin = await prisma.admin.create({
     *   data: {
     *     // ... data to create a Admin
     *   }
     * })
     * 
     */
    create<T extends AdminCreateArgs>(args: SelectSubset<T, AdminCreateArgs<ExtArgs>>): Prisma__AdminClient<$Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Admins.
     * @param {AdminCreateManyArgs} args - Arguments to create many Admins.
     * @example
     * // Create many Admins
     * const admin = await prisma.admin.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends AdminCreateManyArgs>(args?: SelectSubset<T, AdminCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Admins and returns the data saved in the database.
     * @param {AdminCreateManyAndReturnArgs} args - Arguments to create many Admins.
     * @example
     * // Create many Admins
     * const admin = await prisma.admin.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Admins and only return the `id`
     * const adminWithIdOnly = await prisma.admin.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends AdminCreateManyAndReturnArgs>(args?: SelectSubset<T, AdminCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Admin.
     * @param {AdminDeleteArgs} args - Arguments to delete one Admin.
     * @example
     * // Delete one Admin
     * const Admin = await prisma.admin.delete({
     *   where: {
     *     // ... filter to delete one Admin
     *   }
     * })
     * 
     */
    delete<T extends AdminDeleteArgs>(args: SelectSubset<T, AdminDeleteArgs<ExtArgs>>): Prisma__AdminClient<$Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Admin.
     * @param {AdminUpdateArgs} args - Arguments to update one Admin.
     * @example
     * // Update one Admin
     * const admin = await prisma.admin.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends AdminUpdateArgs>(args: SelectSubset<T, AdminUpdateArgs<ExtArgs>>): Prisma__AdminClient<$Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Admins.
     * @param {AdminDeleteManyArgs} args - Arguments to filter Admins to delete.
     * @example
     * // Delete a few Admins
     * const { count } = await prisma.admin.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends AdminDeleteManyArgs>(args?: SelectSubset<T, AdminDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Admins.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AdminUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Admins
     * const admin = await prisma.admin.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends AdminUpdateManyArgs>(args: SelectSubset<T, AdminUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Admins and returns the data updated in the database.
     * @param {AdminUpdateManyAndReturnArgs} args - Arguments to update many Admins.
     * @example
     * // Update many Admins
     * const admin = await prisma.admin.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Admins and only return the `id`
     * const adminWithIdOnly = await prisma.admin.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends AdminUpdateManyAndReturnArgs>(args: SelectSubset<T, AdminUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Admin.
     * @param {AdminUpsertArgs} args - Arguments to update or create a Admin.
     * @example
     * // Update or create a Admin
     * const admin = await prisma.admin.upsert({
     *   create: {
     *     // ... data to create a Admin
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Admin we want to update
     *   }
     * })
     */
    upsert<T extends AdminUpsertArgs>(args: SelectSubset<T, AdminUpsertArgs<ExtArgs>>): Prisma__AdminClient<$Result.GetResult<Prisma.$AdminPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Admins.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AdminCountArgs} args - Arguments to filter Admins to count.
     * @example
     * // Count the number of Admins
     * const count = await prisma.admin.count({
     *   where: {
     *     // ... the filter for the Admins we want to count
     *   }
     * })
    **/
    count<T extends AdminCountArgs>(
      args?: Subset<T, AdminCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], AdminCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Admin.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AdminAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends AdminAggregateArgs>(args: Subset<T, AdminAggregateArgs>): Prisma.PrismaPromise<GetAdminAggregateType<T>>

    /**
     * Group by Admin.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AdminGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends AdminGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: AdminGroupByArgs['orderBy'] }
        : { orderBy?: AdminGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, AdminGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAdminGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Admin model
   */
  readonly fields: AdminFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Admin.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__AdminClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Admin model
   */
  interface AdminFieldRefs {
    readonly id: FieldRef<"Admin", 'String'>
    readonly email: FieldRef<"Admin", 'String'>
    readonly password: FieldRef<"Admin", 'String'>
    readonly name: FieldRef<"Admin", 'String'>
    readonly role: FieldRef<"Admin", 'UserRole'>
    readonly isActive: FieldRef<"Admin", 'Boolean'>
    readonly lastLogin: FieldRef<"Admin", 'DateTime'>
    readonly createdAt: FieldRef<"Admin", 'DateTime'>
    readonly updatedAt: FieldRef<"Admin", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Admin findUnique
   */
  export type AdminFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Admin
     */
    select?: AdminSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Admin
     */
    omit?: AdminOmit<ExtArgs> | null
    /**
     * Filter, which Admin to fetch.
     */
    where: AdminWhereUniqueInput
  }

  /**
   * Admin findUniqueOrThrow
   */
  export type AdminFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Admin
     */
    select?: AdminSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Admin
     */
    omit?: AdminOmit<ExtArgs> | null
    /**
     * Filter, which Admin to fetch.
     */
    where: AdminWhereUniqueInput
  }

  /**
   * Admin findFirst
   */
  export type AdminFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Admin
     */
    select?: AdminSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Admin
     */
    omit?: AdminOmit<ExtArgs> | null
    /**
     * Filter, which Admin to fetch.
     */
    where?: AdminWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Admins to fetch.
     */
    orderBy?: AdminOrderByWithRelationInput | AdminOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Admins.
     */
    cursor?: AdminWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Admins from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Admins.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Admins.
     */
    distinct?: AdminScalarFieldEnum | AdminScalarFieldEnum[]
  }

  /**
   * Admin findFirstOrThrow
   */
  export type AdminFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Admin
     */
    select?: AdminSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Admin
     */
    omit?: AdminOmit<ExtArgs> | null
    /**
     * Filter, which Admin to fetch.
     */
    where?: AdminWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Admins to fetch.
     */
    orderBy?: AdminOrderByWithRelationInput | AdminOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Admins.
     */
    cursor?: AdminWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Admins from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Admins.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Admins.
     */
    distinct?: AdminScalarFieldEnum | AdminScalarFieldEnum[]
  }

  /**
   * Admin findMany
   */
  export type AdminFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Admin
     */
    select?: AdminSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Admin
     */
    omit?: AdminOmit<ExtArgs> | null
    /**
     * Filter, which Admins to fetch.
     */
    where?: AdminWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Admins to fetch.
     */
    orderBy?: AdminOrderByWithRelationInput | AdminOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Admins.
     */
    cursor?: AdminWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Admins from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Admins.
     */
    skip?: number
    distinct?: AdminScalarFieldEnum | AdminScalarFieldEnum[]
  }

  /**
   * Admin create
   */
  export type AdminCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Admin
     */
    select?: AdminSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Admin
     */
    omit?: AdminOmit<ExtArgs> | null
    /**
     * The data needed to create a Admin.
     */
    data: XOR<AdminCreateInput, AdminUncheckedCreateInput>
  }

  /**
   * Admin createMany
   */
  export type AdminCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Admins.
     */
    data: AdminCreateManyInput | AdminCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Admin createManyAndReturn
   */
  export type AdminCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Admin
     */
    select?: AdminSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Admin
     */
    omit?: AdminOmit<ExtArgs> | null
    /**
     * The data used to create many Admins.
     */
    data: AdminCreateManyInput | AdminCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Admin update
   */
  export type AdminUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Admin
     */
    select?: AdminSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Admin
     */
    omit?: AdminOmit<ExtArgs> | null
    /**
     * The data needed to update a Admin.
     */
    data: XOR<AdminUpdateInput, AdminUncheckedUpdateInput>
    /**
     * Choose, which Admin to update.
     */
    where: AdminWhereUniqueInput
  }

  /**
   * Admin updateMany
   */
  export type AdminUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Admins.
     */
    data: XOR<AdminUpdateManyMutationInput, AdminUncheckedUpdateManyInput>
    /**
     * Filter which Admins to update
     */
    where?: AdminWhereInput
    /**
     * Limit how many Admins to update.
     */
    limit?: number
  }

  /**
   * Admin updateManyAndReturn
   */
  export type AdminUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Admin
     */
    select?: AdminSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Admin
     */
    omit?: AdminOmit<ExtArgs> | null
    /**
     * The data used to update Admins.
     */
    data: XOR<AdminUpdateManyMutationInput, AdminUncheckedUpdateManyInput>
    /**
     * Filter which Admins to update
     */
    where?: AdminWhereInput
    /**
     * Limit how many Admins to update.
     */
    limit?: number
  }

  /**
   * Admin upsert
   */
  export type AdminUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Admin
     */
    select?: AdminSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Admin
     */
    omit?: AdminOmit<ExtArgs> | null
    /**
     * The filter to search for the Admin to update in case it exists.
     */
    where: AdminWhereUniqueInput
    /**
     * In case the Admin found by the `where` argument doesn't exist, create a new Admin with this data.
     */
    create: XOR<AdminCreateInput, AdminUncheckedCreateInput>
    /**
     * In case the Admin was found with the provided `where` argument, update it with this data.
     */
    update: XOR<AdminUpdateInput, AdminUncheckedUpdateInput>
  }

  /**
   * Admin delete
   */
  export type AdminDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Admin
     */
    select?: AdminSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Admin
     */
    omit?: AdminOmit<ExtArgs> | null
    /**
     * Filter which Admin to delete.
     */
    where: AdminWhereUniqueInput
  }

  /**
   * Admin deleteMany
   */
  export type AdminDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Admins to delete
     */
    where?: AdminWhereInput
    /**
     * Limit how many Admins to delete.
     */
    limit?: number
  }

  /**
   * Admin without action
   */
  export type AdminDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Admin
     */
    select?: AdminSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Admin
     */
    omit?: AdminOmit<ExtArgs> | null
  }


  /**
   * Model User
   */

  export type AggregateUser = {
    _count: UserCountAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  export type UserMinAggregateOutputType = {
    id: string | null
    email: string | null
    name: string | null
    phoneNumber: string | null
    address: string | null
    zodiacSign: $Enums.ZodiacSign | null
    birthDate: Date | null
    birthTime: string | null
    qrToken: string | null
    languagePreference: $Enums.LanguageCode | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserMaxAggregateOutputType = {
    id: string | null
    email: string | null
    name: string | null
    phoneNumber: string | null
    address: string | null
    zodiacSign: $Enums.ZodiacSign | null
    birthDate: Date | null
    birthTime: string | null
    qrToken: string | null
    languagePreference: $Enums.LanguageCode | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserCountAggregateOutputType = {
    id: number
    email: number
    name: number
    phoneNumber: number
    address: number
    zodiacSign: number
    birthDate: number
    birthTime: number
    qrToken: number
    languagePreference: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserMinAggregateInputType = {
    id?: true
    email?: true
    name?: true
    phoneNumber?: true
    address?: true
    zodiacSign?: true
    birthDate?: true
    birthTime?: true
    qrToken?: true
    languagePreference?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserMaxAggregateInputType = {
    id?: true
    email?: true
    name?: true
    phoneNumber?: true
    address?: true
    zodiacSign?: true
    birthDate?: true
    birthTime?: true
    qrToken?: true
    languagePreference?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserCountAggregateInputType = {
    id?: true
    email?: true
    name?: true
    phoneNumber?: true
    address?: true
    zodiacSign?: true
    birthDate?: true
    birthTime?: true
    qrToken?: true
    languagePreference?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which User to aggregate.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserMaxAggregateInputType
  }

  export type GetUserAggregateType<T extends UserAggregateArgs> = {
        [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser[P]>
      : GetScalarType<T[P], AggregateUser[P]>
  }




  export type UserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
    orderBy?: UserOrderByWithAggregationInput | UserOrderByWithAggregationInput[]
    by: UserScalarFieldEnum[] | UserScalarFieldEnum
    having?: UserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserCountAggregateInputType | true
    _min?: UserMinAggregateInputType
    _max?: UserMaxAggregateInputType
  }

  export type UserGroupByOutputType = {
    id: string
    email: string | null
    name: string
    phoneNumber: string | null
    address: string | null
    zodiacSign: $Enums.ZodiacSign
    birthDate: Date
    birthTime: string | null
    qrToken: string
    languagePreference: $Enums.LanguageCode
    createdAt: Date
    updatedAt: Date
    _count: UserCountAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserGroupByOutputType[P]>
            : GetScalarType<T[P], UserGroupByOutputType[P]>
        }
      >
    >


  export type UserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    name?: boolean
    phoneNumber?: boolean
    address?: boolean
    zodiacSign?: boolean
    birthDate?: boolean
    birthTime?: boolean
    qrToken?: boolean
    languagePreference?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    qrCodeMappings?: boolean | User$qrCodeMappingsArgs<ExtArgs>
    personalHoroscopes?: boolean | User$personalHoroscopesArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    name?: boolean
    phoneNumber?: boolean
    address?: boolean
    zodiacSign?: boolean
    birthDate?: boolean
    birthTime?: boolean
    qrToken?: boolean
    languagePreference?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    name?: boolean
    phoneNumber?: boolean
    address?: boolean
    zodiacSign?: boolean
    birthDate?: boolean
    birthTime?: boolean
    qrToken?: boolean
    languagePreference?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectScalar = {
    id?: boolean
    email?: boolean
    name?: boolean
    phoneNumber?: boolean
    address?: boolean
    zodiacSign?: boolean
    birthDate?: boolean
    birthTime?: boolean
    qrToken?: boolean
    languagePreference?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "email" | "name" | "phoneNumber" | "address" | "zodiacSign" | "birthDate" | "birthTime" | "qrToken" | "languagePreference" | "createdAt" | "updatedAt", ExtArgs["result"]["user"]>
  export type UserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    qrCodeMappings?: boolean | User$qrCodeMappingsArgs<ExtArgs>
    personalHoroscopes?: boolean | User$personalHoroscopesArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type UserIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type UserIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $UserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "User"
    objects: {
      qrCodeMappings: Prisma.$QrCodeMappingPayload<ExtArgs>[]
      personalHoroscopes: Prisma.$PersonalHoroscopePayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      email: string | null
      name: string
      phoneNumber: string | null
      address: string | null
      zodiacSign: $Enums.ZodiacSign
      birthDate: Date
      birthTime: string | null
      qrToken: string
      languagePreference: $Enums.LanguageCode
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["user"]>
    composites: {}
  }

  type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = $Result.GetResult<Prisma.$UserPayload, S>

  type UserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UserCountAggregateInputType | true
    }

  export interface UserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
    /**
     * Find zero or one User that matches the filter.
     * @param {UserFindUniqueArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFindUniqueArgs>(args: SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one User that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFindFirstArgs>(args?: SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.user.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.user.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFindManyArgs>(args?: SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a User.
     * @param {UserCreateArgs} args - Arguments to create a User.
     * @example
     * // Create one User
     * const User = await prisma.user.create({
     *   data: {
     *     // ... data to create a User
     *   }
     * })
     * 
     */
    create<T extends UserCreateArgs>(args: SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Users.
     * @param {UserCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserCreateManyArgs>(args?: SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `id`
     * const userWithIdOnly = await prisma.user.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a User.
     * @param {UserDeleteArgs} args - Arguments to delete one User.
     * @example
     * // Delete one User
     * const User = await prisma.user.delete({
     *   where: {
     *     // ... filter to delete one User
     *   }
     * })
     * 
     */
    delete<T extends UserDeleteArgs>(args: SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one User.
     * @param {UserUpdateArgs} args - Arguments to update one User.
     * @example
     * // Update one User
     * const user = await prisma.user.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserUpdateArgs>(args: SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Users.
     * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.user.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserDeleteManyArgs>(args?: SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserUpdateManyArgs>(args: SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users and returns the data updated in the database.
     * @param {UserUpdateManyAndReturnArgs} args - Arguments to update many Users.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Users and only return the `id`
     * const userWithIdOnly = await prisma.user.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends UserUpdateManyAndReturnArgs>(args: SelectSubset<T, UserUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one User.
     * @param {UserUpsertArgs} args - Arguments to update or create a User.
     * @example
     * // Update or create a User
     * const user = await prisma.user.upsert({
     *   create: {
     *     // ... data to create a User
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User we want to update
     *   }
     * })
     */
    upsert<T extends UserUpsertArgs>(args: SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.user.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UserCountArgs>(
      args?: Subset<T, UserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserAggregateArgs>(args: Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

    /**
     * Group by User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserGroupByArgs['orderBy'] }
        : { orderBy?: UserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for User.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    qrCodeMappings<T extends User$qrCodeMappingsArgs<ExtArgs> = {}>(args?: Subset<T, User$qrCodeMappingsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$QrCodeMappingPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    personalHoroscopes<T extends User$personalHoroscopesArgs<ExtArgs> = {}>(args?: Subset<T, User$personalHoroscopesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PersonalHoroscopePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the User model
   */
  interface UserFieldRefs {
    readonly id: FieldRef<"User", 'String'>
    readonly email: FieldRef<"User", 'String'>
    readonly name: FieldRef<"User", 'String'>
    readonly phoneNumber: FieldRef<"User", 'String'>
    readonly address: FieldRef<"User", 'String'>
    readonly zodiacSign: FieldRef<"User", 'ZodiacSign'>
    readonly birthDate: FieldRef<"User", 'DateTime'>
    readonly birthTime: FieldRef<"User", 'String'>
    readonly qrToken: FieldRef<"User", 'String'>
    readonly languagePreference: FieldRef<"User", 'LanguageCode'>
    readonly createdAt: FieldRef<"User", 'DateTime'>
    readonly updatedAt: FieldRef<"User", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * User findUnique
   */
  export type UserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findUniqueOrThrow
   */
  export type UserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findFirst
   */
  export type UserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findFirstOrThrow
   */
  export type UserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findMany
   */
  export type UserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User create
   */
  export type UserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to create a User.
     */
    data: XOR<UserCreateInput, UserUncheckedCreateInput>
  }

  /**
   * User createMany
   */
  export type UserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User createManyAndReturn
   */
  export type UserCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User update
   */
  export type UserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to update a User.
     */
    data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    /**
     * Choose, which User to update.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User updateMany
   */
  export type UserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User updateManyAndReturn
   */
  export type UserUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User upsert
   */
  export type UserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The filter to search for the User to update in case it exists.
     */
    where: UserWhereUniqueInput
    /**
     * In case the User found by the `where` argument doesn't exist, create a new User with this data.
     */
    create: XOR<UserCreateInput, UserUncheckedCreateInput>
    /**
     * In case the User was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserUpdateInput, UserUncheckedUpdateInput>
  }

  /**
   * User delete
   */
  export type UserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter which User to delete.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User deleteMany
   */
  export type UserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to delete.
     */
    limit?: number
  }

  /**
   * User.qrCodeMappings
   */
  export type User$qrCodeMappingsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the QrCodeMapping
     */
    select?: QrCodeMappingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the QrCodeMapping
     */
    omit?: QrCodeMappingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: QrCodeMappingInclude<ExtArgs> | null
    where?: QrCodeMappingWhereInput
    orderBy?: QrCodeMappingOrderByWithRelationInput | QrCodeMappingOrderByWithRelationInput[]
    cursor?: QrCodeMappingWhereUniqueInput
    take?: number
    skip?: number
    distinct?: QrCodeMappingScalarFieldEnum | QrCodeMappingScalarFieldEnum[]
  }

  /**
   * User.personalHoroscopes
   */
  export type User$personalHoroscopesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PersonalHoroscope
     */
    select?: PersonalHoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PersonalHoroscope
     */
    omit?: PersonalHoroscopeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PersonalHoroscopeInclude<ExtArgs> | null
    where?: PersonalHoroscopeWhereInput
    orderBy?: PersonalHoroscopeOrderByWithRelationInput | PersonalHoroscopeOrderByWithRelationInput[]
    cursor?: PersonalHoroscopeWhereUniqueInput
    take?: number
    skip?: number
    distinct?: PersonalHoroscopeScalarFieldEnum | PersonalHoroscopeScalarFieldEnum[]
  }

  /**
   * User without action
   */
  export type UserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
  }


  /**
   * Model QrCodeMapping
   */

  export type AggregateQrCodeMapping = {
    _count: QrCodeMappingCountAggregateOutputType | null
    _avg: QrCodeMappingAvgAggregateOutputType | null
    _sum: QrCodeMappingSumAggregateOutputType | null
    _min: QrCodeMappingMinAggregateOutputType | null
    _max: QrCodeMappingMaxAggregateOutputType | null
  }

  export type QrCodeMappingAvgAggregateOutputType = {
    scanCount: number | null
  }

  export type QrCodeMappingSumAggregateOutputType = {
    scanCount: number | null
  }

  export type QrCodeMappingMinAggregateOutputType = {
    id: string | null
    qrToken: string | null
    userId: string | null
    createdAt: Date | null
    lastScanned: Date | null
    scanCount: number | null
  }

  export type QrCodeMappingMaxAggregateOutputType = {
    id: string | null
    qrToken: string | null
    userId: string | null
    createdAt: Date | null
    lastScanned: Date | null
    scanCount: number | null
  }

  export type QrCodeMappingCountAggregateOutputType = {
    id: number
    qrToken: number
    userId: number
    createdAt: number
    lastScanned: number
    scanCount: number
    _all: number
  }


  export type QrCodeMappingAvgAggregateInputType = {
    scanCount?: true
  }

  export type QrCodeMappingSumAggregateInputType = {
    scanCount?: true
  }

  export type QrCodeMappingMinAggregateInputType = {
    id?: true
    qrToken?: true
    userId?: true
    createdAt?: true
    lastScanned?: true
    scanCount?: true
  }

  export type QrCodeMappingMaxAggregateInputType = {
    id?: true
    qrToken?: true
    userId?: true
    createdAt?: true
    lastScanned?: true
    scanCount?: true
  }

  export type QrCodeMappingCountAggregateInputType = {
    id?: true
    qrToken?: true
    userId?: true
    createdAt?: true
    lastScanned?: true
    scanCount?: true
    _all?: true
  }

  export type QrCodeMappingAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which QrCodeMapping to aggregate.
     */
    where?: QrCodeMappingWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of QrCodeMappings to fetch.
     */
    orderBy?: QrCodeMappingOrderByWithRelationInput | QrCodeMappingOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: QrCodeMappingWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` QrCodeMappings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` QrCodeMappings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned QrCodeMappings
    **/
    _count?: true | QrCodeMappingCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: QrCodeMappingAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: QrCodeMappingSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: QrCodeMappingMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: QrCodeMappingMaxAggregateInputType
  }

  export type GetQrCodeMappingAggregateType<T extends QrCodeMappingAggregateArgs> = {
        [P in keyof T & keyof AggregateQrCodeMapping]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateQrCodeMapping[P]>
      : GetScalarType<T[P], AggregateQrCodeMapping[P]>
  }




  export type QrCodeMappingGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: QrCodeMappingWhereInput
    orderBy?: QrCodeMappingOrderByWithAggregationInput | QrCodeMappingOrderByWithAggregationInput[]
    by: QrCodeMappingScalarFieldEnum[] | QrCodeMappingScalarFieldEnum
    having?: QrCodeMappingScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: QrCodeMappingCountAggregateInputType | true
    _avg?: QrCodeMappingAvgAggregateInputType
    _sum?: QrCodeMappingSumAggregateInputType
    _min?: QrCodeMappingMinAggregateInputType
    _max?: QrCodeMappingMaxAggregateInputType
  }

  export type QrCodeMappingGroupByOutputType = {
    id: string
    qrToken: string
    userId: string
    createdAt: Date
    lastScanned: Date | null
    scanCount: number
    _count: QrCodeMappingCountAggregateOutputType | null
    _avg: QrCodeMappingAvgAggregateOutputType | null
    _sum: QrCodeMappingSumAggregateOutputType | null
    _min: QrCodeMappingMinAggregateOutputType | null
    _max: QrCodeMappingMaxAggregateOutputType | null
  }

  type GetQrCodeMappingGroupByPayload<T extends QrCodeMappingGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<QrCodeMappingGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof QrCodeMappingGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], QrCodeMappingGroupByOutputType[P]>
            : GetScalarType<T[P], QrCodeMappingGroupByOutputType[P]>
        }
      >
    >


  export type QrCodeMappingSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    qrToken?: boolean
    userId?: boolean
    createdAt?: boolean
    lastScanned?: boolean
    scanCount?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["qrCodeMapping"]>

  export type QrCodeMappingSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    qrToken?: boolean
    userId?: boolean
    createdAt?: boolean
    lastScanned?: boolean
    scanCount?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["qrCodeMapping"]>

  export type QrCodeMappingSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    qrToken?: boolean
    userId?: boolean
    createdAt?: boolean
    lastScanned?: boolean
    scanCount?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["qrCodeMapping"]>

  export type QrCodeMappingSelectScalar = {
    id?: boolean
    qrToken?: boolean
    userId?: boolean
    createdAt?: boolean
    lastScanned?: boolean
    scanCount?: boolean
  }

  export type QrCodeMappingOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "qrToken" | "userId" | "createdAt" | "lastScanned" | "scanCount", ExtArgs["result"]["qrCodeMapping"]>
  export type QrCodeMappingInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type QrCodeMappingIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type QrCodeMappingIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $QrCodeMappingPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "QrCodeMapping"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      qrToken: string
      userId: string
      createdAt: Date
      lastScanned: Date | null
      scanCount: number
    }, ExtArgs["result"]["qrCodeMapping"]>
    composites: {}
  }

  type QrCodeMappingGetPayload<S extends boolean | null | undefined | QrCodeMappingDefaultArgs> = $Result.GetResult<Prisma.$QrCodeMappingPayload, S>

  type QrCodeMappingCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<QrCodeMappingFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: QrCodeMappingCountAggregateInputType | true
    }

  export interface QrCodeMappingDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['QrCodeMapping'], meta: { name: 'QrCodeMapping' } }
    /**
     * Find zero or one QrCodeMapping that matches the filter.
     * @param {QrCodeMappingFindUniqueArgs} args - Arguments to find a QrCodeMapping
     * @example
     * // Get one QrCodeMapping
     * const qrCodeMapping = await prisma.qrCodeMapping.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends QrCodeMappingFindUniqueArgs>(args: SelectSubset<T, QrCodeMappingFindUniqueArgs<ExtArgs>>): Prisma__QrCodeMappingClient<$Result.GetResult<Prisma.$QrCodeMappingPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one QrCodeMapping that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {QrCodeMappingFindUniqueOrThrowArgs} args - Arguments to find a QrCodeMapping
     * @example
     * // Get one QrCodeMapping
     * const qrCodeMapping = await prisma.qrCodeMapping.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends QrCodeMappingFindUniqueOrThrowArgs>(args: SelectSubset<T, QrCodeMappingFindUniqueOrThrowArgs<ExtArgs>>): Prisma__QrCodeMappingClient<$Result.GetResult<Prisma.$QrCodeMappingPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first QrCodeMapping that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {QrCodeMappingFindFirstArgs} args - Arguments to find a QrCodeMapping
     * @example
     * // Get one QrCodeMapping
     * const qrCodeMapping = await prisma.qrCodeMapping.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends QrCodeMappingFindFirstArgs>(args?: SelectSubset<T, QrCodeMappingFindFirstArgs<ExtArgs>>): Prisma__QrCodeMappingClient<$Result.GetResult<Prisma.$QrCodeMappingPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first QrCodeMapping that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {QrCodeMappingFindFirstOrThrowArgs} args - Arguments to find a QrCodeMapping
     * @example
     * // Get one QrCodeMapping
     * const qrCodeMapping = await prisma.qrCodeMapping.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends QrCodeMappingFindFirstOrThrowArgs>(args?: SelectSubset<T, QrCodeMappingFindFirstOrThrowArgs<ExtArgs>>): Prisma__QrCodeMappingClient<$Result.GetResult<Prisma.$QrCodeMappingPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more QrCodeMappings that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {QrCodeMappingFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all QrCodeMappings
     * const qrCodeMappings = await prisma.qrCodeMapping.findMany()
     * 
     * // Get first 10 QrCodeMappings
     * const qrCodeMappings = await prisma.qrCodeMapping.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const qrCodeMappingWithIdOnly = await prisma.qrCodeMapping.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends QrCodeMappingFindManyArgs>(args?: SelectSubset<T, QrCodeMappingFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$QrCodeMappingPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a QrCodeMapping.
     * @param {QrCodeMappingCreateArgs} args - Arguments to create a QrCodeMapping.
     * @example
     * // Create one QrCodeMapping
     * const QrCodeMapping = await prisma.qrCodeMapping.create({
     *   data: {
     *     // ... data to create a QrCodeMapping
     *   }
     * })
     * 
     */
    create<T extends QrCodeMappingCreateArgs>(args: SelectSubset<T, QrCodeMappingCreateArgs<ExtArgs>>): Prisma__QrCodeMappingClient<$Result.GetResult<Prisma.$QrCodeMappingPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many QrCodeMappings.
     * @param {QrCodeMappingCreateManyArgs} args - Arguments to create many QrCodeMappings.
     * @example
     * // Create many QrCodeMappings
     * const qrCodeMapping = await prisma.qrCodeMapping.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends QrCodeMappingCreateManyArgs>(args?: SelectSubset<T, QrCodeMappingCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many QrCodeMappings and returns the data saved in the database.
     * @param {QrCodeMappingCreateManyAndReturnArgs} args - Arguments to create many QrCodeMappings.
     * @example
     * // Create many QrCodeMappings
     * const qrCodeMapping = await prisma.qrCodeMapping.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many QrCodeMappings and only return the `id`
     * const qrCodeMappingWithIdOnly = await prisma.qrCodeMapping.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends QrCodeMappingCreateManyAndReturnArgs>(args?: SelectSubset<T, QrCodeMappingCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$QrCodeMappingPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a QrCodeMapping.
     * @param {QrCodeMappingDeleteArgs} args - Arguments to delete one QrCodeMapping.
     * @example
     * // Delete one QrCodeMapping
     * const QrCodeMapping = await prisma.qrCodeMapping.delete({
     *   where: {
     *     // ... filter to delete one QrCodeMapping
     *   }
     * })
     * 
     */
    delete<T extends QrCodeMappingDeleteArgs>(args: SelectSubset<T, QrCodeMappingDeleteArgs<ExtArgs>>): Prisma__QrCodeMappingClient<$Result.GetResult<Prisma.$QrCodeMappingPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one QrCodeMapping.
     * @param {QrCodeMappingUpdateArgs} args - Arguments to update one QrCodeMapping.
     * @example
     * // Update one QrCodeMapping
     * const qrCodeMapping = await prisma.qrCodeMapping.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends QrCodeMappingUpdateArgs>(args: SelectSubset<T, QrCodeMappingUpdateArgs<ExtArgs>>): Prisma__QrCodeMappingClient<$Result.GetResult<Prisma.$QrCodeMappingPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more QrCodeMappings.
     * @param {QrCodeMappingDeleteManyArgs} args - Arguments to filter QrCodeMappings to delete.
     * @example
     * // Delete a few QrCodeMappings
     * const { count } = await prisma.qrCodeMapping.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends QrCodeMappingDeleteManyArgs>(args?: SelectSubset<T, QrCodeMappingDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more QrCodeMappings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {QrCodeMappingUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many QrCodeMappings
     * const qrCodeMapping = await prisma.qrCodeMapping.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends QrCodeMappingUpdateManyArgs>(args: SelectSubset<T, QrCodeMappingUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more QrCodeMappings and returns the data updated in the database.
     * @param {QrCodeMappingUpdateManyAndReturnArgs} args - Arguments to update many QrCodeMappings.
     * @example
     * // Update many QrCodeMappings
     * const qrCodeMapping = await prisma.qrCodeMapping.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more QrCodeMappings and only return the `id`
     * const qrCodeMappingWithIdOnly = await prisma.qrCodeMapping.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends QrCodeMappingUpdateManyAndReturnArgs>(args: SelectSubset<T, QrCodeMappingUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$QrCodeMappingPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one QrCodeMapping.
     * @param {QrCodeMappingUpsertArgs} args - Arguments to update or create a QrCodeMapping.
     * @example
     * // Update or create a QrCodeMapping
     * const qrCodeMapping = await prisma.qrCodeMapping.upsert({
     *   create: {
     *     // ... data to create a QrCodeMapping
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the QrCodeMapping we want to update
     *   }
     * })
     */
    upsert<T extends QrCodeMappingUpsertArgs>(args: SelectSubset<T, QrCodeMappingUpsertArgs<ExtArgs>>): Prisma__QrCodeMappingClient<$Result.GetResult<Prisma.$QrCodeMappingPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of QrCodeMappings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {QrCodeMappingCountArgs} args - Arguments to filter QrCodeMappings to count.
     * @example
     * // Count the number of QrCodeMappings
     * const count = await prisma.qrCodeMapping.count({
     *   where: {
     *     // ... the filter for the QrCodeMappings we want to count
     *   }
     * })
    **/
    count<T extends QrCodeMappingCountArgs>(
      args?: Subset<T, QrCodeMappingCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], QrCodeMappingCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a QrCodeMapping.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {QrCodeMappingAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends QrCodeMappingAggregateArgs>(args: Subset<T, QrCodeMappingAggregateArgs>): Prisma.PrismaPromise<GetQrCodeMappingAggregateType<T>>

    /**
     * Group by QrCodeMapping.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {QrCodeMappingGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends QrCodeMappingGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: QrCodeMappingGroupByArgs['orderBy'] }
        : { orderBy?: QrCodeMappingGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, QrCodeMappingGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetQrCodeMappingGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the QrCodeMapping model
   */
  readonly fields: QrCodeMappingFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for QrCodeMapping.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__QrCodeMappingClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the QrCodeMapping model
   */
  interface QrCodeMappingFieldRefs {
    readonly id: FieldRef<"QrCodeMapping", 'String'>
    readonly qrToken: FieldRef<"QrCodeMapping", 'String'>
    readonly userId: FieldRef<"QrCodeMapping", 'String'>
    readonly createdAt: FieldRef<"QrCodeMapping", 'DateTime'>
    readonly lastScanned: FieldRef<"QrCodeMapping", 'DateTime'>
    readonly scanCount: FieldRef<"QrCodeMapping", 'Int'>
  }
    

  // Custom InputTypes
  /**
   * QrCodeMapping findUnique
   */
  export type QrCodeMappingFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the QrCodeMapping
     */
    select?: QrCodeMappingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the QrCodeMapping
     */
    omit?: QrCodeMappingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: QrCodeMappingInclude<ExtArgs> | null
    /**
     * Filter, which QrCodeMapping to fetch.
     */
    where: QrCodeMappingWhereUniqueInput
  }

  /**
   * QrCodeMapping findUniqueOrThrow
   */
  export type QrCodeMappingFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the QrCodeMapping
     */
    select?: QrCodeMappingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the QrCodeMapping
     */
    omit?: QrCodeMappingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: QrCodeMappingInclude<ExtArgs> | null
    /**
     * Filter, which QrCodeMapping to fetch.
     */
    where: QrCodeMappingWhereUniqueInput
  }

  /**
   * QrCodeMapping findFirst
   */
  export type QrCodeMappingFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the QrCodeMapping
     */
    select?: QrCodeMappingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the QrCodeMapping
     */
    omit?: QrCodeMappingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: QrCodeMappingInclude<ExtArgs> | null
    /**
     * Filter, which QrCodeMapping to fetch.
     */
    where?: QrCodeMappingWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of QrCodeMappings to fetch.
     */
    orderBy?: QrCodeMappingOrderByWithRelationInput | QrCodeMappingOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for QrCodeMappings.
     */
    cursor?: QrCodeMappingWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` QrCodeMappings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` QrCodeMappings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of QrCodeMappings.
     */
    distinct?: QrCodeMappingScalarFieldEnum | QrCodeMappingScalarFieldEnum[]
  }

  /**
   * QrCodeMapping findFirstOrThrow
   */
  export type QrCodeMappingFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the QrCodeMapping
     */
    select?: QrCodeMappingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the QrCodeMapping
     */
    omit?: QrCodeMappingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: QrCodeMappingInclude<ExtArgs> | null
    /**
     * Filter, which QrCodeMapping to fetch.
     */
    where?: QrCodeMappingWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of QrCodeMappings to fetch.
     */
    orderBy?: QrCodeMappingOrderByWithRelationInput | QrCodeMappingOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for QrCodeMappings.
     */
    cursor?: QrCodeMappingWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` QrCodeMappings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` QrCodeMappings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of QrCodeMappings.
     */
    distinct?: QrCodeMappingScalarFieldEnum | QrCodeMappingScalarFieldEnum[]
  }

  /**
   * QrCodeMapping findMany
   */
  export type QrCodeMappingFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the QrCodeMapping
     */
    select?: QrCodeMappingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the QrCodeMapping
     */
    omit?: QrCodeMappingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: QrCodeMappingInclude<ExtArgs> | null
    /**
     * Filter, which QrCodeMappings to fetch.
     */
    where?: QrCodeMappingWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of QrCodeMappings to fetch.
     */
    orderBy?: QrCodeMappingOrderByWithRelationInput | QrCodeMappingOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing QrCodeMappings.
     */
    cursor?: QrCodeMappingWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` QrCodeMappings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` QrCodeMappings.
     */
    skip?: number
    distinct?: QrCodeMappingScalarFieldEnum | QrCodeMappingScalarFieldEnum[]
  }

  /**
   * QrCodeMapping create
   */
  export type QrCodeMappingCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the QrCodeMapping
     */
    select?: QrCodeMappingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the QrCodeMapping
     */
    omit?: QrCodeMappingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: QrCodeMappingInclude<ExtArgs> | null
    /**
     * The data needed to create a QrCodeMapping.
     */
    data: XOR<QrCodeMappingCreateInput, QrCodeMappingUncheckedCreateInput>
  }

  /**
   * QrCodeMapping createMany
   */
  export type QrCodeMappingCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many QrCodeMappings.
     */
    data: QrCodeMappingCreateManyInput | QrCodeMappingCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * QrCodeMapping createManyAndReturn
   */
  export type QrCodeMappingCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the QrCodeMapping
     */
    select?: QrCodeMappingSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the QrCodeMapping
     */
    omit?: QrCodeMappingOmit<ExtArgs> | null
    /**
     * The data used to create many QrCodeMappings.
     */
    data: QrCodeMappingCreateManyInput | QrCodeMappingCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: QrCodeMappingIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * QrCodeMapping update
   */
  export type QrCodeMappingUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the QrCodeMapping
     */
    select?: QrCodeMappingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the QrCodeMapping
     */
    omit?: QrCodeMappingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: QrCodeMappingInclude<ExtArgs> | null
    /**
     * The data needed to update a QrCodeMapping.
     */
    data: XOR<QrCodeMappingUpdateInput, QrCodeMappingUncheckedUpdateInput>
    /**
     * Choose, which QrCodeMapping to update.
     */
    where: QrCodeMappingWhereUniqueInput
  }

  /**
   * QrCodeMapping updateMany
   */
  export type QrCodeMappingUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update QrCodeMappings.
     */
    data: XOR<QrCodeMappingUpdateManyMutationInput, QrCodeMappingUncheckedUpdateManyInput>
    /**
     * Filter which QrCodeMappings to update
     */
    where?: QrCodeMappingWhereInput
    /**
     * Limit how many QrCodeMappings to update.
     */
    limit?: number
  }

  /**
   * QrCodeMapping updateManyAndReturn
   */
  export type QrCodeMappingUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the QrCodeMapping
     */
    select?: QrCodeMappingSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the QrCodeMapping
     */
    omit?: QrCodeMappingOmit<ExtArgs> | null
    /**
     * The data used to update QrCodeMappings.
     */
    data: XOR<QrCodeMappingUpdateManyMutationInput, QrCodeMappingUncheckedUpdateManyInput>
    /**
     * Filter which QrCodeMappings to update
     */
    where?: QrCodeMappingWhereInput
    /**
     * Limit how many QrCodeMappings to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: QrCodeMappingIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * QrCodeMapping upsert
   */
  export type QrCodeMappingUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the QrCodeMapping
     */
    select?: QrCodeMappingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the QrCodeMapping
     */
    omit?: QrCodeMappingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: QrCodeMappingInclude<ExtArgs> | null
    /**
     * The filter to search for the QrCodeMapping to update in case it exists.
     */
    where: QrCodeMappingWhereUniqueInput
    /**
     * In case the QrCodeMapping found by the `where` argument doesn't exist, create a new QrCodeMapping with this data.
     */
    create: XOR<QrCodeMappingCreateInput, QrCodeMappingUncheckedCreateInput>
    /**
     * In case the QrCodeMapping was found with the provided `where` argument, update it with this data.
     */
    update: XOR<QrCodeMappingUpdateInput, QrCodeMappingUncheckedUpdateInput>
  }

  /**
   * QrCodeMapping delete
   */
  export type QrCodeMappingDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the QrCodeMapping
     */
    select?: QrCodeMappingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the QrCodeMapping
     */
    omit?: QrCodeMappingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: QrCodeMappingInclude<ExtArgs> | null
    /**
     * Filter which QrCodeMapping to delete.
     */
    where: QrCodeMappingWhereUniqueInput
  }

  /**
   * QrCodeMapping deleteMany
   */
  export type QrCodeMappingDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which QrCodeMappings to delete
     */
    where?: QrCodeMappingWhereInput
    /**
     * Limit how many QrCodeMappings to delete.
     */
    limit?: number
  }

  /**
   * QrCodeMapping without action
   */
  export type QrCodeMappingDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the QrCodeMapping
     */
    select?: QrCodeMappingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the QrCodeMapping
     */
    omit?: QrCodeMappingOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: QrCodeMappingInclude<ExtArgs> | null
  }


  /**
   * Model Horoscope
   */

  export type AggregateHoroscope = {
    _count: HoroscopeCountAggregateOutputType | null
    _min: HoroscopeMinAggregateOutputType | null
    _max: HoroscopeMaxAggregateOutputType | null
  }

  export type HoroscopeMinAggregateOutputType = {
    id: string | null
    zodiacSign: $Enums.ZodiacSign | null
    type: $Enums.HoroscopeType | null
    content: string | null
    date: Date | null
    language: $Enums.LanguageCode | null
    createdAt: Date | null
  }

  export type HoroscopeMaxAggregateOutputType = {
    id: string | null
    zodiacSign: $Enums.ZodiacSign | null
    type: $Enums.HoroscopeType | null
    content: string | null
    date: Date | null
    language: $Enums.LanguageCode | null
    createdAt: Date | null
  }

  export type HoroscopeCountAggregateOutputType = {
    id: number
    zodiacSign: number
    type: number
    content: number
    date: number
    language: number
    createdAt: number
    _all: number
  }


  export type HoroscopeMinAggregateInputType = {
    id?: true
    zodiacSign?: true
    type?: true
    content?: true
    date?: true
    language?: true
    createdAt?: true
  }

  export type HoroscopeMaxAggregateInputType = {
    id?: true
    zodiacSign?: true
    type?: true
    content?: true
    date?: true
    language?: true
    createdAt?: true
  }

  export type HoroscopeCountAggregateInputType = {
    id?: true
    zodiacSign?: true
    type?: true
    content?: true
    date?: true
    language?: true
    createdAt?: true
    _all?: true
  }

  export type HoroscopeAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Horoscope to aggregate.
     */
    where?: HoroscopeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Horoscopes to fetch.
     */
    orderBy?: HoroscopeOrderByWithRelationInput | HoroscopeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: HoroscopeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Horoscopes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Horoscopes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Horoscopes
    **/
    _count?: true | HoroscopeCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: HoroscopeMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: HoroscopeMaxAggregateInputType
  }

  export type GetHoroscopeAggregateType<T extends HoroscopeAggregateArgs> = {
        [P in keyof T & keyof AggregateHoroscope]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateHoroscope[P]>
      : GetScalarType<T[P], AggregateHoroscope[P]>
  }




  export type HoroscopeGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: HoroscopeWhereInput
    orderBy?: HoroscopeOrderByWithAggregationInput | HoroscopeOrderByWithAggregationInput[]
    by: HoroscopeScalarFieldEnum[] | HoroscopeScalarFieldEnum
    having?: HoroscopeScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: HoroscopeCountAggregateInputType | true
    _min?: HoroscopeMinAggregateInputType
    _max?: HoroscopeMaxAggregateInputType
  }

  export type HoroscopeGroupByOutputType = {
    id: string
    zodiacSign: $Enums.ZodiacSign
    type: $Enums.HoroscopeType
    content: string
    date: Date
    language: $Enums.LanguageCode
    createdAt: Date
    _count: HoroscopeCountAggregateOutputType | null
    _min: HoroscopeMinAggregateOutputType | null
    _max: HoroscopeMaxAggregateOutputType | null
  }

  type GetHoroscopeGroupByPayload<T extends HoroscopeGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<HoroscopeGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof HoroscopeGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], HoroscopeGroupByOutputType[P]>
            : GetScalarType<T[P], HoroscopeGroupByOutputType[P]>
        }
      >
    >


  export type HoroscopeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    zodiacSign?: boolean
    type?: boolean
    content?: boolean
    date?: boolean
    language?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["horoscope"]>

  export type HoroscopeSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    zodiacSign?: boolean
    type?: boolean
    content?: boolean
    date?: boolean
    language?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["horoscope"]>

  export type HoroscopeSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    zodiacSign?: boolean
    type?: boolean
    content?: boolean
    date?: boolean
    language?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["horoscope"]>

  export type HoroscopeSelectScalar = {
    id?: boolean
    zodiacSign?: boolean
    type?: boolean
    content?: boolean
    date?: boolean
    language?: boolean
    createdAt?: boolean
  }

  export type HoroscopeOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "zodiacSign" | "type" | "content" | "date" | "language" | "createdAt", ExtArgs["result"]["horoscope"]>

  export type $HoroscopePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Horoscope"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      zodiacSign: $Enums.ZodiacSign
      type: $Enums.HoroscopeType
      content: string
      date: Date
      language: $Enums.LanguageCode
      createdAt: Date
    }, ExtArgs["result"]["horoscope"]>
    composites: {}
  }

  type HoroscopeGetPayload<S extends boolean | null | undefined | HoroscopeDefaultArgs> = $Result.GetResult<Prisma.$HoroscopePayload, S>

  type HoroscopeCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<HoroscopeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: HoroscopeCountAggregateInputType | true
    }

  export interface HoroscopeDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Horoscope'], meta: { name: 'Horoscope' } }
    /**
     * Find zero or one Horoscope that matches the filter.
     * @param {HoroscopeFindUniqueArgs} args - Arguments to find a Horoscope
     * @example
     * // Get one Horoscope
     * const horoscope = await prisma.horoscope.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends HoroscopeFindUniqueArgs>(args: SelectSubset<T, HoroscopeFindUniqueArgs<ExtArgs>>): Prisma__HoroscopeClient<$Result.GetResult<Prisma.$HoroscopePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Horoscope that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {HoroscopeFindUniqueOrThrowArgs} args - Arguments to find a Horoscope
     * @example
     * // Get one Horoscope
     * const horoscope = await prisma.horoscope.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends HoroscopeFindUniqueOrThrowArgs>(args: SelectSubset<T, HoroscopeFindUniqueOrThrowArgs<ExtArgs>>): Prisma__HoroscopeClient<$Result.GetResult<Prisma.$HoroscopePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Horoscope that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {HoroscopeFindFirstArgs} args - Arguments to find a Horoscope
     * @example
     * // Get one Horoscope
     * const horoscope = await prisma.horoscope.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends HoroscopeFindFirstArgs>(args?: SelectSubset<T, HoroscopeFindFirstArgs<ExtArgs>>): Prisma__HoroscopeClient<$Result.GetResult<Prisma.$HoroscopePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Horoscope that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {HoroscopeFindFirstOrThrowArgs} args - Arguments to find a Horoscope
     * @example
     * // Get one Horoscope
     * const horoscope = await prisma.horoscope.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends HoroscopeFindFirstOrThrowArgs>(args?: SelectSubset<T, HoroscopeFindFirstOrThrowArgs<ExtArgs>>): Prisma__HoroscopeClient<$Result.GetResult<Prisma.$HoroscopePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Horoscopes that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {HoroscopeFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Horoscopes
     * const horoscopes = await prisma.horoscope.findMany()
     * 
     * // Get first 10 Horoscopes
     * const horoscopes = await prisma.horoscope.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const horoscopeWithIdOnly = await prisma.horoscope.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends HoroscopeFindManyArgs>(args?: SelectSubset<T, HoroscopeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$HoroscopePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Horoscope.
     * @param {HoroscopeCreateArgs} args - Arguments to create a Horoscope.
     * @example
     * // Create one Horoscope
     * const Horoscope = await prisma.horoscope.create({
     *   data: {
     *     // ... data to create a Horoscope
     *   }
     * })
     * 
     */
    create<T extends HoroscopeCreateArgs>(args: SelectSubset<T, HoroscopeCreateArgs<ExtArgs>>): Prisma__HoroscopeClient<$Result.GetResult<Prisma.$HoroscopePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Horoscopes.
     * @param {HoroscopeCreateManyArgs} args - Arguments to create many Horoscopes.
     * @example
     * // Create many Horoscopes
     * const horoscope = await prisma.horoscope.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends HoroscopeCreateManyArgs>(args?: SelectSubset<T, HoroscopeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Horoscopes and returns the data saved in the database.
     * @param {HoroscopeCreateManyAndReturnArgs} args - Arguments to create many Horoscopes.
     * @example
     * // Create many Horoscopes
     * const horoscope = await prisma.horoscope.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Horoscopes and only return the `id`
     * const horoscopeWithIdOnly = await prisma.horoscope.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends HoroscopeCreateManyAndReturnArgs>(args?: SelectSubset<T, HoroscopeCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$HoroscopePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Horoscope.
     * @param {HoroscopeDeleteArgs} args - Arguments to delete one Horoscope.
     * @example
     * // Delete one Horoscope
     * const Horoscope = await prisma.horoscope.delete({
     *   where: {
     *     // ... filter to delete one Horoscope
     *   }
     * })
     * 
     */
    delete<T extends HoroscopeDeleteArgs>(args: SelectSubset<T, HoroscopeDeleteArgs<ExtArgs>>): Prisma__HoroscopeClient<$Result.GetResult<Prisma.$HoroscopePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Horoscope.
     * @param {HoroscopeUpdateArgs} args - Arguments to update one Horoscope.
     * @example
     * // Update one Horoscope
     * const horoscope = await prisma.horoscope.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends HoroscopeUpdateArgs>(args: SelectSubset<T, HoroscopeUpdateArgs<ExtArgs>>): Prisma__HoroscopeClient<$Result.GetResult<Prisma.$HoroscopePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Horoscopes.
     * @param {HoroscopeDeleteManyArgs} args - Arguments to filter Horoscopes to delete.
     * @example
     * // Delete a few Horoscopes
     * const { count } = await prisma.horoscope.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends HoroscopeDeleteManyArgs>(args?: SelectSubset<T, HoroscopeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Horoscopes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {HoroscopeUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Horoscopes
     * const horoscope = await prisma.horoscope.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends HoroscopeUpdateManyArgs>(args: SelectSubset<T, HoroscopeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Horoscopes and returns the data updated in the database.
     * @param {HoroscopeUpdateManyAndReturnArgs} args - Arguments to update many Horoscopes.
     * @example
     * // Update many Horoscopes
     * const horoscope = await prisma.horoscope.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Horoscopes and only return the `id`
     * const horoscopeWithIdOnly = await prisma.horoscope.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends HoroscopeUpdateManyAndReturnArgs>(args: SelectSubset<T, HoroscopeUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$HoroscopePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Horoscope.
     * @param {HoroscopeUpsertArgs} args - Arguments to update or create a Horoscope.
     * @example
     * // Update or create a Horoscope
     * const horoscope = await prisma.horoscope.upsert({
     *   create: {
     *     // ... data to create a Horoscope
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Horoscope we want to update
     *   }
     * })
     */
    upsert<T extends HoroscopeUpsertArgs>(args: SelectSubset<T, HoroscopeUpsertArgs<ExtArgs>>): Prisma__HoroscopeClient<$Result.GetResult<Prisma.$HoroscopePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Horoscopes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {HoroscopeCountArgs} args - Arguments to filter Horoscopes to count.
     * @example
     * // Count the number of Horoscopes
     * const count = await prisma.horoscope.count({
     *   where: {
     *     // ... the filter for the Horoscopes we want to count
     *   }
     * })
    **/
    count<T extends HoroscopeCountArgs>(
      args?: Subset<T, HoroscopeCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], HoroscopeCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Horoscope.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {HoroscopeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends HoroscopeAggregateArgs>(args: Subset<T, HoroscopeAggregateArgs>): Prisma.PrismaPromise<GetHoroscopeAggregateType<T>>

    /**
     * Group by Horoscope.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {HoroscopeGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends HoroscopeGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: HoroscopeGroupByArgs['orderBy'] }
        : { orderBy?: HoroscopeGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, HoroscopeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetHoroscopeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Horoscope model
   */
  readonly fields: HoroscopeFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Horoscope.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__HoroscopeClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Horoscope model
   */
  interface HoroscopeFieldRefs {
    readonly id: FieldRef<"Horoscope", 'String'>
    readonly zodiacSign: FieldRef<"Horoscope", 'ZodiacSign'>
    readonly type: FieldRef<"Horoscope", 'HoroscopeType'>
    readonly content: FieldRef<"Horoscope", 'String'>
    readonly date: FieldRef<"Horoscope", 'DateTime'>
    readonly language: FieldRef<"Horoscope", 'LanguageCode'>
    readonly createdAt: FieldRef<"Horoscope", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Horoscope findUnique
   */
  export type HoroscopeFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Horoscope
     */
    select?: HoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Horoscope
     */
    omit?: HoroscopeOmit<ExtArgs> | null
    /**
     * Filter, which Horoscope to fetch.
     */
    where: HoroscopeWhereUniqueInput
  }

  /**
   * Horoscope findUniqueOrThrow
   */
  export type HoroscopeFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Horoscope
     */
    select?: HoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Horoscope
     */
    omit?: HoroscopeOmit<ExtArgs> | null
    /**
     * Filter, which Horoscope to fetch.
     */
    where: HoroscopeWhereUniqueInput
  }

  /**
   * Horoscope findFirst
   */
  export type HoroscopeFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Horoscope
     */
    select?: HoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Horoscope
     */
    omit?: HoroscopeOmit<ExtArgs> | null
    /**
     * Filter, which Horoscope to fetch.
     */
    where?: HoroscopeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Horoscopes to fetch.
     */
    orderBy?: HoroscopeOrderByWithRelationInput | HoroscopeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Horoscopes.
     */
    cursor?: HoroscopeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Horoscopes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Horoscopes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Horoscopes.
     */
    distinct?: HoroscopeScalarFieldEnum | HoroscopeScalarFieldEnum[]
  }

  /**
   * Horoscope findFirstOrThrow
   */
  export type HoroscopeFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Horoscope
     */
    select?: HoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Horoscope
     */
    omit?: HoroscopeOmit<ExtArgs> | null
    /**
     * Filter, which Horoscope to fetch.
     */
    where?: HoroscopeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Horoscopes to fetch.
     */
    orderBy?: HoroscopeOrderByWithRelationInput | HoroscopeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Horoscopes.
     */
    cursor?: HoroscopeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Horoscopes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Horoscopes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Horoscopes.
     */
    distinct?: HoroscopeScalarFieldEnum | HoroscopeScalarFieldEnum[]
  }

  /**
   * Horoscope findMany
   */
  export type HoroscopeFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Horoscope
     */
    select?: HoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Horoscope
     */
    omit?: HoroscopeOmit<ExtArgs> | null
    /**
     * Filter, which Horoscopes to fetch.
     */
    where?: HoroscopeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Horoscopes to fetch.
     */
    orderBy?: HoroscopeOrderByWithRelationInput | HoroscopeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Horoscopes.
     */
    cursor?: HoroscopeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Horoscopes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Horoscopes.
     */
    skip?: number
    distinct?: HoroscopeScalarFieldEnum | HoroscopeScalarFieldEnum[]
  }

  /**
   * Horoscope create
   */
  export type HoroscopeCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Horoscope
     */
    select?: HoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Horoscope
     */
    omit?: HoroscopeOmit<ExtArgs> | null
    /**
     * The data needed to create a Horoscope.
     */
    data: XOR<HoroscopeCreateInput, HoroscopeUncheckedCreateInput>
  }

  /**
   * Horoscope createMany
   */
  export type HoroscopeCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Horoscopes.
     */
    data: HoroscopeCreateManyInput | HoroscopeCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Horoscope createManyAndReturn
   */
  export type HoroscopeCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Horoscope
     */
    select?: HoroscopeSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Horoscope
     */
    omit?: HoroscopeOmit<ExtArgs> | null
    /**
     * The data used to create many Horoscopes.
     */
    data: HoroscopeCreateManyInput | HoroscopeCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Horoscope update
   */
  export type HoroscopeUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Horoscope
     */
    select?: HoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Horoscope
     */
    omit?: HoroscopeOmit<ExtArgs> | null
    /**
     * The data needed to update a Horoscope.
     */
    data: XOR<HoroscopeUpdateInput, HoroscopeUncheckedUpdateInput>
    /**
     * Choose, which Horoscope to update.
     */
    where: HoroscopeWhereUniqueInput
  }

  /**
   * Horoscope updateMany
   */
  export type HoroscopeUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Horoscopes.
     */
    data: XOR<HoroscopeUpdateManyMutationInput, HoroscopeUncheckedUpdateManyInput>
    /**
     * Filter which Horoscopes to update
     */
    where?: HoroscopeWhereInput
    /**
     * Limit how many Horoscopes to update.
     */
    limit?: number
  }

  /**
   * Horoscope updateManyAndReturn
   */
  export type HoroscopeUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Horoscope
     */
    select?: HoroscopeSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Horoscope
     */
    omit?: HoroscopeOmit<ExtArgs> | null
    /**
     * The data used to update Horoscopes.
     */
    data: XOR<HoroscopeUpdateManyMutationInput, HoroscopeUncheckedUpdateManyInput>
    /**
     * Filter which Horoscopes to update
     */
    where?: HoroscopeWhereInput
    /**
     * Limit how many Horoscopes to update.
     */
    limit?: number
  }

  /**
   * Horoscope upsert
   */
  export type HoroscopeUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Horoscope
     */
    select?: HoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Horoscope
     */
    omit?: HoroscopeOmit<ExtArgs> | null
    /**
     * The filter to search for the Horoscope to update in case it exists.
     */
    where: HoroscopeWhereUniqueInput
    /**
     * In case the Horoscope found by the `where` argument doesn't exist, create a new Horoscope with this data.
     */
    create: XOR<HoroscopeCreateInput, HoroscopeUncheckedCreateInput>
    /**
     * In case the Horoscope was found with the provided `where` argument, update it with this data.
     */
    update: XOR<HoroscopeUpdateInput, HoroscopeUncheckedUpdateInput>
  }

  /**
   * Horoscope delete
   */
  export type HoroscopeDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Horoscope
     */
    select?: HoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Horoscope
     */
    omit?: HoroscopeOmit<ExtArgs> | null
    /**
     * Filter which Horoscope to delete.
     */
    where: HoroscopeWhereUniqueInput
  }

  /**
   * Horoscope deleteMany
   */
  export type HoroscopeDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Horoscopes to delete
     */
    where?: HoroscopeWhereInput
    /**
     * Limit how many Horoscopes to delete.
     */
    limit?: number
  }

  /**
   * Horoscope without action
   */
  export type HoroscopeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Horoscope
     */
    select?: HoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Horoscope
     */
    omit?: HoroscopeOmit<ExtArgs> | null
  }


  /**
   * Model DailyZodiacReading
   */

  export type AggregateDailyZodiacReading = {
    _count: DailyZodiacReadingCountAggregateOutputType | null
    _avg: DailyZodiacReadingAvgAggregateOutputType | null
    _sum: DailyZodiacReadingSumAggregateOutputType | null
    _min: DailyZodiacReadingMinAggregateOutputType | null
    _max: DailyZodiacReadingMaxAggregateOutputType | null
  }

  export type DailyZodiacReadingAvgAggregateOutputType = {
    luckyNumber: number | null
  }

  export type DailyZodiacReadingSumAggregateOutputType = {
    luckyNumber: number | null
  }

  export type DailyZodiacReadingMinAggregateOutputType = {
    id: string | null
    zodiacSign: $Enums.ZodiacSign | null
    date: Date | null
    generalReading: string | null
    loveReading: string | null
    careerReading: string | null
    healthReading: string | null
    luckyNumber: number | null
    luckyColor: string | null
    luckyTime: string | null
    luckyGem: string | null
    advice: string | null
    mood: string | null
    compatibility: string | null
    language: $Enums.LanguageCode | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type DailyZodiacReadingMaxAggregateOutputType = {
    id: string | null
    zodiacSign: $Enums.ZodiacSign | null
    date: Date | null
    generalReading: string | null
    loveReading: string | null
    careerReading: string | null
    healthReading: string | null
    luckyNumber: number | null
    luckyColor: string | null
    luckyTime: string | null
    luckyGem: string | null
    advice: string | null
    mood: string | null
    compatibility: string | null
    language: $Enums.LanguageCode | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type DailyZodiacReadingCountAggregateOutputType = {
    id: number
    zodiacSign: number
    date: number
    generalReading: number
    loveReading: number
    careerReading: number
    healthReading: number
    luckyNumber: number
    luckyColor: number
    luckyTime: number
    luckyGem: number
    advice: number
    mood: number
    compatibility: number
    language: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type DailyZodiacReadingAvgAggregateInputType = {
    luckyNumber?: true
  }

  export type DailyZodiacReadingSumAggregateInputType = {
    luckyNumber?: true
  }

  export type DailyZodiacReadingMinAggregateInputType = {
    id?: true
    zodiacSign?: true
    date?: true
    generalReading?: true
    loveReading?: true
    careerReading?: true
    healthReading?: true
    luckyNumber?: true
    luckyColor?: true
    luckyTime?: true
    luckyGem?: true
    advice?: true
    mood?: true
    compatibility?: true
    language?: true
    createdAt?: true
    updatedAt?: true
  }

  export type DailyZodiacReadingMaxAggregateInputType = {
    id?: true
    zodiacSign?: true
    date?: true
    generalReading?: true
    loveReading?: true
    careerReading?: true
    healthReading?: true
    luckyNumber?: true
    luckyColor?: true
    luckyTime?: true
    luckyGem?: true
    advice?: true
    mood?: true
    compatibility?: true
    language?: true
    createdAt?: true
    updatedAt?: true
  }

  export type DailyZodiacReadingCountAggregateInputType = {
    id?: true
    zodiacSign?: true
    date?: true
    generalReading?: true
    loveReading?: true
    careerReading?: true
    healthReading?: true
    luckyNumber?: true
    luckyColor?: true
    luckyTime?: true
    luckyGem?: true
    advice?: true
    mood?: true
    compatibility?: true
    language?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type DailyZodiacReadingAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which DailyZodiacReading to aggregate.
     */
    where?: DailyZodiacReadingWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DailyZodiacReadings to fetch.
     */
    orderBy?: DailyZodiacReadingOrderByWithRelationInput | DailyZodiacReadingOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: DailyZodiacReadingWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DailyZodiacReadings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DailyZodiacReadings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned DailyZodiacReadings
    **/
    _count?: true | DailyZodiacReadingCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: DailyZodiacReadingAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: DailyZodiacReadingSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: DailyZodiacReadingMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: DailyZodiacReadingMaxAggregateInputType
  }

  export type GetDailyZodiacReadingAggregateType<T extends DailyZodiacReadingAggregateArgs> = {
        [P in keyof T & keyof AggregateDailyZodiacReading]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateDailyZodiacReading[P]>
      : GetScalarType<T[P], AggregateDailyZodiacReading[P]>
  }




  export type DailyZodiacReadingGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: DailyZodiacReadingWhereInput
    orderBy?: DailyZodiacReadingOrderByWithAggregationInput | DailyZodiacReadingOrderByWithAggregationInput[]
    by: DailyZodiacReadingScalarFieldEnum[] | DailyZodiacReadingScalarFieldEnum
    having?: DailyZodiacReadingScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: DailyZodiacReadingCountAggregateInputType | true
    _avg?: DailyZodiacReadingAvgAggregateInputType
    _sum?: DailyZodiacReadingSumAggregateInputType
    _min?: DailyZodiacReadingMinAggregateInputType
    _max?: DailyZodiacReadingMaxAggregateInputType
  }

  export type DailyZodiacReadingGroupByOutputType = {
    id: string
    zodiacSign: $Enums.ZodiacSign
    date: Date
    generalReading: string
    loveReading: string
    careerReading: string
    healthReading: string
    luckyNumber: number
    luckyColor: string
    luckyTime: string
    luckyGem: string
    advice: string
    mood: string
    compatibility: string
    language: $Enums.LanguageCode
    createdAt: Date
    updatedAt: Date
    _count: DailyZodiacReadingCountAggregateOutputType | null
    _avg: DailyZodiacReadingAvgAggregateOutputType | null
    _sum: DailyZodiacReadingSumAggregateOutputType | null
    _min: DailyZodiacReadingMinAggregateOutputType | null
    _max: DailyZodiacReadingMaxAggregateOutputType | null
  }

  type GetDailyZodiacReadingGroupByPayload<T extends DailyZodiacReadingGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<DailyZodiacReadingGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof DailyZodiacReadingGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], DailyZodiacReadingGroupByOutputType[P]>
            : GetScalarType<T[P], DailyZodiacReadingGroupByOutputType[P]>
        }
      >
    >


  export type DailyZodiacReadingSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    zodiacSign?: boolean
    date?: boolean
    generalReading?: boolean
    loveReading?: boolean
    careerReading?: boolean
    healthReading?: boolean
    luckyNumber?: boolean
    luckyColor?: boolean
    luckyTime?: boolean
    luckyGem?: boolean
    advice?: boolean
    mood?: boolean
    compatibility?: boolean
    language?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["dailyZodiacReading"]>

  export type DailyZodiacReadingSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    zodiacSign?: boolean
    date?: boolean
    generalReading?: boolean
    loveReading?: boolean
    careerReading?: boolean
    healthReading?: boolean
    luckyNumber?: boolean
    luckyColor?: boolean
    luckyTime?: boolean
    luckyGem?: boolean
    advice?: boolean
    mood?: boolean
    compatibility?: boolean
    language?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["dailyZodiacReading"]>

  export type DailyZodiacReadingSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    zodiacSign?: boolean
    date?: boolean
    generalReading?: boolean
    loveReading?: boolean
    careerReading?: boolean
    healthReading?: boolean
    luckyNumber?: boolean
    luckyColor?: boolean
    luckyTime?: boolean
    luckyGem?: boolean
    advice?: boolean
    mood?: boolean
    compatibility?: boolean
    language?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["dailyZodiacReading"]>

  export type DailyZodiacReadingSelectScalar = {
    id?: boolean
    zodiacSign?: boolean
    date?: boolean
    generalReading?: boolean
    loveReading?: boolean
    careerReading?: boolean
    healthReading?: boolean
    luckyNumber?: boolean
    luckyColor?: boolean
    luckyTime?: boolean
    luckyGem?: boolean
    advice?: boolean
    mood?: boolean
    compatibility?: boolean
    language?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type DailyZodiacReadingOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "zodiacSign" | "date" | "generalReading" | "loveReading" | "careerReading" | "healthReading" | "luckyNumber" | "luckyColor" | "luckyTime" | "luckyGem" | "advice" | "mood" | "compatibility" | "language" | "createdAt" | "updatedAt", ExtArgs["result"]["dailyZodiacReading"]>

  export type $DailyZodiacReadingPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "DailyZodiacReading"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      zodiacSign: $Enums.ZodiacSign
      date: Date
      generalReading: string
      loveReading: string
      careerReading: string
      healthReading: string
      luckyNumber: number
      luckyColor: string
      luckyTime: string
      luckyGem: string
      advice: string
      mood: string
      compatibility: string
      language: $Enums.LanguageCode
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["dailyZodiacReading"]>
    composites: {}
  }

  type DailyZodiacReadingGetPayload<S extends boolean | null | undefined | DailyZodiacReadingDefaultArgs> = $Result.GetResult<Prisma.$DailyZodiacReadingPayload, S>

  type DailyZodiacReadingCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<DailyZodiacReadingFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: DailyZodiacReadingCountAggregateInputType | true
    }

  export interface DailyZodiacReadingDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['DailyZodiacReading'], meta: { name: 'DailyZodiacReading' } }
    /**
     * Find zero or one DailyZodiacReading that matches the filter.
     * @param {DailyZodiacReadingFindUniqueArgs} args - Arguments to find a DailyZodiacReading
     * @example
     * // Get one DailyZodiacReading
     * const dailyZodiacReading = await prisma.dailyZodiacReading.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends DailyZodiacReadingFindUniqueArgs>(args: SelectSubset<T, DailyZodiacReadingFindUniqueArgs<ExtArgs>>): Prisma__DailyZodiacReadingClient<$Result.GetResult<Prisma.$DailyZodiacReadingPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one DailyZodiacReading that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {DailyZodiacReadingFindUniqueOrThrowArgs} args - Arguments to find a DailyZodiacReading
     * @example
     * // Get one DailyZodiacReading
     * const dailyZodiacReading = await prisma.dailyZodiacReading.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends DailyZodiacReadingFindUniqueOrThrowArgs>(args: SelectSubset<T, DailyZodiacReadingFindUniqueOrThrowArgs<ExtArgs>>): Prisma__DailyZodiacReadingClient<$Result.GetResult<Prisma.$DailyZodiacReadingPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first DailyZodiacReading that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DailyZodiacReadingFindFirstArgs} args - Arguments to find a DailyZodiacReading
     * @example
     * // Get one DailyZodiacReading
     * const dailyZodiacReading = await prisma.dailyZodiacReading.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends DailyZodiacReadingFindFirstArgs>(args?: SelectSubset<T, DailyZodiacReadingFindFirstArgs<ExtArgs>>): Prisma__DailyZodiacReadingClient<$Result.GetResult<Prisma.$DailyZodiacReadingPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first DailyZodiacReading that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DailyZodiacReadingFindFirstOrThrowArgs} args - Arguments to find a DailyZodiacReading
     * @example
     * // Get one DailyZodiacReading
     * const dailyZodiacReading = await prisma.dailyZodiacReading.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends DailyZodiacReadingFindFirstOrThrowArgs>(args?: SelectSubset<T, DailyZodiacReadingFindFirstOrThrowArgs<ExtArgs>>): Prisma__DailyZodiacReadingClient<$Result.GetResult<Prisma.$DailyZodiacReadingPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more DailyZodiacReadings that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DailyZodiacReadingFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all DailyZodiacReadings
     * const dailyZodiacReadings = await prisma.dailyZodiacReading.findMany()
     * 
     * // Get first 10 DailyZodiacReadings
     * const dailyZodiacReadings = await prisma.dailyZodiacReading.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const dailyZodiacReadingWithIdOnly = await prisma.dailyZodiacReading.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends DailyZodiacReadingFindManyArgs>(args?: SelectSubset<T, DailyZodiacReadingFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$DailyZodiacReadingPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a DailyZodiacReading.
     * @param {DailyZodiacReadingCreateArgs} args - Arguments to create a DailyZodiacReading.
     * @example
     * // Create one DailyZodiacReading
     * const DailyZodiacReading = await prisma.dailyZodiacReading.create({
     *   data: {
     *     // ... data to create a DailyZodiacReading
     *   }
     * })
     * 
     */
    create<T extends DailyZodiacReadingCreateArgs>(args: SelectSubset<T, DailyZodiacReadingCreateArgs<ExtArgs>>): Prisma__DailyZodiacReadingClient<$Result.GetResult<Prisma.$DailyZodiacReadingPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many DailyZodiacReadings.
     * @param {DailyZodiacReadingCreateManyArgs} args - Arguments to create many DailyZodiacReadings.
     * @example
     * // Create many DailyZodiacReadings
     * const dailyZodiacReading = await prisma.dailyZodiacReading.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends DailyZodiacReadingCreateManyArgs>(args?: SelectSubset<T, DailyZodiacReadingCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many DailyZodiacReadings and returns the data saved in the database.
     * @param {DailyZodiacReadingCreateManyAndReturnArgs} args - Arguments to create many DailyZodiacReadings.
     * @example
     * // Create many DailyZodiacReadings
     * const dailyZodiacReading = await prisma.dailyZodiacReading.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many DailyZodiacReadings and only return the `id`
     * const dailyZodiacReadingWithIdOnly = await prisma.dailyZodiacReading.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends DailyZodiacReadingCreateManyAndReturnArgs>(args?: SelectSubset<T, DailyZodiacReadingCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$DailyZodiacReadingPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a DailyZodiacReading.
     * @param {DailyZodiacReadingDeleteArgs} args - Arguments to delete one DailyZodiacReading.
     * @example
     * // Delete one DailyZodiacReading
     * const DailyZodiacReading = await prisma.dailyZodiacReading.delete({
     *   where: {
     *     // ... filter to delete one DailyZodiacReading
     *   }
     * })
     * 
     */
    delete<T extends DailyZodiacReadingDeleteArgs>(args: SelectSubset<T, DailyZodiacReadingDeleteArgs<ExtArgs>>): Prisma__DailyZodiacReadingClient<$Result.GetResult<Prisma.$DailyZodiacReadingPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one DailyZodiacReading.
     * @param {DailyZodiacReadingUpdateArgs} args - Arguments to update one DailyZodiacReading.
     * @example
     * // Update one DailyZodiacReading
     * const dailyZodiacReading = await prisma.dailyZodiacReading.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends DailyZodiacReadingUpdateArgs>(args: SelectSubset<T, DailyZodiacReadingUpdateArgs<ExtArgs>>): Prisma__DailyZodiacReadingClient<$Result.GetResult<Prisma.$DailyZodiacReadingPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more DailyZodiacReadings.
     * @param {DailyZodiacReadingDeleteManyArgs} args - Arguments to filter DailyZodiacReadings to delete.
     * @example
     * // Delete a few DailyZodiacReadings
     * const { count } = await prisma.dailyZodiacReading.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends DailyZodiacReadingDeleteManyArgs>(args?: SelectSubset<T, DailyZodiacReadingDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more DailyZodiacReadings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DailyZodiacReadingUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many DailyZodiacReadings
     * const dailyZodiacReading = await prisma.dailyZodiacReading.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends DailyZodiacReadingUpdateManyArgs>(args: SelectSubset<T, DailyZodiacReadingUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more DailyZodiacReadings and returns the data updated in the database.
     * @param {DailyZodiacReadingUpdateManyAndReturnArgs} args - Arguments to update many DailyZodiacReadings.
     * @example
     * // Update many DailyZodiacReadings
     * const dailyZodiacReading = await prisma.dailyZodiacReading.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more DailyZodiacReadings and only return the `id`
     * const dailyZodiacReadingWithIdOnly = await prisma.dailyZodiacReading.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends DailyZodiacReadingUpdateManyAndReturnArgs>(args: SelectSubset<T, DailyZodiacReadingUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$DailyZodiacReadingPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one DailyZodiacReading.
     * @param {DailyZodiacReadingUpsertArgs} args - Arguments to update or create a DailyZodiacReading.
     * @example
     * // Update or create a DailyZodiacReading
     * const dailyZodiacReading = await prisma.dailyZodiacReading.upsert({
     *   create: {
     *     // ... data to create a DailyZodiacReading
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the DailyZodiacReading we want to update
     *   }
     * })
     */
    upsert<T extends DailyZodiacReadingUpsertArgs>(args: SelectSubset<T, DailyZodiacReadingUpsertArgs<ExtArgs>>): Prisma__DailyZodiacReadingClient<$Result.GetResult<Prisma.$DailyZodiacReadingPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of DailyZodiacReadings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DailyZodiacReadingCountArgs} args - Arguments to filter DailyZodiacReadings to count.
     * @example
     * // Count the number of DailyZodiacReadings
     * const count = await prisma.dailyZodiacReading.count({
     *   where: {
     *     // ... the filter for the DailyZodiacReadings we want to count
     *   }
     * })
    **/
    count<T extends DailyZodiacReadingCountArgs>(
      args?: Subset<T, DailyZodiacReadingCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], DailyZodiacReadingCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a DailyZodiacReading.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DailyZodiacReadingAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends DailyZodiacReadingAggregateArgs>(args: Subset<T, DailyZodiacReadingAggregateArgs>): Prisma.PrismaPromise<GetDailyZodiacReadingAggregateType<T>>

    /**
     * Group by DailyZodiacReading.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {DailyZodiacReadingGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends DailyZodiacReadingGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: DailyZodiacReadingGroupByArgs['orderBy'] }
        : { orderBy?: DailyZodiacReadingGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, DailyZodiacReadingGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetDailyZodiacReadingGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the DailyZodiacReading model
   */
  readonly fields: DailyZodiacReadingFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for DailyZodiacReading.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__DailyZodiacReadingClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the DailyZodiacReading model
   */
  interface DailyZodiacReadingFieldRefs {
    readonly id: FieldRef<"DailyZodiacReading", 'String'>
    readonly zodiacSign: FieldRef<"DailyZodiacReading", 'ZodiacSign'>
    readonly date: FieldRef<"DailyZodiacReading", 'DateTime'>
    readonly generalReading: FieldRef<"DailyZodiacReading", 'String'>
    readonly loveReading: FieldRef<"DailyZodiacReading", 'String'>
    readonly careerReading: FieldRef<"DailyZodiacReading", 'String'>
    readonly healthReading: FieldRef<"DailyZodiacReading", 'String'>
    readonly luckyNumber: FieldRef<"DailyZodiacReading", 'Int'>
    readonly luckyColor: FieldRef<"DailyZodiacReading", 'String'>
    readonly luckyTime: FieldRef<"DailyZodiacReading", 'String'>
    readonly luckyGem: FieldRef<"DailyZodiacReading", 'String'>
    readonly advice: FieldRef<"DailyZodiacReading", 'String'>
    readonly mood: FieldRef<"DailyZodiacReading", 'String'>
    readonly compatibility: FieldRef<"DailyZodiacReading", 'String'>
    readonly language: FieldRef<"DailyZodiacReading", 'LanguageCode'>
    readonly createdAt: FieldRef<"DailyZodiacReading", 'DateTime'>
    readonly updatedAt: FieldRef<"DailyZodiacReading", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * DailyZodiacReading findUnique
   */
  export type DailyZodiacReadingFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DailyZodiacReading
     */
    select?: DailyZodiacReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DailyZodiacReading
     */
    omit?: DailyZodiacReadingOmit<ExtArgs> | null
    /**
     * Filter, which DailyZodiacReading to fetch.
     */
    where: DailyZodiacReadingWhereUniqueInput
  }

  /**
   * DailyZodiacReading findUniqueOrThrow
   */
  export type DailyZodiacReadingFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DailyZodiacReading
     */
    select?: DailyZodiacReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DailyZodiacReading
     */
    omit?: DailyZodiacReadingOmit<ExtArgs> | null
    /**
     * Filter, which DailyZodiacReading to fetch.
     */
    where: DailyZodiacReadingWhereUniqueInput
  }

  /**
   * DailyZodiacReading findFirst
   */
  export type DailyZodiacReadingFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DailyZodiacReading
     */
    select?: DailyZodiacReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DailyZodiacReading
     */
    omit?: DailyZodiacReadingOmit<ExtArgs> | null
    /**
     * Filter, which DailyZodiacReading to fetch.
     */
    where?: DailyZodiacReadingWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DailyZodiacReadings to fetch.
     */
    orderBy?: DailyZodiacReadingOrderByWithRelationInput | DailyZodiacReadingOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for DailyZodiacReadings.
     */
    cursor?: DailyZodiacReadingWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DailyZodiacReadings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DailyZodiacReadings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of DailyZodiacReadings.
     */
    distinct?: DailyZodiacReadingScalarFieldEnum | DailyZodiacReadingScalarFieldEnum[]
  }

  /**
   * DailyZodiacReading findFirstOrThrow
   */
  export type DailyZodiacReadingFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DailyZodiacReading
     */
    select?: DailyZodiacReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DailyZodiacReading
     */
    omit?: DailyZodiacReadingOmit<ExtArgs> | null
    /**
     * Filter, which DailyZodiacReading to fetch.
     */
    where?: DailyZodiacReadingWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DailyZodiacReadings to fetch.
     */
    orderBy?: DailyZodiacReadingOrderByWithRelationInput | DailyZodiacReadingOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for DailyZodiacReadings.
     */
    cursor?: DailyZodiacReadingWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DailyZodiacReadings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DailyZodiacReadings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of DailyZodiacReadings.
     */
    distinct?: DailyZodiacReadingScalarFieldEnum | DailyZodiacReadingScalarFieldEnum[]
  }

  /**
   * DailyZodiacReading findMany
   */
  export type DailyZodiacReadingFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DailyZodiacReading
     */
    select?: DailyZodiacReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DailyZodiacReading
     */
    omit?: DailyZodiacReadingOmit<ExtArgs> | null
    /**
     * Filter, which DailyZodiacReadings to fetch.
     */
    where?: DailyZodiacReadingWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of DailyZodiacReadings to fetch.
     */
    orderBy?: DailyZodiacReadingOrderByWithRelationInput | DailyZodiacReadingOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing DailyZodiacReadings.
     */
    cursor?: DailyZodiacReadingWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` DailyZodiacReadings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` DailyZodiacReadings.
     */
    skip?: number
    distinct?: DailyZodiacReadingScalarFieldEnum | DailyZodiacReadingScalarFieldEnum[]
  }

  /**
   * DailyZodiacReading create
   */
  export type DailyZodiacReadingCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DailyZodiacReading
     */
    select?: DailyZodiacReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DailyZodiacReading
     */
    omit?: DailyZodiacReadingOmit<ExtArgs> | null
    /**
     * The data needed to create a DailyZodiacReading.
     */
    data: XOR<DailyZodiacReadingCreateInput, DailyZodiacReadingUncheckedCreateInput>
  }

  /**
   * DailyZodiacReading createMany
   */
  export type DailyZodiacReadingCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many DailyZodiacReadings.
     */
    data: DailyZodiacReadingCreateManyInput | DailyZodiacReadingCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * DailyZodiacReading createManyAndReturn
   */
  export type DailyZodiacReadingCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DailyZodiacReading
     */
    select?: DailyZodiacReadingSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the DailyZodiacReading
     */
    omit?: DailyZodiacReadingOmit<ExtArgs> | null
    /**
     * The data used to create many DailyZodiacReadings.
     */
    data: DailyZodiacReadingCreateManyInput | DailyZodiacReadingCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * DailyZodiacReading update
   */
  export type DailyZodiacReadingUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DailyZodiacReading
     */
    select?: DailyZodiacReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DailyZodiacReading
     */
    omit?: DailyZodiacReadingOmit<ExtArgs> | null
    /**
     * The data needed to update a DailyZodiacReading.
     */
    data: XOR<DailyZodiacReadingUpdateInput, DailyZodiacReadingUncheckedUpdateInput>
    /**
     * Choose, which DailyZodiacReading to update.
     */
    where: DailyZodiacReadingWhereUniqueInput
  }

  /**
   * DailyZodiacReading updateMany
   */
  export type DailyZodiacReadingUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update DailyZodiacReadings.
     */
    data: XOR<DailyZodiacReadingUpdateManyMutationInput, DailyZodiacReadingUncheckedUpdateManyInput>
    /**
     * Filter which DailyZodiacReadings to update
     */
    where?: DailyZodiacReadingWhereInput
    /**
     * Limit how many DailyZodiacReadings to update.
     */
    limit?: number
  }

  /**
   * DailyZodiacReading updateManyAndReturn
   */
  export type DailyZodiacReadingUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DailyZodiacReading
     */
    select?: DailyZodiacReadingSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the DailyZodiacReading
     */
    omit?: DailyZodiacReadingOmit<ExtArgs> | null
    /**
     * The data used to update DailyZodiacReadings.
     */
    data: XOR<DailyZodiacReadingUpdateManyMutationInput, DailyZodiacReadingUncheckedUpdateManyInput>
    /**
     * Filter which DailyZodiacReadings to update
     */
    where?: DailyZodiacReadingWhereInput
    /**
     * Limit how many DailyZodiacReadings to update.
     */
    limit?: number
  }

  /**
   * DailyZodiacReading upsert
   */
  export type DailyZodiacReadingUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DailyZodiacReading
     */
    select?: DailyZodiacReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DailyZodiacReading
     */
    omit?: DailyZodiacReadingOmit<ExtArgs> | null
    /**
     * The filter to search for the DailyZodiacReading to update in case it exists.
     */
    where: DailyZodiacReadingWhereUniqueInput
    /**
     * In case the DailyZodiacReading found by the `where` argument doesn't exist, create a new DailyZodiacReading with this data.
     */
    create: XOR<DailyZodiacReadingCreateInput, DailyZodiacReadingUncheckedCreateInput>
    /**
     * In case the DailyZodiacReading was found with the provided `where` argument, update it with this data.
     */
    update: XOR<DailyZodiacReadingUpdateInput, DailyZodiacReadingUncheckedUpdateInput>
  }

  /**
   * DailyZodiacReading delete
   */
  export type DailyZodiacReadingDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DailyZodiacReading
     */
    select?: DailyZodiacReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DailyZodiacReading
     */
    omit?: DailyZodiacReadingOmit<ExtArgs> | null
    /**
     * Filter which DailyZodiacReading to delete.
     */
    where: DailyZodiacReadingWhereUniqueInput
  }

  /**
   * DailyZodiacReading deleteMany
   */
  export type DailyZodiacReadingDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which DailyZodiacReadings to delete
     */
    where?: DailyZodiacReadingWhereInput
    /**
     * Limit how many DailyZodiacReadings to delete.
     */
    limit?: number
  }

  /**
   * DailyZodiacReading without action
   */
  export type DailyZodiacReadingDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the DailyZodiacReading
     */
    select?: DailyZodiacReadingSelect<ExtArgs> | null
    /**
     * Omit specific fields from the DailyZodiacReading
     */
    omit?: DailyZodiacReadingOmit<ExtArgs> | null
  }


  /**
   * Model PersonalHoroscope
   */

  export type AggregatePersonalHoroscope = {
    _count: PersonalHoroscopeCountAggregateOutputType | null
    _min: PersonalHoroscopeMinAggregateOutputType | null
    _max: PersonalHoroscopeMaxAggregateOutputType | null
  }

  export type PersonalHoroscopeMinAggregateOutputType = {
    id: string | null
    userId: string | null
    title: string | null
    content: string | null
    isActive: boolean | null
    language: $Enums.LanguageCode | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type PersonalHoroscopeMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    title: string | null
    content: string | null
    isActive: boolean | null
    language: $Enums.LanguageCode | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type PersonalHoroscopeCountAggregateOutputType = {
    id: number
    userId: number
    title: number
    content: number
    isActive: number
    language: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type PersonalHoroscopeMinAggregateInputType = {
    id?: true
    userId?: true
    title?: true
    content?: true
    isActive?: true
    language?: true
    createdAt?: true
    updatedAt?: true
  }

  export type PersonalHoroscopeMaxAggregateInputType = {
    id?: true
    userId?: true
    title?: true
    content?: true
    isActive?: true
    language?: true
    createdAt?: true
    updatedAt?: true
  }

  export type PersonalHoroscopeCountAggregateInputType = {
    id?: true
    userId?: true
    title?: true
    content?: true
    isActive?: true
    language?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type PersonalHoroscopeAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which PersonalHoroscope to aggregate.
     */
    where?: PersonalHoroscopeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PersonalHoroscopes to fetch.
     */
    orderBy?: PersonalHoroscopeOrderByWithRelationInput | PersonalHoroscopeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: PersonalHoroscopeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PersonalHoroscopes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PersonalHoroscopes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned PersonalHoroscopes
    **/
    _count?: true | PersonalHoroscopeCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: PersonalHoroscopeMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: PersonalHoroscopeMaxAggregateInputType
  }

  export type GetPersonalHoroscopeAggregateType<T extends PersonalHoroscopeAggregateArgs> = {
        [P in keyof T & keyof AggregatePersonalHoroscope]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregatePersonalHoroscope[P]>
      : GetScalarType<T[P], AggregatePersonalHoroscope[P]>
  }




  export type PersonalHoroscopeGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PersonalHoroscopeWhereInput
    orderBy?: PersonalHoroscopeOrderByWithAggregationInput | PersonalHoroscopeOrderByWithAggregationInput[]
    by: PersonalHoroscopeScalarFieldEnum[] | PersonalHoroscopeScalarFieldEnum
    having?: PersonalHoroscopeScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: PersonalHoroscopeCountAggregateInputType | true
    _min?: PersonalHoroscopeMinAggregateInputType
    _max?: PersonalHoroscopeMaxAggregateInputType
  }

  export type PersonalHoroscopeGroupByOutputType = {
    id: string
    userId: string
    title: string
    content: string
    isActive: boolean
    language: $Enums.LanguageCode
    createdAt: Date
    updatedAt: Date
    _count: PersonalHoroscopeCountAggregateOutputType | null
    _min: PersonalHoroscopeMinAggregateOutputType | null
    _max: PersonalHoroscopeMaxAggregateOutputType | null
  }

  type GetPersonalHoroscopeGroupByPayload<T extends PersonalHoroscopeGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<PersonalHoroscopeGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof PersonalHoroscopeGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], PersonalHoroscopeGroupByOutputType[P]>
            : GetScalarType<T[P], PersonalHoroscopeGroupByOutputType[P]>
        }
      >
    >


  export type PersonalHoroscopeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    title?: boolean
    content?: boolean
    isActive?: boolean
    language?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["personalHoroscope"]>

  export type PersonalHoroscopeSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    title?: boolean
    content?: boolean
    isActive?: boolean
    language?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["personalHoroscope"]>

  export type PersonalHoroscopeSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    title?: boolean
    content?: boolean
    isActive?: boolean
    language?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["personalHoroscope"]>

  export type PersonalHoroscopeSelectScalar = {
    id?: boolean
    userId?: boolean
    title?: boolean
    content?: boolean
    isActive?: boolean
    language?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type PersonalHoroscopeOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "userId" | "title" | "content" | "isActive" | "language" | "createdAt" | "updatedAt", ExtArgs["result"]["personalHoroscope"]>
  export type PersonalHoroscopeInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type PersonalHoroscopeIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type PersonalHoroscopeIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $PersonalHoroscopePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "PersonalHoroscope"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      title: string
      content: string
      isActive: boolean
      language: $Enums.LanguageCode
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["personalHoroscope"]>
    composites: {}
  }

  type PersonalHoroscopeGetPayload<S extends boolean | null | undefined | PersonalHoroscopeDefaultArgs> = $Result.GetResult<Prisma.$PersonalHoroscopePayload, S>

  type PersonalHoroscopeCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<PersonalHoroscopeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: PersonalHoroscopeCountAggregateInputType | true
    }

  export interface PersonalHoroscopeDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['PersonalHoroscope'], meta: { name: 'PersonalHoroscope' } }
    /**
     * Find zero or one PersonalHoroscope that matches the filter.
     * @param {PersonalHoroscopeFindUniqueArgs} args - Arguments to find a PersonalHoroscope
     * @example
     * // Get one PersonalHoroscope
     * const personalHoroscope = await prisma.personalHoroscope.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends PersonalHoroscopeFindUniqueArgs>(args: SelectSubset<T, PersonalHoroscopeFindUniqueArgs<ExtArgs>>): Prisma__PersonalHoroscopeClient<$Result.GetResult<Prisma.$PersonalHoroscopePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one PersonalHoroscope that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {PersonalHoroscopeFindUniqueOrThrowArgs} args - Arguments to find a PersonalHoroscope
     * @example
     * // Get one PersonalHoroscope
     * const personalHoroscope = await prisma.personalHoroscope.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends PersonalHoroscopeFindUniqueOrThrowArgs>(args: SelectSubset<T, PersonalHoroscopeFindUniqueOrThrowArgs<ExtArgs>>): Prisma__PersonalHoroscopeClient<$Result.GetResult<Prisma.$PersonalHoroscopePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first PersonalHoroscope that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PersonalHoroscopeFindFirstArgs} args - Arguments to find a PersonalHoroscope
     * @example
     * // Get one PersonalHoroscope
     * const personalHoroscope = await prisma.personalHoroscope.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends PersonalHoroscopeFindFirstArgs>(args?: SelectSubset<T, PersonalHoroscopeFindFirstArgs<ExtArgs>>): Prisma__PersonalHoroscopeClient<$Result.GetResult<Prisma.$PersonalHoroscopePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first PersonalHoroscope that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PersonalHoroscopeFindFirstOrThrowArgs} args - Arguments to find a PersonalHoroscope
     * @example
     * // Get one PersonalHoroscope
     * const personalHoroscope = await prisma.personalHoroscope.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends PersonalHoroscopeFindFirstOrThrowArgs>(args?: SelectSubset<T, PersonalHoroscopeFindFirstOrThrowArgs<ExtArgs>>): Prisma__PersonalHoroscopeClient<$Result.GetResult<Prisma.$PersonalHoroscopePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more PersonalHoroscopes that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PersonalHoroscopeFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all PersonalHoroscopes
     * const personalHoroscopes = await prisma.personalHoroscope.findMany()
     * 
     * // Get first 10 PersonalHoroscopes
     * const personalHoroscopes = await prisma.personalHoroscope.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const personalHoroscopeWithIdOnly = await prisma.personalHoroscope.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends PersonalHoroscopeFindManyArgs>(args?: SelectSubset<T, PersonalHoroscopeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PersonalHoroscopePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a PersonalHoroscope.
     * @param {PersonalHoroscopeCreateArgs} args - Arguments to create a PersonalHoroscope.
     * @example
     * // Create one PersonalHoroscope
     * const PersonalHoroscope = await prisma.personalHoroscope.create({
     *   data: {
     *     // ... data to create a PersonalHoroscope
     *   }
     * })
     * 
     */
    create<T extends PersonalHoroscopeCreateArgs>(args: SelectSubset<T, PersonalHoroscopeCreateArgs<ExtArgs>>): Prisma__PersonalHoroscopeClient<$Result.GetResult<Prisma.$PersonalHoroscopePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many PersonalHoroscopes.
     * @param {PersonalHoroscopeCreateManyArgs} args - Arguments to create many PersonalHoroscopes.
     * @example
     * // Create many PersonalHoroscopes
     * const personalHoroscope = await prisma.personalHoroscope.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends PersonalHoroscopeCreateManyArgs>(args?: SelectSubset<T, PersonalHoroscopeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many PersonalHoroscopes and returns the data saved in the database.
     * @param {PersonalHoroscopeCreateManyAndReturnArgs} args - Arguments to create many PersonalHoroscopes.
     * @example
     * // Create many PersonalHoroscopes
     * const personalHoroscope = await prisma.personalHoroscope.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many PersonalHoroscopes and only return the `id`
     * const personalHoroscopeWithIdOnly = await prisma.personalHoroscope.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends PersonalHoroscopeCreateManyAndReturnArgs>(args?: SelectSubset<T, PersonalHoroscopeCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PersonalHoroscopePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a PersonalHoroscope.
     * @param {PersonalHoroscopeDeleteArgs} args - Arguments to delete one PersonalHoroscope.
     * @example
     * // Delete one PersonalHoroscope
     * const PersonalHoroscope = await prisma.personalHoroscope.delete({
     *   where: {
     *     // ... filter to delete one PersonalHoroscope
     *   }
     * })
     * 
     */
    delete<T extends PersonalHoroscopeDeleteArgs>(args: SelectSubset<T, PersonalHoroscopeDeleteArgs<ExtArgs>>): Prisma__PersonalHoroscopeClient<$Result.GetResult<Prisma.$PersonalHoroscopePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one PersonalHoroscope.
     * @param {PersonalHoroscopeUpdateArgs} args - Arguments to update one PersonalHoroscope.
     * @example
     * // Update one PersonalHoroscope
     * const personalHoroscope = await prisma.personalHoroscope.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends PersonalHoroscopeUpdateArgs>(args: SelectSubset<T, PersonalHoroscopeUpdateArgs<ExtArgs>>): Prisma__PersonalHoroscopeClient<$Result.GetResult<Prisma.$PersonalHoroscopePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more PersonalHoroscopes.
     * @param {PersonalHoroscopeDeleteManyArgs} args - Arguments to filter PersonalHoroscopes to delete.
     * @example
     * // Delete a few PersonalHoroscopes
     * const { count } = await prisma.personalHoroscope.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends PersonalHoroscopeDeleteManyArgs>(args?: SelectSubset<T, PersonalHoroscopeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more PersonalHoroscopes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PersonalHoroscopeUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many PersonalHoroscopes
     * const personalHoroscope = await prisma.personalHoroscope.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends PersonalHoroscopeUpdateManyArgs>(args: SelectSubset<T, PersonalHoroscopeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more PersonalHoroscopes and returns the data updated in the database.
     * @param {PersonalHoroscopeUpdateManyAndReturnArgs} args - Arguments to update many PersonalHoroscopes.
     * @example
     * // Update many PersonalHoroscopes
     * const personalHoroscope = await prisma.personalHoroscope.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more PersonalHoroscopes and only return the `id`
     * const personalHoroscopeWithIdOnly = await prisma.personalHoroscope.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends PersonalHoroscopeUpdateManyAndReturnArgs>(args: SelectSubset<T, PersonalHoroscopeUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PersonalHoroscopePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one PersonalHoroscope.
     * @param {PersonalHoroscopeUpsertArgs} args - Arguments to update or create a PersonalHoroscope.
     * @example
     * // Update or create a PersonalHoroscope
     * const personalHoroscope = await prisma.personalHoroscope.upsert({
     *   create: {
     *     // ... data to create a PersonalHoroscope
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the PersonalHoroscope we want to update
     *   }
     * })
     */
    upsert<T extends PersonalHoroscopeUpsertArgs>(args: SelectSubset<T, PersonalHoroscopeUpsertArgs<ExtArgs>>): Prisma__PersonalHoroscopeClient<$Result.GetResult<Prisma.$PersonalHoroscopePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of PersonalHoroscopes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PersonalHoroscopeCountArgs} args - Arguments to filter PersonalHoroscopes to count.
     * @example
     * // Count the number of PersonalHoroscopes
     * const count = await prisma.personalHoroscope.count({
     *   where: {
     *     // ... the filter for the PersonalHoroscopes we want to count
     *   }
     * })
    **/
    count<T extends PersonalHoroscopeCountArgs>(
      args?: Subset<T, PersonalHoroscopeCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], PersonalHoroscopeCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a PersonalHoroscope.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PersonalHoroscopeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends PersonalHoroscopeAggregateArgs>(args: Subset<T, PersonalHoroscopeAggregateArgs>): Prisma.PrismaPromise<GetPersonalHoroscopeAggregateType<T>>

    /**
     * Group by PersonalHoroscope.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PersonalHoroscopeGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends PersonalHoroscopeGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: PersonalHoroscopeGroupByArgs['orderBy'] }
        : { orderBy?: PersonalHoroscopeGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, PersonalHoroscopeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPersonalHoroscopeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the PersonalHoroscope model
   */
  readonly fields: PersonalHoroscopeFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for PersonalHoroscope.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__PersonalHoroscopeClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the PersonalHoroscope model
   */
  interface PersonalHoroscopeFieldRefs {
    readonly id: FieldRef<"PersonalHoroscope", 'String'>
    readonly userId: FieldRef<"PersonalHoroscope", 'String'>
    readonly title: FieldRef<"PersonalHoroscope", 'String'>
    readonly content: FieldRef<"PersonalHoroscope", 'String'>
    readonly isActive: FieldRef<"PersonalHoroscope", 'Boolean'>
    readonly language: FieldRef<"PersonalHoroscope", 'LanguageCode'>
    readonly createdAt: FieldRef<"PersonalHoroscope", 'DateTime'>
    readonly updatedAt: FieldRef<"PersonalHoroscope", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * PersonalHoroscope findUnique
   */
  export type PersonalHoroscopeFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PersonalHoroscope
     */
    select?: PersonalHoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PersonalHoroscope
     */
    omit?: PersonalHoroscopeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PersonalHoroscopeInclude<ExtArgs> | null
    /**
     * Filter, which PersonalHoroscope to fetch.
     */
    where: PersonalHoroscopeWhereUniqueInput
  }

  /**
   * PersonalHoroscope findUniqueOrThrow
   */
  export type PersonalHoroscopeFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PersonalHoroscope
     */
    select?: PersonalHoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PersonalHoroscope
     */
    omit?: PersonalHoroscopeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PersonalHoroscopeInclude<ExtArgs> | null
    /**
     * Filter, which PersonalHoroscope to fetch.
     */
    where: PersonalHoroscopeWhereUniqueInput
  }

  /**
   * PersonalHoroscope findFirst
   */
  export type PersonalHoroscopeFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PersonalHoroscope
     */
    select?: PersonalHoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PersonalHoroscope
     */
    omit?: PersonalHoroscopeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PersonalHoroscopeInclude<ExtArgs> | null
    /**
     * Filter, which PersonalHoroscope to fetch.
     */
    where?: PersonalHoroscopeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PersonalHoroscopes to fetch.
     */
    orderBy?: PersonalHoroscopeOrderByWithRelationInput | PersonalHoroscopeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for PersonalHoroscopes.
     */
    cursor?: PersonalHoroscopeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PersonalHoroscopes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PersonalHoroscopes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of PersonalHoroscopes.
     */
    distinct?: PersonalHoroscopeScalarFieldEnum | PersonalHoroscopeScalarFieldEnum[]
  }

  /**
   * PersonalHoroscope findFirstOrThrow
   */
  export type PersonalHoroscopeFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PersonalHoroscope
     */
    select?: PersonalHoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PersonalHoroscope
     */
    omit?: PersonalHoroscopeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PersonalHoroscopeInclude<ExtArgs> | null
    /**
     * Filter, which PersonalHoroscope to fetch.
     */
    where?: PersonalHoroscopeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PersonalHoroscopes to fetch.
     */
    orderBy?: PersonalHoroscopeOrderByWithRelationInput | PersonalHoroscopeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for PersonalHoroscopes.
     */
    cursor?: PersonalHoroscopeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PersonalHoroscopes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PersonalHoroscopes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of PersonalHoroscopes.
     */
    distinct?: PersonalHoroscopeScalarFieldEnum | PersonalHoroscopeScalarFieldEnum[]
  }

  /**
   * PersonalHoroscope findMany
   */
  export type PersonalHoroscopeFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PersonalHoroscope
     */
    select?: PersonalHoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PersonalHoroscope
     */
    omit?: PersonalHoroscopeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PersonalHoroscopeInclude<ExtArgs> | null
    /**
     * Filter, which PersonalHoroscopes to fetch.
     */
    where?: PersonalHoroscopeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PersonalHoroscopes to fetch.
     */
    orderBy?: PersonalHoroscopeOrderByWithRelationInput | PersonalHoroscopeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing PersonalHoroscopes.
     */
    cursor?: PersonalHoroscopeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PersonalHoroscopes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PersonalHoroscopes.
     */
    skip?: number
    distinct?: PersonalHoroscopeScalarFieldEnum | PersonalHoroscopeScalarFieldEnum[]
  }

  /**
   * PersonalHoroscope create
   */
  export type PersonalHoroscopeCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PersonalHoroscope
     */
    select?: PersonalHoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PersonalHoroscope
     */
    omit?: PersonalHoroscopeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PersonalHoroscopeInclude<ExtArgs> | null
    /**
     * The data needed to create a PersonalHoroscope.
     */
    data: XOR<PersonalHoroscopeCreateInput, PersonalHoroscopeUncheckedCreateInput>
  }

  /**
   * PersonalHoroscope createMany
   */
  export type PersonalHoroscopeCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many PersonalHoroscopes.
     */
    data: PersonalHoroscopeCreateManyInput | PersonalHoroscopeCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * PersonalHoroscope createManyAndReturn
   */
  export type PersonalHoroscopeCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PersonalHoroscope
     */
    select?: PersonalHoroscopeSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the PersonalHoroscope
     */
    omit?: PersonalHoroscopeOmit<ExtArgs> | null
    /**
     * The data used to create many PersonalHoroscopes.
     */
    data: PersonalHoroscopeCreateManyInput | PersonalHoroscopeCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PersonalHoroscopeIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * PersonalHoroscope update
   */
  export type PersonalHoroscopeUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PersonalHoroscope
     */
    select?: PersonalHoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PersonalHoroscope
     */
    omit?: PersonalHoroscopeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PersonalHoroscopeInclude<ExtArgs> | null
    /**
     * The data needed to update a PersonalHoroscope.
     */
    data: XOR<PersonalHoroscopeUpdateInput, PersonalHoroscopeUncheckedUpdateInput>
    /**
     * Choose, which PersonalHoroscope to update.
     */
    where: PersonalHoroscopeWhereUniqueInput
  }

  /**
   * PersonalHoroscope updateMany
   */
  export type PersonalHoroscopeUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update PersonalHoroscopes.
     */
    data: XOR<PersonalHoroscopeUpdateManyMutationInput, PersonalHoroscopeUncheckedUpdateManyInput>
    /**
     * Filter which PersonalHoroscopes to update
     */
    where?: PersonalHoroscopeWhereInput
    /**
     * Limit how many PersonalHoroscopes to update.
     */
    limit?: number
  }

  /**
   * PersonalHoroscope updateManyAndReturn
   */
  export type PersonalHoroscopeUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PersonalHoroscope
     */
    select?: PersonalHoroscopeSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the PersonalHoroscope
     */
    omit?: PersonalHoroscopeOmit<ExtArgs> | null
    /**
     * The data used to update PersonalHoroscopes.
     */
    data: XOR<PersonalHoroscopeUpdateManyMutationInput, PersonalHoroscopeUncheckedUpdateManyInput>
    /**
     * Filter which PersonalHoroscopes to update
     */
    where?: PersonalHoroscopeWhereInput
    /**
     * Limit how many PersonalHoroscopes to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PersonalHoroscopeIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * PersonalHoroscope upsert
   */
  export type PersonalHoroscopeUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PersonalHoroscope
     */
    select?: PersonalHoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PersonalHoroscope
     */
    omit?: PersonalHoroscopeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PersonalHoroscopeInclude<ExtArgs> | null
    /**
     * The filter to search for the PersonalHoroscope to update in case it exists.
     */
    where: PersonalHoroscopeWhereUniqueInput
    /**
     * In case the PersonalHoroscope found by the `where` argument doesn't exist, create a new PersonalHoroscope with this data.
     */
    create: XOR<PersonalHoroscopeCreateInput, PersonalHoroscopeUncheckedCreateInput>
    /**
     * In case the PersonalHoroscope was found with the provided `where` argument, update it with this data.
     */
    update: XOR<PersonalHoroscopeUpdateInput, PersonalHoroscopeUncheckedUpdateInput>
  }

  /**
   * PersonalHoroscope delete
   */
  export type PersonalHoroscopeDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PersonalHoroscope
     */
    select?: PersonalHoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PersonalHoroscope
     */
    omit?: PersonalHoroscopeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PersonalHoroscopeInclude<ExtArgs> | null
    /**
     * Filter which PersonalHoroscope to delete.
     */
    where: PersonalHoroscopeWhereUniqueInput
  }

  /**
   * PersonalHoroscope deleteMany
   */
  export type PersonalHoroscopeDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which PersonalHoroscopes to delete
     */
    where?: PersonalHoroscopeWhereInput
    /**
     * Limit how many PersonalHoroscopes to delete.
     */
    limit?: number
  }

  /**
   * PersonalHoroscope without action
   */
  export type PersonalHoroscopeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PersonalHoroscope
     */
    select?: PersonalHoroscopeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PersonalHoroscope
     */
    omit?: PersonalHoroscopeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PersonalHoroscopeInclude<ExtArgs> | null
  }


  /**
   * Model TranslationCache
   */

  export type AggregateTranslationCache = {
    _count: TranslationCacheCountAggregateOutputType | null
    _min: TranslationCacheMinAggregateOutputType | null
    _max: TranslationCacheMaxAggregateOutputType | null
  }

  export type TranslationCacheMinAggregateOutputType = {
    id: string | null
    originalText: string | null
    translatedText: string | null
    sourceLanguage: $Enums.LanguageCode | null
    targetLanguage: $Enums.LanguageCode | null
    createdAt: Date | null
  }

  export type TranslationCacheMaxAggregateOutputType = {
    id: string | null
    originalText: string | null
    translatedText: string | null
    sourceLanguage: $Enums.LanguageCode | null
    targetLanguage: $Enums.LanguageCode | null
    createdAt: Date | null
  }

  export type TranslationCacheCountAggregateOutputType = {
    id: number
    originalText: number
    translatedText: number
    sourceLanguage: number
    targetLanguage: number
    createdAt: number
    _all: number
  }


  export type TranslationCacheMinAggregateInputType = {
    id?: true
    originalText?: true
    translatedText?: true
    sourceLanguage?: true
    targetLanguage?: true
    createdAt?: true
  }

  export type TranslationCacheMaxAggregateInputType = {
    id?: true
    originalText?: true
    translatedText?: true
    sourceLanguage?: true
    targetLanguage?: true
    createdAt?: true
  }

  export type TranslationCacheCountAggregateInputType = {
    id?: true
    originalText?: true
    translatedText?: true
    sourceLanguage?: true
    targetLanguage?: true
    createdAt?: true
    _all?: true
  }

  export type TranslationCacheAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TranslationCache to aggregate.
     */
    where?: TranslationCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TranslationCaches to fetch.
     */
    orderBy?: TranslationCacheOrderByWithRelationInput | TranslationCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: TranslationCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TranslationCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TranslationCaches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned TranslationCaches
    **/
    _count?: true | TranslationCacheCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: TranslationCacheMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: TranslationCacheMaxAggregateInputType
  }

  export type GetTranslationCacheAggregateType<T extends TranslationCacheAggregateArgs> = {
        [P in keyof T & keyof AggregateTranslationCache]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateTranslationCache[P]>
      : GetScalarType<T[P], AggregateTranslationCache[P]>
  }




  export type TranslationCacheGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: TranslationCacheWhereInput
    orderBy?: TranslationCacheOrderByWithAggregationInput | TranslationCacheOrderByWithAggregationInput[]
    by: TranslationCacheScalarFieldEnum[] | TranslationCacheScalarFieldEnum
    having?: TranslationCacheScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: TranslationCacheCountAggregateInputType | true
    _min?: TranslationCacheMinAggregateInputType
    _max?: TranslationCacheMaxAggregateInputType
  }

  export type TranslationCacheGroupByOutputType = {
    id: string
    originalText: string
    translatedText: string
    sourceLanguage: $Enums.LanguageCode
    targetLanguage: $Enums.LanguageCode
    createdAt: Date
    _count: TranslationCacheCountAggregateOutputType | null
    _min: TranslationCacheMinAggregateOutputType | null
    _max: TranslationCacheMaxAggregateOutputType | null
  }

  type GetTranslationCacheGroupByPayload<T extends TranslationCacheGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<TranslationCacheGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof TranslationCacheGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], TranslationCacheGroupByOutputType[P]>
            : GetScalarType<T[P], TranslationCacheGroupByOutputType[P]>
        }
      >
    >


  export type TranslationCacheSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    originalText?: boolean
    translatedText?: boolean
    sourceLanguage?: boolean
    targetLanguage?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["translationCache"]>

  export type TranslationCacheSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    originalText?: boolean
    translatedText?: boolean
    sourceLanguage?: boolean
    targetLanguage?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["translationCache"]>

  export type TranslationCacheSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    originalText?: boolean
    translatedText?: boolean
    sourceLanguage?: boolean
    targetLanguage?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["translationCache"]>

  export type TranslationCacheSelectScalar = {
    id?: boolean
    originalText?: boolean
    translatedText?: boolean
    sourceLanguage?: boolean
    targetLanguage?: boolean
    createdAt?: boolean
  }

  export type TranslationCacheOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "originalText" | "translatedText" | "sourceLanguage" | "targetLanguage" | "createdAt", ExtArgs["result"]["translationCache"]>

  export type $TranslationCachePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "TranslationCache"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      originalText: string
      translatedText: string
      sourceLanguage: $Enums.LanguageCode
      targetLanguage: $Enums.LanguageCode
      createdAt: Date
    }, ExtArgs["result"]["translationCache"]>
    composites: {}
  }

  type TranslationCacheGetPayload<S extends boolean | null | undefined | TranslationCacheDefaultArgs> = $Result.GetResult<Prisma.$TranslationCachePayload, S>

  type TranslationCacheCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<TranslationCacheFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: TranslationCacheCountAggregateInputType | true
    }

  export interface TranslationCacheDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TranslationCache'], meta: { name: 'TranslationCache' } }
    /**
     * Find zero or one TranslationCache that matches the filter.
     * @param {TranslationCacheFindUniqueArgs} args - Arguments to find a TranslationCache
     * @example
     * // Get one TranslationCache
     * const translationCache = await prisma.translationCache.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends TranslationCacheFindUniqueArgs>(args: SelectSubset<T, TranslationCacheFindUniqueArgs<ExtArgs>>): Prisma__TranslationCacheClient<$Result.GetResult<Prisma.$TranslationCachePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one TranslationCache that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {TranslationCacheFindUniqueOrThrowArgs} args - Arguments to find a TranslationCache
     * @example
     * // Get one TranslationCache
     * const translationCache = await prisma.translationCache.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends TranslationCacheFindUniqueOrThrowArgs>(args: SelectSubset<T, TranslationCacheFindUniqueOrThrowArgs<ExtArgs>>): Prisma__TranslationCacheClient<$Result.GetResult<Prisma.$TranslationCachePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TranslationCache that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TranslationCacheFindFirstArgs} args - Arguments to find a TranslationCache
     * @example
     * // Get one TranslationCache
     * const translationCache = await prisma.translationCache.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends TranslationCacheFindFirstArgs>(args?: SelectSubset<T, TranslationCacheFindFirstArgs<ExtArgs>>): Prisma__TranslationCacheClient<$Result.GetResult<Prisma.$TranslationCachePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TranslationCache that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TranslationCacheFindFirstOrThrowArgs} args - Arguments to find a TranslationCache
     * @example
     * // Get one TranslationCache
     * const translationCache = await prisma.translationCache.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends TranslationCacheFindFirstOrThrowArgs>(args?: SelectSubset<T, TranslationCacheFindFirstOrThrowArgs<ExtArgs>>): Prisma__TranslationCacheClient<$Result.GetResult<Prisma.$TranslationCachePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more TranslationCaches that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TranslationCacheFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all TranslationCaches
     * const translationCaches = await prisma.translationCache.findMany()
     * 
     * // Get first 10 TranslationCaches
     * const translationCaches = await prisma.translationCache.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const translationCacheWithIdOnly = await prisma.translationCache.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends TranslationCacheFindManyArgs>(args?: SelectSubset<T, TranslationCacheFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TranslationCachePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a TranslationCache.
     * @param {TranslationCacheCreateArgs} args - Arguments to create a TranslationCache.
     * @example
     * // Create one TranslationCache
     * const TranslationCache = await prisma.translationCache.create({
     *   data: {
     *     // ... data to create a TranslationCache
     *   }
     * })
     * 
     */
    create<T extends TranslationCacheCreateArgs>(args: SelectSubset<T, TranslationCacheCreateArgs<ExtArgs>>): Prisma__TranslationCacheClient<$Result.GetResult<Prisma.$TranslationCachePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many TranslationCaches.
     * @param {TranslationCacheCreateManyArgs} args - Arguments to create many TranslationCaches.
     * @example
     * // Create many TranslationCaches
     * const translationCache = await prisma.translationCache.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends TranslationCacheCreateManyArgs>(args?: SelectSubset<T, TranslationCacheCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many TranslationCaches and returns the data saved in the database.
     * @param {TranslationCacheCreateManyAndReturnArgs} args - Arguments to create many TranslationCaches.
     * @example
     * // Create many TranslationCaches
     * const translationCache = await prisma.translationCache.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many TranslationCaches and only return the `id`
     * const translationCacheWithIdOnly = await prisma.translationCache.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends TranslationCacheCreateManyAndReturnArgs>(args?: SelectSubset<T, TranslationCacheCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TranslationCachePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a TranslationCache.
     * @param {TranslationCacheDeleteArgs} args - Arguments to delete one TranslationCache.
     * @example
     * // Delete one TranslationCache
     * const TranslationCache = await prisma.translationCache.delete({
     *   where: {
     *     // ... filter to delete one TranslationCache
     *   }
     * })
     * 
     */
    delete<T extends TranslationCacheDeleteArgs>(args: SelectSubset<T, TranslationCacheDeleteArgs<ExtArgs>>): Prisma__TranslationCacheClient<$Result.GetResult<Prisma.$TranslationCachePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one TranslationCache.
     * @param {TranslationCacheUpdateArgs} args - Arguments to update one TranslationCache.
     * @example
     * // Update one TranslationCache
     * const translationCache = await prisma.translationCache.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends TranslationCacheUpdateArgs>(args: SelectSubset<T, TranslationCacheUpdateArgs<ExtArgs>>): Prisma__TranslationCacheClient<$Result.GetResult<Prisma.$TranslationCachePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more TranslationCaches.
     * @param {TranslationCacheDeleteManyArgs} args - Arguments to filter TranslationCaches to delete.
     * @example
     * // Delete a few TranslationCaches
     * const { count } = await prisma.translationCache.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends TranslationCacheDeleteManyArgs>(args?: SelectSubset<T, TranslationCacheDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TranslationCaches.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TranslationCacheUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many TranslationCaches
     * const translationCache = await prisma.translationCache.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends TranslationCacheUpdateManyArgs>(args: SelectSubset<T, TranslationCacheUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TranslationCaches and returns the data updated in the database.
     * @param {TranslationCacheUpdateManyAndReturnArgs} args - Arguments to update many TranslationCaches.
     * @example
     * // Update many TranslationCaches
     * const translationCache = await prisma.translationCache.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more TranslationCaches and only return the `id`
     * const translationCacheWithIdOnly = await prisma.translationCache.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends TranslationCacheUpdateManyAndReturnArgs>(args: SelectSubset<T, TranslationCacheUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TranslationCachePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one TranslationCache.
     * @param {TranslationCacheUpsertArgs} args - Arguments to update or create a TranslationCache.
     * @example
     * // Update or create a TranslationCache
     * const translationCache = await prisma.translationCache.upsert({
     *   create: {
     *     // ... data to create a TranslationCache
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the TranslationCache we want to update
     *   }
     * })
     */
    upsert<T extends TranslationCacheUpsertArgs>(args: SelectSubset<T, TranslationCacheUpsertArgs<ExtArgs>>): Prisma__TranslationCacheClient<$Result.GetResult<Prisma.$TranslationCachePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of TranslationCaches.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TranslationCacheCountArgs} args - Arguments to filter TranslationCaches to count.
     * @example
     * // Count the number of TranslationCaches
     * const count = await prisma.translationCache.count({
     *   where: {
     *     // ... the filter for the TranslationCaches we want to count
     *   }
     * })
    **/
    count<T extends TranslationCacheCountArgs>(
      args?: Subset<T, TranslationCacheCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], TranslationCacheCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a TranslationCache.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TranslationCacheAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends TranslationCacheAggregateArgs>(args: Subset<T, TranslationCacheAggregateArgs>): Prisma.PrismaPromise<GetTranslationCacheAggregateType<T>>

    /**
     * Group by TranslationCache.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TranslationCacheGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends TranslationCacheGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: TranslationCacheGroupByArgs['orderBy'] }
        : { orderBy?: TranslationCacheGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, TranslationCacheGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTranslationCacheGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the TranslationCache model
   */
  readonly fields: TranslationCacheFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for TranslationCache.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__TranslationCacheClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the TranslationCache model
   */
  interface TranslationCacheFieldRefs {
    readonly id: FieldRef<"TranslationCache", 'String'>
    readonly originalText: FieldRef<"TranslationCache", 'String'>
    readonly translatedText: FieldRef<"TranslationCache", 'String'>
    readonly sourceLanguage: FieldRef<"TranslationCache", 'LanguageCode'>
    readonly targetLanguage: FieldRef<"TranslationCache", 'LanguageCode'>
    readonly createdAt: FieldRef<"TranslationCache", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * TranslationCache findUnique
   */
  export type TranslationCacheFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TranslationCache
     */
    select?: TranslationCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TranslationCache
     */
    omit?: TranslationCacheOmit<ExtArgs> | null
    /**
     * Filter, which TranslationCache to fetch.
     */
    where: TranslationCacheWhereUniqueInput
  }

  /**
   * TranslationCache findUniqueOrThrow
   */
  export type TranslationCacheFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TranslationCache
     */
    select?: TranslationCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TranslationCache
     */
    omit?: TranslationCacheOmit<ExtArgs> | null
    /**
     * Filter, which TranslationCache to fetch.
     */
    where: TranslationCacheWhereUniqueInput
  }

  /**
   * TranslationCache findFirst
   */
  export type TranslationCacheFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TranslationCache
     */
    select?: TranslationCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TranslationCache
     */
    omit?: TranslationCacheOmit<ExtArgs> | null
    /**
     * Filter, which TranslationCache to fetch.
     */
    where?: TranslationCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TranslationCaches to fetch.
     */
    orderBy?: TranslationCacheOrderByWithRelationInput | TranslationCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TranslationCaches.
     */
    cursor?: TranslationCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TranslationCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TranslationCaches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TranslationCaches.
     */
    distinct?: TranslationCacheScalarFieldEnum | TranslationCacheScalarFieldEnum[]
  }

  /**
   * TranslationCache findFirstOrThrow
   */
  export type TranslationCacheFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TranslationCache
     */
    select?: TranslationCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TranslationCache
     */
    omit?: TranslationCacheOmit<ExtArgs> | null
    /**
     * Filter, which TranslationCache to fetch.
     */
    where?: TranslationCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TranslationCaches to fetch.
     */
    orderBy?: TranslationCacheOrderByWithRelationInput | TranslationCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TranslationCaches.
     */
    cursor?: TranslationCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TranslationCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TranslationCaches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TranslationCaches.
     */
    distinct?: TranslationCacheScalarFieldEnum | TranslationCacheScalarFieldEnum[]
  }

  /**
   * TranslationCache findMany
   */
  export type TranslationCacheFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TranslationCache
     */
    select?: TranslationCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TranslationCache
     */
    omit?: TranslationCacheOmit<ExtArgs> | null
    /**
     * Filter, which TranslationCaches to fetch.
     */
    where?: TranslationCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TranslationCaches to fetch.
     */
    orderBy?: TranslationCacheOrderByWithRelationInput | TranslationCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing TranslationCaches.
     */
    cursor?: TranslationCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TranslationCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TranslationCaches.
     */
    skip?: number
    distinct?: TranslationCacheScalarFieldEnum | TranslationCacheScalarFieldEnum[]
  }

  /**
   * TranslationCache create
   */
  export type TranslationCacheCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TranslationCache
     */
    select?: TranslationCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TranslationCache
     */
    omit?: TranslationCacheOmit<ExtArgs> | null
    /**
     * The data needed to create a TranslationCache.
     */
    data: XOR<TranslationCacheCreateInput, TranslationCacheUncheckedCreateInput>
  }

  /**
   * TranslationCache createMany
   */
  export type TranslationCacheCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many TranslationCaches.
     */
    data: TranslationCacheCreateManyInput | TranslationCacheCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * TranslationCache createManyAndReturn
   */
  export type TranslationCacheCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TranslationCache
     */
    select?: TranslationCacheSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TranslationCache
     */
    omit?: TranslationCacheOmit<ExtArgs> | null
    /**
     * The data used to create many TranslationCaches.
     */
    data: TranslationCacheCreateManyInput | TranslationCacheCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * TranslationCache update
   */
  export type TranslationCacheUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TranslationCache
     */
    select?: TranslationCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TranslationCache
     */
    omit?: TranslationCacheOmit<ExtArgs> | null
    /**
     * The data needed to update a TranslationCache.
     */
    data: XOR<TranslationCacheUpdateInput, TranslationCacheUncheckedUpdateInput>
    /**
     * Choose, which TranslationCache to update.
     */
    where: TranslationCacheWhereUniqueInput
  }

  /**
   * TranslationCache updateMany
   */
  export type TranslationCacheUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update TranslationCaches.
     */
    data: XOR<TranslationCacheUpdateManyMutationInput, TranslationCacheUncheckedUpdateManyInput>
    /**
     * Filter which TranslationCaches to update
     */
    where?: TranslationCacheWhereInput
    /**
     * Limit how many TranslationCaches to update.
     */
    limit?: number
  }

  /**
   * TranslationCache updateManyAndReturn
   */
  export type TranslationCacheUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TranslationCache
     */
    select?: TranslationCacheSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TranslationCache
     */
    omit?: TranslationCacheOmit<ExtArgs> | null
    /**
     * The data used to update TranslationCaches.
     */
    data: XOR<TranslationCacheUpdateManyMutationInput, TranslationCacheUncheckedUpdateManyInput>
    /**
     * Filter which TranslationCaches to update
     */
    where?: TranslationCacheWhereInput
    /**
     * Limit how many TranslationCaches to update.
     */
    limit?: number
  }

  /**
   * TranslationCache upsert
   */
  export type TranslationCacheUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TranslationCache
     */
    select?: TranslationCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TranslationCache
     */
    omit?: TranslationCacheOmit<ExtArgs> | null
    /**
     * The filter to search for the TranslationCache to update in case it exists.
     */
    where: TranslationCacheWhereUniqueInput
    /**
     * In case the TranslationCache found by the `where` argument doesn't exist, create a new TranslationCache with this data.
     */
    create: XOR<TranslationCacheCreateInput, TranslationCacheUncheckedCreateInput>
    /**
     * In case the TranslationCache was found with the provided `where` argument, update it with this data.
     */
    update: XOR<TranslationCacheUpdateInput, TranslationCacheUncheckedUpdateInput>
  }

  /**
   * TranslationCache delete
   */
  export type TranslationCacheDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TranslationCache
     */
    select?: TranslationCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TranslationCache
     */
    omit?: TranslationCacheOmit<ExtArgs> | null
    /**
     * Filter which TranslationCache to delete.
     */
    where: TranslationCacheWhereUniqueInput
  }

  /**
   * TranslationCache deleteMany
   */
  export type TranslationCacheDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TranslationCaches to delete
     */
    where?: TranslationCacheWhereInput
    /**
     * Limit how many TranslationCaches to delete.
     */
    limit?: number
  }

  /**
   * TranslationCache without action
   */
  export type TranslationCacheDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TranslationCache
     */
    select?: TranslationCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TranslationCache
     */
    omit?: TranslationCacheOmit<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const AdminScalarFieldEnum: {
    id: 'id',
    email: 'email',
    password: 'password',
    name: 'name',
    role: 'role',
    isActive: 'isActive',
    lastLogin: 'lastLogin',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type AdminScalarFieldEnum = (typeof AdminScalarFieldEnum)[keyof typeof AdminScalarFieldEnum]


  export const UserScalarFieldEnum: {
    id: 'id',
    email: 'email',
    name: 'name',
    phoneNumber: 'phoneNumber',
    address: 'address',
    zodiacSign: 'zodiacSign',
    birthDate: 'birthDate',
    birthTime: 'birthTime',
    qrToken: 'qrToken',
    languagePreference: 'languagePreference',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


  export const QrCodeMappingScalarFieldEnum: {
    id: 'id',
    qrToken: 'qrToken',
    userId: 'userId',
    createdAt: 'createdAt',
    lastScanned: 'lastScanned',
    scanCount: 'scanCount'
  };

  export type QrCodeMappingScalarFieldEnum = (typeof QrCodeMappingScalarFieldEnum)[keyof typeof QrCodeMappingScalarFieldEnum]


  export const HoroscopeScalarFieldEnum: {
    id: 'id',
    zodiacSign: 'zodiacSign',
    type: 'type',
    content: 'content',
    date: 'date',
    language: 'language',
    createdAt: 'createdAt'
  };

  export type HoroscopeScalarFieldEnum = (typeof HoroscopeScalarFieldEnum)[keyof typeof HoroscopeScalarFieldEnum]


  export const DailyZodiacReadingScalarFieldEnum: {
    id: 'id',
    zodiacSign: 'zodiacSign',
    date: 'date',
    generalReading: 'generalReading',
    loveReading: 'loveReading',
    careerReading: 'careerReading',
    healthReading: 'healthReading',
    luckyNumber: 'luckyNumber',
    luckyColor: 'luckyColor',
    luckyTime: 'luckyTime',
    luckyGem: 'luckyGem',
    advice: 'advice',
    mood: 'mood',
    compatibility: 'compatibility',
    language: 'language',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type DailyZodiacReadingScalarFieldEnum = (typeof DailyZodiacReadingScalarFieldEnum)[keyof typeof DailyZodiacReadingScalarFieldEnum]


  export const PersonalHoroscopeScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    title: 'title',
    content: 'content',
    isActive: 'isActive',
    language: 'language',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type PersonalHoroscopeScalarFieldEnum = (typeof PersonalHoroscopeScalarFieldEnum)[keyof typeof PersonalHoroscopeScalarFieldEnum]


  export const TranslationCacheScalarFieldEnum: {
    id: 'id',
    originalText: 'originalText',
    translatedText: 'translatedText',
    sourceLanguage: 'sourceLanguage',
    targetLanguage: 'targetLanguage',
    createdAt: 'createdAt'
  };

  export type TranslationCacheScalarFieldEnum = (typeof TranslationCacheScalarFieldEnum)[keyof typeof TranslationCacheScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'UserRole'
   */
  export type EnumUserRoleFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'UserRole'>
    


  /**
   * Reference to a field of type 'UserRole[]'
   */
  export type ListEnumUserRoleFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'UserRole[]'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'ZodiacSign'
   */
  export type EnumZodiacSignFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ZodiacSign'>
    


  /**
   * Reference to a field of type 'ZodiacSign[]'
   */
  export type ListEnumZodiacSignFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'ZodiacSign[]'>
    


  /**
   * Reference to a field of type 'LanguageCode'
   */
  export type EnumLanguageCodeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'LanguageCode'>
    


  /**
   * Reference to a field of type 'LanguageCode[]'
   */
  export type ListEnumLanguageCodeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'LanguageCode[]'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    


  /**
   * Reference to a field of type 'HoroscopeType'
   */
  export type EnumHoroscopeTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'HoroscopeType'>
    


  /**
   * Reference to a field of type 'HoroscopeType[]'
   */
  export type ListEnumHoroscopeTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'HoroscopeType[]'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'Float[]'
   */
  export type ListFloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float[]'>
    
  /**
   * Deep Input Types
   */


  export type AdminWhereInput = {
    AND?: AdminWhereInput | AdminWhereInput[]
    OR?: AdminWhereInput[]
    NOT?: AdminWhereInput | AdminWhereInput[]
    id?: StringFilter<"Admin"> | string
    email?: StringFilter<"Admin"> | string
    password?: StringFilter<"Admin"> | string
    name?: StringFilter<"Admin"> | string
    role?: EnumUserRoleFilter<"Admin"> | $Enums.UserRole
    isActive?: BoolFilter<"Admin"> | boolean
    lastLogin?: DateTimeNullableFilter<"Admin"> | Date | string | null
    createdAt?: DateTimeFilter<"Admin"> | Date | string
    updatedAt?: DateTimeFilter<"Admin"> | Date | string
  }

  export type AdminOrderByWithRelationInput = {
    id?: SortOrder
    email?: SortOrder
    password?: SortOrder
    name?: SortOrder
    role?: SortOrder
    isActive?: SortOrder
    lastLogin?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AdminWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    email?: string
    AND?: AdminWhereInput | AdminWhereInput[]
    OR?: AdminWhereInput[]
    NOT?: AdminWhereInput | AdminWhereInput[]
    password?: StringFilter<"Admin"> | string
    name?: StringFilter<"Admin"> | string
    role?: EnumUserRoleFilter<"Admin"> | $Enums.UserRole
    isActive?: BoolFilter<"Admin"> | boolean
    lastLogin?: DateTimeNullableFilter<"Admin"> | Date | string | null
    createdAt?: DateTimeFilter<"Admin"> | Date | string
    updatedAt?: DateTimeFilter<"Admin"> | Date | string
  }, "id" | "email">

  export type AdminOrderByWithAggregationInput = {
    id?: SortOrder
    email?: SortOrder
    password?: SortOrder
    name?: SortOrder
    role?: SortOrder
    isActive?: SortOrder
    lastLogin?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: AdminCountOrderByAggregateInput
    _max?: AdminMaxOrderByAggregateInput
    _min?: AdminMinOrderByAggregateInput
  }

  export type AdminScalarWhereWithAggregatesInput = {
    AND?: AdminScalarWhereWithAggregatesInput | AdminScalarWhereWithAggregatesInput[]
    OR?: AdminScalarWhereWithAggregatesInput[]
    NOT?: AdminScalarWhereWithAggregatesInput | AdminScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Admin"> | string
    email?: StringWithAggregatesFilter<"Admin"> | string
    password?: StringWithAggregatesFilter<"Admin"> | string
    name?: StringWithAggregatesFilter<"Admin"> | string
    role?: EnumUserRoleWithAggregatesFilter<"Admin"> | $Enums.UserRole
    isActive?: BoolWithAggregatesFilter<"Admin"> | boolean
    lastLogin?: DateTimeNullableWithAggregatesFilter<"Admin"> | Date | string | null
    createdAt?: DateTimeWithAggregatesFilter<"Admin"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Admin"> | Date | string
  }

  export type UserWhereInput = {
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    id?: StringFilter<"User"> | string
    email?: StringNullableFilter<"User"> | string | null
    name?: StringFilter<"User"> | string
    phoneNumber?: StringNullableFilter<"User"> | string | null
    address?: StringNullableFilter<"User"> | string | null
    zodiacSign?: EnumZodiacSignFilter<"User"> | $Enums.ZodiacSign
    birthDate?: DateTimeFilter<"User"> | Date | string
    birthTime?: StringNullableFilter<"User"> | string | null
    qrToken?: StringFilter<"User"> | string
    languagePreference?: EnumLanguageCodeFilter<"User"> | $Enums.LanguageCode
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    qrCodeMappings?: QrCodeMappingListRelationFilter
    personalHoroscopes?: PersonalHoroscopeListRelationFilter
  }

  export type UserOrderByWithRelationInput = {
    id?: SortOrder
    email?: SortOrderInput | SortOrder
    name?: SortOrder
    phoneNumber?: SortOrderInput | SortOrder
    address?: SortOrderInput | SortOrder
    zodiacSign?: SortOrder
    birthDate?: SortOrder
    birthTime?: SortOrderInput | SortOrder
    qrToken?: SortOrder
    languagePreference?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    qrCodeMappings?: QrCodeMappingOrderByRelationAggregateInput
    personalHoroscopes?: PersonalHoroscopeOrderByRelationAggregateInput
  }

  export type UserWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    email?: string
    qrToken?: string
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    name?: StringFilter<"User"> | string
    phoneNumber?: StringNullableFilter<"User"> | string | null
    address?: StringNullableFilter<"User"> | string | null
    zodiacSign?: EnumZodiacSignFilter<"User"> | $Enums.ZodiacSign
    birthDate?: DateTimeFilter<"User"> | Date | string
    birthTime?: StringNullableFilter<"User"> | string | null
    languagePreference?: EnumLanguageCodeFilter<"User"> | $Enums.LanguageCode
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    qrCodeMappings?: QrCodeMappingListRelationFilter
    personalHoroscopes?: PersonalHoroscopeListRelationFilter
  }, "id" | "email" | "qrToken">

  export type UserOrderByWithAggregationInput = {
    id?: SortOrder
    email?: SortOrderInput | SortOrder
    name?: SortOrder
    phoneNumber?: SortOrderInput | SortOrder
    address?: SortOrderInput | SortOrder
    zodiacSign?: SortOrder
    birthDate?: SortOrder
    birthTime?: SortOrderInput | SortOrder
    qrToken?: SortOrder
    languagePreference?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserCountOrderByAggregateInput
    _max?: UserMaxOrderByAggregateInput
    _min?: UserMinOrderByAggregateInput
  }

  export type UserScalarWhereWithAggregatesInput = {
    AND?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    OR?: UserScalarWhereWithAggregatesInput[]
    NOT?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"User"> | string
    email?: StringNullableWithAggregatesFilter<"User"> | string | null
    name?: StringWithAggregatesFilter<"User"> | string
    phoneNumber?: StringNullableWithAggregatesFilter<"User"> | string | null
    address?: StringNullableWithAggregatesFilter<"User"> | string | null
    zodiacSign?: EnumZodiacSignWithAggregatesFilter<"User"> | $Enums.ZodiacSign
    birthDate?: DateTimeWithAggregatesFilter<"User"> | Date | string
    birthTime?: StringNullableWithAggregatesFilter<"User"> | string | null
    qrToken?: StringWithAggregatesFilter<"User"> | string
    languagePreference?: EnumLanguageCodeWithAggregatesFilter<"User"> | $Enums.LanguageCode
    createdAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
  }

  export type QrCodeMappingWhereInput = {
    AND?: QrCodeMappingWhereInput | QrCodeMappingWhereInput[]
    OR?: QrCodeMappingWhereInput[]
    NOT?: QrCodeMappingWhereInput | QrCodeMappingWhereInput[]
    id?: StringFilter<"QrCodeMapping"> | string
    qrToken?: StringFilter<"QrCodeMapping"> | string
    userId?: StringFilter<"QrCodeMapping"> | string
    createdAt?: DateTimeFilter<"QrCodeMapping"> | Date | string
    lastScanned?: DateTimeNullableFilter<"QrCodeMapping"> | Date | string | null
    scanCount?: IntFilter<"QrCodeMapping"> | number
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }

  export type QrCodeMappingOrderByWithRelationInput = {
    id?: SortOrder
    qrToken?: SortOrder
    userId?: SortOrder
    createdAt?: SortOrder
    lastScanned?: SortOrderInput | SortOrder
    scanCount?: SortOrder
    user?: UserOrderByWithRelationInput
  }

  export type QrCodeMappingWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    qrToken?: string
    AND?: QrCodeMappingWhereInput | QrCodeMappingWhereInput[]
    OR?: QrCodeMappingWhereInput[]
    NOT?: QrCodeMappingWhereInput | QrCodeMappingWhereInput[]
    userId?: StringFilter<"QrCodeMapping"> | string
    createdAt?: DateTimeFilter<"QrCodeMapping"> | Date | string
    lastScanned?: DateTimeNullableFilter<"QrCodeMapping"> | Date | string | null
    scanCount?: IntFilter<"QrCodeMapping"> | number
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }, "id" | "qrToken">

  export type QrCodeMappingOrderByWithAggregationInput = {
    id?: SortOrder
    qrToken?: SortOrder
    userId?: SortOrder
    createdAt?: SortOrder
    lastScanned?: SortOrderInput | SortOrder
    scanCount?: SortOrder
    _count?: QrCodeMappingCountOrderByAggregateInput
    _avg?: QrCodeMappingAvgOrderByAggregateInput
    _max?: QrCodeMappingMaxOrderByAggregateInput
    _min?: QrCodeMappingMinOrderByAggregateInput
    _sum?: QrCodeMappingSumOrderByAggregateInput
  }

  export type QrCodeMappingScalarWhereWithAggregatesInput = {
    AND?: QrCodeMappingScalarWhereWithAggregatesInput | QrCodeMappingScalarWhereWithAggregatesInput[]
    OR?: QrCodeMappingScalarWhereWithAggregatesInput[]
    NOT?: QrCodeMappingScalarWhereWithAggregatesInput | QrCodeMappingScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"QrCodeMapping"> | string
    qrToken?: StringWithAggregatesFilter<"QrCodeMapping"> | string
    userId?: StringWithAggregatesFilter<"QrCodeMapping"> | string
    createdAt?: DateTimeWithAggregatesFilter<"QrCodeMapping"> | Date | string
    lastScanned?: DateTimeNullableWithAggregatesFilter<"QrCodeMapping"> | Date | string | null
    scanCount?: IntWithAggregatesFilter<"QrCodeMapping"> | number
  }

  export type HoroscopeWhereInput = {
    AND?: HoroscopeWhereInput | HoroscopeWhereInput[]
    OR?: HoroscopeWhereInput[]
    NOT?: HoroscopeWhereInput | HoroscopeWhereInput[]
    id?: StringFilter<"Horoscope"> | string
    zodiacSign?: EnumZodiacSignFilter<"Horoscope"> | $Enums.ZodiacSign
    type?: EnumHoroscopeTypeFilter<"Horoscope"> | $Enums.HoroscopeType
    content?: StringFilter<"Horoscope"> | string
    date?: DateTimeFilter<"Horoscope"> | Date | string
    language?: EnumLanguageCodeFilter<"Horoscope"> | $Enums.LanguageCode
    createdAt?: DateTimeFilter<"Horoscope"> | Date | string
  }

  export type HoroscopeOrderByWithRelationInput = {
    id?: SortOrder
    zodiacSign?: SortOrder
    type?: SortOrder
    content?: SortOrder
    date?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
  }

  export type HoroscopeWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    zodiacSign_type_date_language?: HoroscopeZodiacSignTypeDateLanguageCompoundUniqueInput
    AND?: HoroscopeWhereInput | HoroscopeWhereInput[]
    OR?: HoroscopeWhereInput[]
    NOT?: HoroscopeWhereInput | HoroscopeWhereInput[]
    zodiacSign?: EnumZodiacSignFilter<"Horoscope"> | $Enums.ZodiacSign
    type?: EnumHoroscopeTypeFilter<"Horoscope"> | $Enums.HoroscopeType
    content?: StringFilter<"Horoscope"> | string
    date?: DateTimeFilter<"Horoscope"> | Date | string
    language?: EnumLanguageCodeFilter<"Horoscope"> | $Enums.LanguageCode
    createdAt?: DateTimeFilter<"Horoscope"> | Date | string
  }, "id" | "zodiacSign_type_date_language">

  export type HoroscopeOrderByWithAggregationInput = {
    id?: SortOrder
    zodiacSign?: SortOrder
    type?: SortOrder
    content?: SortOrder
    date?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
    _count?: HoroscopeCountOrderByAggregateInput
    _max?: HoroscopeMaxOrderByAggregateInput
    _min?: HoroscopeMinOrderByAggregateInput
  }

  export type HoroscopeScalarWhereWithAggregatesInput = {
    AND?: HoroscopeScalarWhereWithAggregatesInput | HoroscopeScalarWhereWithAggregatesInput[]
    OR?: HoroscopeScalarWhereWithAggregatesInput[]
    NOT?: HoroscopeScalarWhereWithAggregatesInput | HoroscopeScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Horoscope"> | string
    zodiacSign?: EnumZodiacSignWithAggregatesFilter<"Horoscope"> | $Enums.ZodiacSign
    type?: EnumHoroscopeTypeWithAggregatesFilter<"Horoscope"> | $Enums.HoroscopeType
    content?: StringWithAggregatesFilter<"Horoscope"> | string
    date?: DateTimeWithAggregatesFilter<"Horoscope"> | Date | string
    language?: EnumLanguageCodeWithAggregatesFilter<"Horoscope"> | $Enums.LanguageCode
    createdAt?: DateTimeWithAggregatesFilter<"Horoscope"> | Date | string
  }

  export type DailyZodiacReadingWhereInput = {
    AND?: DailyZodiacReadingWhereInput | DailyZodiacReadingWhereInput[]
    OR?: DailyZodiacReadingWhereInput[]
    NOT?: DailyZodiacReadingWhereInput | DailyZodiacReadingWhereInput[]
    id?: StringFilter<"DailyZodiacReading"> | string
    zodiacSign?: EnumZodiacSignFilter<"DailyZodiacReading"> | $Enums.ZodiacSign
    date?: DateTimeFilter<"DailyZodiacReading"> | Date | string
    generalReading?: StringFilter<"DailyZodiacReading"> | string
    loveReading?: StringFilter<"DailyZodiacReading"> | string
    careerReading?: StringFilter<"DailyZodiacReading"> | string
    healthReading?: StringFilter<"DailyZodiacReading"> | string
    luckyNumber?: IntFilter<"DailyZodiacReading"> | number
    luckyColor?: StringFilter<"DailyZodiacReading"> | string
    luckyTime?: StringFilter<"DailyZodiacReading"> | string
    luckyGem?: StringFilter<"DailyZodiacReading"> | string
    advice?: StringFilter<"DailyZodiacReading"> | string
    mood?: StringFilter<"DailyZodiacReading"> | string
    compatibility?: StringFilter<"DailyZodiacReading"> | string
    language?: EnumLanguageCodeFilter<"DailyZodiacReading"> | $Enums.LanguageCode
    createdAt?: DateTimeFilter<"DailyZodiacReading"> | Date | string
    updatedAt?: DateTimeFilter<"DailyZodiacReading"> | Date | string
  }

  export type DailyZodiacReadingOrderByWithRelationInput = {
    id?: SortOrder
    zodiacSign?: SortOrder
    date?: SortOrder
    generalReading?: SortOrder
    loveReading?: SortOrder
    careerReading?: SortOrder
    healthReading?: SortOrder
    luckyNumber?: SortOrder
    luckyColor?: SortOrder
    luckyTime?: SortOrder
    luckyGem?: SortOrder
    advice?: SortOrder
    mood?: SortOrder
    compatibility?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type DailyZodiacReadingWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    zodiacSign_date_language?: DailyZodiacReadingZodiacSignDateLanguageCompoundUniqueInput
    AND?: DailyZodiacReadingWhereInput | DailyZodiacReadingWhereInput[]
    OR?: DailyZodiacReadingWhereInput[]
    NOT?: DailyZodiacReadingWhereInput | DailyZodiacReadingWhereInput[]
    zodiacSign?: EnumZodiacSignFilter<"DailyZodiacReading"> | $Enums.ZodiacSign
    date?: DateTimeFilter<"DailyZodiacReading"> | Date | string
    generalReading?: StringFilter<"DailyZodiacReading"> | string
    loveReading?: StringFilter<"DailyZodiacReading"> | string
    careerReading?: StringFilter<"DailyZodiacReading"> | string
    healthReading?: StringFilter<"DailyZodiacReading"> | string
    luckyNumber?: IntFilter<"DailyZodiacReading"> | number
    luckyColor?: StringFilter<"DailyZodiacReading"> | string
    luckyTime?: StringFilter<"DailyZodiacReading"> | string
    luckyGem?: StringFilter<"DailyZodiacReading"> | string
    advice?: StringFilter<"DailyZodiacReading"> | string
    mood?: StringFilter<"DailyZodiacReading"> | string
    compatibility?: StringFilter<"DailyZodiacReading"> | string
    language?: EnumLanguageCodeFilter<"DailyZodiacReading"> | $Enums.LanguageCode
    createdAt?: DateTimeFilter<"DailyZodiacReading"> | Date | string
    updatedAt?: DateTimeFilter<"DailyZodiacReading"> | Date | string
  }, "id" | "zodiacSign_date_language">

  export type DailyZodiacReadingOrderByWithAggregationInput = {
    id?: SortOrder
    zodiacSign?: SortOrder
    date?: SortOrder
    generalReading?: SortOrder
    loveReading?: SortOrder
    careerReading?: SortOrder
    healthReading?: SortOrder
    luckyNumber?: SortOrder
    luckyColor?: SortOrder
    luckyTime?: SortOrder
    luckyGem?: SortOrder
    advice?: SortOrder
    mood?: SortOrder
    compatibility?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: DailyZodiacReadingCountOrderByAggregateInput
    _avg?: DailyZodiacReadingAvgOrderByAggregateInput
    _max?: DailyZodiacReadingMaxOrderByAggregateInput
    _min?: DailyZodiacReadingMinOrderByAggregateInput
    _sum?: DailyZodiacReadingSumOrderByAggregateInput
  }

  export type DailyZodiacReadingScalarWhereWithAggregatesInput = {
    AND?: DailyZodiacReadingScalarWhereWithAggregatesInput | DailyZodiacReadingScalarWhereWithAggregatesInput[]
    OR?: DailyZodiacReadingScalarWhereWithAggregatesInput[]
    NOT?: DailyZodiacReadingScalarWhereWithAggregatesInput | DailyZodiacReadingScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"DailyZodiacReading"> | string
    zodiacSign?: EnumZodiacSignWithAggregatesFilter<"DailyZodiacReading"> | $Enums.ZodiacSign
    date?: DateTimeWithAggregatesFilter<"DailyZodiacReading"> | Date | string
    generalReading?: StringWithAggregatesFilter<"DailyZodiacReading"> | string
    loveReading?: StringWithAggregatesFilter<"DailyZodiacReading"> | string
    careerReading?: StringWithAggregatesFilter<"DailyZodiacReading"> | string
    healthReading?: StringWithAggregatesFilter<"DailyZodiacReading"> | string
    luckyNumber?: IntWithAggregatesFilter<"DailyZodiacReading"> | number
    luckyColor?: StringWithAggregatesFilter<"DailyZodiacReading"> | string
    luckyTime?: StringWithAggregatesFilter<"DailyZodiacReading"> | string
    luckyGem?: StringWithAggregatesFilter<"DailyZodiacReading"> | string
    advice?: StringWithAggregatesFilter<"DailyZodiacReading"> | string
    mood?: StringWithAggregatesFilter<"DailyZodiacReading"> | string
    compatibility?: StringWithAggregatesFilter<"DailyZodiacReading"> | string
    language?: EnumLanguageCodeWithAggregatesFilter<"DailyZodiacReading"> | $Enums.LanguageCode
    createdAt?: DateTimeWithAggregatesFilter<"DailyZodiacReading"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"DailyZodiacReading"> | Date | string
  }

  export type PersonalHoroscopeWhereInput = {
    AND?: PersonalHoroscopeWhereInput | PersonalHoroscopeWhereInput[]
    OR?: PersonalHoroscopeWhereInput[]
    NOT?: PersonalHoroscopeWhereInput | PersonalHoroscopeWhereInput[]
    id?: StringFilter<"PersonalHoroscope"> | string
    userId?: StringFilter<"PersonalHoroscope"> | string
    title?: StringFilter<"PersonalHoroscope"> | string
    content?: StringFilter<"PersonalHoroscope"> | string
    isActive?: BoolFilter<"PersonalHoroscope"> | boolean
    language?: EnumLanguageCodeFilter<"PersonalHoroscope"> | $Enums.LanguageCode
    createdAt?: DateTimeFilter<"PersonalHoroscope"> | Date | string
    updatedAt?: DateTimeFilter<"PersonalHoroscope"> | Date | string
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }

  export type PersonalHoroscopeOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    content?: SortOrder
    isActive?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    user?: UserOrderByWithRelationInput
  }

  export type PersonalHoroscopeWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: PersonalHoroscopeWhereInput | PersonalHoroscopeWhereInput[]
    OR?: PersonalHoroscopeWhereInput[]
    NOT?: PersonalHoroscopeWhereInput | PersonalHoroscopeWhereInput[]
    userId?: StringFilter<"PersonalHoroscope"> | string
    title?: StringFilter<"PersonalHoroscope"> | string
    content?: StringFilter<"PersonalHoroscope"> | string
    isActive?: BoolFilter<"PersonalHoroscope"> | boolean
    language?: EnumLanguageCodeFilter<"PersonalHoroscope"> | $Enums.LanguageCode
    createdAt?: DateTimeFilter<"PersonalHoroscope"> | Date | string
    updatedAt?: DateTimeFilter<"PersonalHoroscope"> | Date | string
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }, "id">

  export type PersonalHoroscopeOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    content?: SortOrder
    isActive?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: PersonalHoroscopeCountOrderByAggregateInput
    _max?: PersonalHoroscopeMaxOrderByAggregateInput
    _min?: PersonalHoroscopeMinOrderByAggregateInput
  }

  export type PersonalHoroscopeScalarWhereWithAggregatesInput = {
    AND?: PersonalHoroscopeScalarWhereWithAggregatesInput | PersonalHoroscopeScalarWhereWithAggregatesInput[]
    OR?: PersonalHoroscopeScalarWhereWithAggregatesInput[]
    NOT?: PersonalHoroscopeScalarWhereWithAggregatesInput | PersonalHoroscopeScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"PersonalHoroscope"> | string
    userId?: StringWithAggregatesFilter<"PersonalHoroscope"> | string
    title?: StringWithAggregatesFilter<"PersonalHoroscope"> | string
    content?: StringWithAggregatesFilter<"PersonalHoroscope"> | string
    isActive?: BoolWithAggregatesFilter<"PersonalHoroscope"> | boolean
    language?: EnumLanguageCodeWithAggregatesFilter<"PersonalHoroscope"> | $Enums.LanguageCode
    createdAt?: DateTimeWithAggregatesFilter<"PersonalHoroscope"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"PersonalHoroscope"> | Date | string
  }

  export type TranslationCacheWhereInput = {
    AND?: TranslationCacheWhereInput | TranslationCacheWhereInput[]
    OR?: TranslationCacheWhereInput[]
    NOT?: TranslationCacheWhereInput | TranslationCacheWhereInput[]
    id?: StringFilter<"TranslationCache"> | string
    originalText?: StringFilter<"TranslationCache"> | string
    translatedText?: StringFilter<"TranslationCache"> | string
    sourceLanguage?: EnumLanguageCodeFilter<"TranslationCache"> | $Enums.LanguageCode
    targetLanguage?: EnumLanguageCodeFilter<"TranslationCache"> | $Enums.LanguageCode
    createdAt?: DateTimeFilter<"TranslationCache"> | Date | string
  }

  export type TranslationCacheOrderByWithRelationInput = {
    id?: SortOrder
    originalText?: SortOrder
    translatedText?: SortOrder
    sourceLanguage?: SortOrder
    targetLanguage?: SortOrder
    createdAt?: SortOrder
  }

  export type TranslationCacheWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    originalText_sourceLanguage_targetLanguage?: TranslationCacheOriginalTextSourceLanguageTargetLanguageCompoundUniqueInput
    AND?: TranslationCacheWhereInput | TranslationCacheWhereInput[]
    OR?: TranslationCacheWhereInput[]
    NOT?: TranslationCacheWhereInput | TranslationCacheWhereInput[]
    originalText?: StringFilter<"TranslationCache"> | string
    translatedText?: StringFilter<"TranslationCache"> | string
    sourceLanguage?: EnumLanguageCodeFilter<"TranslationCache"> | $Enums.LanguageCode
    targetLanguage?: EnumLanguageCodeFilter<"TranslationCache"> | $Enums.LanguageCode
    createdAt?: DateTimeFilter<"TranslationCache"> | Date | string
  }, "id" | "originalText_sourceLanguage_targetLanguage">

  export type TranslationCacheOrderByWithAggregationInput = {
    id?: SortOrder
    originalText?: SortOrder
    translatedText?: SortOrder
    sourceLanguage?: SortOrder
    targetLanguage?: SortOrder
    createdAt?: SortOrder
    _count?: TranslationCacheCountOrderByAggregateInput
    _max?: TranslationCacheMaxOrderByAggregateInput
    _min?: TranslationCacheMinOrderByAggregateInput
  }

  export type TranslationCacheScalarWhereWithAggregatesInput = {
    AND?: TranslationCacheScalarWhereWithAggregatesInput | TranslationCacheScalarWhereWithAggregatesInput[]
    OR?: TranslationCacheScalarWhereWithAggregatesInput[]
    NOT?: TranslationCacheScalarWhereWithAggregatesInput | TranslationCacheScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"TranslationCache"> | string
    originalText?: StringWithAggregatesFilter<"TranslationCache"> | string
    translatedText?: StringWithAggregatesFilter<"TranslationCache"> | string
    sourceLanguage?: EnumLanguageCodeWithAggregatesFilter<"TranslationCache"> | $Enums.LanguageCode
    targetLanguage?: EnumLanguageCodeWithAggregatesFilter<"TranslationCache"> | $Enums.LanguageCode
    createdAt?: DateTimeWithAggregatesFilter<"TranslationCache"> | Date | string
  }

  export type AdminCreateInput = {
    id?: string
    email: string
    password: string
    name: string
    role?: $Enums.UserRole
    isActive?: boolean
    lastLogin?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AdminUncheckedCreateInput = {
    id?: string
    email: string
    password: string
    name: string
    role?: $Enums.UserRole
    isActive?: boolean
    lastLogin?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AdminUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    isActive?: BoolFieldUpdateOperationsInput | boolean
    lastLogin?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AdminUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    isActive?: BoolFieldUpdateOperationsInput | boolean
    lastLogin?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AdminCreateManyInput = {
    id?: string
    email: string
    password: string
    name: string
    role?: $Enums.UserRole
    isActive?: boolean
    lastLogin?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AdminUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    isActive?: BoolFieldUpdateOperationsInput | boolean
    lastLogin?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AdminUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    isActive?: BoolFieldUpdateOperationsInput | boolean
    lastLogin?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserCreateInput = {
    id?: string
    email?: string | null
    name: string
    phoneNumber?: string | null
    address?: string | null
    zodiacSign: $Enums.ZodiacSign
    birthDate: Date | string
    birthTime?: string | null
    qrToken?: string
    languagePreference?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
    qrCodeMappings?: QrCodeMappingCreateNestedManyWithoutUserInput
    personalHoroscopes?: PersonalHoroscopeCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateInput = {
    id?: string
    email?: string | null
    name: string
    phoneNumber?: string | null
    address?: string | null
    zodiacSign: $Enums.ZodiacSign
    birthDate: Date | string
    birthTime?: string | null
    qrToken?: string
    languagePreference?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
    qrCodeMappings?: QrCodeMappingUncheckedCreateNestedManyWithoutUserInput
    personalHoroscopes?: PersonalHoroscopeUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: StringFieldUpdateOperationsInput | string
    phoneNumber?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    birthDate?: DateTimeFieldUpdateOperationsInput | Date | string
    birthTime?: NullableStringFieldUpdateOperationsInput | string | null
    qrToken?: StringFieldUpdateOperationsInput | string
    languagePreference?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    qrCodeMappings?: QrCodeMappingUpdateManyWithoutUserNestedInput
    personalHoroscopes?: PersonalHoroscopeUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: StringFieldUpdateOperationsInput | string
    phoneNumber?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    birthDate?: DateTimeFieldUpdateOperationsInput | Date | string
    birthTime?: NullableStringFieldUpdateOperationsInput | string | null
    qrToken?: StringFieldUpdateOperationsInput | string
    languagePreference?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    qrCodeMappings?: QrCodeMappingUncheckedUpdateManyWithoutUserNestedInput
    personalHoroscopes?: PersonalHoroscopeUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateManyInput = {
    id?: string
    email?: string | null
    name: string
    phoneNumber?: string | null
    address?: string | null
    zodiacSign: $Enums.ZodiacSign
    birthDate: Date | string
    birthTime?: string | null
    qrToken?: string
    languagePreference?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: StringFieldUpdateOperationsInput | string
    phoneNumber?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    birthDate?: DateTimeFieldUpdateOperationsInput | Date | string
    birthTime?: NullableStringFieldUpdateOperationsInput | string | null
    qrToken?: StringFieldUpdateOperationsInput | string
    languagePreference?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: StringFieldUpdateOperationsInput | string
    phoneNumber?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    birthDate?: DateTimeFieldUpdateOperationsInput | Date | string
    birthTime?: NullableStringFieldUpdateOperationsInput | string | null
    qrToken?: StringFieldUpdateOperationsInput | string
    languagePreference?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type QrCodeMappingCreateInput = {
    id?: string
    qrToken: string
    createdAt?: Date | string
    lastScanned?: Date | string | null
    scanCount?: number
    user: UserCreateNestedOneWithoutQrCodeMappingsInput
  }

  export type QrCodeMappingUncheckedCreateInput = {
    id?: string
    qrToken: string
    userId: string
    createdAt?: Date | string
    lastScanned?: Date | string | null
    scanCount?: number
  }

  export type QrCodeMappingUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    qrToken?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    lastScanned?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scanCount?: IntFieldUpdateOperationsInput | number
    user?: UserUpdateOneRequiredWithoutQrCodeMappingsNestedInput
  }

  export type QrCodeMappingUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    qrToken?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    lastScanned?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scanCount?: IntFieldUpdateOperationsInput | number
  }

  export type QrCodeMappingCreateManyInput = {
    id?: string
    qrToken: string
    userId: string
    createdAt?: Date | string
    lastScanned?: Date | string | null
    scanCount?: number
  }

  export type QrCodeMappingUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    qrToken?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    lastScanned?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scanCount?: IntFieldUpdateOperationsInput | number
  }

  export type QrCodeMappingUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    qrToken?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    lastScanned?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scanCount?: IntFieldUpdateOperationsInput | number
  }

  export type HoroscopeCreateInput = {
    id?: string
    zodiacSign: $Enums.ZodiacSign
    type: $Enums.HoroscopeType
    content: string
    date: Date | string
    language?: $Enums.LanguageCode
    createdAt?: Date | string
  }

  export type HoroscopeUncheckedCreateInput = {
    id?: string
    zodiacSign: $Enums.ZodiacSign
    type: $Enums.HoroscopeType
    content: string
    date: Date | string
    language?: $Enums.LanguageCode
    createdAt?: Date | string
  }

  export type HoroscopeUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    type?: EnumHoroscopeTypeFieldUpdateOperationsInput | $Enums.HoroscopeType
    content?: StringFieldUpdateOperationsInput | string
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type HoroscopeUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    type?: EnumHoroscopeTypeFieldUpdateOperationsInput | $Enums.HoroscopeType
    content?: StringFieldUpdateOperationsInput | string
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type HoroscopeCreateManyInput = {
    id?: string
    zodiacSign: $Enums.ZodiacSign
    type: $Enums.HoroscopeType
    content: string
    date: Date | string
    language?: $Enums.LanguageCode
    createdAt?: Date | string
  }

  export type HoroscopeUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    type?: EnumHoroscopeTypeFieldUpdateOperationsInput | $Enums.HoroscopeType
    content?: StringFieldUpdateOperationsInput | string
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type HoroscopeUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    type?: EnumHoroscopeTypeFieldUpdateOperationsInput | $Enums.HoroscopeType
    content?: StringFieldUpdateOperationsInput | string
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DailyZodiacReadingCreateInput = {
    id?: string
    zodiacSign: $Enums.ZodiacSign
    date: Date | string
    generalReading: string
    loveReading: string
    careerReading: string
    healthReading: string
    luckyNumber: number
    luckyColor: string
    luckyTime: string
    luckyGem: string
    advice: string
    mood: string
    compatibility: string
    language?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type DailyZodiacReadingUncheckedCreateInput = {
    id?: string
    zodiacSign: $Enums.ZodiacSign
    date: Date | string
    generalReading: string
    loveReading: string
    careerReading: string
    healthReading: string
    luckyNumber: number
    luckyColor: string
    luckyTime: string
    luckyGem: string
    advice: string
    mood: string
    compatibility: string
    language?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type DailyZodiacReadingUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    generalReading?: StringFieldUpdateOperationsInput | string
    loveReading?: StringFieldUpdateOperationsInput | string
    careerReading?: StringFieldUpdateOperationsInput | string
    healthReading?: StringFieldUpdateOperationsInput | string
    luckyNumber?: IntFieldUpdateOperationsInput | number
    luckyColor?: StringFieldUpdateOperationsInput | string
    luckyTime?: StringFieldUpdateOperationsInput | string
    luckyGem?: StringFieldUpdateOperationsInput | string
    advice?: StringFieldUpdateOperationsInput | string
    mood?: StringFieldUpdateOperationsInput | string
    compatibility?: StringFieldUpdateOperationsInput | string
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DailyZodiacReadingUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    generalReading?: StringFieldUpdateOperationsInput | string
    loveReading?: StringFieldUpdateOperationsInput | string
    careerReading?: StringFieldUpdateOperationsInput | string
    healthReading?: StringFieldUpdateOperationsInput | string
    luckyNumber?: IntFieldUpdateOperationsInput | number
    luckyColor?: StringFieldUpdateOperationsInput | string
    luckyTime?: StringFieldUpdateOperationsInput | string
    luckyGem?: StringFieldUpdateOperationsInput | string
    advice?: StringFieldUpdateOperationsInput | string
    mood?: StringFieldUpdateOperationsInput | string
    compatibility?: StringFieldUpdateOperationsInput | string
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DailyZodiacReadingCreateManyInput = {
    id?: string
    zodiacSign: $Enums.ZodiacSign
    date: Date | string
    generalReading: string
    loveReading: string
    careerReading: string
    healthReading: string
    luckyNumber: number
    luckyColor: string
    luckyTime: string
    luckyGem: string
    advice: string
    mood: string
    compatibility: string
    language?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type DailyZodiacReadingUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    generalReading?: StringFieldUpdateOperationsInput | string
    loveReading?: StringFieldUpdateOperationsInput | string
    careerReading?: StringFieldUpdateOperationsInput | string
    healthReading?: StringFieldUpdateOperationsInput | string
    luckyNumber?: IntFieldUpdateOperationsInput | number
    luckyColor?: StringFieldUpdateOperationsInput | string
    luckyTime?: StringFieldUpdateOperationsInput | string
    luckyGem?: StringFieldUpdateOperationsInput | string
    advice?: StringFieldUpdateOperationsInput | string
    mood?: StringFieldUpdateOperationsInput | string
    compatibility?: StringFieldUpdateOperationsInput | string
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type DailyZodiacReadingUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    date?: DateTimeFieldUpdateOperationsInput | Date | string
    generalReading?: StringFieldUpdateOperationsInput | string
    loveReading?: StringFieldUpdateOperationsInput | string
    careerReading?: StringFieldUpdateOperationsInput | string
    healthReading?: StringFieldUpdateOperationsInput | string
    luckyNumber?: IntFieldUpdateOperationsInput | number
    luckyColor?: StringFieldUpdateOperationsInput | string
    luckyTime?: StringFieldUpdateOperationsInput | string
    luckyGem?: StringFieldUpdateOperationsInput | string
    advice?: StringFieldUpdateOperationsInput | string
    mood?: StringFieldUpdateOperationsInput | string
    compatibility?: StringFieldUpdateOperationsInput | string
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PersonalHoroscopeCreateInput = {
    id?: string
    title: string
    content: string
    isActive?: boolean
    language?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutPersonalHoroscopesInput
  }

  export type PersonalHoroscopeUncheckedCreateInput = {
    id?: string
    userId: string
    title: string
    content: string
    isActive?: boolean
    language?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type PersonalHoroscopeUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutPersonalHoroscopesNestedInput
  }

  export type PersonalHoroscopeUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PersonalHoroscopeCreateManyInput = {
    id?: string
    userId: string
    title: string
    content: string
    isActive?: boolean
    language?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type PersonalHoroscopeUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PersonalHoroscopeUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TranslationCacheCreateInput = {
    id?: string
    originalText: string
    translatedText: string
    sourceLanguage: $Enums.LanguageCode
    targetLanguage: $Enums.LanguageCode
    createdAt?: Date | string
  }

  export type TranslationCacheUncheckedCreateInput = {
    id?: string
    originalText: string
    translatedText: string
    sourceLanguage: $Enums.LanguageCode
    targetLanguage: $Enums.LanguageCode
    createdAt?: Date | string
  }

  export type TranslationCacheUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    originalText?: StringFieldUpdateOperationsInput | string
    translatedText?: StringFieldUpdateOperationsInput | string
    sourceLanguage?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    targetLanguage?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TranslationCacheUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    originalText?: StringFieldUpdateOperationsInput | string
    translatedText?: StringFieldUpdateOperationsInput | string
    sourceLanguage?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    targetLanguage?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TranslationCacheCreateManyInput = {
    id?: string
    originalText: string
    translatedText: string
    sourceLanguage: $Enums.LanguageCode
    targetLanguage: $Enums.LanguageCode
    createdAt?: Date | string
  }

  export type TranslationCacheUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    originalText?: StringFieldUpdateOperationsInput | string
    translatedText?: StringFieldUpdateOperationsInput | string
    sourceLanguage?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    targetLanguage?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TranslationCacheUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    originalText?: StringFieldUpdateOperationsInput | string
    translatedText?: StringFieldUpdateOperationsInput | string
    sourceLanguage?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    targetLanguage?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type EnumUserRoleFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    notIn?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    not?: NestedEnumUserRoleFilter<$PrismaModel> | $Enums.UserRole
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type AdminCountOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    password?: SortOrder
    name?: SortOrder
    role?: SortOrder
    isActive?: SortOrder
    lastLogin?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AdminMaxOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    password?: SortOrder
    name?: SortOrder
    role?: SortOrder
    isActive?: SortOrder
    lastLogin?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AdminMinOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    password?: SortOrder
    name?: SortOrder
    role?: SortOrder
    isActive?: SortOrder
    lastLogin?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type EnumUserRoleWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    notIn?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    not?: NestedEnumUserRoleWithAggregatesFilter<$PrismaModel> | $Enums.UserRole
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumUserRoleFilter<$PrismaModel>
    _max?: NestedEnumUserRoleFilter<$PrismaModel>
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type EnumZodiacSignFilter<$PrismaModel = never> = {
    equals?: $Enums.ZodiacSign | EnumZodiacSignFieldRefInput<$PrismaModel>
    in?: $Enums.ZodiacSign[] | ListEnumZodiacSignFieldRefInput<$PrismaModel>
    notIn?: $Enums.ZodiacSign[] | ListEnumZodiacSignFieldRefInput<$PrismaModel>
    not?: NestedEnumZodiacSignFilter<$PrismaModel> | $Enums.ZodiacSign
  }

  export type EnumLanguageCodeFilter<$PrismaModel = never> = {
    equals?: $Enums.LanguageCode | EnumLanguageCodeFieldRefInput<$PrismaModel>
    in?: $Enums.LanguageCode[] | ListEnumLanguageCodeFieldRefInput<$PrismaModel>
    notIn?: $Enums.LanguageCode[] | ListEnumLanguageCodeFieldRefInput<$PrismaModel>
    not?: NestedEnumLanguageCodeFilter<$PrismaModel> | $Enums.LanguageCode
  }

  export type QrCodeMappingListRelationFilter = {
    every?: QrCodeMappingWhereInput
    some?: QrCodeMappingWhereInput
    none?: QrCodeMappingWhereInput
  }

  export type PersonalHoroscopeListRelationFilter = {
    every?: PersonalHoroscopeWhereInput
    some?: PersonalHoroscopeWhereInput
    none?: PersonalHoroscopeWhereInput
  }

  export type QrCodeMappingOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type PersonalHoroscopeOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserCountOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    name?: SortOrder
    phoneNumber?: SortOrder
    address?: SortOrder
    zodiacSign?: SortOrder
    birthDate?: SortOrder
    birthTime?: SortOrder
    qrToken?: SortOrder
    languagePreference?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMaxOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    name?: SortOrder
    phoneNumber?: SortOrder
    address?: SortOrder
    zodiacSign?: SortOrder
    birthDate?: SortOrder
    birthTime?: SortOrder
    qrToken?: SortOrder
    languagePreference?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMinOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    name?: SortOrder
    phoneNumber?: SortOrder
    address?: SortOrder
    zodiacSign?: SortOrder
    birthDate?: SortOrder
    birthTime?: SortOrder
    qrToken?: SortOrder
    languagePreference?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type EnumZodiacSignWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.ZodiacSign | EnumZodiacSignFieldRefInput<$PrismaModel>
    in?: $Enums.ZodiacSign[] | ListEnumZodiacSignFieldRefInput<$PrismaModel>
    notIn?: $Enums.ZodiacSign[] | ListEnumZodiacSignFieldRefInput<$PrismaModel>
    not?: NestedEnumZodiacSignWithAggregatesFilter<$PrismaModel> | $Enums.ZodiacSign
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumZodiacSignFilter<$PrismaModel>
    _max?: NestedEnumZodiacSignFilter<$PrismaModel>
  }

  export type EnumLanguageCodeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.LanguageCode | EnumLanguageCodeFieldRefInput<$PrismaModel>
    in?: $Enums.LanguageCode[] | ListEnumLanguageCodeFieldRefInput<$PrismaModel>
    notIn?: $Enums.LanguageCode[] | ListEnumLanguageCodeFieldRefInput<$PrismaModel>
    not?: NestedEnumLanguageCodeWithAggregatesFilter<$PrismaModel> | $Enums.LanguageCode
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumLanguageCodeFilter<$PrismaModel>
    _max?: NestedEnumLanguageCodeFilter<$PrismaModel>
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type UserScalarRelationFilter = {
    is?: UserWhereInput
    isNot?: UserWhereInput
  }

  export type QrCodeMappingCountOrderByAggregateInput = {
    id?: SortOrder
    qrToken?: SortOrder
    userId?: SortOrder
    createdAt?: SortOrder
    lastScanned?: SortOrder
    scanCount?: SortOrder
  }

  export type QrCodeMappingAvgOrderByAggregateInput = {
    scanCount?: SortOrder
  }

  export type QrCodeMappingMaxOrderByAggregateInput = {
    id?: SortOrder
    qrToken?: SortOrder
    userId?: SortOrder
    createdAt?: SortOrder
    lastScanned?: SortOrder
    scanCount?: SortOrder
  }

  export type QrCodeMappingMinOrderByAggregateInput = {
    id?: SortOrder
    qrToken?: SortOrder
    userId?: SortOrder
    createdAt?: SortOrder
    lastScanned?: SortOrder
    scanCount?: SortOrder
  }

  export type QrCodeMappingSumOrderByAggregateInput = {
    scanCount?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type EnumHoroscopeTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.HoroscopeType | EnumHoroscopeTypeFieldRefInput<$PrismaModel>
    in?: $Enums.HoroscopeType[] | ListEnumHoroscopeTypeFieldRefInput<$PrismaModel>
    notIn?: $Enums.HoroscopeType[] | ListEnumHoroscopeTypeFieldRefInput<$PrismaModel>
    not?: NestedEnumHoroscopeTypeFilter<$PrismaModel> | $Enums.HoroscopeType
  }

  export type HoroscopeZodiacSignTypeDateLanguageCompoundUniqueInput = {
    zodiacSign: $Enums.ZodiacSign
    type: $Enums.HoroscopeType
    date: Date | string
    language: $Enums.LanguageCode
  }

  export type HoroscopeCountOrderByAggregateInput = {
    id?: SortOrder
    zodiacSign?: SortOrder
    type?: SortOrder
    content?: SortOrder
    date?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
  }

  export type HoroscopeMaxOrderByAggregateInput = {
    id?: SortOrder
    zodiacSign?: SortOrder
    type?: SortOrder
    content?: SortOrder
    date?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
  }

  export type HoroscopeMinOrderByAggregateInput = {
    id?: SortOrder
    zodiacSign?: SortOrder
    type?: SortOrder
    content?: SortOrder
    date?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
  }

  export type EnumHoroscopeTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.HoroscopeType | EnumHoroscopeTypeFieldRefInput<$PrismaModel>
    in?: $Enums.HoroscopeType[] | ListEnumHoroscopeTypeFieldRefInput<$PrismaModel>
    notIn?: $Enums.HoroscopeType[] | ListEnumHoroscopeTypeFieldRefInput<$PrismaModel>
    not?: NestedEnumHoroscopeTypeWithAggregatesFilter<$PrismaModel> | $Enums.HoroscopeType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumHoroscopeTypeFilter<$PrismaModel>
    _max?: NestedEnumHoroscopeTypeFilter<$PrismaModel>
  }

  export type DailyZodiacReadingZodiacSignDateLanguageCompoundUniqueInput = {
    zodiacSign: $Enums.ZodiacSign
    date: Date | string
    language: $Enums.LanguageCode
  }

  export type DailyZodiacReadingCountOrderByAggregateInput = {
    id?: SortOrder
    zodiacSign?: SortOrder
    date?: SortOrder
    generalReading?: SortOrder
    loveReading?: SortOrder
    careerReading?: SortOrder
    healthReading?: SortOrder
    luckyNumber?: SortOrder
    luckyColor?: SortOrder
    luckyTime?: SortOrder
    luckyGem?: SortOrder
    advice?: SortOrder
    mood?: SortOrder
    compatibility?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type DailyZodiacReadingAvgOrderByAggregateInput = {
    luckyNumber?: SortOrder
  }

  export type DailyZodiacReadingMaxOrderByAggregateInput = {
    id?: SortOrder
    zodiacSign?: SortOrder
    date?: SortOrder
    generalReading?: SortOrder
    loveReading?: SortOrder
    careerReading?: SortOrder
    healthReading?: SortOrder
    luckyNumber?: SortOrder
    luckyColor?: SortOrder
    luckyTime?: SortOrder
    luckyGem?: SortOrder
    advice?: SortOrder
    mood?: SortOrder
    compatibility?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type DailyZodiacReadingMinOrderByAggregateInput = {
    id?: SortOrder
    zodiacSign?: SortOrder
    date?: SortOrder
    generalReading?: SortOrder
    loveReading?: SortOrder
    careerReading?: SortOrder
    healthReading?: SortOrder
    luckyNumber?: SortOrder
    luckyColor?: SortOrder
    luckyTime?: SortOrder
    luckyGem?: SortOrder
    advice?: SortOrder
    mood?: SortOrder
    compatibility?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type DailyZodiacReadingSumOrderByAggregateInput = {
    luckyNumber?: SortOrder
  }

  export type PersonalHoroscopeCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    content?: SortOrder
    isActive?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type PersonalHoroscopeMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    content?: SortOrder
    isActive?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type PersonalHoroscopeMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    title?: SortOrder
    content?: SortOrder
    isActive?: SortOrder
    language?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type TranslationCacheOriginalTextSourceLanguageTargetLanguageCompoundUniqueInput = {
    originalText: string
    sourceLanguage: $Enums.LanguageCode
    targetLanguage: $Enums.LanguageCode
  }

  export type TranslationCacheCountOrderByAggregateInput = {
    id?: SortOrder
    originalText?: SortOrder
    translatedText?: SortOrder
    sourceLanguage?: SortOrder
    targetLanguage?: SortOrder
    createdAt?: SortOrder
  }

  export type TranslationCacheMaxOrderByAggregateInput = {
    id?: SortOrder
    originalText?: SortOrder
    translatedText?: SortOrder
    sourceLanguage?: SortOrder
    targetLanguage?: SortOrder
    createdAt?: SortOrder
  }

  export type TranslationCacheMinOrderByAggregateInput = {
    id?: SortOrder
    originalText?: SortOrder
    translatedText?: SortOrder
    sourceLanguage?: SortOrder
    targetLanguage?: SortOrder
    createdAt?: SortOrder
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type EnumUserRoleFieldUpdateOperationsInput = {
    set?: $Enums.UserRole
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type QrCodeMappingCreateNestedManyWithoutUserInput = {
    create?: XOR<QrCodeMappingCreateWithoutUserInput, QrCodeMappingUncheckedCreateWithoutUserInput> | QrCodeMappingCreateWithoutUserInput[] | QrCodeMappingUncheckedCreateWithoutUserInput[]
    connectOrCreate?: QrCodeMappingCreateOrConnectWithoutUserInput | QrCodeMappingCreateOrConnectWithoutUserInput[]
    createMany?: QrCodeMappingCreateManyUserInputEnvelope
    connect?: QrCodeMappingWhereUniqueInput | QrCodeMappingWhereUniqueInput[]
  }

  export type PersonalHoroscopeCreateNestedManyWithoutUserInput = {
    create?: XOR<PersonalHoroscopeCreateWithoutUserInput, PersonalHoroscopeUncheckedCreateWithoutUserInput> | PersonalHoroscopeCreateWithoutUserInput[] | PersonalHoroscopeUncheckedCreateWithoutUserInput[]
    connectOrCreate?: PersonalHoroscopeCreateOrConnectWithoutUserInput | PersonalHoroscopeCreateOrConnectWithoutUserInput[]
    createMany?: PersonalHoroscopeCreateManyUserInputEnvelope
    connect?: PersonalHoroscopeWhereUniqueInput | PersonalHoroscopeWhereUniqueInput[]
  }

  export type QrCodeMappingUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<QrCodeMappingCreateWithoutUserInput, QrCodeMappingUncheckedCreateWithoutUserInput> | QrCodeMappingCreateWithoutUserInput[] | QrCodeMappingUncheckedCreateWithoutUserInput[]
    connectOrCreate?: QrCodeMappingCreateOrConnectWithoutUserInput | QrCodeMappingCreateOrConnectWithoutUserInput[]
    createMany?: QrCodeMappingCreateManyUserInputEnvelope
    connect?: QrCodeMappingWhereUniqueInput | QrCodeMappingWhereUniqueInput[]
  }

  export type PersonalHoroscopeUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<PersonalHoroscopeCreateWithoutUserInput, PersonalHoroscopeUncheckedCreateWithoutUserInput> | PersonalHoroscopeCreateWithoutUserInput[] | PersonalHoroscopeUncheckedCreateWithoutUserInput[]
    connectOrCreate?: PersonalHoroscopeCreateOrConnectWithoutUserInput | PersonalHoroscopeCreateOrConnectWithoutUserInput[]
    createMany?: PersonalHoroscopeCreateManyUserInputEnvelope
    connect?: PersonalHoroscopeWhereUniqueInput | PersonalHoroscopeWhereUniqueInput[]
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type EnumZodiacSignFieldUpdateOperationsInput = {
    set?: $Enums.ZodiacSign
  }

  export type EnumLanguageCodeFieldUpdateOperationsInput = {
    set?: $Enums.LanguageCode
  }

  export type QrCodeMappingUpdateManyWithoutUserNestedInput = {
    create?: XOR<QrCodeMappingCreateWithoutUserInput, QrCodeMappingUncheckedCreateWithoutUserInput> | QrCodeMappingCreateWithoutUserInput[] | QrCodeMappingUncheckedCreateWithoutUserInput[]
    connectOrCreate?: QrCodeMappingCreateOrConnectWithoutUserInput | QrCodeMappingCreateOrConnectWithoutUserInput[]
    upsert?: QrCodeMappingUpsertWithWhereUniqueWithoutUserInput | QrCodeMappingUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: QrCodeMappingCreateManyUserInputEnvelope
    set?: QrCodeMappingWhereUniqueInput | QrCodeMappingWhereUniqueInput[]
    disconnect?: QrCodeMappingWhereUniqueInput | QrCodeMappingWhereUniqueInput[]
    delete?: QrCodeMappingWhereUniqueInput | QrCodeMappingWhereUniqueInput[]
    connect?: QrCodeMappingWhereUniqueInput | QrCodeMappingWhereUniqueInput[]
    update?: QrCodeMappingUpdateWithWhereUniqueWithoutUserInput | QrCodeMappingUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: QrCodeMappingUpdateManyWithWhereWithoutUserInput | QrCodeMappingUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: QrCodeMappingScalarWhereInput | QrCodeMappingScalarWhereInput[]
  }

  export type PersonalHoroscopeUpdateManyWithoutUserNestedInput = {
    create?: XOR<PersonalHoroscopeCreateWithoutUserInput, PersonalHoroscopeUncheckedCreateWithoutUserInput> | PersonalHoroscopeCreateWithoutUserInput[] | PersonalHoroscopeUncheckedCreateWithoutUserInput[]
    connectOrCreate?: PersonalHoroscopeCreateOrConnectWithoutUserInput | PersonalHoroscopeCreateOrConnectWithoutUserInput[]
    upsert?: PersonalHoroscopeUpsertWithWhereUniqueWithoutUserInput | PersonalHoroscopeUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: PersonalHoroscopeCreateManyUserInputEnvelope
    set?: PersonalHoroscopeWhereUniqueInput | PersonalHoroscopeWhereUniqueInput[]
    disconnect?: PersonalHoroscopeWhereUniqueInput | PersonalHoroscopeWhereUniqueInput[]
    delete?: PersonalHoroscopeWhereUniqueInput | PersonalHoroscopeWhereUniqueInput[]
    connect?: PersonalHoroscopeWhereUniqueInput | PersonalHoroscopeWhereUniqueInput[]
    update?: PersonalHoroscopeUpdateWithWhereUniqueWithoutUserInput | PersonalHoroscopeUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: PersonalHoroscopeUpdateManyWithWhereWithoutUserInput | PersonalHoroscopeUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: PersonalHoroscopeScalarWhereInput | PersonalHoroscopeScalarWhereInput[]
  }

  export type QrCodeMappingUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<QrCodeMappingCreateWithoutUserInput, QrCodeMappingUncheckedCreateWithoutUserInput> | QrCodeMappingCreateWithoutUserInput[] | QrCodeMappingUncheckedCreateWithoutUserInput[]
    connectOrCreate?: QrCodeMappingCreateOrConnectWithoutUserInput | QrCodeMappingCreateOrConnectWithoutUserInput[]
    upsert?: QrCodeMappingUpsertWithWhereUniqueWithoutUserInput | QrCodeMappingUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: QrCodeMappingCreateManyUserInputEnvelope
    set?: QrCodeMappingWhereUniqueInput | QrCodeMappingWhereUniqueInput[]
    disconnect?: QrCodeMappingWhereUniqueInput | QrCodeMappingWhereUniqueInput[]
    delete?: QrCodeMappingWhereUniqueInput | QrCodeMappingWhereUniqueInput[]
    connect?: QrCodeMappingWhereUniqueInput | QrCodeMappingWhereUniqueInput[]
    update?: QrCodeMappingUpdateWithWhereUniqueWithoutUserInput | QrCodeMappingUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: QrCodeMappingUpdateManyWithWhereWithoutUserInput | QrCodeMappingUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: QrCodeMappingScalarWhereInput | QrCodeMappingScalarWhereInput[]
  }

  export type PersonalHoroscopeUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<PersonalHoroscopeCreateWithoutUserInput, PersonalHoroscopeUncheckedCreateWithoutUserInput> | PersonalHoroscopeCreateWithoutUserInput[] | PersonalHoroscopeUncheckedCreateWithoutUserInput[]
    connectOrCreate?: PersonalHoroscopeCreateOrConnectWithoutUserInput | PersonalHoroscopeCreateOrConnectWithoutUserInput[]
    upsert?: PersonalHoroscopeUpsertWithWhereUniqueWithoutUserInput | PersonalHoroscopeUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: PersonalHoroscopeCreateManyUserInputEnvelope
    set?: PersonalHoroscopeWhereUniqueInput | PersonalHoroscopeWhereUniqueInput[]
    disconnect?: PersonalHoroscopeWhereUniqueInput | PersonalHoroscopeWhereUniqueInput[]
    delete?: PersonalHoroscopeWhereUniqueInput | PersonalHoroscopeWhereUniqueInput[]
    connect?: PersonalHoroscopeWhereUniqueInput | PersonalHoroscopeWhereUniqueInput[]
    update?: PersonalHoroscopeUpdateWithWhereUniqueWithoutUserInput | PersonalHoroscopeUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: PersonalHoroscopeUpdateManyWithWhereWithoutUserInput | PersonalHoroscopeUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: PersonalHoroscopeScalarWhereInput | PersonalHoroscopeScalarWhereInput[]
  }

  export type UserCreateNestedOneWithoutQrCodeMappingsInput = {
    create?: XOR<UserCreateWithoutQrCodeMappingsInput, UserUncheckedCreateWithoutQrCodeMappingsInput>
    connectOrCreate?: UserCreateOrConnectWithoutQrCodeMappingsInput
    connect?: UserWhereUniqueInput
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type UserUpdateOneRequiredWithoutQrCodeMappingsNestedInput = {
    create?: XOR<UserCreateWithoutQrCodeMappingsInput, UserUncheckedCreateWithoutQrCodeMappingsInput>
    connectOrCreate?: UserCreateOrConnectWithoutQrCodeMappingsInput
    upsert?: UserUpsertWithoutQrCodeMappingsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutQrCodeMappingsInput, UserUpdateWithoutQrCodeMappingsInput>, UserUncheckedUpdateWithoutQrCodeMappingsInput>
  }

  export type EnumHoroscopeTypeFieldUpdateOperationsInput = {
    set?: $Enums.HoroscopeType
  }

  export type UserCreateNestedOneWithoutPersonalHoroscopesInput = {
    create?: XOR<UserCreateWithoutPersonalHoroscopesInput, UserUncheckedCreateWithoutPersonalHoroscopesInput>
    connectOrCreate?: UserCreateOrConnectWithoutPersonalHoroscopesInput
    connect?: UserWhereUniqueInput
  }

  export type UserUpdateOneRequiredWithoutPersonalHoroscopesNestedInput = {
    create?: XOR<UserCreateWithoutPersonalHoroscopesInput, UserUncheckedCreateWithoutPersonalHoroscopesInput>
    connectOrCreate?: UserCreateOrConnectWithoutPersonalHoroscopesInput
    upsert?: UserUpsertWithoutPersonalHoroscopesInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutPersonalHoroscopesInput, UserUpdateWithoutPersonalHoroscopesInput>, UserUncheckedUpdateWithoutPersonalHoroscopesInput>
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedEnumUserRoleFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    notIn?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    not?: NestedEnumUserRoleFilter<$PrismaModel> | $Enums.UserRole
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedEnumUserRoleWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    notIn?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    not?: NestedEnumUserRoleWithAggregatesFilter<$PrismaModel> | $Enums.UserRole
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumUserRoleFilter<$PrismaModel>
    _max?: NestedEnumUserRoleFilter<$PrismaModel>
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedEnumZodiacSignFilter<$PrismaModel = never> = {
    equals?: $Enums.ZodiacSign | EnumZodiacSignFieldRefInput<$PrismaModel>
    in?: $Enums.ZodiacSign[] | ListEnumZodiacSignFieldRefInput<$PrismaModel>
    notIn?: $Enums.ZodiacSign[] | ListEnumZodiacSignFieldRefInput<$PrismaModel>
    not?: NestedEnumZodiacSignFilter<$PrismaModel> | $Enums.ZodiacSign
  }

  export type NestedEnumLanguageCodeFilter<$PrismaModel = never> = {
    equals?: $Enums.LanguageCode | EnumLanguageCodeFieldRefInput<$PrismaModel>
    in?: $Enums.LanguageCode[] | ListEnumLanguageCodeFieldRefInput<$PrismaModel>
    notIn?: $Enums.LanguageCode[] | ListEnumLanguageCodeFieldRefInput<$PrismaModel>
    not?: NestedEnumLanguageCodeFilter<$PrismaModel> | $Enums.LanguageCode
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedEnumZodiacSignWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.ZodiacSign | EnumZodiacSignFieldRefInput<$PrismaModel>
    in?: $Enums.ZodiacSign[] | ListEnumZodiacSignFieldRefInput<$PrismaModel>
    notIn?: $Enums.ZodiacSign[] | ListEnumZodiacSignFieldRefInput<$PrismaModel>
    not?: NestedEnumZodiacSignWithAggregatesFilter<$PrismaModel> | $Enums.ZodiacSign
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumZodiacSignFilter<$PrismaModel>
    _max?: NestedEnumZodiacSignFilter<$PrismaModel>
  }

  export type NestedEnumLanguageCodeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.LanguageCode | EnumLanguageCodeFieldRefInput<$PrismaModel>
    in?: $Enums.LanguageCode[] | ListEnumLanguageCodeFieldRefInput<$PrismaModel>
    notIn?: $Enums.LanguageCode[] | ListEnumLanguageCodeFieldRefInput<$PrismaModel>
    not?: NestedEnumLanguageCodeWithAggregatesFilter<$PrismaModel> | $Enums.LanguageCode
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumLanguageCodeFilter<$PrismaModel>
    _max?: NestedEnumLanguageCodeFilter<$PrismaModel>
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedEnumHoroscopeTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.HoroscopeType | EnumHoroscopeTypeFieldRefInput<$PrismaModel>
    in?: $Enums.HoroscopeType[] | ListEnumHoroscopeTypeFieldRefInput<$PrismaModel>
    notIn?: $Enums.HoroscopeType[] | ListEnumHoroscopeTypeFieldRefInput<$PrismaModel>
    not?: NestedEnumHoroscopeTypeFilter<$PrismaModel> | $Enums.HoroscopeType
  }

  export type NestedEnumHoroscopeTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.HoroscopeType | EnumHoroscopeTypeFieldRefInput<$PrismaModel>
    in?: $Enums.HoroscopeType[] | ListEnumHoroscopeTypeFieldRefInput<$PrismaModel>
    notIn?: $Enums.HoroscopeType[] | ListEnumHoroscopeTypeFieldRefInput<$PrismaModel>
    not?: NestedEnumHoroscopeTypeWithAggregatesFilter<$PrismaModel> | $Enums.HoroscopeType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumHoroscopeTypeFilter<$PrismaModel>
    _max?: NestedEnumHoroscopeTypeFilter<$PrismaModel>
  }

  export type QrCodeMappingCreateWithoutUserInput = {
    id?: string
    qrToken: string
    createdAt?: Date | string
    lastScanned?: Date | string | null
    scanCount?: number
  }

  export type QrCodeMappingUncheckedCreateWithoutUserInput = {
    id?: string
    qrToken: string
    createdAt?: Date | string
    lastScanned?: Date | string | null
    scanCount?: number
  }

  export type QrCodeMappingCreateOrConnectWithoutUserInput = {
    where: QrCodeMappingWhereUniqueInput
    create: XOR<QrCodeMappingCreateWithoutUserInput, QrCodeMappingUncheckedCreateWithoutUserInput>
  }

  export type QrCodeMappingCreateManyUserInputEnvelope = {
    data: QrCodeMappingCreateManyUserInput | QrCodeMappingCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type PersonalHoroscopeCreateWithoutUserInput = {
    id?: string
    title: string
    content: string
    isActive?: boolean
    language?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type PersonalHoroscopeUncheckedCreateWithoutUserInput = {
    id?: string
    title: string
    content: string
    isActive?: boolean
    language?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type PersonalHoroscopeCreateOrConnectWithoutUserInput = {
    where: PersonalHoroscopeWhereUniqueInput
    create: XOR<PersonalHoroscopeCreateWithoutUserInput, PersonalHoroscopeUncheckedCreateWithoutUserInput>
  }

  export type PersonalHoroscopeCreateManyUserInputEnvelope = {
    data: PersonalHoroscopeCreateManyUserInput | PersonalHoroscopeCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type QrCodeMappingUpsertWithWhereUniqueWithoutUserInput = {
    where: QrCodeMappingWhereUniqueInput
    update: XOR<QrCodeMappingUpdateWithoutUserInput, QrCodeMappingUncheckedUpdateWithoutUserInput>
    create: XOR<QrCodeMappingCreateWithoutUserInput, QrCodeMappingUncheckedCreateWithoutUserInput>
  }

  export type QrCodeMappingUpdateWithWhereUniqueWithoutUserInput = {
    where: QrCodeMappingWhereUniqueInput
    data: XOR<QrCodeMappingUpdateWithoutUserInput, QrCodeMappingUncheckedUpdateWithoutUserInput>
  }

  export type QrCodeMappingUpdateManyWithWhereWithoutUserInput = {
    where: QrCodeMappingScalarWhereInput
    data: XOR<QrCodeMappingUpdateManyMutationInput, QrCodeMappingUncheckedUpdateManyWithoutUserInput>
  }

  export type QrCodeMappingScalarWhereInput = {
    AND?: QrCodeMappingScalarWhereInput | QrCodeMappingScalarWhereInput[]
    OR?: QrCodeMappingScalarWhereInput[]
    NOT?: QrCodeMappingScalarWhereInput | QrCodeMappingScalarWhereInput[]
    id?: StringFilter<"QrCodeMapping"> | string
    qrToken?: StringFilter<"QrCodeMapping"> | string
    userId?: StringFilter<"QrCodeMapping"> | string
    createdAt?: DateTimeFilter<"QrCodeMapping"> | Date | string
    lastScanned?: DateTimeNullableFilter<"QrCodeMapping"> | Date | string | null
    scanCount?: IntFilter<"QrCodeMapping"> | number
  }

  export type PersonalHoroscopeUpsertWithWhereUniqueWithoutUserInput = {
    where: PersonalHoroscopeWhereUniqueInput
    update: XOR<PersonalHoroscopeUpdateWithoutUserInput, PersonalHoroscopeUncheckedUpdateWithoutUserInput>
    create: XOR<PersonalHoroscopeCreateWithoutUserInput, PersonalHoroscopeUncheckedCreateWithoutUserInput>
  }

  export type PersonalHoroscopeUpdateWithWhereUniqueWithoutUserInput = {
    where: PersonalHoroscopeWhereUniqueInput
    data: XOR<PersonalHoroscopeUpdateWithoutUserInput, PersonalHoroscopeUncheckedUpdateWithoutUserInput>
  }

  export type PersonalHoroscopeUpdateManyWithWhereWithoutUserInput = {
    where: PersonalHoroscopeScalarWhereInput
    data: XOR<PersonalHoroscopeUpdateManyMutationInput, PersonalHoroscopeUncheckedUpdateManyWithoutUserInput>
  }

  export type PersonalHoroscopeScalarWhereInput = {
    AND?: PersonalHoroscopeScalarWhereInput | PersonalHoroscopeScalarWhereInput[]
    OR?: PersonalHoroscopeScalarWhereInput[]
    NOT?: PersonalHoroscopeScalarWhereInput | PersonalHoroscopeScalarWhereInput[]
    id?: StringFilter<"PersonalHoroscope"> | string
    userId?: StringFilter<"PersonalHoroscope"> | string
    title?: StringFilter<"PersonalHoroscope"> | string
    content?: StringFilter<"PersonalHoroscope"> | string
    isActive?: BoolFilter<"PersonalHoroscope"> | boolean
    language?: EnumLanguageCodeFilter<"PersonalHoroscope"> | $Enums.LanguageCode
    createdAt?: DateTimeFilter<"PersonalHoroscope"> | Date | string
    updatedAt?: DateTimeFilter<"PersonalHoroscope"> | Date | string
  }

  export type UserCreateWithoutQrCodeMappingsInput = {
    id?: string
    email?: string | null
    name: string
    phoneNumber?: string | null
    address?: string | null
    zodiacSign: $Enums.ZodiacSign
    birthDate: Date | string
    birthTime?: string | null
    qrToken?: string
    languagePreference?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
    personalHoroscopes?: PersonalHoroscopeCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutQrCodeMappingsInput = {
    id?: string
    email?: string | null
    name: string
    phoneNumber?: string | null
    address?: string | null
    zodiacSign: $Enums.ZodiacSign
    birthDate: Date | string
    birthTime?: string | null
    qrToken?: string
    languagePreference?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
    personalHoroscopes?: PersonalHoroscopeUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutQrCodeMappingsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutQrCodeMappingsInput, UserUncheckedCreateWithoutQrCodeMappingsInput>
  }

  export type UserUpsertWithoutQrCodeMappingsInput = {
    update: XOR<UserUpdateWithoutQrCodeMappingsInput, UserUncheckedUpdateWithoutQrCodeMappingsInput>
    create: XOR<UserCreateWithoutQrCodeMappingsInput, UserUncheckedCreateWithoutQrCodeMappingsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutQrCodeMappingsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutQrCodeMappingsInput, UserUncheckedUpdateWithoutQrCodeMappingsInput>
  }

  export type UserUpdateWithoutQrCodeMappingsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: StringFieldUpdateOperationsInput | string
    phoneNumber?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    birthDate?: DateTimeFieldUpdateOperationsInput | Date | string
    birthTime?: NullableStringFieldUpdateOperationsInput | string | null
    qrToken?: StringFieldUpdateOperationsInput | string
    languagePreference?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    personalHoroscopes?: PersonalHoroscopeUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutQrCodeMappingsInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: StringFieldUpdateOperationsInput | string
    phoneNumber?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    birthDate?: DateTimeFieldUpdateOperationsInput | Date | string
    birthTime?: NullableStringFieldUpdateOperationsInput | string | null
    qrToken?: StringFieldUpdateOperationsInput | string
    languagePreference?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    personalHoroscopes?: PersonalHoroscopeUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateWithoutPersonalHoroscopesInput = {
    id?: string
    email?: string | null
    name: string
    phoneNumber?: string | null
    address?: string | null
    zodiacSign: $Enums.ZodiacSign
    birthDate: Date | string
    birthTime?: string | null
    qrToken?: string
    languagePreference?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
    qrCodeMappings?: QrCodeMappingCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutPersonalHoroscopesInput = {
    id?: string
    email?: string | null
    name: string
    phoneNumber?: string | null
    address?: string | null
    zodiacSign: $Enums.ZodiacSign
    birthDate: Date | string
    birthTime?: string | null
    qrToken?: string
    languagePreference?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
    qrCodeMappings?: QrCodeMappingUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutPersonalHoroscopesInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutPersonalHoroscopesInput, UserUncheckedCreateWithoutPersonalHoroscopesInput>
  }

  export type UserUpsertWithoutPersonalHoroscopesInput = {
    update: XOR<UserUpdateWithoutPersonalHoroscopesInput, UserUncheckedUpdateWithoutPersonalHoroscopesInput>
    create: XOR<UserCreateWithoutPersonalHoroscopesInput, UserUncheckedCreateWithoutPersonalHoroscopesInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutPersonalHoroscopesInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutPersonalHoroscopesInput, UserUncheckedUpdateWithoutPersonalHoroscopesInput>
  }

  export type UserUpdateWithoutPersonalHoroscopesInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: StringFieldUpdateOperationsInput | string
    phoneNumber?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    birthDate?: DateTimeFieldUpdateOperationsInput | Date | string
    birthTime?: NullableStringFieldUpdateOperationsInput | string | null
    qrToken?: StringFieldUpdateOperationsInput | string
    languagePreference?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    qrCodeMappings?: QrCodeMappingUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutPersonalHoroscopesInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: NullableStringFieldUpdateOperationsInput | string | null
    name?: StringFieldUpdateOperationsInput | string
    phoneNumber?: NullableStringFieldUpdateOperationsInput | string | null
    address?: NullableStringFieldUpdateOperationsInput | string | null
    zodiacSign?: EnumZodiacSignFieldUpdateOperationsInput | $Enums.ZodiacSign
    birthDate?: DateTimeFieldUpdateOperationsInput | Date | string
    birthTime?: NullableStringFieldUpdateOperationsInput | string | null
    qrToken?: StringFieldUpdateOperationsInput | string
    languagePreference?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    qrCodeMappings?: QrCodeMappingUncheckedUpdateManyWithoutUserNestedInput
  }

  export type QrCodeMappingCreateManyUserInput = {
    id?: string
    qrToken: string
    createdAt?: Date | string
    lastScanned?: Date | string | null
    scanCount?: number
  }

  export type PersonalHoroscopeCreateManyUserInput = {
    id?: string
    title: string
    content: string
    isActive?: boolean
    language?: $Enums.LanguageCode
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type QrCodeMappingUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    qrToken?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    lastScanned?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scanCount?: IntFieldUpdateOperationsInput | number
  }

  export type QrCodeMappingUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    qrToken?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    lastScanned?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scanCount?: IntFieldUpdateOperationsInput | number
  }

  export type QrCodeMappingUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    qrToken?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    lastScanned?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    scanCount?: IntFieldUpdateOperationsInput | number
  }

  export type PersonalHoroscopeUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PersonalHoroscopeUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PersonalHoroscopeUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    content?: StringFieldUpdateOperationsInput | string
    isActive?: BoolFieldUpdateOperationsInput | boolean
    language?: EnumLanguageCodeFieldUpdateOperationsInput | $Enums.LanguageCode
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}