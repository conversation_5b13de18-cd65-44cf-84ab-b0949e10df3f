const { default: fetch } = require('node-fetch');

async function testLoginAPI() {
  try {
    console.log('Testing login API endpoint...');
    
    const response = await fetch('http://localhost:3000/api/admin/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      }),
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const data = await response.json();
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log('✅ Login API test successful!');
      console.log('Token received:', data.data.token ? 'Yes' : 'No');
      console.log('Admin data:', data.data.admin);
    } else {
      console.log('❌ Login API test failed:', data.error);
    }
    
  } catch (error) {
    console.error('❌ Error testing login API:', error);
  }
}

testLoginAPI();
