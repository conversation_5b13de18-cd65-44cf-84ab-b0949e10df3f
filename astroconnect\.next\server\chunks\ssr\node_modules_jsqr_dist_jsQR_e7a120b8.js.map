{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/node_modules/jsqr/dist/jsQR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"jsQR\"] = factory();\n\telse\n\t\troot[\"jsQR\"] = factory();\n})(typeof self !== 'undefined' ? self : this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 3);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar BitMatrix = /** @class */ (function () {\n    function BitMatrix(data, width) {\n        this.width = width;\n        this.height = data.length / width;\n        this.data = data;\n    }\n    BitMatrix.createEmpty = function (width, height) {\n        return new BitMatrix(new Uint8ClampedArray(width * height), width);\n    };\n    BitMatrix.prototype.get = function (x, y) {\n        if (x < 0 || x >= this.width || y < 0 || y >= this.height) {\n            return false;\n        }\n        return !!this.data[y * this.width + x];\n    };\n    BitMatrix.prototype.set = function (x, y, v) {\n        this.data[y * this.width + x] = v ? 1 : 0;\n    };\n    BitMatrix.prototype.setRegion = function (left, top, width, height, v) {\n        for (var y = top; y < top + height; y++) {\n            for (var x = left; x < left + width; x++) {\n                this.set(x, y, !!v);\n            }\n        }\n    };\n    return BitMatrix;\n}());\nexports.BitMatrix = BitMatrix;\n\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar GenericGFPoly_1 = __webpack_require__(2);\nfunction addOrSubtractGF(a, b) {\n    return a ^ b; // tslint:disable-line:no-bitwise\n}\nexports.addOrSubtractGF = addOrSubtractGF;\nvar GenericGF = /** @class */ (function () {\n    function GenericGF(primitive, size, genBase) {\n        this.primitive = primitive;\n        this.size = size;\n        this.generatorBase = genBase;\n        this.expTable = new Array(this.size);\n        this.logTable = new Array(this.size);\n        var x = 1;\n        for (var i = 0; i < this.size; i++) {\n            this.expTable[i] = x;\n            x = x * 2;\n            if (x >= this.size) {\n                x = (x ^ this.primitive) & (this.size - 1); // tslint:disable-line:no-bitwise\n            }\n        }\n        for (var i = 0; i < this.size - 1; i++) {\n            this.logTable[this.expTable[i]] = i;\n        }\n        this.zero = new GenericGFPoly_1.default(this, Uint8ClampedArray.from([0]));\n        this.one = new GenericGFPoly_1.default(this, Uint8ClampedArray.from([1]));\n    }\n    GenericGF.prototype.multiply = function (a, b) {\n        if (a === 0 || b === 0) {\n            return 0;\n        }\n        return this.expTable[(this.logTable[a] + this.logTable[b]) % (this.size - 1)];\n    };\n    GenericGF.prototype.inverse = function (a) {\n        if (a === 0) {\n            throw new Error(\"Can't invert 0\");\n        }\n        return this.expTable[this.size - this.logTable[a] - 1];\n    };\n    GenericGF.prototype.buildMonomial = function (degree, coefficient) {\n        if (degree < 0) {\n            throw new Error(\"Invalid monomial degree less than 0\");\n        }\n        if (coefficient === 0) {\n            return this.zero;\n        }\n        var coefficients = new Uint8ClampedArray(degree + 1);\n        coefficients[0] = coefficient;\n        return new GenericGFPoly_1.default(this, coefficients);\n    };\n    GenericGF.prototype.log = function (a) {\n        if (a === 0) {\n            throw new Error(\"Can't take log(0)\");\n        }\n        return this.logTable[a];\n    };\n    GenericGF.prototype.exp = function (a) {\n        return this.expTable[a];\n    };\n    return GenericGF;\n}());\nexports.default = GenericGF;\n\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar GenericGF_1 = __webpack_require__(1);\nvar GenericGFPoly = /** @class */ (function () {\n    function GenericGFPoly(field, coefficients) {\n        if (coefficients.length === 0) {\n            throw new Error(\"No coefficients.\");\n        }\n        this.field = field;\n        var coefficientsLength = coefficients.length;\n        if (coefficientsLength > 1 && coefficients[0] === 0) {\n            // Leading term must be non-zero for anything except the constant polynomial \"0\"\n            var firstNonZero = 1;\n            while (firstNonZero < coefficientsLength && coefficients[firstNonZero] === 0) {\n                firstNonZero++;\n            }\n            if (firstNonZero === coefficientsLength) {\n                this.coefficients = field.zero.coefficients;\n            }\n            else {\n                this.coefficients = new Uint8ClampedArray(coefficientsLength - firstNonZero);\n                for (var i = 0; i < this.coefficients.length; i++) {\n                    this.coefficients[i] = coefficients[firstNonZero + i];\n                }\n            }\n        }\n        else {\n            this.coefficients = coefficients;\n        }\n    }\n    GenericGFPoly.prototype.degree = function () {\n        return this.coefficients.length - 1;\n    };\n    GenericGFPoly.prototype.isZero = function () {\n        return this.coefficients[0] === 0;\n    };\n    GenericGFPoly.prototype.getCoefficient = function (degree) {\n        return this.coefficients[this.coefficients.length - 1 - degree];\n    };\n    GenericGFPoly.prototype.addOrSubtract = function (other) {\n        var _a;\n        if (this.isZero()) {\n            return other;\n        }\n        if (other.isZero()) {\n            return this;\n        }\n        var smallerCoefficients = this.coefficients;\n        var largerCoefficients = other.coefficients;\n        if (smallerCoefficients.length > largerCoefficients.length) {\n            _a = [largerCoefficients, smallerCoefficients], smallerCoefficients = _a[0], largerCoefficients = _a[1];\n        }\n        var sumDiff = new Uint8ClampedArray(largerCoefficients.length);\n        var lengthDiff = largerCoefficients.length - smallerCoefficients.length;\n        for (var i = 0; i < lengthDiff; i++) {\n            sumDiff[i] = largerCoefficients[i];\n        }\n        for (var i = lengthDiff; i < largerCoefficients.length; i++) {\n            sumDiff[i] = GenericGF_1.addOrSubtractGF(smallerCoefficients[i - lengthDiff], largerCoefficients[i]);\n        }\n        return new GenericGFPoly(this.field, sumDiff);\n    };\n    GenericGFPoly.prototype.multiply = function (scalar) {\n        if (scalar === 0) {\n            return this.field.zero;\n        }\n        if (scalar === 1) {\n            return this;\n        }\n        var size = this.coefficients.length;\n        var product = new Uint8ClampedArray(size);\n        for (var i = 0; i < size; i++) {\n            product[i] = this.field.multiply(this.coefficients[i], scalar);\n        }\n        return new GenericGFPoly(this.field, product);\n    };\n    GenericGFPoly.prototype.multiplyPoly = function (other) {\n        if (this.isZero() || other.isZero()) {\n            return this.field.zero;\n        }\n        var aCoefficients = this.coefficients;\n        var aLength = aCoefficients.length;\n        var bCoefficients = other.coefficients;\n        var bLength = bCoefficients.length;\n        var product = new Uint8ClampedArray(aLength + bLength - 1);\n        for (var i = 0; i < aLength; i++) {\n            var aCoeff = aCoefficients[i];\n            for (var j = 0; j < bLength; j++) {\n                product[i + j] = GenericGF_1.addOrSubtractGF(product[i + j], this.field.multiply(aCoeff, bCoefficients[j]));\n            }\n        }\n        return new GenericGFPoly(this.field, product);\n    };\n    GenericGFPoly.prototype.multiplyByMonomial = function (degree, coefficient) {\n        if (degree < 0) {\n            throw new Error(\"Invalid degree less than 0\");\n        }\n        if (coefficient === 0) {\n            return this.field.zero;\n        }\n        var size = this.coefficients.length;\n        var product = new Uint8ClampedArray(size + degree);\n        for (var i = 0; i < size; i++) {\n            product[i] = this.field.multiply(this.coefficients[i], coefficient);\n        }\n        return new GenericGFPoly(this.field, product);\n    };\n    GenericGFPoly.prototype.evaluateAt = function (a) {\n        var result = 0;\n        if (a === 0) {\n            // Just return the x^0 coefficient\n            return this.getCoefficient(0);\n        }\n        var size = this.coefficients.length;\n        if (a === 1) {\n            // Just the sum of the coefficients\n            this.coefficients.forEach(function (coefficient) {\n                result = GenericGF_1.addOrSubtractGF(result, coefficient);\n            });\n            return result;\n        }\n        result = this.coefficients[0];\n        for (var i = 1; i < size; i++) {\n            result = GenericGF_1.addOrSubtractGF(this.field.multiply(a, result), this.coefficients[i]);\n        }\n        return result;\n    };\n    return GenericGFPoly;\n}());\nexports.default = GenericGFPoly;\n\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar binarizer_1 = __webpack_require__(4);\nvar decoder_1 = __webpack_require__(5);\nvar extractor_1 = __webpack_require__(11);\nvar locator_1 = __webpack_require__(12);\nfunction scan(matrix) {\n    var locations = locator_1.locate(matrix);\n    if (!locations) {\n        return null;\n    }\n    for (var _i = 0, locations_1 = locations; _i < locations_1.length; _i++) {\n        var location_1 = locations_1[_i];\n        var extracted = extractor_1.extract(matrix, location_1);\n        var decoded = decoder_1.decode(extracted.matrix);\n        if (decoded) {\n            return {\n                binaryData: decoded.bytes,\n                data: decoded.text,\n                chunks: decoded.chunks,\n                version: decoded.version,\n                location: {\n                    topRightCorner: extracted.mappingFunction(location_1.dimension, 0),\n                    topLeftCorner: extracted.mappingFunction(0, 0),\n                    bottomRightCorner: extracted.mappingFunction(location_1.dimension, location_1.dimension),\n                    bottomLeftCorner: extracted.mappingFunction(0, location_1.dimension),\n                    topRightFinderPattern: location_1.topRight,\n                    topLeftFinderPattern: location_1.topLeft,\n                    bottomLeftFinderPattern: location_1.bottomLeft,\n                    bottomRightAlignmentPattern: location_1.alignmentPattern,\n                },\n            };\n        }\n    }\n    return null;\n}\nvar defaultOptions = {\n    inversionAttempts: \"attemptBoth\",\n};\nfunction jsQR(data, width, height, providedOptions) {\n    if (providedOptions === void 0) { providedOptions = {}; }\n    var options = defaultOptions;\n    Object.keys(options || {}).forEach(function (opt) {\n        options[opt] = providedOptions[opt] || options[opt];\n    });\n    var shouldInvert = options.inversionAttempts === \"attemptBoth\" || options.inversionAttempts === \"invertFirst\";\n    var tryInvertedFirst = options.inversionAttempts === \"onlyInvert\" || options.inversionAttempts === \"invertFirst\";\n    var _a = binarizer_1.binarize(data, width, height, shouldInvert), binarized = _a.binarized, inverted = _a.inverted;\n    var result = scan(tryInvertedFirst ? inverted : binarized);\n    if (!result && (options.inversionAttempts === \"attemptBoth\" || options.inversionAttempts === \"invertFirst\")) {\n        result = scan(tryInvertedFirst ? binarized : inverted);\n    }\n    return result;\n}\njsQR.default = jsQR;\nexports.default = jsQR;\n\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar BitMatrix_1 = __webpack_require__(0);\nvar REGION_SIZE = 8;\nvar MIN_DYNAMIC_RANGE = 24;\nfunction numBetween(value, min, max) {\n    return value < min ? min : value > max ? max : value;\n}\n// Like BitMatrix but accepts arbitry Uint8 values\nvar Matrix = /** @class */ (function () {\n    function Matrix(width, height) {\n        this.width = width;\n        this.data = new Uint8ClampedArray(width * height);\n    }\n    Matrix.prototype.get = function (x, y) {\n        return this.data[y * this.width + x];\n    };\n    Matrix.prototype.set = function (x, y, value) {\n        this.data[y * this.width + x] = value;\n    };\n    return Matrix;\n}());\nfunction binarize(data, width, height, returnInverted) {\n    if (data.length !== width * height * 4) {\n        throw new Error(\"Malformed data passed to binarizer.\");\n    }\n    // Convert image to greyscale\n    var greyscalePixels = new Matrix(width, height);\n    for (var x = 0; x < width; x++) {\n        for (var y = 0; y < height; y++) {\n            var r = data[((y * width + x) * 4) + 0];\n            var g = data[((y * width + x) * 4) + 1];\n            var b = data[((y * width + x) * 4) + 2];\n            greyscalePixels.set(x, y, 0.2126 * r + 0.7152 * g + 0.0722 * b);\n        }\n    }\n    var horizontalRegionCount = Math.ceil(width / REGION_SIZE);\n    var verticalRegionCount = Math.ceil(height / REGION_SIZE);\n    var blackPoints = new Matrix(horizontalRegionCount, verticalRegionCount);\n    for (var verticalRegion = 0; verticalRegion < verticalRegionCount; verticalRegion++) {\n        for (var hortizontalRegion = 0; hortizontalRegion < horizontalRegionCount; hortizontalRegion++) {\n            var sum = 0;\n            var min = Infinity;\n            var max = 0;\n            for (var y = 0; y < REGION_SIZE; y++) {\n                for (var x = 0; x < REGION_SIZE; x++) {\n                    var pixelLumosity = greyscalePixels.get(hortizontalRegion * REGION_SIZE + x, verticalRegion * REGION_SIZE + y);\n                    sum += pixelLumosity;\n                    min = Math.min(min, pixelLumosity);\n                    max = Math.max(max, pixelLumosity);\n                }\n            }\n            var average = sum / (Math.pow(REGION_SIZE, 2));\n            if (max - min <= MIN_DYNAMIC_RANGE) {\n                // If variation within the block is low, assume this is a block with only light or only\n                // dark pixels. In that case we do not want to use the average, as it would divide this\n                // low contrast area into black and white pixels, essentially creating data out of noise.\n                //\n                // Default the blackpoint for these blocks to be half the min - effectively white them out\n                average = min / 2;\n                if (verticalRegion > 0 && hortizontalRegion > 0) {\n                    // Correct the \"white background\" assumption for blocks that have neighbors by comparing\n                    // the pixels in this block to the previously calculated black points. This is based on\n                    // the fact that dark barcode symbology is always surrounded by some amount of light\n                    // background for which reasonable black point estimates were made. The bp estimated at\n                    // the boundaries is used for the interior.\n                    // The (min < bp) is arbitrary but works better than other heuristics that were tried.\n                    var averageNeighborBlackPoint = (blackPoints.get(hortizontalRegion, verticalRegion - 1) +\n                        (2 * blackPoints.get(hortizontalRegion - 1, verticalRegion)) +\n                        blackPoints.get(hortizontalRegion - 1, verticalRegion - 1)) / 4;\n                    if (min < averageNeighborBlackPoint) {\n                        average = averageNeighborBlackPoint;\n                    }\n                }\n            }\n            blackPoints.set(hortizontalRegion, verticalRegion, average);\n        }\n    }\n    var binarized = BitMatrix_1.BitMatrix.createEmpty(width, height);\n    var inverted = null;\n    if (returnInverted) {\n        inverted = BitMatrix_1.BitMatrix.createEmpty(width, height);\n    }\n    for (var verticalRegion = 0; verticalRegion < verticalRegionCount; verticalRegion++) {\n        for (var hortizontalRegion = 0; hortizontalRegion < horizontalRegionCount; hortizontalRegion++) {\n            var left = numBetween(hortizontalRegion, 2, horizontalRegionCount - 3);\n            var top_1 = numBetween(verticalRegion, 2, verticalRegionCount - 3);\n            var sum = 0;\n            for (var xRegion = -2; xRegion <= 2; xRegion++) {\n                for (var yRegion = -2; yRegion <= 2; yRegion++) {\n                    sum += blackPoints.get(left + xRegion, top_1 + yRegion);\n                }\n            }\n            var threshold = sum / 25;\n            for (var xRegion = 0; xRegion < REGION_SIZE; xRegion++) {\n                for (var yRegion = 0; yRegion < REGION_SIZE; yRegion++) {\n                    var x = hortizontalRegion * REGION_SIZE + xRegion;\n                    var y = verticalRegion * REGION_SIZE + yRegion;\n                    var lum = greyscalePixels.get(x, y);\n                    binarized.set(x, y, lum <= threshold);\n                    if (returnInverted) {\n                        inverted.set(x, y, !(lum <= threshold));\n                    }\n                }\n            }\n        }\n    }\n    if (returnInverted) {\n        return { binarized: binarized, inverted: inverted };\n    }\n    return { binarized: binarized };\n}\nexports.binarize = binarize;\n\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar BitMatrix_1 = __webpack_require__(0);\nvar decodeData_1 = __webpack_require__(6);\nvar reedsolomon_1 = __webpack_require__(9);\nvar version_1 = __webpack_require__(10);\n// tslint:disable:no-bitwise\nfunction numBitsDiffering(x, y) {\n    var z = x ^ y;\n    var bitCount = 0;\n    while (z) {\n        bitCount++;\n        z &= z - 1;\n    }\n    return bitCount;\n}\nfunction pushBit(bit, byte) {\n    return (byte << 1) | bit;\n}\n// tslint:enable:no-bitwise\nvar FORMAT_INFO_TABLE = [\n    { bits: 0x5412, formatInfo: { errorCorrectionLevel: 1, dataMask: 0 } },\n    { bits: 0x5125, formatInfo: { errorCorrectionLevel: 1, dataMask: 1 } },\n    { bits: 0x5E7C, formatInfo: { errorCorrectionLevel: 1, dataMask: 2 } },\n    { bits: 0x5B4B, formatInfo: { errorCorrectionLevel: 1, dataMask: 3 } },\n    { bits: 0x45F9, formatInfo: { errorCorrectionLevel: 1, dataMask: 4 } },\n    { bits: 0x40CE, formatInfo: { errorCorrectionLevel: 1, dataMask: 5 } },\n    { bits: 0x4F97, formatInfo: { errorCorrectionLevel: 1, dataMask: 6 } },\n    { bits: 0x4AA0, formatInfo: { errorCorrectionLevel: 1, dataMask: 7 } },\n    { bits: 0x77C4, formatInfo: { errorCorrectionLevel: 0, dataMask: 0 } },\n    { bits: 0x72F3, formatInfo: { errorCorrectionLevel: 0, dataMask: 1 } },\n    { bits: 0x7DAA, formatInfo: { errorCorrectionLevel: 0, dataMask: 2 } },\n    { bits: 0x789D, formatInfo: { errorCorrectionLevel: 0, dataMask: 3 } },\n    { bits: 0x662F, formatInfo: { errorCorrectionLevel: 0, dataMask: 4 } },\n    { bits: 0x6318, formatInfo: { errorCorrectionLevel: 0, dataMask: 5 } },\n    { bits: 0x6C41, formatInfo: { errorCorrectionLevel: 0, dataMask: 6 } },\n    { bits: 0x6976, formatInfo: { errorCorrectionLevel: 0, dataMask: 7 } },\n    { bits: 0x1689, formatInfo: { errorCorrectionLevel: 3, dataMask: 0 } },\n    { bits: 0x13BE, formatInfo: { errorCorrectionLevel: 3, dataMask: 1 } },\n    { bits: 0x1CE7, formatInfo: { errorCorrectionLevel: 3, dataMask: 2 } },\n    { bits: 0x19D0, formatInfo: { errorCorrectionLevel: 3, dataMask: 3 } },\n    { bits: 0x0762, formatInfo: { errorCorrectionLevel: 3, dataMask: 4 } },\n    { bits: 0x0255, formatInfo: { errorCorrectionLevel: 3, dataMask: 5 } },\n    { bits: 0x0D0C, formatInfo: { errorCorrectionLevel: 3, dataMask: 6 } },\n    { bits: 0x083B, formatInfo: { errorCorrectionLevel: 3, dataMask: 7 } },\n    { bits: 0x355F, formatInfo: { errorCorrectionLevel: 2, dataMask: 0 } },\n    { bits: 0x3068, formatInfo: { errorCorrectionLevel: 2, dataMask: 1 } },\n    { bits: 0x3F31, formatInfo: { errorCorrectionLevel: 2, dataMask: 2 } },\n    { bits: 0x3A06, formatInfo: { errorCorrectionLevel: 2, dataMask: 3 } },\n    { bits: 0x24B4, formatInfo: { errorCorrectionLevel: 2, dataMask: 4 } },\n    { bits: 0x2183, formatInfo: { errorCorrectionLevel: 2, dataMask: 5 } },\n    { bits: 0x2EDA, formatInfo: { errorCorrectionLevel: 2, dataMask: 6 } },\n    { bits: 0x2BED, formatInfo: { errorCorrectionLevel: 2, dataMask: 7 } },\n];\nvar DATA_MASKS = [\n    function (p) { return ((p.y + p.x) % 2) === 0; },\n    function (p) { return (p.y % 2) === 0; },\n    function (p) { return p.x % 3 === 0; },\n    function (p) { return (p.y + p.x) % 3 === 0; },\n    function (p) { return (Math.floor(p.y / 2) + Math.floor(p.x / 3)) % 2 === 0; },\n    function (p) { return ((p.x * p.y) % 2) + ((p.x * p.y) % 3) === 0; },\n    function (p) { return ((((p.y * p.x) % 2) + (p.y * p.x) % 3) % 2) === 0; },\n    function (p) { return ((((p.y + p.x) % 2) + (p.y * p.x) % 3) % 2) === 0; },\n];\nfunction buildFunctionPatternMask(version) {\n    var dimension = 17 + 4 * version.versionNumber;\n    var matrix = BitMatrix_1.BitMatrix.createEmpty(dimension, dimension);\n    matrix.setRegion(0, 0, 9, 9, true); // Top left finder pattern + separator + format\n    matrix.setRegion(dimension - 8, 0, 8, 9, true); // Top right finder pattern + separator + format\n    matrix.setRegion(0, dimension - 8, 9, 8, true); // Bottom left finder pattern + separator + format\n    // Alignment patterns\n    for (var _i = 0, _a = version.alignmentPatternCenters; _i < _a.length; _i++) {\n        var x = _a[_i];\n        for (var _b = 0, _c = version.alignmentPatternCenters; _b < _c.length; _b++) {\n            var y = _c[_b];\n            if (!(x === 6 && y === 6 || x === 6 && y === dimension - 7 || x === dimension - 7 && y === 6)) {\n                matrix.setRegion(x - 2, y - 2, 5, 5, true);\n            }\n        }\n    }\n    matrix.setRegion(6, 9, 1, dimension - 17, true); // Vertical timing pattern\n    matrix.setRegion(9, 6, dimension - 17, 1, true); // Horizontal timing pattern\n    if (version.versionNumber > 6) {\n        matrix.setRegion(dimension - 11, 0, 3, 6, true); // Version info, top right\n        matrix.setRegion(0, dimension - 11, 6, 3, true); // Version info, bottom left\n    }\n    return matrix;\n}\nfunction readCodewords(matrix, version, formatInfo) {\n    var dataMask = DATA_MASKS[formatInfo.dataMask];\n    var dimension = matrix.height;\n    var functionPatternMask = buildFunctionPatternMask(version);\n    var codewords = [];\n    var currentByte = 0;\n    var bitsRead = 0;\n    // Read columns in pairs, from right to left\n    var readingUp = true;\n    for (var columnIndex = dimension - 1; columnIndex > 0; columnIndex -= 2) {\n        if (columnIndex === 6) { // Skip whole column with vertical alignment pattern;\n            columnIndex--;\n        }\n        for (var i = 0; i < dimension; i++) {\n            var y = readingUp ? dimension - 1 - i : i;\n            for (var columnOffset = 0; columnOffset < 2; columnOffset++) {\n                var x = columnIndex - columnOffset;\n                if (!functionPatternMask.get(x, y)) {\n                    bitsRead++;\n                    var bit = matrix.get(x, y);\n                    if (dataMask({ y: y, x: x })) {\n                        bit = !bit;\n                    }\n                    currentByte = pushBit(bit, currentByte);\n                    if (bitsRead === 8) { // Whole bytes\n                        codewords.push(currentByte);\n                        bitsRead = 0;\n                        currentByte = 0;\n                    }\n                }\n            }\n        }\n        readingUp = !readingUp;\n    }\n    return codewords;\n}\nfunction readVersion(matrix) {\n    var dimension = matrix.height;\n    var provisionalVersion = Math.floor((dimension - 17) / 4);\n    if (provisionalVersion <= 6) { // 6 and under dont have version info in the QR code\n        return version_1.VERSIONS[provisionalVersion - 1];\n    }\n    var topRightVersionBits = 0;\n    for (var y = 5; y >= 0; y--) {\n        for (var x = dimension - 9; x >= dimension - 11; x--) {\n            topRightVersionBits = pushBit(matrix.get(x, y), topRightVersionBits);\n        }\n    }\n    var bottomLeftVersionBits = 0;\n    for (var x = 5; x >= 0; x--) {\n        for (var y = dimension - 9; y >= dimension - 11; y--) {\n            bottomLeftVersionBits = pushBit(matrix.get(x, y), bottomLeftVersionBits);\n        }\n    }\n    var bestDifference = Infinity;\n    var bestVersion;\n    for (var _i = 0, VERSIONS_1 = version_1.VERSIONS; _i < VERSIONS_1.length; _i++) {\n        var version = VERSIONS_1[_i];\n        if (version.infoBits === topRightVersionBits || version.infoBits === bottomLeftVersionBits) {\n            return version;\n        }\n        var difference = numBitsDiffering(topRightVersionBits, version.infoBits);\n        if (difference < bestDifference) {\n            bestVersion = version;\n            bestDifference = difference;\n        }\n        difference = numBitsDiffering(bottomLeftVersionBits, version.infoBits);\n        if (difference < bestDifference) {\n            bestVersion = version;\n            bestDifference = difference;\n        }\n    }\n    // We can tolerate up to 3 bits of error since no two version info codewords will\n    // differ in less than 8 bits.\n    if (bestDifference <= 3) {\n        return bestVersion;\n    }\n}\nfunction readFormatInformation(matrix) {\n    var topLeftFormatInfoBits = 0;\n    for (var x = 0; x <= 8; x++) {\n        if (x !== 6) { // Skip timing pattern bit\n            topLeftFormatInfoBits = pushBit(matrix.get(x, 8), topLeftFormatInfoBits);\n        }\n    }\n    for (var y = 7; y >= 0; y--) {\n        if (y !== 6) { // Skip timing pattern bit\n            topLeftFormatInfoBits = pushBit(matrix.get(8, y), topLeftFormatInfoBits);\n        }\n    }\n    var dimension = matrix.height;\n    var topRightBottomRightFormatInfoBits = 0;\n    for (var y = dimension - 1; y >= dimension - 7; y--) { // bottom left\n        topRightBottomRightFormatInfoBits = pushBit(matrix.get(8, y), topRightBottomRightFormatInfoBits);\n    }\n    for (var x = dimension - 8; x < dimension; x++) { // top right\n        topRightBottomRightFormatInfoBits = pushBit(matrix.get(x, 8), topRightBottomRightFormatInfoBits);\n    }\n    var bestDifference = Infinity;\n    var bestFormatInfo = null;\n    for (var _i = 0, FORMAT_INFO_TABLE_1 = FORMAT_INFO_TABLE; _i < FORMAT_INFO_TABLE_1.length; _i++) {\n        var _a = FORMAT_INFO_TABLE_1[_i], bits = _a.bits, formatInfo = _a.formatInfo;\n        if (bits === topLeftFormatInfoBits || bits === topRightBottomRightFormatInfoBits) {\n            return formatInfo;\n        }\n        var difference = numBitsDiffering(topLeftFormatInfoBits, bits);\n        if (difference < bestDifference) {\n            bestFormatInfo = formatInfo;\n            bestDifference = difference;\n        }\n        if (topLeftFormatInfoBits !== topRightBottomRightFormatInfoBits) { // also try the other option\n            difference = numBitsDiffering(topRightBottomRightFormatInfoBits, bits);\n            if (difference < bestDifference) {\n                bestFormatInfo = formatInfo;\n                bestDifference = difference;\n            }\n        }\n    }\n    // Hamming distance of the 32 masked codes is 7, by construction, so <= 3 bits differing means we found a match\n    if (bestDifference <= 3) {\n        return bestFormatInfo;\n    }\n    return null;\n}\nfunction getDataBlocks(codewords, version, ecLevel) {\n    var ecInfo = version.errorCorrectionLevels[ecLevel];\n    var dataBlocks = [];\n    var totalCodewords = 0;\n    ecInfo.ecBlocks.forEach(function (block) {\n        for (var i = 0; i < block.numBlocks; i++) {\n            dataBlocks.push({ numDataCodewords: block.dataCodewordsPerBlock, codewords: [] });\n            totalCodewords += block.dataCodewordsPerBlock + ecInfo.ecCodewordsPerBlock;\n        }\n    });\n    // In some cases the QR code will be malformed enough that we pull off more or less than we should.\n    // If we pull off less there's nothing we can do.\n    // If we pull off more we can safely truncate\n    if (codewords.length < totalCodewords) {\n        return null;\n    }\n    codewords = codewords.slice(0, totalCodewords);\n    var shortBlockSize = ecInfo.ecBlocks[0].dataCodewordsPerBlock;\n    // Pull codewords to fill the blocks up to the minimum size\n    for (var i = 0; i < shortBlockSize; i++) {\n        for (var _i = 0, dataBlocks_1 = dataBlocks; _i < dataBlocks_1.length; _i++) {\n            var dataBlock = dataBlocks_1[_i];\n            dataBlock.codewords.push(codewords.shift());\n        }\n    }\n    // If there are any large blocks, pull codewords to fill the last element of those\n    if (ecInfo.ecBlocks.length > 1) {\n        var smallBlockCount = ecInfo.ecBlocks[0].numBlocks;\n        var largeBlockCount = ecInfo.ecBlocks[1].numBlocks;\n        for (var i = 0; i < largeBlockCount; i++) {\n            dataBlocks[smallBlockCount + i].codewords.push(codewords.shift());\n        }\n    }\n    // Add the rest of the codewords to the blocks. These are the error correction codewords.\n    while (codewords.length > 0) {\n        for (var _a = 0, dataBlocks_2 = dataBlocks; _a < dataBlocks_2.length; _a++) {\n            var dataBlock = dataBlocks_2[_a];\n            dataBlock.codewords.push(codewords.shift());\n        }\n    }\n    return dataBlocks;\n}\nfunction decodeMatrix(matrix) {\n    var version = readVersion(matrix);\n    if (!version) {\n        return null;\n    }\n    var formatInfo = readFormatInformation(matrix);\n    if (!formatInfo) {\n        return null;\n    }\n    var codewords = readCodewords(matrix, version, formatInfo);\n    var dataBlocks = getDataBlocks(codewords, version, formatInfo.errorCorrectionLevel);\n    if (!dataBlocks) {\n        return null;\n    }\n    // Count total number of data bytes\n    var totalBytes = dataBlocks.reduce(function (a, b) { return a + b.numDataCodewords; }, 0);\n    var resultBytes = new Uint8ClampedArray(totalBytes);\n    var resultIndex = 0;\n    for (var _i = 0, dataBlocks_3 = dataBlocks; _i < dataBlocks_3.length; _i++) {\n        var dataBlock = dataBlocks_3[_i];\n        var correctedBytes = reedsolomon_1.decode(dataBlock.codewords, dataBlock.codewords.length - dataBlock.numDataCodewords);\n        if (!correctedBytes) {\n            return null;\n        }\n        for (var i = 0; i < dataBlock.numDataCodewords; i++) {\n            resultBytes[resultIndex++] = correctedBytes[i];\n        }\n    }\n    try {\n        return decodeData_1.decode(resultBytes, version.versionNumber);\n    }\n    catch (_a) {\n        return null;\n    }\n}\nfunction decode(matrix) {\n    if (matrix == null) {\n        return null;\n    }\n    var result = decodeMatrix(matrix);\n    if (result) {\n        return result;\n    }\n    // Decoding didn't work, try mirroring the QR across the topLeft -> bottomRight line.\n    for (var x = 0; x < matrix.width; x++) {\n        for (var y = x + 1; y < matrix.height; y++) {\n            if (matrix.get(x, y) !== matrix.get(y, x)) {\n                matrix.set(x, y, !matrix.get(x, y));\n                matrix.set(y, x, !matrix.get(y, x));\n            }\n        }\n    }\n    return decodeMatrix(matrix);\n}\nexports.decode = decode;\n\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\n// tslint:disable:no-bitwise\nvar BitStream_1 = __webpack_require__(7);\nvar shiftJISTable_1 = __webpack_require__(8);\nvar Mode;\n(function (Mode) {\n    Mode[\"Numeric\"] = \"numeric\";\n    Mode[\"Alphanumeric\"] = \"alphanumeric\";\n    Mode[\"Byte\"] = \"byte\";\n    Mode[\"Kanji\"] = \"kanji\";\n    Mode[\"ECI\"] = \"eci\";\n})(Mode = exports.Mode || (exports.Mode = {}));\nvar ModeByte;\n(function (ModeByte) {\n    ModeByte[ModeByte[\"Terminator\"] = 0] = \"Terminator\";\n    ModeByte[ModeByte[\"Numeric\"] = 1] = \"Numeric\";\n    ModeByte[ModeByte[\"Alphanumeric\"] = 2] = \"Alphanumeric\";\n    ModeByte[ModeByte[\"Byte\"] = 4] = \"Byte\";\n    ModeByte[ModeByte[\"Kanji\"] = 8] = \"Kanji\";\n    ModeByte[ModeByte[\"ECI\"] = 7] = \"ECI\";\n    // StructuredAppend = 0x3,\n    // FNC1FirstPosition = 0x5,\n    // FNC1SecondPosition = 0x9,\n})(ModeByte || (ModeByte = {}));\nfunction decodeNumeric(stream, size) {\n    var bytes = [];\n    var text = \"\";\n    var characterCountSize = [10, 12, 14][size];\n    var length = stream.readBits(characterCountSize);\n    // Read digits in groups of 3\n    while (length >= 3) {\n        var num = stream.readBits(10);\n        if (num >= 1000) {\n            throw new Error(\"Invalid numeric value above 999\");\n        }\n        var a = Math.floor(num / 100);\n        var b = Math.floor(num / 10) % 10;\n        var c = num % 10;\n        bytes.push(48 + a, 48 + b, 48 + c);\n        text += a.toString() + b.toString() + c.toString();\n        length -= 3;\n    }\n    // If the number of digits aren't a multiple of 3, the remaining digits are special cased.\n    if (length === 2) {\n        var num = stream.readBits(7);\n        if (num >= 100) {\n            throw new Error(\"Invalid numeric value above 99\");\n        }\n        var a = Math.floor(num / 10);\n        var b = num % 10;\n        bytes.push(48 + a, 48 + b);\n        text += a.toString() + b.toString();\n    }\n    else if (length === 1) {\n        var num = stream.readBits(4);\n        if (num >= 10) {\n            throw new Error(\"Invalid numeric value above 9\");\n        }\n        bytes.push(48 + num);\n        text += num.toString();\n    }\n    return { bytes: bytes, text: text };\n}\nvar AlphanumericCharacterCodes = [\n    \"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\",\n    \"9\", \"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\",\n    \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\",\n    \"R\", \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\",\n    \" \", \"$\", \"%\", \"*\", \"+\", \"-\", \".\", \"/\", \":\",\n];\nfunction decodeAlphanumeric(stream, size) {\n    var bytes = [];\n    var text = \"\";\n    var characterCountSize = [9, 11, 13][size];\n    var length = stream.readBits(characterCountSize);\n    while (length >= 2) {\n        var v = stream.readBits(11);\n        var a = Math.floor(v / 45);\n        var b = v % 45;\n        bytes.push(AlphanumericCharacterCodes[a].charCodeAt(0), AlphanumericCharacterCodes[b].charCodeAt(0));\n        text += AlphanumericCharacterCodes[a] + AlphanumericCharacterCodes[b];\n        length -= 2;\n    }\n    if (length === 1) {\n        var a = stream.readBits(6);\n        bytes.push(AlphanumericCharacterCodes[a].charCodeAt(0));\n        text += AlphanumericCharacterCodes[a];\n    }\n    return { bytes: bytes, text: text };\n}\nfunction decodeByte(stream, size) {\n    var bytes = [];\n    var text = \"\";\n    var characterCountSize = [8, 16, 16][size];\n    var length = stream.readBits(characterCountSize);\n    for (var i = 0; i < length; i++) {\n        var b = stream.readBits(8);\n        bytes.push(b);\n    }\n    try {\n        text += decodeURIComponent(bytes.map(function (b) { return \"%\" + (\"0\" + b.toString(16)).substr(-2); }).join(\"\"));\n    }\n    catch (_a) {\n        // failed to decode\n    }\n    return { bytes: bytes, text: text };\n}\nfunction decodeKanji(stream, size) {\n    var bytes = [];\n    var text = \"\";\n    var characterCountSize = [8, 10, 12][size];\n    var length = stream.readBits(characterCountSize);\n    for (var i = 0; i < length; i++) {\n        var k = stream.readBits(13);\n        var c = (Math.floor(k / 0xC0) << 8) | (k % 0xC0);\n        if (c < 0x1F00) {\n            c += 0x8140;\n        }\n        else {\n            c += 0xC140;\n        }\n        bytes.push(c >> 8, c & 0xFF);\n        text += String.fromCharCode(shiftJISTable_1.shiftJISTable[c]);\n    }\n    return { bytes: bytes, text: text };\n}\nfunction decode(data, version) {\n    var _a, _b, _c, _d;\n    var stream = new BitStream_1.BitStream(data);\n    // There are 3 'sizes' based on the version. 1-9 is small (0), 10-26 is medium (1) and 27-40 is large (2).\n    var size = version <= 9 ? 0 : version <= 26 ? 1 : 2;\n    var result = {\n        text: \"\",\n        bytes: [],\n        chunks: [],\n        version: version,\n    };\n    while (stream.available() >= 4) {\n        var mode = stream.readBits(4);\n        if (mode === ModeByte.Terminator) {\n            return result;\n        }\n        else if (mode === ModeByte.ECI) {\n            if (stream.readBits(1) === 0) {\n                result.chunks.push({\n                    type: Mode.ECI,\n                    assignmentNumber: stream.readBits(7),\n                });\n            }\n            else if (stream.readBits(1) === 0) {\n                result.chunks.push({\n                    type: Mode.ECI,\n                    assignmentNumber: stream.readBits(14),\n                });\n            }\n            else if (stream.readBits(1) === 0) {\n                result.chunks.push({\n                    type: Mode.ECI,\n                    assignmentNumber: stream.readBits(21),\n                });\n            }\n            else {\n                // ECI data seems corrupted\n                result.chunks.push({\n                    type: Mode.ECI,\n                    assignmentNumber: -1,\n                });\n            }\n        }\n        else if (mode === ModeByte.Numeric) {\n            var numericResult = decodeNumeric(stream, size);\n            result.text += numericResult.text;\n            (_a = result.bytes).push.apply(_a, numericResult.bytes);\n            result.chunks.push({\n                type: Mode.Numeric,\n                text: numericResult.text,\n            });\n        }\n        else if (mode === ModeByte.Alphanumeric) {\n            var alphanumericResult = decodeAlphanumeric(stream, size);\n            result.text += alphanumericResult.text;\n            (_b = result.bytes).push.apply(_b, alphanumericResult.bytes);\n            result.chunks.push({\n                type: Mode.Alphanumeric,\n                text: alphanumericResult.text,\n            });\n        }\n        else if (mode === ModeByte.Byte) {\n            var byteResult = decodeByte(stream, size);\n            result.text += byteResult.text;\n            (_c = result.bytes).push.apply(_c, byteResult.bytes);\n            result.chunks.push({\n                type: Mode.Byte,\n                bytes: byteResult.bytes,\n                text: byteResult.text,\n            });\n        }\n        else if (mode === ModeByte.Kanji) {\n            var kanjiResult = decodeKanji(stream, size);\n            result.text += kanjiResult.text;\n            (_d = result.bytes).push.apply(_d, kanjiResult.bytes);\n            result.chunks.push({\n                type: Mode.Kanji,\n                bytes: kanjiResult.bytes,\n                text: kanjiResult.text,\n            });\n        }\n    }\n    // If there is no data left, or the remaining bits are all 0, then that counts as a termination marker\n    if (stream.available() === 0 || stream.readBits(stream.available()) === 0) {\n        return result;\n    }\n}\nexports.decode = decode;\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n// tslint:disable:no-bitwise\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar BitStream = /** @class */ (function () {\n    function BitStream(bytes) {\n        this.byteOffset = 0;\n        this.bitOffset = 0;\n        this.bytes = bytes;\n    }\n    BitStream.prototype.readBits = function (numBits) {\n        if (numBits < 1 || numBits > 32 || numBits > this.available()) {\n            throw new Error(\"Cannot read \" + numBits.toString() + \" bits\");\n        }\n        var result = 0;\n        // First, read remainder from current byte\n        if (this.bitOffset > 0) {\n            var bitsLeft = 8 - this.bitOffset;\n            var toRead = numBits < bitsLeft ? numBits : bitsLeft;\n            var bitsToNotRead = bitsLeft - toRead;\n            var mask = (0xFF >> (8 - toRead)) << bitsToNotRead;\n            result = (this.bytes[this.byteOffset] & mask) >> bitsToNotRead;\n            numBits -= toRead;\n            this.bitOffset += toRead;\n            if (this.bitOffset === 8) {\n                this.bitOffset = 0;\n                this.byteOffset++;\n            }\n        }\n        // Next read whole bytes\n        if (numBits > 0) {\n            while (numBits >= 8) {\n                result = (result << 8) | (this.bytes[this.byteOffset] & 0xFF);\n                this.byteOffset++;\n                numBits -= 8;\n            }\n            // Finally read a partial byte\n            if (numBits > 0) {\n                var bitsToNotRead = 8 - numBits;\n                var mask = (0xFF >> bitsToNotRead) << bitsToNotRead;\n                result = (result << numBits) | ((this.bytes[this.byteOffset] & mask) >> bitsToNotRead);\n                this.bitOffset += numBits;\n            }\n        }\n        return result;\n    };\n    BitStream.prototype.available = function () {\n        return 8 * (this.bytes.length - this.byteOffset) - this.bitOffset;\n    };\n    return BitStream;\n}());\nexports.BitStream = BitStream;\n\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.shiftJISTable = {\n    0x20: 0x0020,\n    0x21: 0x0021,\n    0x22: 0x0022,\n    0x23: 0x0023,\n    0x24: 0x0024,\n    0x25: 0x0025,\n    0x26: 0x0026,\n    0x27: 0x0027,\n    0x28: 0x0028,\n    0x29: 0x0029,\n    0x2A: 0x002A,\n    0x2B: 0x002B,\n    0x2C: 0x002C,\n    0x2D: 0x002D,\n    0x2E: 0x002E,\n    0x2F: 0x002F,\n    0x30: 0x0030,\n    0x31: 0x0031,\n    0x32: 0x0032,\n    0x33: 0x0033,\n    0x34: 0x0034,\n    0x35: 0x0035,\n    0x36: 0x0036,\n    0x37: 0x0037,\n    0x38: 0x0038,\n    0x39: 0x0039,\n    0x3A: 0x003A,\n    0x3B: 0x003B,\n    0x3C: 0x003C,\n    0x3D: 0x003D,\n    0x3E: 0x003E,\n    0x3F: 0x003F,\n    0x40: 0x0040,\n    0x41: 0x0041,\n    0x42: 0x0042,\n    0x43: 0x0043,\n    0x44: 0x0044,\n    0x45: 0x0045,\n    0x46: 0x0046,\n    0x47: 0x0047,\n    0x48: 0x0048,\n    0x49: 0x0049,\n    0x4A: 0x004A,\n    0x4B: 0x004B,\n    0x4C: 0x004C,\n    0x4D: 0x004D,\n    0x4E: 0x004E,\n    0x4F: 0x004F,\n    0x50: 0x0050,\n    0x51: 0x0051,\n    0x52: 0x0052,\n    0x53: 0x0053,\n    0x54: 0x0054,\n    0x55: 0x0055,\n    0x56: 0x0056,\n    0x57: 0x0057,\n    0x58: 0x0058,\n    0x59: 0x0059,\n    0x5A: 0x005A,\n    0x5B: 0x005B,\n    0x5C: 0x00A5,\n    0x5D: 0x005D,\n    0x5E: 0x005E,\n    0x5F: 0x005F,\n    0x60: 0x0060,\n    0x61: 0x0061,\n    0x62: 0x0062,\n    0x63: 0x0063,\n    0x64: 0x0064,\n    0x65: 0x0065,\n    0x66: 0x0066,\n    0x67: 0x0067,\n    0x68: 0x0068,\n    0x69: 0x0069,\n    0x6A: 0x006A,\n    0x6B: 0x006B,\n    0x6C: 0x006C,\n    0x6D: 0x006D,\n    0x6E: 0x006E,\n    0x6F: 0x006F,\n    0x70: 0x0070,\n    0x71: 0x0071,\n    0x72: 0x0072,\n    0x73: 0x0073,\n    0x74: 0x0074,\n    0x75: 0x0075,\n    0x76: 0x0076,\n    0x77: 0x0077,\n    0x78: 0x0078,\n    0x79: 0x0079,\n    0x7A: 0x007A,\n    0x7B: 0x007B,\n    0x7C: 0x007C,\n    0x7D: 0x007D,\n    0x7E: 0x203E,\n    0x8140: 0x3000,\n    0x8141: 0x3001,\n    0x8142: 0x3002,\n    0x8143: 0xFF0C,\n    0x8144: 0xFF0E,\n    0x8145: 0x30FB,\n    0x8146: 0xFF1A,\n    0x8147: 0xFF1B,\n    0x8148: 0xFF1F,\n    0x8149: 0xFF01,\n    0x814A: 0x309B,\n    0x814B: 0x309C,\n    0x814C: 0x00B4,\n    0x814D: 0xFF40,\n    0x814E: 0x00A8,\n    0x814F: 0xFF3E,\n    0x8150: 0xFFE3,\n    0x8151: 0xFF3F,\n    0x8152: 0x30FD,\n    0x8153: 0x30FE,\n    0x8154: 0x309D,\n    0x8155: 0x309E,\n    0x8156: 0x3003,\n    0x8157: 0x4EDD,\n    0x8158: 0x3005,\n    0x8159: 0x3006,\n    0x815A: 0x3007,\n    0x815B: 0x30FC,\n    0x815C: 0x2015,\n    0x815D: 0x2010,\n    0x815E: 0xFF0F,\n    0x815F: 0x005C,\n    0x8160: 0x301C,\n    0x8161: 0x2016,\n    0x8162: 0xFF5C,\n    0x8163: 0x2026,\n    0x8164: 0x2025,\n    0x8165: 0x2018,\n    0x8166: 0x2019,\n    0x8167: 0x201C,\n    0x8168: 0x201D,\n    0x8169: 0xFF08,\n    0x816A: 0xFF09,\n    0x816B: 0x3014,\n    0x816C: 0x3015,\n    0x816D: 0xFF3B,\n    0x816E: 0xFF3D,\n    0x816F: 0xFF5B,\n    0x8170: 0xFF5D,\n    0x8171: 0x3008,\n    0x8172: 0x3009,\n    0x8173: 0x300A,\n    0x8174: 0x300B,\n    0x8175: 0x300C,\n    0x8176: 0x300D,\n    0x8177: 0x300E,\n    0x8178: 0x300F,\n    0x8179: 0x3010,\n    0x817A: 0x3011,\n    0x817B: 0xFF0B,\n    0x817C: 0x2212,\n    0x817D: 0x00B1,\n    0x817E: 0x00D7,\n    0x8180: 0x00F7,\n    0x8181: 0xFF1D,\n    0x8182: 0x2260,\n    0x8183: 0xFF1C,\n    0x8184: 0xFF1E,\n    0x8185: 0x2266,\n    0x8186: 0x2267,\n    0x8187: 0x221E,\n    0x8188: 0x2234,\n    0x8189: 0x2642,\n    0x818A: 0x2640,\n    0x818B: 0x00B0,\n    0x818C: 0x2032,\n    0x818D: 0x2033,\n    0x818E: 0x2103,\n    0x818F: 0xFFE5,\n    0x8190: 0xFF04,\n    0x8191: 0x00A2,\n    0x8192: 0x00A3,\n    0x8193: 0xFF05,\n    0x8194: 0xFF03,\n    0x8195: 0xFF06,\n    0x8196: 0xFF0A,\n    0x8197: 0xFF20,\n    0x8198: 0x00A7,\n    0x8199: 0x2606,\n    0x819A: 0x2605,\n    0x819B: 0x25CB,\n    0x819C: 0x25CF,\n    0x819D: 0x25CE,\n    0x819E: 0x25C7,\n    0x819F: 0x25C6,\n    0x81A0: 0x25A1,\n    0x81A1: 0x25A0,\n    0x81A2: 0x25B3,\n    0x81A3: 0x25B2,\n    0x81A4: 0x25BD,\n    0x81A5: 0x25BC,\n    0x81A6: 0x203B,\n    0x81A7: 0x3012,\n    0x81A8: 0x2192,\n    0x81A9: 0x2190,\n    0x81AA: 0x2191,\n    0x81AB: 0x2193,\n    0x81AC: 0x3013,\n    0x81B8: 0x2208,\n    0x81B9: 0x220B,\n    0x81BA: 0x2286,\n    0x81BB: 0x2287,\n    0x81BC: 0x2282,\n    0x81BD: 0x2283,\n    0x81BE: 0x222A,\n    0x81BF: 0x2229,\n    0x81C8: 0x2227,\n    0x81C9: 0x2228,\n    0x81CA: 0x00AC,\n    0x81CB: 0x21D2,\n    0x81CC: 0x21D4,\n    0x81CD: 0x2200,\n    0x81CE: 0x2203,\n    0x81DA: 0x2220,\n    0x81DB: 0x22A5,\n    0x81DC: 0x2312,\n    0x81DD: 0x2202,\n    0x81DE: 0x2207,\n    0x81DF: 0x2261,\n    0x81E0: 0x2252,\n    0x81E1: 0x226A,\n    0x81E2: 0x226B,\n    0x81E3: 0x221A,\n    0x81E4: 0x223D,\n    0x81E5: 0x221D,\n    0x81E6: 0x2235,\n    0x81E7: 0x222B,\n    0x81E8: 0x222C,\n    0x81F0: 0x212B,\n    0x81F1: 0x2030,\n    0x81F2: 0x266F,\n    0x81F3: 0x266D,\n    0x81F4: 0x266A,\n    0x81F5: 0x2020,\n    0x81F6: 0x2021,\n    0x81F7: 0x00B6,\n    0x81FC: 0x25EF,\n    0x824F: 0xFF10,\n    0x8250: 0xFF11,\n    0x8251: 0xFF12,\n    0x8252: 0xFF13,\n    0x8253: 0xFF14,\n    0x8254: 0xFF15,\n    0x8255: 0xFF16,\n    0x8256: 0xFF17,\n    0x8257: 0xFF18,\n    0x8258: 0xFF19,\n    0x8260: 0xFF21,\n    0x8261: 0xFF22,\n    0x8262: 0xFF23,\n    0x8263: 0xFF24,\n    0x8264: 0xFF25,\n    0x8265: 0xFF26,\n    0x8266: 0xFF27,\n    0x8267: 0xFF28,\n    0x8268: 0xFF29,\n    0x8269: 0xFF2A,\n    0x826A: 0xFF2B,\n    0x826B: 0xFF2C,\n    0x826C: 0xFF2D,\n    0x826D: 0xFF2E,\n    0x826E: 0xFF2F,\n    0x826F: 0xFF30,\n    0x8270: 0xFF31,\n    0x8271: 0xFF32,\n    0x8272: 0xFF33,\n    0x8273: 0xFF34,\n    0x8274: 0xFF35,\n    0x8275: 0xFF36,\n    0x8276: 0xFF37,\n    0x8277: 0xFF38,\n    0x8278: 0xFF39,\n    0x8279: 0xFF3A,\n    0x8281: 0xFF41,\n    0x8282: 0xFF42,\n    0x8283: 0xFF43,\n    0x8284: 0xFF44,\n    0x8285: 0xFF45,\n    0x8286: 0xFF46,\n    0x8287: 0xFF47,\n    0x8288: 0xFF48,\n    0x8289: 0xFF49,\n    0x828A: 0xFF4A,\n    0x828B: 0xFF4B,\n    0x828C: 0xFF4C,\n    0x828D: 0xFF4D,\n    0x828E: 0xFF4E,\n    0x828F: 0xFF4F,\n    0x8290: 0xFF50,\n    0x8291: 0xFF51,\n    0x8292: 0xFF52,\n    0x8293: 0xFF53,\n    0x8294: 0xFF54,\n    0x8295: 0xFF55,\n    0x8296: 0xFF56,\n    0x8297: 0xFF57,\n    0x8298: 0xFF58,\n    0x8299: 0xFF59,\n    0x829A: 0xFF5A,\n    0x829F: 0x3041,\n    0x82A0: 0x3042,\n    0x82A1: 0x3043,\n    0x82A2: 0x3044,\n    0x82A3: 0x3045,\n    0x82A4: 0x3046,\n    0x82A5: 0x3047,\n    0x82A6: 0x3048,\n    0x82A7: 0x3049,\n    0x82A8: 0x304A,\n    0x82A9: 0x304B,\n    0x82AA: 0x304C,\n    0x82AB: 0x304D,\n    0x82AC: 0x304E,\n    0x82AD: 0x304F,\n    0x82AE: 0x3050,\n    0x82AF: 0x3051,\n    0x82B0: 0x3052,\n    0x82B1: 0x3053,\n    0x82B2: 0x3054,\n    0x82B3: 0x3055,\n    0x82B4: 0x3056,\n    0x82B5: 0x3057,\n    0x82B6: 0x3058,\n    0x82B7: 0x3059,\n    0x82B8: 0x305A,\n    0x82B9: 0x305B,\n    0x82BA: 0x305C,\n    0x82BB: 0x305D,\n    0x82BC: 0x305E,\n    0x82BD: 0x305F,\n    0x82BE: 0x3060,\n    0x82BF: 0x3061,\n    0x82C0: 0x3062,\n    0x82C1: 0x3063,\n    0x82C2: 0x3064,\n    0x82C3: 0x3065,\n    0x82C4: 0x3066,\n    0x82C5: 0x3067,\n    0x82C6: 0x3068,\n    0x82C7: 0x3069,\n    0x82C8: 0x306A,\n    0x82C9: 0x306B,\n    0x82CA: 0x306C,\n    0x82CB: 0x306D,\n    0x82CC: 0x306E,\n    0x82CD: 0x306F,\n    0x82CE: 0x3070,\n    0x82CF: 0x3071,\n    0x82D0: 0x3072,\n    0x82D1: 0x3073,\n    0x82D2: 0x3074,\n    0x82D3: 0x3075,\n    0x82D4: 0x3076,\n    0x82D5: 0x3077,\n    0x82D6: 0x3078,\n    0x82D7: 0x3079,\n    0x82D8: 0x307A,\n    0x82D9: 0x307B,\n    0x82DA: 0x307C,\n    0x82DB: 0x307D,\n    0x82DC: 0x307E,\n    0x82DD: 0x307F,\n    0x82DE: 0x3080,\n    0x82DF: 0x3081,\n    0x82E0: 0x3082,\n    0x82E1: 0x3083,\n    0x82E2: 0x3084,\n    0x82E3: 0x3085,\n    0x82E4: 0x3086,\n    0x82E5: 0x3087,\n    0x82E6: 0x3088,\n    0x82E7: 0x3089,\n    0x82E8: 0x308A,\n    0x82E9: 0x308B,\n    0x82EA: 0x308C,\n    0x82EB: 0x308D,\n    0x82EC: 0x308E,\n    0x82ED: 0x308F,\n    0x82EE: 0x3090,\n    0x82EF: 0x3091,\n    0x82F0: 0x3092,\n    0x82F1: 0x3093,\n    0x8340: 0x30A1,\n    0x8341: 0x30A2,\n    0x8342: 0x30A3,\n    0x8343: 0x30A4,\n    0x8344: 0x30A5,\n    0x8345: 0x30A6,\n    0x8346: 0x30A7,\n    0x8347: 0x30A8,\n    0x8348: 0x30A9,\n    0x8349: 0x30AA,\n    0x834A: 0x30AB,\n    0x834B: 0x30AC,\n    0x834C: 0x30AD,\n    0x834D: 0x30AE,\n    0x834E: 0x30AF,\n    0x834F: 0x30B0,\n    0x8350: 0x30B1,\n    0x8351: 0x30B2,\n    0x8352: 0x30B3,\n    0x8353: 0x30B4,\n    0x8354: 0x30B5,\n    0x8355: 0x30B6,\n    0x8356: 0x30B7,\n    0x8357: 0x30B8,\n    0x8358: 0x30B9,\n    0x8359: 0x30BA,\n    0x835A: 0x30BB,\n    0x835B: 0x30BC,\n    0x835C: 0x30BD,\n    0x835D: 0x30BE,\n    0x835E: 0x30BF,\n    0x835F: 0x30C0,\n    0x8360: 0x30C1,\n    0x8361: 0x30C2,\n    0x8362: 0x30C3,\n    0x8363: 0x30C4,\n    0x8364: 0x30C5,\n    0x8365: 0x30C6,\n    0x8366: 0x30C7,\n    0x8367: 0x30C8,\n    0x8368: 0x30C9,\n    0x8369: 0x30CA,\n    0x836A: 0x30CB,\n    0x836B: 0x30CC,\n    0x836C: 0x30CD,\n    0x836D: 0x30CE,\n    0x836E: 0x30CF,\n    0x836F: 0x30D0,\n    0x8370: 0x30D1,\n    0x8371: 0x30D2,\n    0x8372: 0x30D3,\n    0x8373: 0x30D4,\n    0x8374: 0x30D5,\n    0x8375: 0x30D6,\n    0x8376: 0x30D7,\n    0x8377: 0x30D8,\n    0x8378: 0x30D9,\n    0x8379: 0x30DA,\n    0x837A: 0x30DB,\n    0x837B: 0x30DC,\n    0x837C: 0x30DD,\n    0x837D: 0x30DE,\n    0x837E: 0x30DF,\n    0x8380: 0x30E0,\n    0x8381: 0x30E1,\n    0x8382: 0x30E2,\n    0x8383: 0x30E3,\n    0x8384: 0x30E4,\n    0x8385: 0x30E5,\n    0x8386: 0x30E6,\n    0x8387: 0x30E7,\n    0x8388: 0x30E8,\n    0x8389: 0x30E9,\n    0x838A: 0x30EA,\n    0x838B: 0x30EB,\n    0x838C: 0x30EC,\n    0x838D: 0x30ED,\n    0x838E: 0x30EE,\n    0x838F: 0x30EF,\n    0x8390: 0x30F0,\n    0x8391: 0x30F1,\n    0x8392: 0x30F2,\n    0x8393: 0x30F3,\n    0x8394: 0x30F4,\n    0x8395: 0x30F5,\n    0x8396: 0x30F6,\n    0x839F: 0x0391,\n    0x83A0: 0x0392,\n    0x83A1: 0x0393,\n    0x83A2: 0x0394,\n    0x83A3: 0x0395,\n    0x83A4: 0x0396,\n    0x83A5: 0x0397,\n    0x83A6: 0x0398,\n    0x83A7: 0x0399,\n    0x83A8: 0x039A,\n    0x83A9: 0x039B,\n    0x83AA: 0x039C,\n    0x83AB: 0x039D,\n    0x83AC: 0x039E,\n    0x83AD: 0x039F,\n    0x83AE: 0x03A0,\n    0x83AF: 0x03A1,\n    0x83B0: 0x03A3,\n    0x83B1: 0x03A4,\n    0x83B2: 0x03A5,\n    0x83B3: 0x03A6,\n    0x83B4: 0x03A7,\n    0x83B5: 0x03A8,\n    0x83B6: 0x03A9,\n    0x83BF: 0x03B1,\n    0x83C0: 0x03B2,\n    0x83C1: 0x03B3,\n    0x83C2: 0x03B4,\n    0x83C3: 0x03B5,\n    0x83C4: 0x03B6,\n    0x83C5: 0x03B7,\n    0x83C6: 0x03B8,\n    0x83C7: 0x03B9,\n    0x83C8: 0x03BA,\n    0x83C9: 0x03BB,\n    0x83CA: 0x03BC,\n    0x83CB: 0x03BD,\n    0x83CC: 0x03BE,\n    0x83CD: 0x03BF,\n    0x83CE: 0x03C0,\n    0x83CF: 0x03C1,\n    0x83D0: 0x03C3,\n    0x83D1: 0x03C4,\n    0x83D2: 0x03C5,\n    0x83D3: 0x03C6,\n    0x83D4: 0x03C7,\n    0x83D5: 0x03C8,\n    0x83D6: 0x03C9,\n    0x8440: 0x0410,\n    0x8441: 0x0411,\n    0x8442: 0x0412,\n    0x8443: 0x0413,\n    0x8444: 0x0414,\n    0x8445: 0x0415,\n    0x8446: 0x0401,\n    0x8447: 0x0416,\n    0x8448: 0x0417,\n    0x8449: 0x0418,\n    0x844A: 0x0419,\n    0x844B: 0x041A,\n    0x844C: 0x041B,\n    0x844D: 0x041C,\n    0x844E: 0x041D,\n    0x844F: 0x041E,\n    0x8450: 0x041F,\n    0x8451: 0x0420,\n    0x8452: 0x0421,\n    0x8453: 0x0422,\n    0x8454: 0x0423,\n    0x8455: 0x0424,\n    0x8456: 0x0425,\n    0x8457: 0x0426,\n    0x8458: 0x0427,\n    0x8459: 0x0428,\n    0x845A: 0x0429,\n    0x845B: 0x042A,\n    0x845C: 0x042B,\n    0x845D: 0x042C,\n    0x845E: 0x042D,\n    0x845F: 0x042E,\n    0x8460: 0x042F,\n    0x8470: 0x0430,\n    0x8471: 0x0431,\n    0x8472: 0x0432,\n    0x8473: 0x0433,\n    0x8474: 0x0434,\n    0x8475: 0x0435,\n    0x8476: 0x0451,\n    0x8477: 0x0436,\n    0x8478: 0x0437,\n    0x8479: 0x0438,\n    0x847A: 0x0439,\n    0x847B: 0x043A,\n    0x847C: 0x043B,\n    0x847D: 0x043C,\n    0x847E: 0x043D,\n    0x8480: 0x043E,\n    0x8481: 0x043F,\n    0x8482: 0x0440,\n    0x8483: 0x0441,\n    0x8484: 0x0442,\n    0x8485: 0x0443,\n    0x8486: 0x0444,\n    0x8487: 0x0445,\n    0x8488: 0x0446,\n    0x8489: 0x0447,\n    0x848A: 0x0448,\n    0x848B: 0x0449,\n    0x848C: 0x044A,\n    0x848D: 0x044B,\n    0x848E: 0x044C,\n    0x848F: 0x044D,\n    0x8490: 0x044E,\n    0x8491: 0x044F,\n    0x849F: 0x2500,\n    0x84A0: 0x2502,\n    0x84A1: 0x250C,\n    0x84A2: 0x2510,\n    0x84A3: 0x2518,\n    0x84A4: 0x2514,\n    0x84A5: 0x251C,\n    0x84A6: 0x252C,\n    0x84A7: 0x2524,\n    0x84A8: 0x2534,\n    0x84A9: 0x253C,\n    0x84AA: 0x2501,\n    0x84AB: 0x2503,\n    0x84AC: 0x250F,\n    0x84AD: 0x2513,\n    0x84AE: 0x251B,\n    0x84AF: 0x2517,\n    0x84B0: 0x2523,\n    0x84B1: 0x2533,\n    0x84B2: 0x252B,\n    0x84B3: 0x253B,\n    0x84B4: 0x254B,\n    0x84B5: 0x2520,\n    0x84B6: 0x252F,\n    0x84B7: 0x2528,\n    0x84B8: 0x2537,\n    0x84B9: 0x253F,\n    0x84BA: 0x251D,\n    0x84BB: 0x2530,\n    0x84BC: 0x2525,\n    0x84BD: 0x2538,\n    0x84BE: 0x2542,\n    0x889F: 0x4E9C,\n    0x88A0: 0x5516,\n    0x88A1: 0x5A03,\n    0x88A2: 0x963F,\n    0x88A3: 0x54C0,\n    0x88A4: 0x611B,\n    0x88A5: 0x6328,\n    0x88A6: 0x59F6,\n    0x88A7: 0x9022,\n    0x88A8: 0x8475,\n    0x88A9: 0x831C,\n    0x88AA: 0x7A50,\n    0x88AB: 0x60AA,\n    0x88AC: 0x63E1,\n    0x88AD: 0x6E25,\n    0x88AE: 0x65ED,\n    0x88AF: 0x8466,\n    0x88B0: 0x82A6,\n    0x88B1: 0x9BF5,\n    0x88B2: 0x6893,\n    0x88B3: 0x5727,\n    0x88B4: 0x65A1,\n    0x88B5: 0x6271,\n    0x88B6: 0x5B9B,\n    0x88B7: 0x59D0,\n    0x88B8: 0x867B,\n    0x88B9: 0x98F4,\n    0x88BA: 0x7D62,\n    0x88BB: 0x7DBE,\n    0x88BC: 0x9B8E,\n    0x88BD: 0x6216,\n    0x88BE: 0x7C9F,\n    0x88BF: 0x88B7,\n    0x88C0: 0x5B89,\n    0x88C1: 0x5EB5,\n    0x88C2: 0x6309,\n    0x88C3: 0x6697,\n    0x88C4: 0x6848,\n    0x88C5: 0x95C7,\n    0x88C6: 0x978D,\n    0x88C7: 0x674F,\n    0x88C8: 0x4EE5,\n    0x88C9: 0x4F0A,\n    0x88CA: 0x4F4D,\n    0x88CB: 0x4F9D,\n    0x88CC: 0x5049,\n    0x88CD: 0x56F2,\n    0x88CE: 0x5937,\n    0x88CF: 0x59D4,\n    0x88D0: 0x5A01,\n    0x88D1: 0x5C09,\n    0x88D2: 0x60DF,\n    0x88D3: 0x610F,\n    0x88D4: 0x6170,\n    0x88D5: 0x6613,\n    0x88D6: 0x6905,\n    0x88D7: 0x70BA,\n    0x88D8: 0x754F,\n    0x88D9: 0x7570,\n    0x88DA: 0x79FB,\n    0x88DB: 0x7DAD,\n    0x88DC: 0x7DEF,\n    0x88DD: 0x80C3,\n    0x88DE: 0x840E,\n    0x88DF: 0x8863,\n    0x88E0: 0x8B02,\n    0x88E1: 0x9055,\n    0x88E2: 0x907A,\n    0x88E3: 0x533B,\n    0x88E4: 0x4E95,\n    0x88E5: 0x4EA5,\n    0x88E6: 0x57DF,\n    0x88E7: 0x80B2,\n    0x88E8: 0x90C1,\n    0x88E9: 0x78EF,\n    0x88EA: 0x4E00,\n    0x88EB: 0x58F1,\n    0x88EC: 0x6EA2,\n    0x88ED: 0x9038,\n    0x88EE: 0x7A32,\n    0x88EF: 0x8328,\n    0x88F0: 0x828B,\n    0x88F1: 0x9C2F,\n    0x88F2: 0x5141,\n    0x88F3: 0x5370,\n    0x88F4: 0x54BD,\n    0x88F5: 0x54E1,\n    0x88F6: 0x56E0,\n    0x88F7: 0x59FB,\n    0x88F8: 0x5F15,\n    0x88F9: 0x98F2,\n    0x88FA: 0x6DEB,\n    0x88FB: 0x80E4,\n    0x88FC: 0x852D,\n    0x8940: 0x9662,\n    0x8941: 0x9670,\n    0x8942: 0x96A0,\n    0x8943: 0x97FB,\n    0x8944: 0x540B,\n    0x8945: 0x53F3,\n    0x8946: 0x5B87,\n    0x8947: 0x70CF,\n    0x8948: 0x7FBD,\n    0x8949: 0x8FC2,\n    0x894A: 0x96E8,\n    0x894B: 0x536F,\n    0x894C: 0x9D5C,\n    0x894D: 0x7ABA,\n    0x894E: 0x4E11,\n    0x894F: 0x7893,\n    0x8950: 0x81FC,\n    0x8951: 0x6E26,\n    0x8952: 0x5618,\n    0x8953: 0x5504,\n    0x8954: 0x6B1D,\n    0x8955: 0x851A,\n    0x8956: 0x9C3B,\n    0x8957: 0x59E5,\n    0x8958: 0x53A9,\n    0x8959: 0x6D66,\n    0x895A: 0x74DC,\n    0x895B: 0x958F,\n    0x895C: 0x5642,\n    0x895D: 0x4E91,\n    0x895E: 0x904B,\n    0x895F: 0x96F2,\n    0x8960: 0x834F,\n    0x8961: 0x990C,\n    0x8962: 0x53E1,\n    0x8963: 0x55B6,\n    0x8964: 0x5B30,\n    0x8965: 0x5F71,\n    0x8966: 0x6620,\n    0x8967: 0x66F3,\n    0x8968: 0x6804,\n    0x8969: 0x6C38,\n    0x896A: 0x6CF3,\n    0x896B: 0x6D29,\n    0x896C: 0x745B,\n    0x896D: 0x76C8,\n    0x896E: 0x7A4E,\n    0x896F: 0x9834,\n    0x8970: 0x82F1,\n    0x8971: 0x885B,\n    0x8972: 0x8A60,\n    0x8973: 0x92ED,\n    0x8974: 0x6DB2,\n    0x8975: 0x75AB,\n    0x8976: 0x76CA,\n    0x8977: 0x99C5,\n    0x8978: 0x60A6,\n    0x8979: 0x8B01,\n    0x897A: 0x8D8A,\n    0x897B: 0x95B2,\n    0x897C: 0x698E,\n    0x897D: 0x53AD,\n    0x897E: 0x5186,\n    0x8980: 0x5712,\n    0x8981: 0x5830,\n    0x8982: 0x5944,\n    0x8983: 0x5BB4,\n    0x8984: 0x5EF6,\n    0x8985: 0x6028,\n    0x8986: 0x63A9,\n    0x8987: 0x63F4,\n    0x8988: 0x6CBF,\n    0x8989: 0x6F14,\n    0x898A: 0x708E,\n    0x898B: 0x7114,\n    0x898C: 0x7159,\n    0x898D: 0x71D5,\n    0x898E: 0x733F,\n    0x898F: 0x7E01,\n    0x8990: 0x8276,\n    0x8991: 0x82D1,\n    0x8992: 0x8597,\n    0x8993: 0x9060,\n    0x8994: 0x925B,\n    0x8995: 0x9D1B,\n    0x8996: 0x5869,\n    0x8997: 0x65BC,\n    0x8998: 0x6C5A,\n    0x8999: 0x7525,\n    0x899A: 0x51F9,\n    0x899B: 0x592E,\n    0x899C: 0x5965,\n    0x899D: 0x5F80,\n    0x899E: 0x5FDC,\n    0x899F: 0x62BC,\n    0x89A0: 0x65FA,\n    0x89A1: 0x6A2A,\n    0x89A2: 0x6B27,\n    0x89A3: 0x6BB4,\n    0x89A4: 0x738B,\n    0x89A5: 0x7FC1,\n    0x89A6: 0x8956,\n    0x89A7: 0x9D2C,\n    0x89A8: 0x9D0E,\n    0x89A9: 0x9EC4,\n    0x89AA: 0x5CA1,\n    0x89AB: 0x6C96,\n    0x89AC: 0x837B,\n    0x89AD: 0x5104,\n    0x89AE: 0x5C4B,\n    0x89AF: 0x61B6,\n    0x89B0: 0x81C6,\n    0x89B1: 0x6876,\n    0x89B2: 0x7261,\n    0x89B3: 0x4E59,\n    0x89B4: 0x4FFA,\n    0x89B5: 0x5378,\n    0x89B6: 0x6069,\n    0x89B7: 0x6E29,\n    0x89B8: 0x7A4F,\n    0x89B9: 0x97F3,\n    0x89BA: 0x4E0B,\n    0x89BB: 0x5316,\n    0x89BC: 0x4EEE,\n    0x89BD: 0x4F55,\n    0x89BE: 0x4F3D,\n    0x89BF: 0x4FA1,\n    0x89C0: 0x4F73,\n    0x89C1: 0x52A0,\n    0x89C2: 0x53EF,\n    0x89C3: 0x5609,\n    0x89C4: 0x590F,\n    0x89C5: 0x5AC1,\n    0x89C6: 0x5BB6,\n    0x89C7: 0x5BE1,\n    0x89C8: 0x79D1,\n    0x89C9: 0x6687,\n    0x89CA: 0x679C,\n    0x89CB: 0x67B6,\n    0x89CC: 0x6B4C,\n    0x89CD: 0x6CB3,\n    0x89CE: 0x706B,\n    0x89CF: 0x73C2,\n    0x89D0: 0x798D,\n    0x89D1: 0x79BE,\n    0x89D2: 0x7A3C,\n    0x89D3: 0x7B87,\n    0x89D4: 0x82B1,\n    0x89D5: 0x82DB,\n    0x89D6: 0x8304,\n    0x89D7: 0x8377,\n    0x89D8: 0x83EF,\n    0x89D9: 0x83D3,\n    0x89DA: 0x8766,\n    0x89DB: 0x8AB2,\n    0x89DC: 0x5629,\n    0x89DD: 0x8CA8,\n    0x89DE: 0x8FE6,\n    0x89DF: 0x904E,\n    0x89E0: 0x971E,\n    0x89E1: 0x868A,\n    0x89E2: 0x4FC4,\n    0x89E3: 0x5CE8,\n    0x89E4: 0x6211,\n    0x89E5: 0x7259,\n    0x89E6: 0x753B,\n    0x89E7: 0x81E5,\n    0x89E8: 0x82BD,\n    0x89E9: 0x86FE,\n    0x89EA: 0x8CC0,\n    0x89EB: 0x96C5,\n    0x89EC: 0x9913,\n    0x89ED: 0x99D5,\n    0x89EE: 0x4ECB,\n    0x89EF: 0x4F1A,\n    0x89F0: 0x89E3,\n    0x89F1: 0x56DE,\n    0x89F2: 0x584A,\n    0x89F3: 0x58CA,\n    0x89F4: 0x5EFB,\n    0x89F5: 0x5FEB,\n    0x89F6: 0x602A,\n    0x89F7: 0x6094,\n    0x89F8: 0x6062,\n    0x89F9: 0x61D0,\n    0x89FA: 0x6212,\n    0x89FB: 0x62D0,\n    0x89FC: 0x6539,\n    0x8A40: 0x9B41,\n    0x8A41: 0x6666,\n    0x8A42: 0x68B0,\n    0x8A43: 0x6D77,\n    0x8A44: 0x7070,\n    0x8A45: 0x754C,\n    0x8A46: 0x7686,\n    0x8A47: 0x7D75,\n    0x8A48: 0x82A5,\n    0x8A49: 0x87F9,\n    0x8A4A: 0x958B,\n    0x8A4B: 0x968E,\n    0x8A4C: 0x8C9D,\n    0x8A4D: 0x51F1,\n    0x8A4E: 0x52BE,\n    0x8A4F: 0x5916,\n    0x8A50: 0x54B3,\n    0x8A51: 0x5BB3,\n    0x8A52: 0x5D16,\n    0x8A53: 0x6168,\n    0x8A54: 0x6982,\n    0x8A55: 0x6DAF,\n    0x8A56: 0x788D,\n    0x8A57: 0x84CB,\n    0x8A58: 0x8857,\n    0x8A59: 0x8A72,\n    0x8A5A: 0x93A7,\n    0x8A5B: 0x9AB8,\n    0x8A5C: 0x6D6C,\n    0x8A5D: 0x99A8,\n    0x8A5E: 0x86D9,\n    0x8A5F: 0x57A3,\n    0x8A60: 0x67FF,\n    0x8A61: 0x86CE,\n    0x8A62: 0x920E,\n    0x8A63: 0x5283,\n    0x8A64: 0x5687,\n    0x8A65: 0x5404,\n    0x8A66: 0x5ED3,\n    0x8A67: 0x62E1,\n    0x8A68: 0x64B9,\n    0x8A69: 0x683C,\n    0x8A6A: 0x6838,\n    0x8A6B: 0x6BBB,\n    0x8A6C: 0x7372,\n    0x8A6D: 0x78BA,\n    0x8A6E: 0x7A6B,\n    0x8A6F: 0x899A,\n    0x8A70: 0x89D2,\n    0x8A71: 0x8D6B,\n    0x8A72: 0x8F03,\n    0x8A73: 0x90ED,\n    0x8A74: 0x95A3,\n    0x8A75: 0x9694,\n    0x8A76: 0x9769,\n    0x8A77: 0x5B66,\n    0x8A78: 0x5CB3,\n    0x8A79: 0x697D,\n    0x8A7A: 0x984D,\n    0x8A7B: 0x984E,\n    0x8A7C: 0x639B,\n    0x8A7D: 0x7B20,\n    0x8A7E: 0x6A2B,\n    0x8A80: 0x6A7F,\n    0x8A81: 0x68B6,\n    0x8A82: 0x9C0D,\n    0x8A83: 0x6F5F,\n    0x8A84: 0x5272,\n    0x8A85: 0x559D,\n    0x8A86: 0x6070,\n    0x8A87: 0x62EC,\n    0x8A88: 0x6D3B,\n    0x8A89: 0x6E07,\n    0x8A8A: 0x6ED1,\n    0x8A8B: 0x845B,\n    0x8A8C: 0x8910,\n    0x8A8D: 0x8F44,\n    0x8A8E: 0x4E14,\n    0x8A8F: 0x9C39,\n    0x8A90: 0x53F6,\n    0x8A91: 0x691B,\n    0x8A92: 0x6A3A,\n    0x8A93: 0x9784,\n    0x8A94: 0x682A,\n    0x8A95: 0x515C,\n    0x8A96: 0x7AC3,\n    0x8A97: 0x84B2,\n    0x8A98: 0x91DC,\n    0x8A99: 0x938C,\n    0x8A9A: 0x565B,\n    0x8A9B: 0x9D28,\n    0x8A9C: 0x6822,\n    0x8A9D: 0x8305,\n    0x8A9E: 0x8431,\n    0x8A9F: 0x7CA5,\n    0x8AA0: 0x5208,\n    0x8AA1: 0x82C5,\n    0x8AA2: 0x74E6,\n    0x8AA3: 0x4E7E,\n    0x8AA4: 0x4F83,\n    0x8AA5: 0x51A0,\n    0x8AA6: 0x5BD2,\n    0x8AA7: 0x520A,\n    0x8AA8: 0x52D8,\n    0x8AA9: 0x52E7,\n    0x8AAA: 0x5DFB,\n    0x8AAB: 0x559A,\n    0x8AAC: 0x582A,\n    0x8AAD: 0x59E6,\n    0x8AAE: 0x5B8C,\n    0x8AAF: 0x5B98,\n    0x8AB0: 0x5BDB,\n    0x8AB1: 0x5E72,\n    0x8AB2: 0x5E79,\n    0x8AB3: 0x60A3,\n    0x8AB4: 0x611F,\n    0x8AB5: 0x6163,\n    0x8AB6: 0x61BE,\n    0x8AB7: 0x63DB,\n    0x8AB8: 0x6562,\n    0x8AB9: 0x67D1,\n    0x8ABA: 0x6853,\n    0x8ABB: 0x68FA,\n    0x8ABC: 0x6B3E,\n    0x8ABD: 0x6B53,\n    0x8ABE: 0x6C57,\n    0x8ABF: 0x6F22,\n    0x8AC0: 0x6F97,\n    0x8AC1: 0x6F45,\n    0x8AC2: 0x74B0,\n    0x8AC3: 0x7518,\n    0x8AC4: 0x76E3,\n    0x8AC5: 0x770B,\n    0x8AC6: 0x7AFF,\n    0x8AC7: 0x7BA1,\n    0x8AC8: 0x7C21,\n    0x8AC9: 0x7DE9,\n    0x8ACA: 0x7F36,\n    0x8ACB: 0x7FF0,\n    0x8ACC: 0x809D,\n    0x8ACD: 0x8266,\n    0x8ACE: 0x839E,\n    0x8ACF: 0x89B3,\n    0x8AD0: 0x8ACC,\n    0x8AD1: 0x8CAB,\n    0x8AD2: 0x9084,\n    0x8AD3: 0x9451,\n    0x8AD4: 0x9593,\n    0x8AD5: 0x9591,\n    0x8AD6: 0x95A2,\n    0x8AD7: 0x9665,\n    0x8AD8: 0x97D3,\n    0x8AD9: 0x9928,\n    0x8ADA: 0x8218,\n    0x8ADB: 0x4E38,\n    0x8ADC: 0x542B,\n    0x8ADD: 0x5CB8,\n    0x8ADE: 0x5DCC,\n    0x8ADF: 0x73A9,\n    0x8AE0: 0x764C,\n    0x8AE1: 0x773C,\n    0x8AE2: 0x5CA9,\n    0x8AE3: 0x7FEB,\n    0x8AE4: 0x8D0B,\n    0x8AE5: 0x96C1,\n    0x8AE6: 0x9811,\n    0x8AE7: 0x9854,\n    0x8AE8: 0x9858,\n    0x8AE9: 0x4F01,\n    0x8AEA: 0x4F0E,\n    0x8AEB: 0x5371,\n    0x8AEC: 0x559C,\n    0x8AED: 0x5668,\n    0x8AEE: 0x57FA,\n    0x8AEF: 0x5947,\n    0x8AF0: 0x5B09,\n    0x8AF1: 0x5BC4,\n    0x8AF2: 0x5C90,\n    0x8AF3: 0x5E0C,\n    0x8AF4: 0x5E7E,\n    0x8AF5: 0x5FCC,\n    0x8AF6: 0x63EE,\n    0x8AF7: 0x673A,\n    0x8AF8: 0x65D7,\n    0x8AF9: 0x65E2,\n    0x8AFA: 0x671F,\n    0x8AFB: 0x68CB,\n    0x8AFC: 0x68C4,\n    0x8B40: 0x6A5F,\n    0x8B41: 0x5E30,\n    0x8B42: 0x6BC5,\n    0x8B43: 0x6C17,\n    0x8B44: 0x6C7D,\n    0x8B45: 0x757F,\n    0x8B46: 0x7948,\n    0x8B47: 0x5B63,\n    0x8B48: 0x7A00,\n    0x8B49: 0x7D00,\n    0x8B4A: 0x5FBD,\n    0x8B4B: 0x898F,\n    0x8B4C: 0x8A18,\n    0x8B4D: 0x8CB4,\n    0x8B4E: 0x8D77,\n    0x8B4F: 0x8ECC,\n    0x8B50: 0x8F1D,\n    0x8B51: 0x98E2,\n    0x8B52: 0x9A0E,\n    0x8B53: 0x9B3C,\n    0x8B54: 0x4E80,\n    0x8B55: 0x507D,\n    0x8B56: 0x5100,\n    0x8B57: 0x5993,\n    0x8B58: 0x5B9C,\n    0x8B59: 0x622F,\n    0x8B5A: 0x6280,\n    0x8B5B: 0x64EC,\n    0x8B5C: 0x6B3A,\n    0x8B5D: 0x72A0,\n    0x8B5E: 0x7591,\n    0x8B5F: 0x7947,\n    0x8B60: 0x7FA9,\n    0x8B61: 0x87FB,\n    0x8B62: 0x8ABC,\n    0x8B63: 0x8B70,\n    0x8B64: 0x63AC,\n    0x8B65: 0x83CA,\n    0x8B66: 0x97A0,\n    0x8B67: 0x5409,\n    0x8B68: 0x5403,\n    0x8B69: 0x55AB,\n    0x8B6A: 0x6854,\n    0x8B6B: 0x6A58,\n    0x8B6C: 0x8A70,\n    0x8B6D: 0x7827,\n    0x8B6E: 0x6775,\n    0x8B6F: 0x9ECD,\n    0x8B70: 0x5374,\n    0x8B71: 0x5BA2,\n    0x8B72: 0x811A,\n    0x8B73: 0x8650,\n    0x8B74: 0x9006,\n    0x8B75: 0x4E18,\n    0x8B76: 0x4E45,\n    0x8B77: 0x4EC7,\n    0x8B78: 0x4F11,\n    0x8B79: 0x53CA,\n    0x8B7A: 0x5438,\n    0x8B7B: 0x5BAE,\n    0x8B7C: 0x5F13,\n    0x8B7D: 0x6025,\n    0x8B7E: 0x6551,\n    0x8B80: 0x673D,\n    0x8B81: 0x6C42,\n    0x8B82: 0x6C72,\n    0x8B83: 0x6CE3,\n    0x8B84: 0x7078,\n    0x8B85: 0x7403,\n    0x8B86: 0x7A76,\n    0x8B87: 0x7AAE,\n    0x8B88: 0x7B08,\n    0x8B89: 0x7D1A,\n    0x8B8A: 0x7CFE,\n    0x8B8B: 0x7D66,\n    0x8B8C: 0x65E7,\n    0x8B8D: 0x725B,\n    0x8B8E: 0x53BB,\n    0x8B8F: 0x5C45,\n    0x8B90: 0x5DE8,\n    0x8B91: 0x62D2,\n    0x8B92: 0x62E0,\n    0x8B93: 0x6319,\n    0x8B94: 0x6E20,\n    0x8B95: 0x865A,\n    0x8B96: 0x8A31,\n    0x8B97: 0x8DDD,\n    0x8B98: 0x92F8,\n    0x8B99: 0x6F01,\n    0x8B9A: 0x79A6,\n    0x8B9B: 0x9B5A,\n    0x8B9C: 0x4EA8,\n    0x8B9D: 0x4EAB,\n    0x8B9E: 0x4EAC,\n    0x8B9F: 0x4F9B,\n    0x8BA0: 0x4FA0,\n    0x8BA1: 0x50D1,\n    0x8BA2: 0x5147,\n    0x8BA3: 0x7AF6,\n    0x8BA4: 0x5171,\n    0x8BA5: 0x51F6,\n    0x8BA6: 0x5354,\n    0x8BA7: 0x5321,\n    0x8BA8: 0x537F,\n    0x8BA9: 0x53EB,\n    0x8BAA: 0x55AC,\n    0x8BAB: 0x5883,\n    0x8BAC: 0x5CE1,\n    0x8BAD: 0x5F37,\n    0x8BAE: 0x5F4A,\n    0x8BAF: 0x602F,\n    0x8BB0: 0x6050,\n    0x8BB1: 0x606D,\n    0x8BB2: 0x631F,\n    0x8BB3: 0x6559,\n    0x8BB4: 0x6A4B,\n    0x8BB5: 0x6CC1,\n    0x8BB6: 0x72C2,\n    0x8BB7: 0x72ED,\n    0x8BB8: 0x77EF,\n    0x8BB9: 0x80F8,\n    0x8BBA: 0x8105,\n    0x8BBB: 0x8208,\n    0x8BBC: 0x854E,\n    0x8BBD: 0x90F7,\n    0x8BBE: 0x93E1,\n    0x8BBF: 0x97FF,\n    0x8BC0: 0x9957,\n    0x8BC1: 0x9A5A,\n    0x8BC2: 0x4EF0,\n    0x8BC3: 0x51DD,\n    0x8BC4: 0x5C2D,\n    0x8BC5: 0x6681,\n    0x8BC6: 0x696D,\n    0x8BC7: 0x5C40,\n    0x8BC8: 0x66F2,\n    0x8BC9: 0x6975,\n    0x8BCA: 0x7389,\n    0x8BCB: 0x6850,\n    0x8BCC: 0x7C81,\n    0x8BCD: 0x50C5,\n    0x8BCE: 0x52E4,\n    0x8BCF: 0x5747,\n    0x8BD0: 0x5DFE,\n    0x8BD1: 0x9326,\n    0x8BD2: 0x65A4,\n    0x8BD3: 0x6B23,\n    0x8BD4: 0x6B3D,\n    0x8BD5: 0x7434,\n    0x8BD6: 0x7981,\n    0x8BD7: 0x79BD,\n    0x8BD8: 0x7B4B,\n    0x8BD9: 0x7DCA,\n    0x8BDA: 0x82B9,\n    0x8BDB: 0x83CC,\n    0x8BDC: 0x887F,\n    0x8BDD: 0x895F,\n    0x8BDE: 0x8B39,\n    0x8BDF: 0x8FD1,\n    0x8BE0: 0x91D1,\n    0x8BE1: 0x541F,\n    0x8BE2: 0x9280,\n    0x8BE3: 0x4E5D,\n    0x8BE4: 0x5036,\n    0x8BE5: 0x53E5,\n    0x8BE6: 0x533A,\n    0x8BE7: 0x72D7,\n    0x8BE8: 0x7396,\n    0x8BE9: 0x77E9,\n    0x8BEA: 0x82E6,\n    0x8BEB: 0x8EAF,\n    0x8BEC: 0x99C6,\n    0x8BED: 0x99C8,\n    0x8BEE: 0x99D2,\n    0x8BEF: 0x5177,\n    0x8BF0: 0x611A,\n    0x8BF1: 0x865E,\n    0x8BF2: 0x55B0,\n    0x8BF3: 0x7A7A,\n    0x8BF4: 0x5076,\n    0x8BF5: 0x5BD3,\n    0x8BF6: 0x9047,\n    0x8BF7: 0x9685,\n    0x8BF8: 0x4E32,\n    0x8BF9: 0x6ADB,\n    0x8BFA: 0x91E7,\n    0x8BFB: 0x5C51,\n    0x8BFC: 0x5C48,\n    0x8C40: 0x6398,\n    0x8C41: 0x7A9F,\n    0x8C42: 0x6C93,\n    0x8C43: 0x9774,\n    0x8C44: 0x8F61,\n    0x8C45: 0x7AAA,\n    0x8C46: 0x718A,\n    0x8C47: 0x9688,\n    0x8C48: 0x7C82,\n    0x8C49: 0x6817,\n    0x8C4A: 0x7E70,\n    0x8C4B: 0x6851,\n    0x8C4C: 0x936C,\n    0x8C4D: 0x52F2,\n    0x8C4E: 0x541B,\n    0x8C4F: 0x85AB,\n    0x8C50: 0x8A13,\n    0x8C51: 0x7FA4,\n    0x8C52: 0x8ECD,\n    0x8C53: 0x90E1,\n    0x8C54: 0x5366,\n    0x8C55: 0x8888,\n    0x8C56: 0x7941,\n    0x8C57: 0x4FC2,\n    0x8C58: 0x50BE,\n    0x8C59: 0x5211,\n    0x8C5A: 0x5144,\n    0x8C5B: 0x5553,\n    0x8C5C: 0x572D,\n    0x8C5D: 0x73EA,\n    0x8C5E: 0x578B,\n    0x8C5F: 0x5951,\n    0x8C60: 0x5F62,\n    0x8C61: 0x5F84,\n    0x8C62: 0x6075,\n    0x8C63: 0x6176,\n    0x8C64: 0x6167,\n    0x8C65: 0x61A9,\n    0x8C66: 0x63B2,\n    0x8C67: 0x643A,\n    0x8C68: 0x656C,\n    0x8C69: 0x666F,\n    0x8C6A: 0x6842,\n    0x8C6B: 0x6E13,\n    0x8C6C: 0x7566,\n    0x8C6D: 0x7A3D,\n    0x8C6E: 0x7CFB,\n    0x8C6F: 0x7D4C,\n    0x8C70: 0x7D99,\n    0x8C71: 0x7E4B,\n    0x8C72: 0x7F6B,\n    0x8C73: 0x830E,\n    0x8C74: 0x834A,\n    0x8C75: 0x86CD,\n    0x8C76: 0x8A08,\n    0x8C77: 0x8A63,\n    0x8C78: 0x8B66,\n    0x8C79: 0x8EFD,\n    0x8C7A: 0x981A,\n    0x8C7B: 0x9D8F,\n    0x8C7C: 0x82B8,\n    0x8C7D: 0x8FCE,\n    0x8C7E: 0x9BE8,\n    0x8C80: 0x5287,\n    0x8C81: 0x621F,\n    0x8C82: 0x6483,\n    0x8C83: 0x6FC0,\n    0x8C84: 0x9699,\n    0x8C85: 0x6841,\n    0x8C86: 0x5091,\n    0x8C87: 0x6B20,\n    0x8C88: 0x6C7A,\n    0x8C89: 0x6F54,\n    0x8C8A: 0x7A74,\n    0x8C8B: 0x7D50,\n    0x8C8C: 0x8840,\n    0x8C8D: 0x8A23,\n    0x8C8E: 0x6708,\n    0x8C8F: 0x4EF6,\n    0x8C90: 0x5039,\n    0x8C91: 0x5026,\n    0x8C92: 0x5065,\n    0x8C93: 0x517C,\n    0x8C94: 0x5238,\n    0x8C95: 0x5263,\n    0x8C96: 0x55A7,\n    0x8C97: 0x570F,\n    0x8C98: 0x5805,\n    0x8C99: 0x5ACC,\n    0x8C9A: 0x5EFA,\n    0x8C9B: 0x61B2,\n    0x8C9C: 0x61F8,\n    0x8C9D: 0x62F3,\n    0x8C9E: 0x6372,\n    0x8C9F: 0x691C,\n    0x8CA0: 0x6A29,\n    0x8CA1: 0x727D,\n    0x8CA2: 0x72AC,\n    0x8CA3: 0x732E,\n    0x8CA4: 0x7814,\n    0x8CA5: 0x786F,\n    0x8CA6: 0x7D79,\n    0x8CA7: 0x770C,\n    0x8CA8: 0x80A9,\n    0x8CA9: 0x898B,\n    0x8CAA: 0x8B19,\n    0x8CAB: 0x8CE2,\n    0x8CAC: 0x8ED2,\n    0x8CAD: 0x9063,\n    0x8CAE: 0x9375,\n    0x8CAF: 0x967A,\n    0x8CB0: 0x9855,\n    0x8CB1: 0x9A13,\n    0x8CB2: 0x9E78,\n    0x8CB3: 0x5143,\n    0x8CB4: 0x539F,\n    0x8CB5: 0x53B3,\n    0x8CB6: 0x5E7B,\n    0x8CB7: 0x5F26,\n    0x8CB8: 0x6E1B,\n    0x8CB9: 0x6E90,\n    0x8CBA: 0x7384,\n    0x8CBB: 0x73FE,\n    0x8CBC: 0x7D43,\n    0x8CBD: 0x8237,\n    0x8CBE: 0x8A00,\n    0x8CBF: 0x8AFA,\n    0x8CC0: 0x9650,\n    0x8CC1: 0x4E4E,\n    0x8CC2: 0x500B,\n    0x8CC3: 0x53E4,\n    0x8CC4: 0x547C,\n    0x8CC5: 0x56FA,\n    0x8CC6: 0x59D1,\n    0x8CC7: 0x5B64,\n    0x8CC8: 0x5DF1,\n    0x8CC9: 0x5EAB,\n    0x8CCA: 0x5F27,\n    0x8CCB: 0x6238,\n    0x8CCC: 0x6545,\n    0x8CCD: 0x67AF,\n    0x8CCE: 0x6E56,\n    0x8CCF: 0x72D0,\n    0x8CD0: 0x7CCA,\n    0x8CD1: 0x88B4,\n    0x8CD2: 0x80A1,\n    0x8CD3: 0x80E1,\n    0x8CD4: 0x83F0,\n    0x8CD5: 0x864E,\n    0x8CD6: 0x8A87,\n    0x8CD7: 0x8DE8,\n    0x8CD8: 0x9237,\n    0x8CD9: 0x96C7,\n    0x8CDA: 0x9867,\n    0x8CDB: 0x9F13,\n    0x8CDC: 0x4E94,\n    0x8CDD: 0x4E92,\n    0x8CDE: 0x4F0D,\n    0x8CDF: 0x5348,\n    0x8CE0: 0x5449,\n    0x8CE1: 0x543E,\n    0x8CE2: 0x5A2F,\n    0x8CE3: 0x5F8C,\n    0x8CE4: 0x5FA1,\n    0x8CE5: 0x609F,\n    0x8CE6: 0x68A7,\n    0x8CE7: 0x6A8E,\n    0x8CE8: 0x745A,\n    0x8CE9: 0x7881,\n    0x8CEA: 0x8A9E,\n    0x8CEB: 0x8AA4,\n    0x8CEC: 0x8B77,\n    0x8CED: 0x9190,\n    0x8CEE: 0x4E5E,\n    0x8CEF: 0x9BC9,\n    0x8CF0: 0x4EA4,\n    0x8CF1: 0x4F7C,\n    0x8CF2: 0x4FAF,\n    0x8CF3: 0x5019,\n    0x8CF4: 0x5016,\n    0x8CF5: 0x5149,\n    0x8CF6: 0x516C,\n    0x8CF7: 0x529F,\n    0x8CF8: 0x52B9,\n    0x8CF9: 0x52FE,\n    0x8CFA: 0x539A,\n    0x8CFB: 0x53E3,\n    0x8CFC: 0x5411,\n    0x8D40: 0x540E,\n    0x8D41: 0x5589,\n    0x8D42: 0x5751,\n    0x8D43: 0x57A2,\n    0x8D44: 0x597D,\n    0x8D45: 0x5B54,\n    0x8D46: 0x5B5D,\n    0x8D47: 0x5B8F,\n    0x8D48: 0x5DE5,\n    0x8D49: 0x5DE7,\n    0x8D4A: 0x5DF7,\n    0x8D4B: 0x5E78,\n    0x8D4C: 0x5E83,\n    0x8D4D: 0x5E9A,\n    0x8D4E: 0x5EB7,\n    0x8D4F: 0x5F18,\n    0x8D50: 0x6052,\n    0x8D51: 0x614C,\n    0x8D52: 0x6297,\n    0x8D53: 0x62D8,\n    0x8D54: 0x63A7,\n    0x8D55: 0x653B,\n    0x8D56: 0x6602,\n    0x8D57: 0x6643,\n    0x8D58: 0x66F4,\n    0x8D59: 0x676D,\n    0x8D5A: 0x6821,\n    0x8D5B: 0x6897,\n    0x8D5C: 0x69CB,\n    0x8D5D: 0x6C5F,\n    0x8D5E: 0x6D2A,\n    0x8D5F: 0x6D69,\n    0x8D60: 0x6E2F,\n    0x8D61: 0x6E9D,\n    0x8D62: 0x7532,\n    0x8D63: 0x7687,\n    0x8D64: 0x786C,\n    0x8D65: 0x7A3F,\n    0x8D66: 0x7CE0,\n    0x8D67: 0x7D05,\n    0x8D68: 0x7D18,\n    0x8D69: 0x7D5E,\n    0x8D6A: 0x7DB1,\n    0x8D6B: 0x8015,\n    0x8D6C: 0x8003,\n    0x8D6D: 0x80AF,\n    0x8D6E: 0x80B1,\n    0x8D6F: 0x8154,\n    0x8D70: 0x818F,\n    0x8D71: 0x822A,\n    0x8D72: 0x8352,\n    0x8D73: 0x884C,\n    0x8D74: 0x8861,\n    0x8D75: 0x8B1B,\n    0x8D76: 0x8CA2,\n    0x8D77: 0x8CFC,\n    0x8D78: 0x90CA,\n    0x8D79: 0x9175,\n    0x8D7A: 0x9271,\n    0x8D7B: 0x783F,\n    0x8D7C: 0x92FC,\n    0x8D7D: 0x95A4,\n    0x8D7E: 0x964D,\n    0x8D80: 0x9805,\n    0x8D81: 0x9999,\n    0x8D82: 0x9AD8,\n    0x8D83: 0x9D3B,\n    0x8D84: 0x525B,\n    0x8D85: 0x52AB,\n    0x8D86: 0x53F7,\n    0x8D87: 0x5408,\n    0x8D88: 0x58D5,\n    0x8D89: 0x62F7,\n    0x8D8A: 0x6FE0,\n    0x8D8B: 0x8C6A,\n    0x8D8C: 0x8F5F,\n    0x8D8D: 0x9EB9,\n    0x8D8E: 0x514B,\n    0x8D8F: 0x523B,\n    0x8D90: 0x544A,\n    0x8D91: 0x56FD,\n    0x8D92: 0x7A40,\n    0x8D93: 0x9177,\n    0x8D94: 0x9D60,\n    0x8D95: 0x9ED2,\n    0x8D96: 0x7344,\n    0x8D97: 0x6F09,\n    0x8D98: 0x8170,\n    0x8D99: 0x7511,\n    0x8D9A: 0x5FFD,\n    0x8D9B: 0x60DA,\n    0x8D9C: 0x9AA8,\n    0x8D9D: 0x72DB,\n    0x8D9E: 0x8FBC,\n    0x8D9F: 0x6B64,\n    0x8DA0: 0x9803,\n    0x8DA1: 0x4ECA,\n    0x8DA2: 0x56F0,\n    0x8DA3: 0x5764,\n    0x8DA4: 0x58BE,\n    0x8DA5: 0x5A5A,\n    0x8DA6: 0x6068,\n    0x8DA7: 0x61C7,\n    0x8DA8: 0x660F,\n    0x8DA9: 0x6606,\n    0x8DAA: 0x6839,\n    0x8DAB: 0x68B1,\n    0x8DAC: 0x6DF7,\n    0x8DAD: 0x75D5,\n    0x8DAE: 0x7D3A,\n    0x8DAF: 0x826E,\n    0x8DB0: 0x9B42,\n    0x8DB1: 0x4E9B,\n    0x8DB2: 0x4F50,\n    0x8DB3: 0x53C9,\n    0x8DB4: 0x5506,\n    0x8DB5: 0x5D6F,\n    0x8DB6: 0x5DE6,\n    0x8DB7: 0x5DEE,\n    0x8DB8: 0x67FB,\n    0x8DB9: 0x6C99,\n    0x8DBA: 0x7473,\n    0x8DBB: 0x7802,\n    0x8DBC: 0x8A50,\n    0x8DBD: 0x9396,\n    0x8DBE: 0x88DF,\n    0x8DBF: 0x5750,\n    0x8DC0: 0x5EA7,\n    0x8DC1: 0x632B,\n    0x8DC2: 0x50B5,\n    0x8DC3: 0x50AC,\n    0x8DC4: 0x518D,\n    0x8DC5: 0x6700,\n    0x8DC6: 0x54C9,\n    0x8DC7: 0x585E,\n    0x8DC8: 0x59BB,\n    0x8DC9: 0x5BB0,\n    0x8DCA: 0x5F69,\n    0x8DCB: 0x624D,\n    0x8DCC: 0x63A1,\n    0x8DCD: 0x683D,\n    0x8DCE: 0x6B73,\n    0x8DCF: 0x6E08,\n    0x8DD0: 0x707D,\n    0x8DD1: 0x91C7,\n    0x8DD2: 0x7280,\n    0x8DD3: 0x7815,\n    0x8DD4: 0x7826,\n    0x8DD5: 0x796D,\n    0x8DD6: 0x658E,\n    0x8DD7: 0x7D30,\n    0x8DD8: 0x83DC,\n    0x8DD9: 0x88C1,\n    0x8DDA: 0x8F09,\n    0x8DDB: 0x969B,\n    0x8DDC: 0x5264,\n    0x8DDD: 0x5728,\n    0x8DDE: 0x6750,\n    0x8DDF: 0x7F6A,\n    0x8DE0: 0x8CA1,\n    0x8DE1: 0x51B4,\n    0x8DE2: 0x5742,\n    0x8DE3: 0x962A,\n    0x8DE4: 0x583A,\n    0x8DE5: 0x698A,\n    0x8DE6: 0x80B4,\n    0x8DE7: 0x54B2,\n    0x8DE8: 0x5D0E,\n    0x8DE9: 0x57FC,\n    0x8DEA: 0x7895,\n    0x8DEB: 0x9DFA,\n    0x8DEC: 0x4F5C,\n    0x8DED: 0x524A,\n    0x8DEE: 0x548B,\n    0x8DEF: 0x643E,\n    0x8DF0: 0x6628,\n    0x8DF1: 0x6714,\n    0x8DF2: 0x67F5,\n    0x8DF3: 0x7A84,\n    0x8DF4: 0x7B56,\n    0x8DF5: 0x7D22,\n    0x8DF6: 0x932F,\n    0x8DF7: 0x685C,\n    0x8DF8: 0x9BAD,\n    0x8DF9: 0x7B39,\n    0x8DFA: 0x5319,\n    0x8DFB: 0x518A,\n    0x8DFC: 0x5237,\n    0x8E40: 0x5BDF,\n    0x8E41: 0x62F6,\n    0x8E42: 0x64AE,\n    0x8E43: 0x64E6,\n    0x8E44: 0x672D,\n    0x8E45: 0x6BBA,\n    0x8E46: 0x85A9,\n    0x8E47: 0x96D1,\n    0x8E48: 0x7690,\n    0x8E49: 0x9BD6,\n    0x8E4A: 0x634C,\n    0x8E4B: 0x9306,\n    0x8E4C: 0x9BAB,\n    0x8E4D: 0x76BF,\n    0x8E4E: 0x6652,\n    0x8E4F: 0x4E09,\n    0x8E50: 0x5098,\n    0x8E51: 0x53C2,\n    0x8E52: 0x5C71,\n    0x8E53: 0x60E8,\n    0x8E54: 0x6492,\n    0x8E55: 0x6563,\n    0x8E56: 0x685F,\n    0x8E57: 0x71E6,\n    0x8E58: 0x73CA,\n    0x8E59: 0x7523,\n    0x8E5A: 0x7B97,\n    0x8E5B: 0x7E82,\n    0x8E5C: 0x8695,\n    0x8E5D: 0x8B83,\n    0x8E5E: 0x8CDB,\n    0x8E5F: 0x9178,\n    0x8E60: 0x9910,\n    0x8E61: 0x65AC,\n    0x8E62: 0x66AB,\n    0x8E63: 0x6B8B,\n    0x8E64: 0x4ED5,\n    0x8E65: 0x4ED4,\n    0x8E66: 0x4F3A,\n    0x8E67: 0x4F7F,\n    0x8E68: 0x523A,\n    0x8E69: 0x53F8,\n    0x8E6A: 0x53F2,\n    0x8E6B: 0x55E3,\n    0x8E6C: 0x56DB,\n    0x8E6D: 0x58EB,\n    0x8E6E: 0x59CB,\n    0x8E6F: 0x59C9,\n    0x8E70: 0x59FF,\n    0x8E71: 0x5B50,\n    0x8E72: 0x5C4D,\n    0x8E73: 0x5E02,\n    0x8E74: 0x5E2B,\n    0x8E75: 0x5FD7,\n    0x8E76: 0x601D,\n    0x8E77: 0x6307,\n    0x8E78: 0x652F,\n    0x8E79: 0x5B5C,\n    0x8E7A: 0x65AF,\n    0x8E7B: 0x65BD,\n    0x8E7C: 0x65E8,\n    0x8E7D: 0x679D,\n    0x8E7E: 0x6B62,\n    0x8E80: 0x6B7B,\n    0x8E81: 0x6C0F,\n    0x8E82: 0x7345,\n    0x8E83: 0x7949,\n    0x8E84: 0x79C1,\n    0x8E85: 0x7CF8,\n    0x8E86: 0x7D19,\n    0x8E87: 0x7D2B,\n    0x8E88: 0x80A2,\n    0x8E89: 0x8102,\n    0x8E8A: 0x81F3,\n    0x8E8B: 0x8996,\n    0x8E8C: 0x8A5E,\n    0x8E8D: 0x8A69,\n    0x8E8E: 0x8A66,\n    0x8E8F: 0x8A8C,\n    0x8E90: 0x8AEE,\n    0x8E91: 0x8CC7,\n    0x8E92: 0x8CDC,\n    0x8E93: 0x96CC,\n    0x8E94: 0x98FC,\n    0x8E95: 0x6B6F,\n    0x8E96: 0x4E8B,\n    0x8E97: 0x4F3C,\n    0x8E98: 0x4F8D,\n    0x8E99: 0x5150,\n    0x8E9A: 0x5B57,\n    0x8E9B: 0x5BFA,\n    0x8E9C: 0x6148,\n    0x8E9D: 0x6301,\n    0x8E9E: 0x6642,\n    0x8E9F: 0x6B21,\n    0x8EA0: 0x6ECB,\n    0x8EA1: 0x6CBB,\n    0x8EA2: 0x723E,\n    0x8EA3: 0x74BD,\n    0x8EA4: 0x75D4,\n    0x8EA5: 0x78C1,\n    0x8EA6: 0x793A,\n    0x8EA7: 0x800C,\n    0x8EA8: 0x8033,\n    0x8EA9: 0x81EA,\n    0x8EAA: 0x8494,\n    0x8EAB: 0x8F9E,\n    0x8EAC: 0x6C50,\n    0x8EAD: 0x9E7F,\n    0x8EAE: 0x5F0F,\n    0x8EAF: 0x8B58,\n    0x8EB0: 0x9D2B,\n    0x8EB1: 0x7AFA,\n    0x8EB2: 0x8EF8,\n    0x8EB3: 0x5B8D,\n    0x8EB4: 0x96EB,\n    0x8EB5: 0x4E03,\n    0x8EB6: 0x53F1,\n    0x8EB7: 0x57F7,\n    0x8EB8: 0x5931,\n    0x8EB9: 0x5AC9,\n    0x8EBA: 0x5BA4,\n    0x8EBB: 0x6089,\n    0x8EBC: 0x6E7F,\n    0x8EBD: 0x6F06,\n    0x8EBE: 0x75BE,\n    0x8EBF: 0x8CEA,\n    0x8EC0: 0x5B9F,\n    0x8EC1: 0x8500,\n    0x8EC2: 0x7BE0,\n    0x8EC3: 0x5072,\n    0x8EC4: 0x67F4,\n    0x8EC5: 0x829D,\n    0x8EC6: 0x5C61,\n    0x8EC7: 0x854A,\n    0x8EC8: 0x7E1E,\n    0x8EC9: 0x820E,\n    0x8ECA: 0x5199,\n    0x8ECB: 0x5C04,\n    0x8ECC: 0x6368,\n    0x8ECD: 0x8D66,\n    0x8ECE: 0x659C,\n    0x8ECF: 0x716E,\n    0x8ED0: 0x793E,\n    0x8ED1: 0x7D17,\n    0x8ED2: 0x8005,\n    0x8ED3: 0x8B1D,\n    0x8ED4: 0x8ECA,\n    0x8ED5: 0x906E,\n    0x8ED6: 0x86C7,\n    0x8ED7: 0x90AA,\n    0x8ED8: 0x501F,\n    0x8ED9: 0x52FA,\n    0x8EDA: 0x5C3A,\n    0x8EDB: 0x6753,\n    0x8EDC: 0x707C,\n    0x8EDD: 0x7235,\n    0x8EDE: 0x914C,\n    0x8EDF: 0x91C8,\n    0x8EE0: 0x932B,\n    0x8EE1: 0x82E5,\n    0x8EE2: 0x5BC2,\n    0x8EE3: 0x5F31,\n    0x8EE4: 0x60F9,\n    0x8EE5: 0x4E3B,\n    0x8EE6: 0x53D6,\n    0x8EE7: 0x5B88,\n    0x8EE8: 0x624B,\n    0x8EE9: 0x6731,\n    0x8EEA: 0x6B8A,\n    0x8EEB: 0x72E9,\n    0x8EEC: 0x73E0,\n    0x8EED: 0x7A2E,\n    0x8EEE: 0x816B,\n    0x8EEF: 0x8DA3,\n    0x8EF0: 0x9152,\n    0x8EF1: 0x9996,\n    0x8EF2: 0x5112,\n    0x8EF3: 0x53D7,\n    0x8EF4: 0x546A,\n    0x8EF5: 0x5BFF,\n    0x8EF6: 0x6388,\n    0x8EF7: 0x6A39,\n    0x8EF8: 0x7DAC,\n    0x8EF9: 0x9700,\n    0x8EFA: 0x56DA,\n    0x8EFB: 0x53CE,\n    0x8EFC: 0x5468,\n    0x8F40: 0x5B97,\n    0x8F41: 0x5C31,\n    0x8F42: 0x5DDE,\n    0x8F43: 0x4FEE,\n    0x8F44: 0x6101,\n    0x8F45: 0x62FE,\n    0x8F46: 0x6D32,\n    0x8F47: 0x79C0,\n    0x8F48: 0x79CB,\n    0x8F49: 0x7D42,\n    0x8F4A: 0x7E4D,\n    0x8F4B: 0x7FD2,\n    0x8F4C: 0x81ED,\n    0x8F4D: 0x821F,\n    0x8F4E: 0x8490,\n    0x8F4F: 0x8846,\n    0x8F50: 0x8972,\n    0x8F51: 0x8B90,\n    0x8F52: 0x8E74,\n    0x8F53: 0x8F2F,\n    0x8F54: 0x9031,\n    0x8F55: 0x914B,\n    0x8F56: 0x916C,\n    0x8F57: 0x96C6,\n    0x8F58: 0x919C,\n    0x8F59: 0x4EC0,\n    0x8F5A: 0x4F4F,\n    0x8F5B: 0x5145,\n    0x8F5C: 0x5341,\n    0x8F5D: 0x5F93,\n    0x8F5E: 0x620E,\n    0x8F5F: 0x67D4,\n    0x8F60: 0x6C41,\n    0x8F61: 0x6E0B,\n    0x8F62: 0x7363,\n    0x8F63: 0x7E26,\n    0x8F64: 0x91CD,\n    0x8F65: 0x9283,\n    0x8F66: 0x53D4,\n    0x8F67: 0x5919,\n    0x8F68: 0x5BBF,\n    0x8F69: 0x6DD1,\n    0x8F6A: 0x795D,\n    0x8F6B: 0x7E2E,\n    0x8F6C: 0x7C9B,\n    0x8F6D: 0x587E,\n    0x8F6E: 0x719F,\n    0x8F6F: 0x51FA,\n    0x8F70: 0x8853,\n    0x8F71: 0x8FF0,\n    0x8F72: 0x4FCA,\n    0x8F73: 0x5CFB,\n    0x8F74: 0x6625,\n    0x8F75: 0x77AC,\n    0x8F76: 0x7AE3,\n    0x8F77: 0x821C,\n    0x8F78: 0x99FF,\n    0x8F79: 0x51C6,\n    0x8F7A: 0x5FAA,\n    0x8F7B: 0x65EC,\n    0x8F7C: 0x696F,\n    0x8F7D: 0x6B89,\n    0x8F7E: 0x6DF3,\n    0x8F80: 0x6E96,\n    0x8F81: 0x6F64,\n    0x8F82: 0x76FE,\n    0x8F83: 0x7D14,\n    0x8F84: 0x5DE1,\n    0x8F85: 0x9075,\n    0x8F86: 0x9187,\n    0x8F87: 0x9806,\n    0x8F88: 0x51E6,\n    0x8F89: 0x521D,\n    0x8F8A: 0x6240,\n    0x8F8B: 0x6691,\n    0x8F8C: 0x66D9,\n    0x8F8D: 0x6E1A,\n    0x8F8E: 0x5EB6,\n    0x8F8F: 0x7DD2,\n    0x8F90: 0x7F72,\n    0x8F91: 0x66F8,\n    0x8F92: 0x85AF,\n    0x8F93: 0x85F7,\n    0x8F94: 0x8AF8,\n    0x8F95: 0x52A9,\n    0x8F96: 0x53D9,\n    0x8F97: 0x5973,\n    0x8F98: 0x5E8F,\n    0x8F99: 0x5F90,\n    0x8F9A: 0x6055,\n    0x8F9B: 0x92E4,\n    0x8F9C: 0x9664,\n    0x8F9D: 0x50B7,\n    0x8F9E: 0x511F,\n    0x8F9F: 0x52DD,\n    0x8FA0: 0x5320,\n    0x8FA1: 0x5347,\n    0x8FA2: 0x53EC,\n    0x8FA3: 0x54E8,\n    0x8FA4: 0x5546,\n    0x8FA5: 0x5531,\n    0x8FA6: 0x5617,\n    0x8FA7: 0x5968,\n    0x8FA8: 0x59BE,\n    0x8FA9: 0x5A3C,\n    0x8FAA: 0x5BB5,\n    0x8FAB: 0x5C06,\n    0x8FAC: 0x5C0F,\n    0x8FAD: 0x5C11,\n    0x8FAE: 0x5C1A,\n    0x8FAF: 0x5E84,\n    0x8FB0: 0x5E8A,\n    0x8FB1: 0x5EE0,\n    0x8FB2: 0x5F70,\n    0x8FB3: 0x627F,\n    0x8FB4: 0x6284,\n    0x8FB5: 0x62DB,\n    0x8FB6: 0x638C,\n    0x8FB7: 0x6377,\n    0x8FB8: 0x6607,\n    0x8FB9: 0x660C,\n    0x8FBA: 0x662D,\n    0x8FBB: 0x6676,\n    0x8FBC: 0x677E,\n    0x8FBD: 0x68A2,\n    0x8FBE: 0x6A1F,\n    0x8FBF: 0x6A35,\n    0x8FC0: 0x6CBC,\n    0x8FC1: 0x6D88,\n    0x8FC2: 0x6E09,\n    0x8FC3: 0x6E58,\n    0x8FC4: 0x713C,\n    0x8FC5: 0x7126,\n    0x8FC6: 0x7167,\n    0x8FC7: 0x75C7,\n    0x8FC8: 0x7701,\n    0x8FC9: 0x785D,\n    0x8FCA: 0x7901,\n    0x8FCB: 0x7965,\n    0x8FCC: 0x79F0,\n    0x8FCD: 0x7AE0,\n    0x8FCE: 0x7B11,\n    0x8FCF: 0x7CA7,\n    0x8FD0: 0x7D39,\n    0x8FD1: 0x8096,\n    0x8FD2: 0x83D6,\n    0x8FD3: 0x848B,\n    0x8FD4: 0x8549,\n    0x8FD5: 0x885D,\n    0x8FD6: 0x88F3,\n    0x8FD7: 0x8A1F,\n    0x8FD8: 0x8A3C,\n    0x8FD9: 0x8A54,\n    0x8FDA: 0x8A73,\n    0x8FDB: 0x8C61,\n    0x8FDC: 0x8CDE,\n    0x8FDD: 0x91A4,\n    0x8FDE: 0x9266,\n    0x8FDF: 0x937E,\n    0x8FE0: 0x9418,\n    0x8FE1: 0x969C,\n    0x8FE2: 0x9798,\n    0x8FE3: 0x4E0A,\n    0x8FE4: 0x4E08,\n    0x8FE5: 0x4E1E,\n    0x8FE6: 0x4E57,\n    0x8FE7: 0x5197,\n    0x8FE8: 0x5270,\n    0x8FE9: 0x57CE,\n    0x8FEA: 0x5834,\n    0x8FEB: 0x58CC,\n    0x8FEC: 0x5B22,\n    0x8FED: 0x5E38,\n    0x8FEE: 0x60C5,\n    0x8FEF: 0x64FE,\n    0x8FF0: 0x6761,\n    0x8FF1: 0x6756,\n    0x8FF2: 0x6D44,\n    0x8FF3: 0x72B6,\n    0x8FF4: 0x7573,\n    0x8FF5: 0x7A63,\n    0x8FF6: 0x84B8,\n    0x8FF7: 0x8B72,\n    0x8FF8: 0x91B8,\n    0x8FF9: 0x9320,\n    0x8FFA: 0x5631,\n    0x8FFB: 0x57F4,\n    0x8FFC: 0x98FE,\n    0x9040: 0x62ED,\n    0x9041: 0x690D,\n    0x9042: 0x6B96,\n    0x9043: 0x71ED,\n    0x9044: 0x7E54,\n    0x9045: 0x8077,\n    0x9046: 0x8272,\n    0x9047: 0x89E6,\n    0x9048: 0x98DF,\n    0x9049: 0x8755,\n    0x904A: 0x8FB1,\n    0x904B: 0x5C3B,\n    0x904C: 0x4F38,\n    0x904D: 0x4FE1,\n    0x904E: 0x4FB5,\n    0x904F: 0x5507,\n    0x9050: 0x5A20,\n    0x9051: 0x5BDD,\n    0x9052: 0x5BE9,\n    0x9053: 0x5FC3,\n    0x9054: 0x614E,\n    0x9055: 0x632F,\n    0x9056: 0x65B0,\n    0x9057: 0x664B,\n    0x9058: 0x68EE,\n    0x9059: 0x699B,\n    0x905A: 0x6D78,\n    0x905B: 0x6DF1,\n    0x905C: 0x7533,\n    0x905D: 0x75B9,\n    0x905E: 0x771F,\n    0x905F: 0x795E,\n    0x9060: 0x79E6,\n    0x9061: 0x7D33,\n    0x9062: 0x81E3,\n    0x9063: 0x82AF,\n    0x9064: 0x85AA,\n    0x9065: 0x89AA,\n    0x9066: 0x8A3A,\n    0x9067: 0x8EAB,\n    0x9068: 0x8F9B,\n    0x9069: 0x9032,\n    0x906A: 0x91DD,\n    0x906B: 0x9707,\n    0x906C: 0x4EBA,\n    0x906D: 0x4EC1,\n    0x906E: 0x5203,\n    0x906F: 0x5875,\n    0x9070: 0x58EC,\n    0x9071: 0x5C0B,\n    0x9072: 0x751A,\n    0x9073: 0x5C3D,\n    0x9074: 0x814E,\n    0x9075: 0x8A0A,\n    0x9076: 0x8FC5,\n    0x9077: 0x9663,\n    0x9078: 0x976D,\n    0x9079: 0x7B25,\n    0x907A: 0x8ACF,\n    0x907B: 0x9808,\n    0x907C: 0x9162,\n    0x907D: 0x56F3,\n    0x907E: 0x53A8,\n    0x9080: 0x9017,\n    0x9081: 0x5439,\n    0x9082: 0x5782,\n    0x9083: 0x5E25,\n    0x9084: 0x63A8,\n    0x9085: 0x6C34,\n    0x9086: 0x708A,\n    0x9087: 0x7761,\n    0x9088: 0x7C8B,\n    0x9089: 0x7FE0,\n    0x908A: 0x8870,\n    0x908B: 0x9042,\n    0x908C: 0x9154,\n    0x908D: 0x9310,\n    0x908E: 0x9318,\n    0x908F: 0x968F,\n    0x9090: 0x745E,\n    0x9091: 0x9AC4,\n    0x9092: 0x5D07,\n    0x9093: 0x5D69,\n    0x9094: 0x6570,\n    0x9095: 0x67A2,\n    0x9096: 0x8DA8,\n    0x9097: 0x96DB,\n    0x9098: 0x636E,\n    0x9099: 0x6749,\n    0x909A: 0x6919,\n    0x909B: 0x83C5,\n    0x909C: 0x9817,\n    0x909D: 0x96C0,\n    0x909E: 0x88FE,\n    0x909F: 0x6F84,\n    0x90A0: 0x647A,\n    0x90A1: 0x5BF8,\n    0x90A2: 0x4E16,\n    0x90A3: 0x702C,\n    0x90A4: 0x755D,\n    0x90A5: 0x662F,\n    0x90A6: 0x51C4,\n    0x90A7: 0x5236,\n    0x90A8: 0x52E2,\n    0x90A9: 0x59D3,\n    0x90AA: 0x5F81,\n    0x90AB: 0x6027,\n    0x90AC: 0x6210,\n    0x90AD: 0x653F,\n    0x90AE: 0x6574,\n    0x90AF: 0x661F,\n    0x90B0: 0x6674,\n    0x90B1: 0x68F2,\n    0x90B2: 0x6816,\n    0x90B3: 0x6B63,\n    0x90B4: 0x6E05,\n    0x90B5: 0x7272,\n    0x90B6: 0x751F,\n    0x90B7: 0x76DB,\n    0x90B8: 0x7CBE,\n    0x90B9: 0x8056,\n    0x90BA: 0x58F0,\n    0x90BB: 0x88FD,\n    0x90BC: 0x897F,\n    0x90BD: 0x8AA0,\n    0x90BE: 0x8A93,\n    0x90BF: 0x8ACB,\n    0x90C0: 0x901D,\n    0x90C1: 0x9192,\n    0x90C2: 0x9752,\n    0x90C3: 0x9759,\n    0x90C4: 0x6589,\n    0x90C5: 0x7A0E,\n    0x90C6: 0x8106,\n    0x90C7: 0x96BB,\n    0x90C8: 0x5E2D,\n    0x90C9: 0x60DC,\n    0x90CA: 0x621A,\n    0x90CB: 0x65A5,\n    0x90CC: 0x6614,\n    0x90CD: 0x6790,\n    0x90CE: 0x77F3,\n    0x90CF: 0x7A4D,\n    0x90D0: 0x7C4D,\n    0x90D1: 0x7E3E,\n    0x90D2: 0x810A,\n    0x90D3: 0x8CAC,\n    0x90D4: 0x8D64,\n    0x90D5: 0x8DE1,\n    0x90D6: 0x8E5F,\n    0x90D7: 0x78A9,\n    0x90D8: 0x5207,\n    0x90D9: 0x62D9,\n    0x90DA: 0x63A5,\n    0x90DB: 0x6442,\n    0x90DC: 0x6298,\n    0x90DD: 0x8A2D,\n    0x90DE: 0x7A83,\n    0x90DF: 0x7BC0,\n    0x90E0: 0x8AAC,\n    0x90E1: 0x96EA,\n    0x90E2: 0x7D76,\n    0x90E3: 0x820C,\n    0x90E4: 0x8749,\n    0x90E5: 0x4ED9,\n    0x90E6: 0x5148,\n    0x90E7: 0x5343,\n    0x90E8: 0x5360,\n    0x90E9: 0x5BA3,\n    0x90EA: 0x5C02,\n    0x90EB: 0x5C16,\n    0x90EC: 0x5DDD,\n    0x90ED: 0x6226,\n    0x90EE: 0x6247,\n    0x90EF: 0x64B0,\n    0x90F0: 0x6813,\n    0x90F1: 0x6834,\n    0x90F2: 0x6CC9,\n    0x90F3: 0x6D45,\n    0x90F4: 0x6D17,\n    0x90F5: 0x67D3,\n    0x90F6: 0x6F5C,\n    0x90F7: 0x714E,\n    0x90F8: 0x717D,\n    0x90F9: 0x65CB,\n    0x90FA: 0x7A7F,\n    0x90FB: 0x7BAD,\n    0x90FC: 0x7DDA,\n    0x9140: 0x7E4A,\n    0x9141: 0x7FA8,\n    0x9142: 0x817A,\n    0x9143: 0x821B,\n    0x9144: 0x8239,\n    0x9145: 0x85A6,\n    0x9146: 0x8A6E,\n    0x9147: 0x8CCE,\n    0x9148: 0x8DF5,\n    0x9149: 0x9078,\n    0x914A: 0x9077,\n    0x914B: 0x92AD,\n    0x914C: 0x9291,\n    0x914D: 0x9583,\n    0x914E: 0x9BAE,\n    0x914F: 0x524D,\n    0x9150: 0x5584,\n    0x9151: 0x6F38,\n    0x9152: 0x7136,\n    0x9153: 0x5168,\n    0x9154: 0x7985,\n    0x9155: 0x7E55,\n    0x9156: 0x81B3,\n    0x9157: 0x7CCE,\n    0x9158: 0x564C,\n    0x9159: 0x5851,\n    0x915A: 0x5CA8,\n    0x915B: 0x63AA,\n    0x915C: 0x66FE,\n    0x915D: 0x66FD,\n    0x915E: 0x695A,\n    0x915F: 0x72D9,\n    0x9160: 0x758F,\n    0x9161: 0x758E,\n    0x9162: 0x790E,\n    0x9163: 0x7956,\n    0x9164: 0x79DF,\n    0x9165: 0x7C97,\n    0x9166: 0x7D20,\n    0x9167: 0x7D44,\n    0x9168: 0x8607,\n    0x9169: 0x8A34,\n    0x916A: 0x963B,\n    0x916B: 0x9061,\n    0x916C: 0x9F20,\n    0x916D: 0x50E7,\n    0x916E: 0x5275,\n    0x916F: 0x53CC,\n    0x9170: 0x53E2,\n    0x9171: 0x5009,\n    0x9172: 0x55AA,\n    0x9173: 0x58EE,\n    0x9174: 0x594F,\n    0x9175: 0x723D,\n    0x9176: 0x5B8B,\n    0x9177: 0x5C64,\n    0x9178: 0x531D,\n    0x9179: 0x60E3,\n    0x917A: 0x60F3,\n    0x917B: 0x635C,\n    0x917C: 0x6383,\n    0x917D: 0x633F,\n    0x917E: 0x63BB,\n    0x9180: 0x64CD,\n    0x9181: 0x65E9,\n    0x9182: 0x66F9,\n    0x9183: 0x5DE3,\n    0x9184: 0x69CD,\n    0x9185: 0x69FD,\n    0x9186: 0x6F15,\n    0x9187: 0x71E5,\n    0x9188: 0x4E89,\n    0x9189: 0x75E9,\n    0x918A: 0x76F8,\n    0x918B: 0x7A93,\n    0x918C: 0x7CDF,\n    0x918D: 0x7DCF,\n    0x918E: 0x7D9C,\n    0x918F: 0x8061,\n    0x9190: 0x8349,\n    0x9191: 0x8358,\n    0x9192: 0x846C,\n    0x9193: 0x84BC,\n    0x9194: 0x85FB,\n    0x9195: 0x88C5,\n    0x9196: 0x8D70,\n    0x9197: 0x9001,\n    0x9198: 0x906D,\n    0x9199: 0x9397,\n    0x919A: 0x971C,\n    0x919B: 0x9A12,\n    0x919C: 0x50CF,\n    0x919D: 0x5897,\n    0x919E: 0x618E,\n    0x919F: 0x81D3,\n    0x91A0: 0x8535,\n    0x91A1: 0x8D08,\n    0x91A2: 0x9020,\n    0x91A3: 0x4FC3,\n    0x91A4: 0x5074,\n    0x91A5: 0x5247,\n    0x91A6: 0x5373,\n    0x91A7: 0x606F,\n    0x91A8: 0x6349,\n    0x91A9: 0x675F,\n    0x91AA: 0x6E2C,\n    0x91AB: 0x8DB3,\n    0x91AC: 0x901F,\n    0x91AD: 0x4FD7,\n    0x91AE: 0x5C5E,\n    0x91AF: 0x8CCA,\n    0x91B0: 0x65CF,\n    0x91B1: 0x7D9A,\n    0x91B2: 0x5352,\n    0x91B3: 0x8896,\n    0x91B4: 0x5176,\n    0x91B5: 0x63C3,\n    0x91B6: 0x5B58,\n    0x91B7: 0x5B6B,\n    0x91B8: 0x5C0A,\n    0x91B9: 0x640D,\n    0x91BA: 0x6751,\n    0x91BB: 0x905C,\n    0x91BC: 0x4ED6,\n    0x91BD: 0x591A,\n    0x91BE: 0x592A,\n    0x91BF: 0x6C70,\n    0x91C0: 0x8A51,\n    0x91C1: 0x553E,\n    0x91C2: 0x5815,\n    0x91C3: 0x59A5,\n    0x91C4: 0x60F0,\n    0x91C5: 0x6253,\n    0x91C6: 0x67C1,\n    0x91C7: 0x8235,\n    0x91C8: 0x6955,\n    0x91C9: 0x9640,\n    0x91CA: 0x99C4,\n    0x91CB: 0x9A28,\n    0x91CC: 0x4F53,\n    0x91CD: 0x5806,\n    0x91CE: 0x5BFE,\n    0x91CF: 0x8010,\n    0x91D0: 0x5CB1,\n    0x91D1: 0x5E2F,\n    0x91D2: 0x5F85,\n    0x91D3: 0x6020,\n    0x91D4: 0x614B,\n    0x91D5: 0x6234,\n    0x91D6: 0x66FF,\n    0x91D7: 0x6CF0,\n    0x91D8: 0x6EDE,\n    0x91D9: 0x80CE,\n    0x91DA: 0x817F,\n    0x91DB: 0x82D4,\n    0x91DC: 0x888B,\n    0x91DD: 0x8CB8,\n    0x91DE: 0x9000,\n    0x91DF: 0x902E,\n    0x91E0: 0x968A,\n    0x91E1: 0x9EDB,\n    0x91E2: 0x9BDB,\n    0x91E3: 0x4EE3,\n    0x91E4: 0x53F0,\n    0x91E5: 0x5927,\n    0x91E6: 0x7B2C,\n    0x91E7: 0x918D,\n    0x91E8: 0x984C,\n    0x91E9: 0x9DF9,\n    0x91EA: 0x6EDD,\n    0x91EB: 0x7027,\n    0x91EC: 0x5353,\n    0x91ED: 0x5544,\n    0x91EE: 0x5B85,\n    0x91EF: 0x6258,\n    0x91F0: 0x629E,\n    0x91F1: 0x62D3,\n    0x91F2: 0x6CA2,\n    0x91F3: 0x6FEF,\n    0x91F4: 0x7422,\n    0x91F5: 0x8A17,\n    0x91F6: 0x9438,\n    0x91F7: 0x6FC1,\n    0x91F8: 0x8AFE,\n    0x91F9: 0x8338,\n    0x91FA: 0x51E7,\n    0x91FB: 0x86F8,\n    0x91FC: 0x53EA,\n    0x9240: 0x53E9,\n    0x9241: 0x4F46,\n    0x9242: 0x9054,\n    0x9243: 0x8FB0,\n    0x9244: 0x596A,\n    0x9245: 0x8131,\n    0x9246: 0x5DFD,\n    0x9247: 0x7AEA,\n    0x9248: 0x8FBF,\n    0x9249: 0x68DA,\n    0x924A: 0x8C37,\n    0x924B: 0x72F8,\n    0x924C: 0x9C48,\n    0x924D: 0x6A3D,\n    0x924E: 0x8AB0,\n    0x924F: 0x4E39,\n    0x9250: 0x5358,\n    0x9251: 0x5606,\n    0x9252: 0x5766,\n    0x9253: 0x62C5,\n    0x9254: 0x63A2,\n    0x9255: 0x65E6,\n    0x9256: 0x6B4E,\n    0x9257: 0x6DE1,\n    0x9258: 0x6E5B,\n    0x9259: 0x70AD,\n    0x925A: 0x77ED,\n    0x925B: 0x7AEF,\n    0x925C: 0x7BAA,\n    0x925D: 0x7DBB,\n    0x925E: 0x803D,\n    0x925F: 0x80C6,\n    0x9260: 0x86CB,\n    0x9261: 0x8A95,\n    0x9262: 0x935B,\n    0x9263: 0x56E3,\n    0x9264: 0x58C7,\n    0x9265: 0x5F3E,\n    0x9266: 0x65AD,\n    0x9267: 0x6696,\n    0x9268: 0x6A80,\n    0x9269: 0x6BB5,\n    0x926A: 0x7537,\n    0x926B: 0x8AC7,\n    0x926C: 0x5024,\n    0x926D: 0x77E5,\n    0x926E: 0x5730,\n    0x926F: 0x5F1B,\n    0x9270: 0x6065,\n    0x9271: 0x667A,\n    0x9272: 0x6C60,\n    0x9273: 0x75F4,\n    0x9274: 0x7A1A,\n    0x9275: 0x7F6E,\n    0x9276: 0x81F4,\n    0x9277: 0x8718,\n    0x9278: 0x9045,\n    0x9279: 0x99B3,\n    0x927A: 0x7BC9,\n    0x927B: 0x755C,\n    0x927C: 0x7AF9,\n    0x927D: 0x7B51,\n    0x927E: 0x84C4,\n    0x9280: 0x9010,\n    0x9281: 0x79E9,\n    0x9282: 0x7A92,\n    0x9283: 0x8336,\n    0x9284: 0x5AE1,\n    0x9285: 0x7740,\n    0x9286: 0x4E2D,\n    0x9287: 0x4EF2,\n    0x9288: 0x5B99,\n    0x9289: 0x5FE0,\n    0x928A: 0x62BD,\n    0x928B: 0x663C,\n    0x928C: 0x67F1,\n    0x928D: 0x6CE8,\n    0x928E: 0x866B,\n    0x928F: 0x8877,\n    0x9290: 0x8A3B,\n    0x9291: 0x914E,\n    0x9292: 0x92F3,\n    0x9293: 0x99D0,\n    0x9294: 0x6A17,\n    0x9295: 0x7026,\n    0x9296: 0x732A,\n    0x9297: 0x82E7,\n    0x9298: 0x8457,\n    0x9299: 0x8CAF,\n    0x929A: 0x4E01,\n    0x929B: 0x5146,\n    0x929C: 0x51CB,\n    0x929D: 0x558B,\n    0x929E: 0x5BF5,\n    0x929F: 0x5E16,\n    0x92A0: 0x5E33,\n    0x92A1: 0x5E81,\n    0x92A2: 0x5F14,\n    0x92A3: 0x5F35,\n    0x92A4: 0x5F6B,\n    0x92A5: 0x5FB4,\n    0x92A6: 0x61F2,\n    0x92A7: 0x6311,\n    0x92A8: 0x66A2,\n    0x92A9: 0x671D,\n    0x92AA: 0x6F6E,\n    0x92AB: 0x7252,\n    0x92AC: 0x753A,\n    0x92AD: 0x773A,\n    0x92AE: 0x8074,\n    0x92AF: 0x8139,\n    0x92B0: 0x8178,\n    0x92B1: 0x8776,\n    0x92B2: 0x8ABF,\n    0x92B3: 0x8ADC,\n    0x92B4: 0x8D85,\n    0x92B5: 0x8DF3,\n    0x92B6: 0x929A,\n    0x92B7: 0x9577,\n    0x92B8: 0x9802,\n    0x92B9: 0x9CE5,\n    0x92BA: 0x52C5,\n    0x92BB: 0x6357,\n    0x92BC: 0x76F4,\n    0x92BD: 0x6715,\n    0x92BE: 0x6C88,\n    0x92BF: 0x73CD,\n    0x92C0: 0x8CC3,\n    0x92C1: 0x93AE,\n    0x92C2: 0x9673,\n    0x92C3: 0x6D25,\n    0x92C4: 0x589C,\n    0x92C5: 0x690E,\n    0x92C6: 0x69CC,\n    0x92C7: 0x8FFD,\n    0x92C8: 0x939A,\n    0x92C9: 0x75DB,\n    0x92CA: 0x901A,\n    0x92CB: 0x585A,\n    0x92CC: 0x6802,\n    0x92CD: 0x63B4,\n    0x92CE: 0x69FB,\n    0x92CF: 0x4F43,\n    0x92D0: 0x6F2C,\n    0x92D1: 0x67D8,\n    0x92D2: 0x8FBB,\n    0x92D3: 0x8526,\n    0x92D4: 0x7DB4,\n    0x92D5: 0x9354,\n    0x92D6: 0x693F,\n    0x92D7: 0x6F70,\n    0x92D8: 0x576A,\n    0x92D9: 0x58F7,\n    0x92DA: 0x5B2C,\n    0x92DB: 0x7D2C,\n    0x92DC: 0x722A,\n    0x92DD: 0x540A,\n    0x92DE: 0x91E3,\n    0x92DF: 0x9DB4,\n    0x92E0: 0x4EAD,\n    0x92E1: 0x4F4E,\n    0x92E2: 0x505C,\n    0x92E3: 0x5075,\n    0x92E4: 0x5243,\n    0x92E5: 0x8C9E,\n    0x92E6: 0x5448,\n    0x92E7: 0x5824,\n    0x92E8: 0x5B9A,\n    0x92E9: 0x5E1D,\n    0x92EA: 0x5E95,\n    0x92EB: 0x5EAD,\n    0x92EC: 0x5EF7,\n    0x92ED: 0x5F1F,\n    0x92EE: 0x608C,\n    0x92EF: 0x62B5,\n    0x92F0: 0x633A,\n    0x92F1: 0x63D0,\n    0x92F2: 0x68AF,\n    0x92F3: 0x6C40,\n    0x92F4: 0x7887,\n    0x92F5: 0x798E,\n    0x92F6: 0x7A0B,\n    0x92F7: 0x7DE0,\n    0x92F8: 0x8247,\n    0x92F9: 0x8A02,\n    0x92FA: 0x8AE6,\n    0x92FB: 0x8E44,\n    0x92FC: 0x9013,\n    0x9340: 0x90B8,\n    0x9341: 0x912D,\n    0x9342: 0x91D8,\n    0x9343: 0x9F0E,\n    0x9344: 0x6CE5,\n    0x9345: 0x6458,\n    0x9346: 0x64E2,\n    0x9347: 0x6575,\n    0x9348: 0x6EF4,\n    0x9349: 0x7684,\n    0x934A: 0x7B1B,\n    0x934B: 0x9069,\n    0x934C: 0x93D1,\n    0x934D: 0x6EBA,\n    0x934E: 0x54F2,\n    0x934F: 0x5FB9,\n    0x9350: 0x64A4,\n    0x9351: 0x8F4D,\n    0x9352: 0x8FED,\n    0x9353: 0x9244,\n    0x9354: 0x5178,\n    0x9355: 0x586B,\n    0x9356: 0x5929,\n    0x9357: 0x5C55,\n    0x9358: 0x5E97,\n    0x9359: 0x6DFB,\n    0x935A: 0x7E8F,\n    0x935B: 0x751C,\n    0x935C: 0x8CBC,\n    0x935D: 0x8EE2,\n    0x935E: 0x985B,\n    0x935F: 0x70B9,\n    0x9360: 0x4F1D,\n    0x9361: 0x6BBF,\n    0x9362: 0x6FB1,\n    0x9363: 0x7530,\n    0x9364: 0x96FB,\n    0x9365: 0x514E,\n    0x9366: 0x5410,\n    0x9367: 0x5835,\n    0x9368: 0x5857,\n    0x9369: 0x59AC,\n    0x936A: 0x5C60,\n    0x936B: 0x5F92,\n    0x936C: 0x6597,\n    0x936D: 0x675C,\n    0x936E: 0x6E21,\n    0x936F: 0x767B,\n    0x9370: 0x83DF,\n    0x9371: 0x8CED,\n    0x9372: 0x9014,\n    0x9373: 0x90FD,\n    0x9374: 0x934D,\n    0x9375: 0x7825,\n    0x9376: 0x783A,\n    0x9377: 0x52AA,\n    0x9378: 0x5EA6,\n    0x9379: 0x571F,\n    0x937A: 0x5974,\n    0x937B: 0x6012,\n    0x937C: 0x5012,\n    0x937D: 0x515A,\n    0x937E: 0x51AC,\n    0x9380: 0x51CD,\n    0x9381: 0x5200,\n    0x9382: 0x5510,\n    0x9383: 0x5854,\n    0x9384: 0x5858,\n    0x9385: 0x5957,\n    0x9386: 0x5B95,\n    0x9387: 0x5CF6,\n    0x9388: 0x5D8B,\n    0x9389: 0x60BC,\n    0x938A: 0x6295,\n    0x938B: 0x642D,\n    0x938C: 0x6771,\n    0x938D: 0x6843,\n    0x938E: 0x68BC,\n    0x938F: 0x68DF,\n    0x9390: 0x76D7,\n    0x9391: 0x6DD8,\n    0x9392: 0x6E6F,\n    0x9393: 0x6D9B,\n    0x9394: 0x706F,\n    0x9395: 0x71C8,\n    0x9396: 0x5F53,\n    0x9397: 0x75D8,\n    0x9398: 0x7977,\n    0x9399: 0x7B49,\n    0x939A: 0x7B54,\n    0x939B: 0x7B52,\n    0x939C: 0x7CD6,\n    0x939D: 0x7D71,\n    0x939E: 0x5230,\n    0x939F: 0x8463,\n    0x93A0: 0x8569,\n    0x93A1: 0x85E4,\n    0x93A2: 0x8A0E,\n    0x93A3: 0x8B04,\n    0x93A4: 0x8C46,\n    0x93A5: 0x8E0F,\n    0x93A6: 0x9003,\n    0x93A7: 0x900F,\n    0x93A8: 0x9419,\n    0x93A9: 0x9676,\n    0x93AA: 0x982D,\n    0x93AB: 0x9A30,\n    0x93AC: 0x95D8,\n    0x93AD: 0x50CD,\n    0x93AE: 0x52D5,\n    0x93AF: 0x540C,\n    0x93B0: 0x5802,\n    0x93B1: 0x5C0E,\n    0x93B2: 0x61A7,\n    0x93B3: 0x649E,\n    0x93B4: 0x6D1E,\n    0x93B5: 0x77B3,\n    0x93B6: 0x7AE5,\n    0x93B7: 0x80F4,\n    0x93B8: 0x8404,\n    0x93B9: 0x9053,\n    0x93BA: 0x9285,\n    0x93BB: 0x5CE0,\n    0x93BC: 0x9D07,\n    0x93BD: 0x533F,\n    0x93BE: 0x5F97,\n    0x93BF: 0x5FB3,\n    0x93C0: 0x6D9C,\n    0x93C1: 0x7279,\n    0x93C2: 0x7763,\n    0x93C3: 0x79BF,\n    0x93C4: 0x7BE4,\n    0x93C5: 0x6BD2,\n    0x93C6: 0x72EC,\n    0x93C7: 0x8AAD,\n    0x93C8: 0x6803,\n    0x93C9: 0x6A61,\n    0x93CA: 0x51F8,\n    0x93CB: 0x7A81,\n    0x93CC: 0x6934,\n    0x93CD: 0x5C4A,\n    0x93CE: 0x9CF6,\n    0x93CF: 0x82EB,\n    0x93D0: 0x5BC5,\n    0x93D1: 0x9149,\n    0x93D2: 0x701E,\n    0x93D3: 0x5678,\n    0x93D4: 0x5C6F,\n    0x93D5: 0x60C7,\n    0x93D6: 0x6566,\n    0x93D7: 0x6C8C,\n    0x93D8: 0x8C5A,\n    0x93D9: 0x9041,\n    0x93DA: 0x9813,\n    0x93DB: 0x5451,\n    0x93DC: 0x66C7,\n    0x93DD: 0x920D,\n    0x93DE: 0x5948,\n    0x93DF: 0x90A3,\n    0x93E0: 0x5185,\n    0x93E1: 0x4E4D,\n    0x93E2: 0x51EA,\n    0x93E3: 0x8599,\n    0x93E4: 0x8B0E,\n    0x93E5: 0x7058,\n    0x93E6: 0x637A,\n    0x93E7: 0x934B,\n    0x93E8: 0x6962,\n    0x93E9: 0x99B4,\n    0x93EA: 0x7E04,\n    0x93EB: 0x7577,\n    0x93EC: 0x5357,\n    0x93ED: 0x6960,\n    0x93EE: 0x8EDF,\n    0x93EF: 0x96E3,\n    0x93F0: 0x6C5D,\n    0x93F1: 0x4E8C,\n    0x93F2: 0x5C3C,\n    0x93F3: 0x5F10,\n    0x93F4: 0x8FE9,\n    0x93F5: 0x5302,\n    0x93F6: 0x8CD1,\n    0x93F7: 0x8089,\n    0x93F8: 0x8679,\n    0x93F9: 0x5EFF,\n    0x93FA: 0x65E5,\n    0x93FB: 0x4E73,\n    0x93FC: 0x5165,\n    0x9440: 0x5982,\n    0x9441: 0x5C3F,\n    0x9442: 0x97EE,\n    0x9443: 0x4EFB,\n    0x9444: 0x598A,\n    0x9445: 0x5FCD,\n    0x9446: 0x8A8D,\n    0x9447: 0x6FE1,\n    0x9448: 0x79B0,\n    0x9449: 0x7962,\n    0x944A: 0x5BE7,\n    0x944B: 0x8471,\n    0x944C: 0x732B,\n    0x944D: 0x71B1,\n    0x944E: 0x5E74,\n    0x944F: 0x5FF5,\n    0x9450: 0x637B,\n    0x9451: 0x649A,\n    0x9452: 0x71C3,\n    0x9453: 0x7C98,\n    0x9454: 0x4E43,\n    0x9455: 0x5EFC,\n    0x9456: 0x4E4B,\n    0x9457: 0x57DC,\n    0x9458: 0x56A2,\n    0x9459: 0x60A9,\n    0x945A: 0x6FC3,\n    0x945B: 0x7D0D,\n    0x945C: 0x80FD,\n    0x945D: 0x8133,\n    0x945E: 0x81BF,\n    0x945F: 0x8FB2,\n    0x9460: 0x8997,\n    0x9461: 0x86A4,\n    0x9462: 0x5DF4,\n    0x9463: 0x628A,\n    0x9464: 0x64AD,\n    0x9465: 0x8987,\n    0x9466: 0x6777,\n    0x9467: 0x6CE2,\n    0x9468: 0x6D3E,\n    0x9469: 0x7436,\n    0x946A: 0x7834,\n    0x946B: 0x5A46,\n    0x946C: 0x7F75,\n    0x946D: 0x82AD,\n    0x946E: 0x99AC,\n    0x946F: 0x4FF3,\n    0x9470: 0x5EC3,\n    0x9471: 0x62DD,\n    0x9472: 0x6392,\n    0x9473: 0x6557,\n    0x9474: 0x676F,\n    0x9475: 0x76C3,\n    0x9476: 0x724C,\n    0x9477: 0x80CC,\n    0x9478: 0x80BA,\n    0x9479: 0x8F29,\n    0x947A: 0x914D,\n    0x947B: 0x500D,\n    0x947C: 0x57F9,\n    0x947D: 0x5A92,\n    0x947E: 0x6885,\n    0x9480: 0x6973,\n    0x9481: 0x7164,\n    0x9482: 0x72FD,\n    0x9483: 0x8CB7,\n    0x9484: 0x58F2,\n    0x9485: 0x8CE0,\n    0x9486: 0x966A,\n    0x9487: 0x9019,\n    0x9488: 0x877F,\n    0x9489: 0x79E4,\n    0x948A: 0x77E7,\n    0x948B: 0x8429,\n    0x948C: 0x4F2F,\n    0x948D: 0x5265,\n    0x948E: 0x535A,\n    0x948F: 0x62CD,\n    0x9490: 0x67CF,\n    0x9491: 0x6CCA,\n    0x9492: 0x767D,\n    0x9493: 0x7B94,\n    0x9494: 0x7C95,\n    0x9495: 0x8236,\n    0x9496: 0x8584,\n    0x9497: 0x8FEB,\n    0x9498: 0x66DD,\n    0x9499: 0x6F20,\n    0x949A: 0x7206,\n    0x949B: 0x7E1B,\n    0x949C: 0x83AB,\n    0x949D: 0x99C1,\n    0x949E: 0x9EA6,\n    0x949F: 0x51FD,\n    0x94A0: 0x7BB1,\n    0x94A1: 0x7872,\n    0x94A2: 0x7BB8,\n    0x94A3: 0x8087,\n    0x94A4: 0x7B48,\n    0x94A5: 0x6AE8,\n    0x94A6: 0x5E61,\n    0x94A7: 0x808C,\n    0x94A8: 0x7551,\n    0x94A9: 0x7560,\n    0x94AA: 0x516B,\n    0x94AB: 0x9262,\n    0x94AC: 0x6E8C,\n    0x94AD: 0x767A,\n    0x94AE: 0x9197,\n    0x94AF: 0x9AEA,\n    0x94B0: 0x4F10,\n    0x94B1: 0x7F70,\n    0x94B2: 0x629C,\n    0x94B3: 0x7B4F,\n    0x94B4: 0x95A5,\n    0x94B5: 0x9CE9,\n    0x94B6: 0x567A,\n    0x94B7: 0x5859,\n    0x94B8: 0x86E4,\n    0x94B9: 0x96BC,\n    0x94BA: 0x4F34,\n    0x94BB: 0x5224,\n    0x94BC: 0x534A,\n    0x94BD: 0x53CD,\n    0x94BE: 0x53DB,\n    0x94BF: 0x5E06,\n    0x94C0: 0x642C,\n    0x94C1: 0x6591,\n    0x94C2: 0x677F,\n    0x94C3: 0x6C3E,\n    0x94C4: 0x6C4E,\n    0x94C5: 0x7248,\n    0x94C6: 0x72AF,\n    0x94C7: 0x73ED,\n    0x94C8: 0x7554,\n    0x94C9: 0x7E41,\n    0x94CA: 0x822C,\n    0x94CB: 0x85E9,\n    0x94CC: 0x8CA9,\n    0x94CD: 0x7BC4,\n    0x94CE: 0x91C6,\n    0x94CF: 0x7169,\n    0x94D0: 0x9812,\n    0x94D1: 0x98EF,\n    0x94D2: 0x633D,\n    0x94D3: 0x6669,\n    0x94D4: 0x756A,\n    0x94D5: 0x76E4,\n    0x94D6: 0x78D0,\n    0x94D7: 0x8543,\n    0x94D8: 0x86EE,\n    0x94D9: 0x532A,\n    0x94DA: 0x5351,\n    0x94DB: 0x5426,\n    0x94DC: 0x5983,\n    0x94DD: 0x5E87,\n    0x94DE: 0x5F7C,\n    0x94DF: 0x60B2,\n    0x94E0: 0x6249,\n    0x94E1: 0x6279,\n    0x94E2: 0x62AB,\n    0x94E3: 0x6590,\n    0x94E4: 0x6BD4,\n    0x94E5: 0x6CCC,\n    0x94E6: 0x75B2,\n    0x94E7: 0x76AE,\n    0x94E8: 0x7891,\n    0x94E9: 0x79D8,\n    0x94EA: 0x7DCB,\n    0x94EB: 0x7F77,\n    0x94EC: 0x80A5,\n    0x94ED: 0x88AB,\n    0x94EE: 0x8AB9,\n    0x94EF: 0x8CBB,\n    0x94F0: 0x907F,\n    0x94F1: 0x975E,\n    0x94F2: 0x98DB,\n    0x94F3: 0x6A0B,\n    0x94F4: 0x7C38,\n    0x94F5: 0x5099,\n    0x94F6: 0x5C3E,\n    0x94F7: 0x5FAE,\n    0x94F8: 0x6787,\n    0x94F9: 0x6BD8,\n    0x94FA: 0x7435,\n    0x94FB: 0x7709,\n    0x94FC: 0x7F8E,\n    0x9540: 0x9F3B,\n    0x9541: 0x67CA,\n    0x9542: 0x7A17,\n    0x9543: 0x5339,\n    0x9544: 0x758B,\n    0x9545: 0x9AED,\n    0x9546: 0x5F66,\n    0x9547: 0x819D,\n    0x9548: 0x83F1,\n    0x9549: 0x8098,\n    0x954A: 0x5F3C,\n    0x954B: 0x5FC5,\n    0x954C: 0x7562,\n    0x954D: 0x7B46,\n    0x954E: 0x903C,\n    0x954F: 0x6867,\n    0x9550: 0x59EB,\n    0x9551: 0x5A9B,\n    0x9552: 0x7D10,\n    0x9553: 0x767E,\n    0x9554: 0x8B2C,\n    0x9555: 0x4FF5,\n    0x9556: 0x5F6A,\n    0x9557: 0x6A19,\n    0x9558: 0x6C37,\n    0x9559: 0x6F02,\n    0x955A: 0x74E2,\n    0x955B: 0x7968,\n    0x955C: 0x8868,\n    0x955D: 0x8A55,\n    0x955E: 0x8C79,\n    0x955F: 0x5EDF,\n    0x9560: 0x63CF,\n    0x9561: 0x75C5,\n    0x9562: 0x79D2,\n    0x9563: 0x82D7,\n    0x9564: 0x9328,\n    0x9565: 0x92F2,\n    0x9566: 0x849C,\n    0x9567: 0x86ED,\n    0x9568: 0x9C2D,\n    0x9569: 0x54C1,\n    0x956A: 0x5F6C,\n    0x956B: 0x658C,\n    0x956C: 0x6D5C,\n    0x956D: 0x7015,\n    0x956E: 0x8CA7,\n    0x956F: 0x8CD3,\n    0x9570: 0x983B,\n    0x9571: 0x654F,\n    0x9572: 0x74F6,\n    0x9573: 0x4E0D,\n    0x9574: 0x4ED8,\n    0x9575: 0x57E0,\n    0x9576: 0x592B,\n    0x9577: 0x5A66,\n    0x9578: 0x5BCC,\n    0x9579: 0x51A8,\n    0x957A: 0x5E03,\n    0x957B: 0x5E9C,\n    0x957C: 0x6016,\n    0x957D: 0x6276,\n    0x957E: 0x6577,\n    0x9580: 0x65A7,\n    0x9581: 0x666E,\n    0x9582: 0x6D6E,\n    0x9583: 0x7236,\n    0x9584: 0x7B26,\n    0x9585: 0x8150,\n    0x9586: 0x819A,\n    0x9587: 0x8299,\n    0x9588: 0x8B5C,\n    0x9589: 0x8CA0,\n    0x958A: 0x8CE6,\n    0x958B: 0x8D74,\n    0x958C: 0x961C,\n    0x958D: 0x9644,\n    0x958E: 0x4FAE,\n    0x958F: 0x64AB,\n    0x9590: 0x6B66,\n    0x9591: 0x821E,\n    0x9592: 0x8461,\n    0x9593: 0x856A,\n    0x9594: 0x90E8,\n    0x9595: 0x5C01,\n    0x9596: 0x6953,\n    0x9597: 0x98A8,\n    0x9598: 0x847A,\n    0x9599: 0x8557,\n    0x959A: 0x4F0F,\n    0x959B: 0x526F,\n    0x959C: 0x5FA9,\n    0x959D: 0x5E45,\n    0x959E: 0x670D,\n    0x959F: 0x798F,\n    0x95A0: 0x8179,\n    0x95A1: 0x8907,\n    0x95A2: 0x8986,\n    0x95A3: 0x6DF5,\n    0x95A4: 0x5F17,\n    0x95A5: 0x6255,\n    0x95A6: 0x6CB8,\n    0x95A7: 0x4ECF,\n    0x95A8: 0x7269,\n    0x95A9: 0x9B92,\n    0x95AA: 0x5206,\n    0x95AB: 0x543B,\n    0x95AC: 0x5674,\n    0x95AD: 0x58B3,\n    0x95AE: 0x61A4,\n    0x95AF: 0x626E,\n    0x95B0: 0x711A,\n    0x95B1: 0x596E,\n    0x95B2: 0x7C89,\n    0x95B3: 0x7CDE,\n    0x95B4: 0x7D1B,\n    0x95B5: 0x96F0,\n    0x95B6: 0x6587,\n    0x95B7: 0x805E,\n    0x95B8: 0x4E19,\n    0x95B9: 0x4F75,\n    0x95BA: 0x5175,\n    0x95BB: 0x5840,\n    0x95BC: 0x5E63,\n    0x95BD: 0x5E73,\n    0x95BE: 0x5F0A,\n    0x95BF: 0x67C4,\n    0x95C0: 0x4E26,\n    0x95C1: 0x853D,\n    0x95C2: 0x9589,\n    0x95C3: 0x965B,\n    0x95C4: 0x7C73,\n    0x95C5: 0x9801,\n    0x95C6: 0x50FB,\n    0x95C7: 0x58C1,\n    0x95C8: 0x7656,\n    0x95C9: 0x78A7,\n    0x95CA: 0x5225,\n    0x95CB: 0x77A5,\n    0x95CC: 0x8511,\n    0x95CD: 0x7B86,\n    0x95CE: 0x504F,\n    0x95CF: 0x5909,\n    0x95D0: 0x7247,\n    0x95D1: 0x7BC7,\n    0x95D2: 0x7DE8,\n    0x95D3: 0x8FBA,\n    0x95D4: 0x8FD4,\n    0x95D5: 0x904D,\n    0x95D6: 0x4FBF,\n    0x95D7: 0x52C9,\n    0x95D8: 0x5A29,\n    0x95D9: 0x5F01,\n    0x95DA: 0x97AD,\n    0x95DB: 0x4FDD,\n    0x95DC: 0x8217,\n    0x95DD: 0x92EA,\n    0x95DE: 0x5703,\n    0x95DF: 0x6355,\n    0x95E0: 0x6B69,\n    0x95E1: 0x752B,\n    0x95E2: 0x88DC,\n    0x95E3: 0x8F14,\n    0x95E4: 0x7A42,\n    0x95E5: 0x52DF,\n    0x95E6: 0x5893,\n    0x95E7: 0x6155,\n    0x95E8: 0x620A,\n    0x95E9: 0x66AE,\n    0x95EA: 0x6BCD,\n    0x95EB: 0x7C3F,\n    0x95EC: 0x83E9,\n    0x95ED: 0x5023,\n    0x95EE: 0x4FF8,\n    0x95EF: 0x5305,\n    0x95F0: 0x5446,\n    0x95F1: 0x5831,\n    0x95F2: 0x5949,\n    0x95F3: 0x5B9D,\n    0x95F4: 0x5CF0,\n    0x95F5: 0x5CEF,\n    0x95F6: 0x5D29,\n    0x95F7: 0x5E96,\n    0x95F8: 0x62B1,\n    0x95F9: 0x6367,\n    0x95FA: 0x653E,\n    0x95FB: 0x65B9,\n    0x95FC: 0x670B,\n    0x9640: 0x6CD5,\n    0x9641: 0x6CE1,\n    0x9642: 0x70F9,\n    0x9643: 0x7832,\n    0x9644: 0x7E2B,\n    0x9645: 0x80DE,\n    0x9646: 0x82B3,\n    0x9647: 0x840C,\n    0x9648: 0x84EC,\n    0x9649: 0x8702,\n    0x964A: 0x8912,\n    0x964B: 0x8A2A,\n    0x964C: 0x8C4A,\n    0x964D: 0x90A6,\n    0x964E: 0x92D2,\n    0x964F: 0x98FD,\n    0x9650: 0x9CF3,\n    0x9651: 0x9D6C,\n    0x9652: 0x4E4F,\n    0x9653: 0x4EA1,\n    0x9654: 0x508D,\n    0x9655: 0x5256,\n    0x9656: 0x574A,\n    0x9657: 0x59A8,\n    0x9658: 0x5E3D,\n    0x9659: 0x5FD8,\n    0x965A: 0x5FD9,\n    0x965B: 0x623F,\n    0x965C: 0x66B4,\n    0x965D: 0x671B,\n    0x965E: 0x67D0,\n    0x965F: 0x68D2,\n    0x9660: 0x5192,\n    0x9661: 0x7D21,\n    0x9662: 0x80AA,\n    0x9663: 0x81A8,\n    0x9664: 0x8B00,\n    0x9665: 0x8C8C,\n    0x9666: 0x8CBF,\n    0x9667: 0x927E,\n    0x9668: 0x9632,\n    0x9669: 0x5420,\n    0x966A: 0x982C,\n    0x966B: 0x5317,\n    0x966C: 0x50D5,\n    0x966D: 0x535C,\n    0x966E: 0x58A8,\n    0x966F: 0x64B2,\n    0x9670: 0x6734,\n    0x9671: 0x7267,\n    0x9672: 0x7766,\n    0x9673: 0x7A46,\n    0x9674: 0x91E6,\n    0x9675: 0x52C3,\n    0x9676: 0x6CA1,\n    0x9677: 0x6B86,\n    0x9678: 0x5800,\n    0x9679: 0x5E4C,\n    0x967A: 0x5954,\n    0x967B: 0x672C,\n    0x967C: 0x7FFB,\n    0x967D: 0x51E1,\n    0x967E: 0x76C6,\n    0x9680: 0x6469,\n    0x9681: 0x78E8,\n    0x9682: 0x9B54,\n    0x9683: 0x9EBB,\n    0x9684: 0x57CB,\n    0x9685: 0x59B9,\n    0x9686: 0x6627,\n    0x9687: 0x679A,\n    0x9688: 0x6BCE,\n    0x9689: 0x54E9,\n    0x968A: 0x69D9,\n    0x968B: 0x5E55,\n    0x968C: 0x819C,\n    0x968D: 0x6795,\n    0x968E: 0x9BAA,\n    0x968F: 0x67FE,\n    0x9690: 0x9C52,\n    0x9691: 0x685D,\n    0x9692: 0x4EA6,\n    0x9693: 0x4FE3,\n    0x9694: 0x53C8,\n    0x9695: 0x62B9,\n    0x9696: 0x672B,\n    0x9697: 0x6CAB,\n    0x9698: 0x8FC4,\n    0x9699: 0x4FAD,\n    0x969A: 0x7E6D,\n    0x969B: 0x9EBF,\n    0x969C: 0x4E07,\n    0x969D: 0x6162,\n    0x969E: 0x6E80,\n    0x969F: 0x6F2B,\n    0x96A0: 0x8513,\n    0x96A1: 0x5473,\n    0x96A2: 0x672A,\n    0x96A3: 0x9B45,\n    0x96A4: 0x5DF3,\n    0x96A5: 0x7B95,\n    0x96A6: 0x5CAC,\n    0x96A7: 0x5BC6,\n    0x96A8: 0x871C,\n    0x96A9: 0x6E4A,\n    0x96AA: 0x84D1,\n    0x96AB: 0x7A14,\n    0x96AC: 0x8108,\n    0x96AD: 0x5999,\n    0x96AE: 0x7C8D,\n    0x96AF: 0x6C11,\n    0x96B0: 0x7720,\n    0x96B1: 0x52D9,\n    0x96B2: 0x5922,\n    0x96B3: 0x7121,\n    0x96B4: 0x725F,\n    0x96B5: 0x77DB,\n    0x96B6: 0x9727,\n    0x96B7: 0x9D61,\n    0x96B8: 0x690B,\n    0x96B9: 0x5A7F,\n    0x96BA: 0x5A18,\n    0x96BB: 0x51A5,\n    0x96BC: 0x540D,\n    0x96BD: 0x547D,\n    0x96BE: 0x660E,\n    0x96BF: 0x76DF,\n    0x96C0: 0x8FF7,\n    0x96C1: 0x9298,\n    0x96C2: 0x9CF4,\n    0x96C3: 0x59EA,\n    0x96C4: 0x725D,\n    0x96C5: 0x6EC5,\n    0x96C6: 0x514D,\n    0x96C7: 0x68C9,\n    0x96C8: 0x7DBF,\n    0x96C9: 0x7DEC,\n    0x96CA: 0x9762,\n    0x96CB: 0x9EBA,\n    0x96CC: 0x6478,\n    0x96CD: 0x6A21,\n    0x96CE: 0x8302,\n    0x96CF: 0x5984,\n    0x96D0: 0x5B5F,\n    0x96D1: 0x6BDB,\n    0x96D2: 0x731B,\n    0x96D3: 0x76F2,\n    0x96D4: 0x7DB2,\n    0x96D5: 0x8017,\n    0x96D6: 0x8499,\n    0x96D7: 0x5132,\n    0x96D8: 0x6728,\n    0x96D9: 0x9ED9,\n    0x96DA: 0x76EE,\n    0x96DB: 0x6762,\n    0x96DC: 0x52FF,\n    0x96DD: 0x9905,\n    0x96DE: 0x5C24,\n    0x96DF: 0x623B,\n    0x96E0: 0x7C7E,\n    0x96E1: 0x8CB0,\n    0x96E2: 0x554F,\n    0x96E3: 0x60B6,\n    0x96E4: 0x7D0B,\n    0x96E5: 0x9580,\n    0x96E6: 0x5301,\n    0x96E7: 0x4E5F,\n    0x96E8: 0x51B6,\n    0x96E9: 0x591C,\n    0x96EA: 0x723A,\n    0x96EB: 0x8036,\n    0x96EC: 0x91CE,\n    0x96ED: 0x5F25,\n    0x96EE: 0x77E2,\n    0x96EF: 0x5384,\n    0x96F0: 0x5F79,\n    0x96F1: 0x7D04,\n    0x96F2: 0x85AC,\n    0x96F3: 0x8A33,\n    0x96F4: 0x8E8D,\n    0x96F5: 0x9756,\n    0x96F6: 0x67F3,\n    0x96F7: 0x85AE,\n    0x96F8: 0x9453,\n    0x96F9: 0x6109,\n    0x96FA: 0x6108,\n    0x96FB: 0x6CB9,\n    0x96FC: 0x7652,\n    0x9740: 0x8AED,\n    0x9741: 0x8F38,\n    0x9742: 0x552F,\n    0x9743: 0x4F51,\n    0x9744: 0x512A,\n    0x9745: 0x52C7,\n    0x9746: 0x53CB,\n    0x9747: 0x5BA5,\n    0x9748: 0x5E7D,\n    0x9749: 0x60A0,\n    0x974A: 0x6182,\n    0x974B: 0x63D6,\n    0x974C: 0x6709,\n    0x974D: 0x67DA,\n    0x974E: 0x6E67,\n    0x974F: 0x6D8C,\n    0x9750: 0x7336,\n    0x9751: 0x7337,\n    0x9752: 0x7531,\n    0x9753: 0x7950,\n    0x9754: 0x88D5,\n    0x9755: 0x8A98,\n    0x9756: 0x904A,\n    0x9757: 0x9091,\n    0x9758: 0x90F5,\n    0x9759: 0x96C4,\n    0x975A: 0x878D,\n    0x975B: 0x5915,\n    0x975C: 0x4E88,\n    0x975D: 0x4F59,\n    0x975E: 0x4E0E,\n    0x975F: 0x8A89,\n    0x9760: 0x8F3F,\n    0x9761: 0x9810,\n    0x9762: 0x50AD,\n    0x9763: 0x5E7C,\n    0x9764: 0x5996,\n    0x9765: 0x5BB9,\n    0x9766: 0x5EB8,\n    0x9767: 0x63DA,\n    0x9768: 0x63FA,\n    0x9769: 0x64C1,\n    0x976A: 0x66DC,\n    0x976B: 0x694A,\n    0x976C: 0x69D8,\n    0x976D: 0x6D0B,\n    0x976E: 0x6EB6,\n    0x976F: 0x7194,\n    0x9770: 0x7528,\n    0x9771: 0x7AAF,\n    0x9772: 0x7F8A,\n    0x9773: 0x8000,\n    0x9774: 0x8449,\n    0x9775: 0x84C9,\n    0x9776: 0x8981,\n    0x9777: 0x8B21,\n    0x9778: 0x8E0A,\n    0x9779: 0x9065,\n    0x977A: 0x967D,\n    0x977B: 0x990A,\n    0x977C: 0x617E,\n    0x977D: 0x6291,\n    0x977E: 0x6B32,\n    0x9780: 0x6C83,\n    0x9781: 0x6D74,\n    0x9782: 0x7FCC,\n    0x9783: 0x7FFC,\n    0x9784: 0x6DC0,\n    0x9785: 0x7F85,\n    0x9786: 0x87BA,\n    0x9787: 0x88F8,\n    0x9788: 0x6765,\n    0x9789: 0x83B1,\n    0x978A: 0x983C,\n    0x978B: 0x96F7,\n    0x978C: 0x6D1B,\n    0x978D: 0x7D61,\n    0x978E: 0x843D,\n    0x978F: 0x916A,\n    0x9790: 0x4E71,\n    0x9791: 0x5375,\n    0x9792: 0x5D50,\n    0x9793: 0x6B04,\n    0x9794: 0x6FEB,\n    0x9795: 0x85CD,\n    0x9796: 0x862D,\n    0x9797: 0x89A7,\n    0x9798: 0x5229,\n    0x9799: 0x540F,\n    0x979A: 0x5C65,\n    0x979B: 0x674E,\n    0x979C: 0x68A8,\n    0x979D: 0x7406,\n    0x979E: 0x7483,\n    0x979F: 0x75E2,\n    0x97A0: 0x88CF,\n    0x97A1: 0x88E1,\n    0x97A2: 0x91CC,\n    0x97A3: 0x96E2,\n    0x97A4: 0x9678,\n    0x97A5: 0x5F8B,\n    0x97A6: 0x7387,\n    0x97A7: 0x7ACB,\n    0x97A8: 0x844E,\n    0x97A9: 0x63A0,\n    0x97AA: 0x7565,\n    0x97AB: 0x5289,\n    0x97AC: 0x6D41,\n    0x97AD: 0x6E9C,\n    0x97AE: 0x7409,\n    0x97AF: 0x7559,\n    0x97B0: 0x786B,\n    0x97B1: 0x7C92,\n    0x97B2: 0x9686,\n    0x97B3: 0x7ADC,\n    0x97B4: 0x9F8D,\n    0x97B5: 0x4FB6,\n    0x97B6: 0x616E,\n    0x97B7: 0x65C5,\n    0x97B8: 0x865C,\n    0x97B9: 0x4E86,\n    0x97BA: 0x4EAE,\n    0x97BB: 0x50DA,\n    0x97BC: 0x4E21,\n    0x97BD: 0x51CC,\n    0x97BE: 0x5BEE,\n    0x97BF: 0x6599,\n    0x97C0: 0x6881,\n    0x97C1: 0x6DBC,\n    0x97C2: 0x731F,\n    0x97C3: 0x7642,\n    0x97C4: 0x77AD,\n    0x97C5: 0x7A1C,\n    0x97C6: 0x7CE7,\n    0x97C7: 0x826F,\n    0x97C8: 0x8AD2,\n    0x97C9: 0x907C,\n    0x97CA: 0x91CF,\n    0x97CB: 0x9675,\n    0x97CC: 0x9818,\n    0x97CD: 0x529B,\n    0x97CE: 0x7DD1,\n    0x97CF: 0x502B,\n    0x97D0: 0x5398,\n    0x97D1: 0x6797,\n    0x97D2: 0x6DCB,\n    0x97D3: 0x71D0,\n    0x97D4: 0x7433,\n    0x97D5: 0x81E8,\n    0x97D6: 0x8F2A,\n    0x97D7: 0x96A3,\n    0x97D8: 0x9C57,\n    0x97D9: 0x9E9F,\n    0x97DA: 0x7460,\n    0x97DB: 0x5841,\n    0x97DC: 0x6D99,\n    0x97DD: 0x7D2F,\n    0x97DE: 0x985E,\n    0x97DF: 0x4EE4,\n    0x97E0: 0x4F36,\n    0x97E1: 0x4F8B,\n    0x97E2: 0x51B7,\n    0x97E3: 0x52B1,\n    0x97E4: 0x5DBA,\n    0x97E5: 0x601C,\n    0x97E6: 0x73B2,\n    0x97E7: 0x793C,\n    0x97E8: 0x82D3,\n    0x97E9: 0x9234,\n    0x97EA: 0x96B7,\n    0x97EB: 0x96F6,\n    0x97EC: 0x970A,\n    0x97ED: 0x9E97,\n    0x97EE: 0x9F62,\n    0x97EF: 0x66A6,\n    0x97F0: 0x6B74,\n    0x97F1: 0x5217,\n    0x97F2: 0x52A3,\n    0x97F3: 0x70C8,\n    0x97F4: 0x88C2,\n    0x97F5: 0x5EC9,\n    0x97F6: 0x604B,\n    0x97F7: 0x6190,\n    0x97F8: 0x6F23,\n    0x97F9: 0x7149,\n    0x97FA: 0x7C3E,\n    0x97FB: 0x7DF4,\n    0x97FC: 0x806F,\n    0x9840: 0x84EE,\n    0x9841: 0x9023,\n    0x9842: 0x932C,\n    0x9843: 0x5442,\n    0x9844: 0x9B6F,\n    0x9845: 0x6AD3,\n    0x9846: 0x7089,\n    0x9847: 0x8CC2,\n    0x9848: 0x8DEF,\n    0x9849: 0x9732,\n    0x984A: 0x52B4,\n    0x984B: 0x5A41,\n    0x984C: 0x5ECA,\n    0x984D: 0x5F04,\n    0x984E: 0x6717,\n    0x984F: 0x697C,\n    0x9850: 0x6994,\n    0x9851: 0x6D6A,\n    0x9852: 0x6F0F,\n    0x9853: 0x7262,\n    0x9854: 0x72FC,\n    0x9855: 0x7BED,\n    0x9856: 0x8001,\n    0x9857: 0x807E,\n    0x9858: 0x874B,\n    0x9859: 0x90CE,\n    0x985A: 0x516D,\n    0x985B: 0x9E93,\n    0x985C: 0x7984,\n    0x985D: 0x808B,\n    0x985E: 0x9332,\n    0x985F: 0x8AD6,\n    0x9860: 0x502D,\n    0x9861: 0x548C,\n    0x9862: 0x8A71,\n    0x9863: 0x6B6A,\n    0x9864: 0x8CC4,\n    0x9865: 0x8107,\n    0x9866: 0x60D1,\n    0x9867: 0x67A0,\n    0x9868: 0x9DF2,\n    0x9869: 0x4E99,\n    0x986A: 0x4E98,\n    0x986B: 0x9C10,\n    0x986C: 0x8A6B,\n    0x986D: 0x85C1,\n    0x986E: 0x8568,\n    0x986F: 0x6900,\n    0x9870: 0x6E7E,\n    0x9871: 0x7897,\n    0x9872: 0x8155,\n    0x989F: 0x5F0C,\n    0x98A0: 0x4E10,\n    0x98A1: 0x4E15,\n    0x98A2: 0x4E2A,\n    0x98A3: 0x4E31,\n    0x98A4: 0x4E36,\n    0x98A5: 0x4E3C,\n    0x98A6: 0x4E3F,\n    0x98A7: 0x4E42,\n    0x98A8: 0x4E56,\n    0x98A9: 0x4E58,\n    0x98AA: 0x4E82,\n    0x98AB: 0x4E85,\n    0x98AC: 0x8C6B,\n    0x98AD: 0x4E8A,\n    0x98AE: 0x8212,\n    0x98AF: 0x5F0D,\n    0x98B0: 0x4E8E,\n    0x98B1: 0x4E9E,\n    0x98B2: 0x4E9F,\n    0x98B3: 0x4EA0,\n    0x98B4: 0x4EA2,\n    0x98B5: 0x4EB0,\n    0x98B6: 0x4EB3,\n    0x98B7: 0x4EB6,\n    0x98B8: 0x4ECE,\n    0x98B9: 0x4ECD,\n    0x98BA: 0x4EC4,\n    0x98BB: 0x4EC6,\n    0x98BC: 0x4EC2,\n    0x98BD: 0x4ED7,\n    0x98BE: 0x4EDE,\n    0x98BF: 0x4EED,\n    0x98C0: 0x4EDF,\n    0x98C1: 0x4EF7,\n    0x98C2: 0x4F09,\n    0x98C3: 0x4F5A,\n    0x98C4: 0x4F30,\n    0x98C5: 0x4F5B,\n    0x98C6: 0x4F5D,\n    0x98C7: 0x4F57,\n    0x98C8: 0x4F47,\n    0x98C9: 0x4F76,\n    0x98CA: 0x4F88,\n    0x98CB: 0x4F8F,\n    0x98CC: 0x4F98,\n    0x98CD: 0x4F7B,\n    0x98CE: 0x4F69,\n    0x98CF: 0x4F70,\n    0x98D0: 0x4F91,\n    0x98D1: 0x4F6F,\n    0x98D2: 0x4F86,\n    0x98D3: 0x4F96,\n    0x98D4: 0x5118,\n    0x98D5: 0x4FD4,\n    0x98D6: 0x4FDF,\n    0x98D7: 0x4FCE,\n    0x98D8: 0x4FD8,\n    0x98D9: 0x4FDB,\n    0x98DA: 0x4FD1,\n    0x98DB: 0x4FDA,\n    0x98DC: 0x4FD0,\n    0x98DD: 0x4FE4,\n    0x98DE: 0x4FE5,\n    0x98DF: 0x501A,\n    0x98E0: 0x5028,\n    0x98E1: 0x5014,\n    0x98E2: 0x502A,\n    0x98E3: 0x5025,\n    0x98E4: 0x5005,\n    0x98E5: 0x4F1C,\n    0x98E6: 0x4FF6,\n    0x98E7: 0x5021,\n    0x98E8: 0x5029,\n    0x98E9: 0x502C,\n    0x98EA: 0x4FFE,\n    0x98EB: 0x4FEF,\n    0x98EC: 0x5011,\n    0x98ED: 0x5006,\n    0x98EE: 0x5043,\n    0x98EF: 0x5047,\n    0x98F0: 0x6703,\n    0x98F1: 0x5055,\n    0x98F2: 0x5050,\n    0x98F3: 0x5048,\n    0x98F4: 0x505A,\n    0x98F5: 0x5056,\n    0x98F6: 0x506C,\n    0x98F7: 0x5078,\n    0x98F8: 0x5080,\n    0x98F9: 0x509A,\n    0x98FA: 0x5085,\n    0x98FB: 0x50B4,\n    0x98FC: 0x50B2,\n    0x9940: 0x50C9,\n    0x9941: 0x50CA,\n    0x9942: 0x50B3,\n    0x9943: 0x50C2,\n    0x9944: 0x50D6,\n    0x9945: 0x50DE,\n    0x9946: 0x50E5,\n    0x9947: 0x50ED,\n    0x9948: 0x50E3,\n    0x9949: 0x50EE,\n    0x994A: 0x50F9,\n    0x994B: 0x50F5,\n    0x994C: 0x5109,\n    0x994D: 0x5101,\n    0x994E: 0x5102,\n    0x994F: 0x5116,\n    0x9950: 0x5115,\n    0x9951: 0x5114,\n    0x9952: 0x511A,\n    0x9953: 0x5121,\n    0x9954: 0x513A,\n    0x9955: 0x5137,\n    0x9956: 0x513C,\n    0x9957: 0x513B,\n    0x9958: 0x513F,\n    0x9959: 0x5140,\n    0x995A: 0x5152,\n    0x995B: 0x514C,\n    0x995C: 0x5154,\n    0x995D: 0x5162,\n    0x995E: 0x7AF8,\n    0x995F: 0x5169,\n    0x9960: 0x516A,\n    0x9961: 0x516E,\n    0x9962: 0x5180,\n    0x9963: 0x5182,\n    0x9964: 0x56D8,\n    0x9965: 0x518C,\n    0x9966: 0x5189,\n    0x9967: 0x518F,\n    0x9968: 0x5191,\n    0x9969: 0x5193,\n    0x996A: 0x5195,\n    0x996B: 0x5196,\n    0x996C: 0x51A4,\n    0x996D: 0x51A6,\n    0x996E: 0x51A2,\n    0x996F: 0x51A9,\n    0x9970: 0x51AA,\n    0x9971: 0x51AB,\n    0x9972: 0x51B3,\n    0x9973: 0x51B1,\n    0x9974: 0x51B2,\n    0x9975: 0x51B0,\n    0x9976: 0x51B5,\n    0x9977: 0x51BD,\n    0x9978: 0x51C5,\n    0x9979: 0x51C9,\n    0x997A: 0x51DB,\n    0x997B: 0x51E0,\n    0x997C: 0x8655,\n    0x997D: 0x51E9,\n    0x997E: 0x51ED,\n    0x9980: 0x51F0,\n    0x9981: 0x51F5,\n    0x9982: 0x51FE,\n    0x9983: 0x5204,\n    0x9984: 0x520B,\n    0x9985: 0x5214,\n    0x9986: 0x520E,\n    0x9987: 0x5227,\n    0x9988: 0x522A,\n    0x9989: 0x522E,\n    0x998A: 0x5233,\n    0x998B: 0x5239,\n    0x998C: 0x524F,\n    0x998D: 0x5244,\n    0x998E: 0x524B,\n    0x998F: 0x524C,\n    0x9990: 0x525E,\n    0x9991: 0x5254,\n    0x9992: 0x526A,\n    0x9993: 0x5274,\n    0x9994: 0x5269,\n    0x9995: 0x5273,\n    0x9996: 0x527F,\n    0x9997: 0x527D,\n    0x9998: 0x528D,\n    0x9999: 0x5294,\n    0x999A: 0x5292,\n    0x999B: 0x5271,\n    0x999C: 0x5288,\n    0x999D: 0x5291,\n    0x999E: 0x8FA8,\n    0x999F: 0x8FA7,\n    0x99A0: 0x52AC,\n    0x99A1: 0x52AD,\n    0x99A2: 0x52BC,\n    0x99A3: 0x52B5,\n    0x99A4: 0x52C1,\n    0x99A5: 0x52CD,\n    0x99A6: 0x52D7,\n    0x99A7: 0x52DE,\n    0x99A8: 0x52E3,\n    0x99A9: 0x52E6,\n    0x99AA: 0x98ED,\n    0x99AB: 0x52E0,\n    0x99AC: 0x52F3,\n    0x99AD: 0x52F5,\n    0x99AE: 0x52F8,\n    0x99AF: 0x52F9,\n    0x99B0: 0x5306,\n    0x99B1: 0x5308,\n    0x99B2: 0x7538,\n    0x99B3: 0x530D,\n    0x99B4: 0x5310,\n    0x99B5: 0x530F,\n    0x99B6: 0x5315,\n    0x99B7: 0x531A,\n    0x99B8: 0x5323,\n    0x99B9: 0x532F,\n    0x99BA: 0x5331,\n    0x99BB: 0x5333,\n    0x99BC: 0x5338,\n    0x99BD: 0x5340,\n    0x99BE: 0x5346,\n    0x99BF: 0x5345,\n    0x99C0: 0x4E17,\n    0x99C1: 0x5349,\n    0x99C2: 0x534D,\n    0x99C3: 0x51D6,\n    0x99C4: 0x535E,\n    0x99C5: 0x5369,\n    0x99C6: 0x536E,\n    0x99C7: 0x5918,\n    0x99C8: 0x537B,\n    0x99C9: 0x5377,\n    0x99CA: 0x5382,\n    0x99CB: 0x5396,\n    0x99CC: 0x53A0,\n    0x99CD: 0x53A6,\n    0x99CE: 0x53A5,\n    0x99CF: 0x53AE,\n    0x99D0: 0x53B0,\n    0x99D1: 0x53B6,\n    0x99D2: 0x53C3,\n    0x99D3: 0x7C12,\n    0x99D4: 0x96D9,\n    0x99D5: 0x53DF,\n    0x99D6: 0x66FC,\n    0x99D7: 0x71EE,\n    0x99D8: 0x53EE,\n    0x99D9: 0x53E8,\n    0x99DA: 0x53ED,\n    0x99DB: 0x53FA,\n    0x99DC: 0x5401,\n    0x99DD: 0x543D,\n    0x99DE: 0x5440,\n    0x99DF: 0x542C,\n    0x99E0: 0x542D,\n    0x99E1: 0x543C,\n    0x99E2: 0x542E,\n    0x99E3: 0x5436,\n    0x99E4: 0x5429,\n    0x99E5: 0x541D,\n    0x99E6: 0x544E,\n    0x99E7: 0x548F,\n    0x99E8: 0x5475,\n    0x99E9: 0x548E,\n    0x99EA: 0x545F,\n    0x99EB: 0x5471,\n    0x99EC: 0x5477,\n    0x99ED: 0x5470,\n    0x99EE: 0x5492,\n    0x99EF: 0x547B,\n    0x99F0: 0x5480,\n    0x99F1: 0x5476,\n    0x99F2: 0x5484,\n    0x99F3: 0x5490,\n    0x99F4: 0x5486,\n    0x99F5: 0x54C7,\n    0x99F6: 0x54A2,\n    0x99F7: 0x54B8,\n    0x99F8: 0x54A5,\n    0x99F9: 0x54AC,\n    0x99FA: 0x54C4,\n    0x99FB: 0x54C8,\n    0x99FC: 0x54A8,\n    0x9A40: 0x54AB,\n    0x9A41: 0x54C2,\n    0x9A42: 0x54A4,\n    0x9A43: 0x54BE,\n    0x9A44: 0x54BC,\n    0x9A45: 0x54D8,\n    0x9A46: 0x54E5,\n    0x9A47: 0x54E6,\n    0x9A48: 0x550F,\n    0x9A49: 0x5514,\n    0x9A4A: 0x54FD,\n    0x9A4B: 0x54EE,\n    0x9A4C: 0x54ED,\n    0x9A4D: 0x54FA,\n    0x9A4E: 0x54E2,\n    0x9A4F: 0x5539,\n    0x9A50: 0x5540,\n    0x9A51: 0x5563,\n    0x9A52: 0x554C,\n    0x9A53: 0x552E,\n    0x9A54: 0x555C,\n    0x9A55: 0x5545,\n    0x9A56: 0x5556,\n    0x9A57: 0x5557,\n    0x9A58: 0x5538,\n    0x9A59: 0x5533,\n    0x9A5A: 0x555D,\n    0x9A5B: 0x5599,\n    0x9A5C: 0x5580,\n    0x9A5D: 0x54AF,\n    0x9A5E: 0x558A,\n    0x9A5F: 0x559F,\n    0x9A60: 0x557B,\n    0x9A61: 0x557E,\n    0x9A62: 0x5598,\n    0x9A63: 0x559E,\n    0x9A64: 0x55AE,\n    0x9A65: 0x557C,\n    0x9A66: 0x5583,\n    0x9A67: 0x55A9,\n    0x9A68: 0x5587,\n    0x9A69: 0x55A8,\n    0x9A6A: 0x55DA,\n    0x9A6B: 0x55C5,\n    0x9A6C: 0x55DF,\n    0x9A6D: 0x55C4,\n    0x9A6E: 0x55DC,\n    0x9A6F: 0x55E4,\n    0x9A70: 0x55D4,\n    0x9A71: 0x5614,\n    0x9A72: 0x55F7,\n    0x9A73: 0x5616,\n    0x9A74: 0x55FE,\n    0x9A75: 0x55FD,\n    0x9A76: 0x561B,\n    0x9A77: 0x55F9,\n    0x9A78: 0x564E,\n    0x9A79: 0x5650,\n    0x9A7A: 0x71DF,\n    0x9A7B: 0x5634,\n    0x9A7C: 0x5636,\n    0x9A7D: 0x5632,\n    0x9A7E: 0x5638,\n    0x9A80: 0x566B,\n    0x9A81: 0x5664,\n    0x9A82: 0x562F,\n    0x9A83: 0x566C,\n    0x9A84: 0x566A,\n    0x9A85: 0x5686,\n    0x9A86: 0x5680,\n    0x9A87: 0x568A,\n    0x9A88: 0x56A0,\n    0x9A89: 0x5694,\n    0x9A8A: 0x568F,\n    0x9A8B: 0x56A5,\n    0x9A8C: 0x56AE,\n    0x9A8D: 0x56B6,\n    0x9A8E: 0x56B4,\n    0x9A8F: 0x56C2,\n    0x9A90: 0x56BC,\n    0x9A91: 0x56C1,\n    0x9A92: 0x56C3,\n    0x9A93: 0x56C0,\n    0x9A94: 0x56C8,\n    0x9A95: 0x56CE,\n    0x9A96: 0x56D1,\n    0x9A97: 0x56D3,\n    0x9A98: 0x56D7,\n    0x9A99: 0x56EE,\n    0x9A9A: 0x56F9,\n    0x9A9B: 0x5700,\n    0x9A9C: 0x56FF,\n    0x9A9D: 0x5704,\n    0x9A9E: 0x5709,\n    0x9A9F: 0x5708,\n    0x9AA0: 0x570B,\n    0x9AA1: 0x570D,\n    0x9AA2: 0x5713,\n    0x9AA3: 0x5718,\n    0x9AA4: 0x5716,\n    0x9AA5: 0x55C7,\n    0x9AA6: 0x571C,\n    0x9AA7: 0x5726,\n    0x9AA8: 0x5737,\n    0x9AA9: 0x5738,\n    0x9AAA: 0x574E,\n    0x9AAB: 0x573B,\n    0x9AAC: 0x5740,\n    0x9AAD: 0x574F,\n    0x9AAE: 0x5769,\n    0x9AAF: 0x57C0,\n    0x9AB0: 0x5788,\n    0x9AB1: 0x5761,\n    0x9AB2: 0x577F,\n    0x9AB3: 0x5789,\n    0x9AB4: 0x5793,\n    0x9AB5: 0x57A0,\n    0x9AB6: 0x57B3,\n    0x9AB7: 0x57A4,\n    0x9AB8: 0x57AA,\n    0x9AB9: 0x57B0,\n    0x9ABA: 0x57C3,\n    0x9ABB: 0x57C6,\n    0x9ABC: 0x57D4,\n    0x9ABD: 0x57D2,\n    0x9ABE: 0x57D3,\n    0x9ABF: 0x580A,\n    0x9AC0: 0x57D6,\n    0x9AC1: 0x57E3,\n    0x9AC2: 0x580B,\n    0x9AC3: 0x5819,\n    0x9AC4: 0x581D,\n    0x9AC5: 0x5872,\n    0x9AC6: 0x5821,\n    0x9AC7: 0x5862,\n    0x9AC8: 0x584B,\n    0x9AC9: 0x5870,\n    0x9ACA: 0x6BC0,\n    0x9ACB: 0x5852,\n    0x9ACC: 0x583D,\n    0x9ACD: 0x5879,\n    0x9ACE: 0x5885,\n    0x9ACF: 0x58B9,\n    0x9AD0: 0x589F,\n    0x9AD1: 0x58AB,\n    0x9AD2: 0x58BA,\n    0x9AD3: 0x58DE,\n    0x9AD4: 0x58BB,\n    0x9AD5: 0x58B8,\n    0x9AD6: 0x58AE,\n    0x9AD7: 0x58C5,\n    0x9AD8: 0x58D3,\n    0x9AD9: 0x58D1,\n    0x9ADA: 0x58D7,\n    0x9ADB: 0x58D9,\n    0x9ADC: 0x58D8,\n    0x9ADD: 0x58E5,\n    0x9ADE: 0x58DC,\n    0x9ADF: 0x58E4,\n    0x9AE0: 0x58DF,\n    0x9AE1: 0x58EF,\n    0x9AE2: 0x58FA,\n    0x9AE3: 0x58F9,\n    0x9AE4: 0x58FB,\n    0x9AE5: 0x58FC,\n    0x9AE6: 0x58FD,\n    0x9AE7: 0x5902,\n    0x9AE8: 0x590A,\n    0x9AE9: 0x5910,\n    0x9AEA: 0x591B,\n    0x9AEB: 0x68A6,\n    0x9AEC: 0x5925,\n    0x9AED: 0x592C,\n    0x9AEE: 0x592D,\n    0x9AEF: 0x5932,\n    0x9AF0: 0x5938,\n    0x9AF1: 0x593E,\n    0x9AF2: 0x7AD2,\n    0x9AF3: 0x5955,\n    0x9AF4: 0x5950,\n    0x9AF5: 0x594E,\n    0x9AF6: 0x595A,\n    0x9AF7: 0x5958,\n    0x9AF8: 0x5962,\n    0x9AF9: 0x5960,\n    0x9AFA: 0x5967,\n    0x9AFB: 0x596C,\n    0x9AFC: 0x5969,\n    0x9B40: 0x5978,\n    0x9B41: 0x5981,\n    0x9B42: 0x599D,\n    0x9B43: 0x4F5E,\n    0x9B44: 0x4FAB,\n    0x9B45: 0x59A3,\n    0x9B46: 0x59B2,\n    0x9B47: 0x59C6,\n    0x9B48: 0x59E8,\n    0x9B49: 0x59DC,\n    0x9B4A: 0x598D,\n    0x9B4B: 0x59D9,\n    0x9B4C: 0x59DA,\n    0x9B4D: 0x5A25,\n    0x9B4E: 0x5A1F,\n    0x9B4F: 0x5A11,\n    0x9B50: 0x5A1C,\n    0x9B51: 0x5A09,\n    0x9B52: 0x5A1A,\n    0x9B53: 0x5A40,\n    0x9B54: 0x5A6C,\n    0x9B55: 0x5A49,\n    0x9B56: 0x5A35,\n    0x9B57: 0x5A36,\n    0x9B58: 0x5A62,\n    0x9B59: 0x5A6A,\n    0x9B5A: 0x5A9A,\n    0x9B5B: 0x5ABC,\n    0x9B5C: 0x5ABE,\n    0x9B5D: 0x5ACB,\n    0x9B5E: 0x5AC2,\n    0x9B5F: 0x5ABD,\n    0x9B60: 0x5AE3,\n    0x9B61: 0x5AD7,\n    0x9B62: 0x5AE6,\n    0x9B63: 0x5AE9,\n    0x9B64: 0x5AD6,\n    0x9B65: 0x5AFA,\n    0x9B66: 0x5AFB,\n    0x9B67: 0x5B0C,\n    0x9B68: 0x5B0B,\n    0x9B69: 0x5B16,\n    0x9B6A: 0x5B32,\n    0x9B6B: 0x5AD0,\n    0x9B6C: 0x5B2A,\n    0x9B6D: 0x5B36,\n    0x9B6E: 0x5B3E,\n    0x9B6F: 0x5B43,\n    0x9B70: 0x5B45,\n    0x9B71: 0x5B40,\n    0x9B72: 0x5B51,\n    0x9B73: 0x5B55,\n    0x9B74: 0x5B5A,\n    0x9B75: 0x5B5B,\n    0x9B76: 0x5B65,\n    0x9B77: 0x5B69,\n    0x9B78: 0x5B70,\n    0x9B79: 0x5B73,\n    0x9B7A: 0x5B75,\n    0x9B7B: 0x5B78,\n    0x9B7C: 0x6588,\n    0x9B7D: 0x5B7A,\n    0x9B7E: 0x5B80,\n    0x9B80: 0x5B83,\n    0x9B81: 0x5BA6,\n    0x9B82: 0x5BB8,\n    0x9B83: 0x5BC3,\n    0x9B84: 0x5BC7,\n    0x9B85: 0x5BC9,\n    0x9B86: 0x5BD4,\n    0x9B87: 0x5BD0,\n    0x9B88: 0x5BE4,\n    0x9B89: 0x5BE6,\n    0x9B8A: 0x5BE2,\n    0x9B8B: 0x5BDE,\n    0x9B8C: 0x5BE5,\n    0x9B8D: 0x5BEB,\n    0x9B8E: 0x5BF0,\n    0x9B8F: 0x5BF6,\n    0x9B90: 0x5BF3,\n    0x9B91: 0x5C05,\n    0x9B92: 0x5C07,\n    0x9B93: 0x5C08,\n    0x9B94: 0x5C0D,\n    0x9B95: 0x5C13,\n    0x9B96: 0x5C20,\n    0x9B97: 0x5C22,\n    0x9B98: 0x5C28,\n    0x9B99: 0x5C38,\n    0x9B9A: 0x5C39,\n    0x9B9B: 0x5C41,\n    0x9B9C: 0x5C46,\n    0x9B9D: 0x5C4E,\n    0x9B9E: 0x5C53,\n    0x9B9F: 0x5C50,\n    0x9BA0: 0x5C4F,\n    0x9BA1: 0x5B71,\n    0x9BA2: 0x5C6C,\n    0x9BA3: 0x5C6E,\n    0x9BA4: 0x4E62,\n    0x9BA5: 0x5C76,\n    0x9BA6: 0x5C79,\n    0x9BA7: 0x5C8C,\n    0x9BA8: 0x5C91,\n    0x9BA9: 0x5C94,\n    0x9BAA: 0x599B,\n    0x9BAB: 0x5CAB,\n    0x9BAC: 0x5CBB,\n    0x9BAD: 0x5CB6,\n    0x9BAE: 0x5CBC,\n    0x9BAF: 0x5CB7,\n    0x9BB0: 0x5CC5,\n    0x9BB1: 0x5CBE,\n    0x9BB2: 0x5CC7,\n    0x9BB3: 0x5CD9,\n    0x9BB4: 0x5CE9,\n    0x9BB5: 0x5CFD,\n    0x9BB6: 0x5CFA,\n    0x9BB7: 0x5CED,\n    0x9BB8: 0x5D8C,\n    0x9BB9: 0x5CEA,\n    0x9BBA: 0x5D0B,\n    0x9BBB: 0x5D15,\n    0x9BBC: 0x5D17,\n    0x9BBD: 0x5D5C,\n    0x9BBE: 0x5D1F,\n    0x9BBF: 0x5D1B,\n    0x9BC0: 0x5D11,\n    0x9BC1: 0x5D14,\n    0x9BC2: 0x5D22,\n    0x9BC3: 0x5D1A,\n    0x9BC4: 0x5D19,\n    0x9BC5: 0x5D18,\n    0x9BC6: 0x5D4C,\n    0x9BC7: 0x5D52,\n    0x9BC8: 0x5D4E,\n    0x9BC9: 0x5D4B,\n    0x9BCA: 0x5D6C,\n    0x9BCB: 0x5D73,\n    0x9BCC: 0x5D76,\n    0x9BCD: 0x5D87,\n    0x9BCE: 0x5D84,\n    0x9BCF: 0x5D82,\n    0x9BD0: 0x5DA2,\n    0x9BD1: 0x5D9D,\n    0x9BD2: 0x5DAC,\n    0x9BD3: 0x5DAE,\n    0x9BD4: 0x5DBD,\n    0x9BD5: 0x5D90,\n    0x9BD6: 0x5DB7,\n    0x9BD7: 0x5DBC,\n    0x9BD8: 0x5DC9,\n    0x9BD9: 0x5DCD,\n    0x9BDA: 0x5DD3,\n    0x9BDB: 0x5DD2,\n    0x9BDC: 0x5DD6,\n    0x9BDD: 0x5DDB,\n    0x9BDE: 0x5DEB,\n    0x9BDF: 0x5DF2,\n    0x9BE0: 0x5DF5,\n    0x9BE1: 0x5E0B,\n    0x9BE2: 0x5E1A,\n    0x9BE3: 0x5E19,\n    0x9BE4: 0x5E11,\n    0x9BE5: 0x5E1B,\n    0x9BE6: 0x5E36,\n    0x9BE7: 0x5E37,\n    0x9BE8: 0x5E44,\n    0x9BE9: 0x5E43,\n    0x9BEA: 0x5E40,\n    0x9BEB: 0x5E4E,\n    0x9BEC: 0x5E57,\n    0x9BED: 0x5E54,\n    0x9BEE: 0x5E5F,\n    0x9BEF: 0x5E62,\n    0x9BF0: 0x5E64,\n    0x9BF1: 0x5E47,\n    0x9BF2: 0x5E75,\n    0x9BF3: 0x5E76,\n    0x9BF4: 0x5E7A,\n    0x9BF5: 0x9EBC,\n    0x9BF6: 0x5E7F,\n    0x9BF7: 0x5EA0,\n    0x9BF8: 0x5EC1,\n    0x9BF9: 0x5EC2,\n    0x9BFA: 0x5EC8,\n    0x9BFB: 0x5ED0,\n    0x9BFC: 0x5ECF,\n    0x9C40: 0x5ED6,\n    0x9C41: 0x5EE3,\n    0x9C42: 0x5EDD,\n    0x9C43: 0x5EDA,\n    0x9C44: 0x5EDB,\n    0x9C45: 0x5EE2,\n    0x9C46: 0x5EE1,\n    0x9C47: 0x5EE8,\n    0x9C48: 0x5EE9,\n    0x9C49: 0x5EEC,\n    0x9C4A: 0x5EF1,\n    0x9C4B: 0x5EF3,\n    0x9C4C: 0x5EF0,\n    0x9C4D: 0x5EF4,\n    0x9C4E: 0x5EF8,\n    0x9C4F: 0x5EFE,\n    0x9C50: 0x5F03,\n    0x9C51: 0x5F09,\n    0x9C52: 0x5F5D,\n    0x9C53: 0x5F5C,\n    0x9C54: 0x5F0B,\n    0x9C55: 0x5F11,\n    0x9C56: 0x5F16,\n    0x9C57: 0x5F29,\n    0x9C58: 0x5F2D,\n    0x9C59: 0x5F38,\n    0x9C5A: 0x5F41,\n    0x9C5B: 0x5F48,\n    0x9C5C: 0x5F4C,\n    0x9C5D: 0x5F4E,\n    0x9C5E: 0x5F2F,\n    0x9C5F: 0x5F51,\n    0x9C60: 0x5F56,\n    0x9C61: 0x5F57,\n    0x9C62: 0x5F59,\n    0x9C63: 0x5F61,\n    0x9C64: 0x5F6D,\n    0x9C65: 0x5F73,\n    0x9C66: 0x5F77,\n    0x9C67: 0x5F83,\n    0x9C68: 0x5F82,\n    0x9C69: 0x5F7F,\n    0x9C6A: 0x5F8A,\n    0x9C6B: 0x5F88,\n    0x9C6C: 0x5F91,\n    0x9C6D: 0x5F87,\n    0x9C6E: 0x5F9E,\n    0x9C6F: 0x5F99,\n    0x9C70: 0x5F98,\n    0x9C71: 0x5FA0,\n    0x9C72: 0x5FA8,\n    0x9C73: 0x5FAD,\n    0x9C74: 0x5FBC,\n    0x9C75: 0x5FD6,\n    0x9C76: 0x5FFB,\n    0x9C77: 0x5FE4,\n    0x9C78: 0x5FF8,\n    0x9C79: 0x5FF1,\n    0x9C7A: 0x5FDD,\n    0x9C7B: 0x60B3,\n    0x9C7C: 0x5FFF,\n    0x9C7D: 0x6021,\n    0x9C7E: 0x6060,\n    0x9C80: 0x6019,\n    0x9C81: 0x6010,\n    0x9C82: 0x6029,\n    0x9C83: 0x600E,\n    0x9C84: 0x6031,\n    0x9C85: 0x601B,\n    0x9C86: 0x6015,\n    0x9C87: 0x602B,\n    0x9C88: 0x6026,\n    0x9C89: 0x600F,\n    0x9C8A: 0x603A,\n    0x9C8B: 0x605A,\n    0x9C8C: 0x6041,\n    0x9C8D: 0x606A,\n    0x9C8E: 0x6077,\n    0x9C8F: 0x605F,\n    0x9C90: 0x604A,\n    0x9C91: 0x6046,\n    0x9C92: 0x604D,\n    0x9C93: 0x6063,\n    0x9C94: 0x6043,\n    0x9C95: 0x6064,\n    0x9C96: 0x6042,\n    0x9C97: 0x606C,\n    0x9C98: 0x606B,\n    0x9C99: 0x6059,\n    0x9C9A: 0x6081,\n    0x9C9B: 0x608D,\n    0x9C9C: 0x60E7,\n    0x9C9D: 0x6083,\n    0x9C9E: 0x609A,\n    0x9C9F: 0x6084,\n    0x9CA0: 0x609B,\n    0x9CA1: 0x6096,\n    0x9CA2: 0x6097,\n    0x9CA3: 0x6092,\n    0x9CA4: 0x60A7,\n    0x9CA5: 0x608B,\n    0x9CA6: 0x60E1,\n    0x9CA7: 0x60B8,\n    0x9CA8: 0x60E0,\n    0x9CA9: 0x60D3,\n    0x9CAA: 0x60B4,\n    0x9CAB: 0x5FF0,\n    0x9CAC: 0x60BD,\n    0x9CAD: 0x60C6,\n    0x9CAE: 0x60B5,\n    0x9CAF: 0x60D8,\n    0x9CB0: 0x614D,\n    0x9CB1: 0x6115,\n    0x9CB2: 0x6106,\n    0x9CB3: 0x60F6,\n    0x9CB4: 0x60F7,\n    0x9CB5: 0x6100,\n    0x9CB6: 0x60F4,\n    0x9CB7: 0x60FA,\n    0x9CB8: 0x6103,\n    0x9CB9: 0x6121,\n    0x9CBA: 0x60FB,\n    0x9CBB: 0x60F1,\n    0x9CBC: 0x610D,\n    0x9CBD: 0x610E,\n    0x9CBE: 0x6147,\n    0x9CBF: 0x613E,\n    0x9CC0: 0x6128,\n    0x9CC1: 0x6127,\n    0x9CC2: 0x614A,\n    0x9CC3: 0x613F,\n    0x9CC4: 0x613C,\n    0x9CC5: 0x612C,\n    0x9CC6: 0x6134,\n    0x9CC7: 0x613D,\n    0x9CC8: 0x6142,\n    0x9CC9: 0x6144,\n    0x9CCA: 0x6173,\n    0x9CCB: 0x6177,\n    0x9CCC: 0x6158,\n    0x9CCD: 0x6159,\n    0x9CCE: 0x615A,\n    0x9CCF: 0x616B,\n    0x9CD0: 0x6174,\n    0x9CD1: 0x616F,\n    0x9CD2: 0x6165,\n    0x9CD3: 0x6171,\n    0x9CD4: 0x615F,\n    0x9CD5: 0x615D,\n    0x9CD6: 0x6153,\n    0x9CD7: 0x6175,\n    0x9CD8: 0x6199,\n    0x9CD9: 0x6196,\n    0x9CDA: 0x6187,\n    0x9CDB: 0x61AC,\n    0x9CDC: 0x6194,\n    0x9CDD: 0x619A,\n    0x9CDE: 0x618A,\n    0x9CDF: 0x6191,\n    0x9CE0: 0x61AB,\n    0x9CE1: 0x61AE,\n    0x9CE2: 0x61CC,\n    0x9CE3: 0x61CA,\n    0x9CE4: 0x61C9,\n    0x9CE5: 0x61F7,\n    0x9CE6: 0x61C8,\n    0x9CE7: 0x61C3,\n    0x9CE8: 0x61C6,\n    0x9CE9: 0x61BA,\n    0x9CEA: 0x61CB,\n    0x9CEB: 0x7F79,\n    0x9CEC: 0x61CD,\n    0x9CED: 0x61E6,\n    0x9CEE: 0x61E3,\n    0x9CEF: 0x61F6,\n    0x9CF0: 0x61FA,\n    0x9CF1: 0x61F4,\n    0x9CF2: 0x61FF,\n    0x9CF3: 0x61FD,\n    0x9CF4: 0x61FC,\n    0x9CF5: 0x61FE,\n    0x9CF6: 0x6200,\n    0x9CF7: 0x6208,\n    0x9CF8: 0x6209,\n    0x9CF9: 0x620D,\n    0x9CFA: 0x620C,\n    0x9CFB: 0x6214,\n    0x9CFC: 0x621B,\n    0x9D40: 0x621E,\n    0x9D41: 0x6221,\n    0x9D42: 0x622A,\n    0x9D43: 0x622E,\n    0x9D44: 0x6230,\n    0x9D45: 0x6232,\n    0x9D46: 0x6233,\n    0x9D47: 0x6241,\n    0x9D48: 0x624E,\n    0x9D49: 0x625E,\n    0x9D4A: 0x6263,\n    0x9D4B: 0x625B,\n    0x9D4C: 0x6260,\n    0x9D4D: 0x6268,\n    0x9D4E: 0x627C,\n    0x9D4F: 0x6282,\n    0x9D50: 0x6289,\n    0x9D51: 0x627E,\n    0x9D52: 0x6292,\n    0x9D53: 0x6293,\n    0x9D54: 0x6296,\n    0x9D55: 0x62D4,\n    0x9D56: 0x6283,\n    0x9D57: 0x6294,\n    0x9D58: 0x62D7,\n    0x9D59: 0x62D1,\n    0x9D5A: 0x62BB,\n    0x9D5B: 0x62CF,\n    0x9D5C: 0x62FF,\n    0x9D5D: 0x62C6,\n    0x9D5E: 0x64D4,\n    0x9D5F: 0x62C8,\n    0x9D60: 0x62DC,\n    0x9D61: 0x62CC,\n    0x9D62: 0x62CA,\n    0x9D63: 0x62C2,\n    0x9D64: 0x62C7,\n    0x9D65: 0x629B,\n    0x9D66: 0x62C9,\n    0x9D67: 0x630C,\n    0x9D68: 0x62EE,\n    0x9D69: 0x62F1,\n    0x9D6A: 0x6327,\n    0x9D6B: 0x6302,\n    0x9D6C: 0x6308,\n    0x9D6D: 0x62EF,\n    0x9D6E: 0x62F5,\n    0x9D6F: 0x6350,\n    0x9D70: 0x633E,\n    0x9D71: 0x634D,\n    0x9D72: 0x641C,\n    0x9D73: 0x634F,\n    0x9D74: 0x6396,\n    0x9D75: 0x638E,\n    0x9D76: 0x6380,\n    0x9D77: 0x63AB,\n    0x9D78: 0x6376,\n    0x9D79: 0x63A3,\n    0x9D7A: 0x638F,\n    0x9D7B: 0x6389,\n    0x9D7C: 0x639F,\n    0x9D7D: 0x63B5,\n    0x9D7E: 0x636B,\n    0x9D80: 0x6369,\n    0x9D81: 0x63BE,\n    0x9D82: 0x63E9,\n    0x9D83: 0x63C0,\n    0x9D84: 0x63C6,\n    0x9D85: 0x63E3,\n    0x9D86: 0x63C9,\n    0x9D87: 0x63D2,\n    0x9D88: 0x63F6,\n    0x9D89: 0x63C4,\n    0x9D8A: 0x6416,\n    0x9D8B: 0x6434,\n    0x9D8C: 0x6406,\n    0x9D8D: 0x6413,\n    0x9D8E: 0x6426,\n    0x9D8F: 0x6436,\n    0x9D90: 0x651D,\n    0x9D91: 0x6417,\n    0x9D92: 0x6428,\n    0x9D93: 0x640F,\n    0x9D94: 0x6467,\n    0x9D95: 0x646F,\n    0x9D96: 0x6476,\n    0x9D97: 0x644E,\n    0x9D98: 0x652A,\n    0x9D99: 0x6495,\n    0x9D9A: 0x6493,\n    0x9D9B: 0x64A5,\n    0x9D9C: 0x64A9,\n    0x9D9D: 0x6488,\n    0x9D9E: 0x64BC,\n    0x9D9F: 0x64DA,\n    0x9DA0: 0x64D2,\n    0x9DA1: 0x64C5,\n    0x9DA2: 0x64C7,\n    0x9DA3: 0x64BB,\n    0x9DA4: 0x64D8,\n    0x9DA5: 0x64C2,\n    0x9DA6: 0x64F1,\n    0x9DA7: 0x64E7,\n    0x9DA8: 0x8209,\n    0x9DA9: 0x64E0,\n    0x9DAA: 0x64E1,\n    0x9DAB: 0x62AC,\n    0x9DAC: 0x64E3,\n    0x9DAD: 0x64EF,\n    0x9DAE: 0x652C,\n    0x9DAF: 0x64F6,\n    0x9DB0: 0x64F4,\n    0x9DB1: 0x64F2,\n    0x9DB2: 0x64FA,\n    0x9DB3: 0x6500,\n    0x9DB4: 0x64FD,\n    0x9DB5: 0x6518,\n    0x9DB6: 0x651C,\n    0x9DB7: 0x6505,\n    0x9DB8: 0x6524,\n    0x9DB9: 0x6523,\n    0x9DBA: 0x652B,\n    0x9DBB: 0x6534,\n    0x9DBC: 0x6535,\n    0x9DBD: 0x6537,\n    0x9DBE: 0x6536,\n    0x9DBF: 0x6538,\n    0x9DC0: 0x754B,\n    0x9DC1: 0x6548,\n    0x9DC2: 0x6556,\n    0x9DC3: 0x6555,\n    0x9DC4: 0x654D,\n    0x9DC5: 0x6558,\n    0x9DC6: 0x655E,\n    0x9DC7: 0x655D,\n    0x9DC8: 0x6572,\n    0x9DC9: 0x6578,\n    0x9DCA: 0x6582,\n    0x9DCB: 0x6583,\n    0x9DCC: 0x8B8A,\n    0x9DCD: 0x659B,\n    0x9DCE: 0x659F,\n    0x9DCF: 0x65AB,\n    0x9DD0: 0x65B7,\n    0x9DD1: 0x65C3,\n    0x9DD2: 0x65C6,\n    0x9DD3: 0x65C1,\n    0x9DD4: 0x65C4,\n    0x9DD5: 0x65CC,\n    0x9DD6: 0x65D2,\n    0x9DD7: 0x65DB,\n    0x9DD8: 0x65D9,\n    0x9DD9: 0x65E0,\n    0x9DDA: 0x65E1,\n    0x9DDB: 0x65F1,\n    0x9DDC: 0x6772,\n    0x9DDD: 0x660A,\n    0x9DDE: 0x6603,\n    0x9DDF: 0x65FB,\n    0x9DE0: 0x6773,\n    0x9DE1: 0x6635,\n    0x9DE2: 0x6636,\n    0x9DE3: 0x6634,\n    0x9DE4: 0x661C,\n    0x9DE5: 0x664F,\n    0x9DE6: 0x6644,\n    0x9DE7: 0x6649,\n    0x9DE8: 0x6641,\n    0x9DE9: 0x665E,\n    0x9DEA: 0x665D,\n    0x9DEB: 0x6664,\n    0x9DEC: 0x6667,\n    0x9DED: 0x6668,\n    0x9DEE: 0x665F,\n    0x9DEF: 0x6662,\n    0x9DF0: 0x6670,\n    0x9DF1: 0x6683,\n    0x9DF2: 0x6688,\n    0x9DF3: 0x668E,\n    0x9DF4: 0x6689,\n    0x9DF5: 0x6684,\n    0x9DF6: 0x6698,\n    0x9DF7: 0x669D,\n    0x9DF8: 0x66C1,\n    0x9DF9: 0x66B9,\n    0x9DFA: 0x66C9,\n    0x9DFB: 0x66BE,\n    0x9DFC: 0x66BC,\n    0x9E40: 0x66C4,\n    0x9E41: 0x66B8,\n    0x9E42: 0x66D6,\n    0x9E43: 0x66DA,\n    0x9E44: 0x66E0,\n    0x9E45: 0x663F,\n    0x9E46: 0x66E6,\n    0x9E47: 0x66E9,\n    0x9E48: 0x66F0,\n    0x9E49: 0x66F5,\n    0x9E4A: 0x66F7,\n    0x9E4B: 0x670F,\n    0x9E4C: 0x6716,\n    0x9E4D: 0x671E,\n    0x9E4E: 0x6726,\n    0x9E4F: 0x6727,\n    0x9E50: 0x9738,\n    0x9E51: 0x672E,\n    0x9E52: 0x673F,\n    0x9E53: 0x6736,\n    0x9E54: 0x6741,\n    0x9E55: 0x6738,\n    0x9E56: 0x6737,\n    0x9E57: 0x6746,\n    0x9E58: 0x675E,\n    0x9E59: 0x6760,\n    0x9E5A: 0x6759,\n    0x9E5B: 0x6763,\n    0x9E5C: 0x6764,\n    0x9E5D: 0x6789,\n    0x9E5E: 0x6770,\n    0x9E5F: 0x67A9,\n    0x9E60: 0x677C,\n    0x9E61: 0x676A,\n    0x9E62: 0x678C,\n    0x9E63: 0x678B,\n    0x9E64: 0x67A6,\n    0x9E65: 0x67A1,\n    0x9E66: 0x6785,\n    0x9E67: 0x67B7,\n    0x9E68: 0x67EF,\n    0x9E69: 0x67B4,\n    0x9E6A: 0x67EC,\n    0x9E6B: 0x67B3,\n    0x9E6C: 0x67E9,\n    0x9E6D: 0x67B8,\n    0x9E6E: 0x67E4,\n    0x9E6F: 0x67DE,\n    0x9E70: 0x67DD,\n    0x9E71: 0x67E2,\n    0x9E72: 0x67EE,\n    0x9E73: 0x67B9,\n    0x9E74: 0x67CE,\n    0x9E75: 0x67C6,\n    0x9E76: 0x67E7,\n    0x9E77: 0x6A9C,\n    0x9E78: 0x681E,\n    0x9E79: 0x6846,\n    0x9E7A: 0x6829,\n    0x9E7B: 0x6840,\n    0x9E7C: 0x684D,\n    0x9E7D: 0x6832,\n    0x9E7E: 0x684E,\n    0x9E80: 0x68B3,\n    0x9E81: 0x682B,\n    0x9E82: 0x6859,\n    0x9E83: 0x6863,\n    0x9E84: 0x6877,\n    0x9E85: 0x687F,\n    0x9E86: 0x689F,\n    0x9E87: 0x688F,\n    0x9E88: 0x68AD,\n    0x9E89: 0x6894,\n    0x9E8A: 0x689D,\n    0x9E8B: 0x689B,\n    0x9E8C: 0x6883,\n    0x9E8D: 0x6AAE,\n    0x9E8E: 0x68B9,\n    0x9E8F: 0x6874,\n    0x9E90: 0x68B5,\n    0x9E91: 0x68A0,\n    0x9E92: 0x68BA,\n    0x9E93: 0x690F,\n    0x9E94: 0x688D,\n    0x9E95: 0x687E,\n    0x9E96: 0x6901,\n    0x9E97: 0x68CA,\n    0x9E98: 0x6908,\n    0x9E99: 0x68D8,\n    0x9E9A: 0x6922,\n    0x9E9B: 0x6926,\n    0x9E9C: 0x68E1,\n    0x9E9D: 0x690C,\n    0x9E9E: 0x68CD,\n    0x9E9F: 0x68D4,\n    0x9EA0: 0x68E7,\n    0x9EA1: 0x68D5,\n    0x9EA2: 0x6936,\n    0x9EA3: 0x6912,\n    0x9EA4: 0x6904,\n    0x9EA5: 0x68D7,\n    0x9EA6: 0x68E3,\n    0x9EA7: 0x6925,\n    0x9EA8: 0x68F9,\n    0x9EA9: 0x68E0,\n    0x9EAA: 0x68EF,\n    0x9EAB: 0x6928,\n    0x9EAC: 0x692A,\n    0x9EAD: 0x691A,\n    0x9EAE: 0x6923,\n    0x9EAF: 0x6921,\n    0x9EB0: 0x68C6,\n    0x9EB1: 0x6979,\n    0x9EB2: 0x6977,\n    0x9EB3: 0x695C,\n    0x9EB4: 0x6978,\n    0x9EB5: 0x696B,\n    0x9EB6: 0x6954,\n    0x9EB7: 0x697E,\n    0x9EB8: 0x696E,\n    0x9EB9: 0x6939,\n    0x9EBA: 0x6974,\n    0x9EBB: 0x693D,\n    0x9EBC: 0x6959,\n    0x9EBD: 0x6930,\n    0x9EBE: 0x6961,\n    0x9EBF: 0x695E,\n    0x9EC0: 0x695D,\n    0x9EC1: 0x6981,\n    0x9EC2: 0x696A,\n    0x9EC3: 0x69B2,\n    0x9EC4: 0x69AE,\n    0x9EC5: 0x69D0,\n    0x9EC6: 0x69BF,\n    0x9EC7: 0x69C1,\n    0x9EC8: 0x69D3,\n    0x9EC9: 0x69BE,\n    0x9ECA: 0x69CE,\n    0x9ECB: 0x5BE8,\n    0x9ECC: 0x69CA,\n    0x9ECD: 0x69DD,\n    0x9ECE: 0x69BB,\n    0x9ECF: 0x69C3,\n    0x9ED0: 0x69A7,\n    0x9ED1: 0x6A2E,\n    0x9ED2: 0x6991,\n    0x9ED3: 0x69A0,\n    0x9ED4: 0x699C,\n    0x9ED5: 0x6995,\n    0x9ED6: 0x69B4,\n    0x9ED7: 0x69DE,\n    0x9ED8: 0x69E8,\n    0x9ED9: 0x6A02,\n    0x9EDA: 0x6A1B,\n    0x9EDB: 0x69FF,\n    0x9EDC: 0x6B0A,\n    0x9EDD: 0x69F9,\n    0x9EDE: 0x69F2,\n    0x9EDF: 0x69E7,\n    0x9EE0: 0x6A05,\n    0x9EE1: 0x69B1,\n    0x9EE2: 0x6A1E,\n    0x9EE3: 0x69ED,\n    0x9EE4: 0x6A14,\n    0x9EE5: 0x69EB,\n    0x9EE6: 0x6A0A,\n    0x9EE7: 0x6A12,\n    0x9EE8: 0x6AC1,\n    0x9EE9: 0x6A23,\n    0x9EEA: 0x6A13,\n    0x9EEB: 0x6A44,\n    0x9EEC: 0x6A0C,\n    0x9EED: 0x6A72,\n    0x9EEE: 0x6A36,\n    0x9EEF: 0x6A78,\n    0x9EF0: 0x6A47,\n    0x9EF1: 0x6A62,\n    0x9EF2: 0x6A59,\n    0x9EF3: 0x6A66,\n    0x9EF4: 0x6A48,\n    0x9EF5: 0x6A38,\n    0x9EF6: 0x6A22,\n    0x9EF7: 0x6A90,\n    0x9EF8: 0x6A8D,\n    0x9EF9: 0x6AA0,\n    0x9EFA: 0x6A84,\n    0x9EFB: 0x6AA2,\n    0x9EFC: 0x6AA3,\n    0x9F40: 0x6A97,\n    0x9F41: 0x8617,\n    0x9F42: 0x6ABB,\n    0x9F43: 0x6AC3,\n    0x9F44: 0x6AC2,\n    0x9F45: 0x6AB8,\n    0x9F46: 0x6AB3,\n    0x9F47: 0x6AAC,\n    0x9F48: 0x6ADE,\n    0x9F49: 0x6AD1,\n    0x9F4A: 0x6ADF,\n    0x9F4B: 0x6AAA,\n    0x9F4C: 0x6ADA,\n    0x9F4D: 0x6AEA,\n    0x9F4E: 0x6AFB,\n    0x9F4F: 0x6B05,\n    0x9F50: 0x8616,\n    0x9F51: 0x6AFA,\n    0x9F52: 0x6B12,\n    0x9F53: 0x6B16,\n    0x9F54: 0x9B31,\n    0x9F55: 0x6B1F,\n    0x9F56: 0x6B38,\n    0x9F57: 0x6B37,\n    0x9F58: 0x76DC,\n    0x9F59: 0x6B39,\n    0x9F5A: 0x98EE,\n    0x9F5B: 0x6B47,\n    0x9F5C: 0x6B43,\n    0x9F5D: 0x6B49,\n    0x9F5E: 0x6B50,\n    0x9F5F: 0x6B59,\n    0x9F60: 0x6B54,\n    0x9F61: 0x6B5B,\n    0x9F62: 0x6B5F,\n    0x9F63: 0x6B61,\n    0x9F64: 0x6B78,\n    0x9F65: 0x6B79,\n    0x9F66: 0x6B7F,\n    0x9F67: 0x6B80,\n    0x9F68: 0x6B84,\n    0x9F69: 0x6B83,\n    0x9F6A: 0x6B8D,\n    0x9F6B: 0x6B98,\n    0x9F6C: 0x6B95,\n    0x9F6D: 0x6B9E,\n    0x9F6E: 0x6BA4,\n    0x9F6F: 0x6BAA,\n    0x9F70: 0x6BAB,\n    0x9F71: 0x6BAF,\n    0x9F72: 0x6BB2,\n    0x9F73: 0x6BB1,\n    0x9F74: 0x6BB3,\n    0x9F75: 0x6BB7,\n    0x9F76: 0x6BBC,\n    0x9F77: 0x6BC6,\n    0x9F78: 0x6BCB,\n    0x9F79: 0x6BD3,\n    0x9F7A: 0x6BDF,\n    0x9F7B: 0x6BEC,\n    0x9F7C: 0x6BEB,\n    0x9F7D: 0x6BF3,\n    0x9F7E: 0x6BEF,\n    0x9F80: 0x9EBE,\n    0x9F81: 0x6C08,\n    0x9F82: 0x6C13,\n    0x9F83: 0x6C14,\n    0x9F84: 0x6C1B,\n    0x9F85: 0x6C24,\n    0x9F86: 0x6C23,\n    0x9F87: 0x6C5E,\n    0x9F88: 0x6C55,\n    0x9F89: 0x6C62,\n    0x9F8A: 0x6C6A,\n    0x9F8B: 0x6C82,\n    0x9F8C: 0x6C8D,\n    0x9F8D: 0x6C9A,\n    0x9F8E: 0x6C81,\n    0x9F8F: 0x6C9B,\n    0x9F90: 0x6C7E,\n    0x9F91: 0x6C68,\n    0x9F92: 0x6C73,\n    0x9F93: 0x6C92,\n    0x9F94: 0x6C90,\n    0x9F95: 0x6CC4,\n    0x9F96: 0x6CF1,\n    0x9F97: 0x6CD3,\n    0x9F98: 0x6CBD,\n    0x9F99: 0x6CD7,\n    0x9F9A: 0x6CC5,\n    0x9F9B: 0x6CDD,\n    0x9F9C: 0x6CAE,\n    0x9F9D: 0x6CB1,\n    0x9F9E: 0x6CBE,\n    0x9F9F: 0x6CBA,\n    0x9FA0: 0x6CDB,\n    0x9FA1: 0x6CEF,\n    0x9FA2: 0x6CD9,\n    0x9FA3: 0x6CEA,\n    0x9FA4: 0x6D1F,\n    0x9FA5: 0x884D,\n    0x9FA6: 0x6D36,\n    0x9FA7: 0x6D2B,\n    0x9FA8: 0x6D3D,\n    0x9FA9: 0x6D38,\n    0x9FAA: 0x6D19,\n    0x9FAB: 0x6D35,\n    0x9FAC: 0x6D33,\n    0x9FAD: 0x6D12,\n    0x9FAE: 0x6D0C,\n    0x9FAF: 0x6D63,\n    0x9FB0: 0x6D93,\n    0x9FB1: 0x6D64,\n    0x9FB2: 0x6D5A,\n    0x9FB3: 0x6D79,\n    0x9FB4: 0x6D59,\n    0x9FB5: 0x6D8E,\n    0x9FB6: 0x6D95,\n    0x9FB7: 0x6FE4,\n    0x9FB8: 0x6D85,\n    0x9FB9: 0x6DF9,\n    0x9FBA: 0x6E15,\n    0x9FBB: 0x6E0A,\n    0x9FBC: 0x6DB5,\n    0x9FBD: 0x6DC7,\n    0x9FBE: 0x6DE6,\n    0x9FBF: 0x6DB8,\n    0x9FC0: 0x6DC6,\n    0x9FC1: 0x6DEC,\n    0x9FC2: 0x6DDE,\n    0x9FC3: 0x6DCC,\n    0x9FC4: 0x6DE8,\n    0x9FC5: 0x6DD2,\n    0x9FC6: 0x6DC5,\n    0x9FC7: 0x6DFA,\n    0x9FC8: 0x6DD9,\n    0x9FC9: 0x6DE4,\n    0x9FCA: 0x6DD5,\n    0x9FCB: 0x6DEA,\n    0x9FCC: 0x6DEE,\n    0x9FCD: 0x6E2D,\n    0x9FCE: 0x6E6E,\n    0x9FCF: 0x6E2E,\n    0x9FD0: 0x6E19,\n    0x9FD1: 0x6E72,\n    0x9FD2: 0x6E5F,\n    0x9FD3: 0x6E3E,\n    0x9FD4: 0x6E23,\n    0x9FD5: 0x6E6B,\n    0x9FD6: 0x6E2B,\n    0x9FD7: 0x6E76,\n    0x9FD8: 0x6E4D,\n    0x9FD9: 0x6E1F,\n    0x9FDA: 0x6E43,\n    0x9FDB: 0x6E3A,\n    0x9FDC: 0x6E4E,\n    0x9FDD: 0x6E24,\n    0x9FDE: 0x6EFF,\n    0x9FDF: 0x6E1D,\n    0x9FE0: 0x6E38,\n    0x9FE1: 0x6E82,\n    0x9FE2: 0x6EAA,\n    0x9FE3: 0x6E98,\n    0x9FE4: 0x6EC9,\n    0x9FE5: 0x6EB7,\n    0x9FE6: 0x6ED3,\n    0x9FE7: 0x6EBD,\n    0x9FE8: 0x6EAF,\n    0x9FE9: 0x6EC4,\n    0x9FEA: 0x6EB2,\n    0x9FEB: 0x6ED4,\n    0x9FEC: 0x6ED5,\n    0x9FED: 0x6E8F,\n    0x9FEE: 0x6EA5,\n    0x9FEF: 0x6EC2,\n    0x9FF0: 0x6E9F,\n    0x9FF1: 0x6F41,\n    0x9FF2: 0x6F11,\n    0x9FF3: 0x704C,\n    0x9FF4: 0x6EEC,\n    0x9FF5: 0x6EF8,\n    0x9FF6: 0x6EFE,\n    0x9FF7: 0x6F3F,\n    0x9FF8: 0x6EF2,\n    0x9FF9: 0x6F31,\n    0x9FFA: 0x6EEF,\n    0x9FFB: 0x6F32,\n    0x9FFC: 0x6ECC,\n    0xA1: 0xFF61,\n    0xA2: 0xFF62,\n    0xA3: 0xFF63,\n    0xA4: 0xFF64,\n    0xA5: 0xFF65,\n    0xA6: 0xFF66,\n    0xA7: 0xFF67,\n    0xA8: 0xFF68,\n    0xA9: 0xFF69,\n    0xAA: 0xFF6A,\n    0xAB: 0xFF6B,\n    0xAC: 0xFF6C,\n    0xAD: 0xFF6D,\n    0xAE: 0xFF6E,\n    0xAF: 0xFF6F,\n    0xB0: 0xFF70,\n    0xB1: 0xFF71,\n    0xB2: 0xFF72,\n    0xB3: 0xFF73,\n    0xB4: 0xFF74,\n    0xB5: 0xFF75,\n    0xB6: 0xFF76,\n    0xB7: 0xFF77,\n    0xB8: 0xFF78,\n    0xB9: 0xFF79,\n    0xBA: 0xFF7A,\n    0xBB: 0xFF7B,\n    0xBC: 0xFF7C,\n    0xBD: 0xFF7D,\n    0xBE: 0xFF7E,\n    0xBF: 0xFF7F,\n    0xC0: 0xFF80,\n    0xC1: 0xFF81,\n    0xC2: 0xFF82,\n    0xC3: 0xFF83,\n    0xC4: 0xFF84,\n    0xC5: 0xFF85,\n    0xC6: 0xFF86,\n    0xC7: 0xFF87,\n    0xC8: 0xFF88,\n    0xC9: 0xFF89,\n    0xCA: 0xFF8A,\n    0xCB: 0xFF8B,\n    0xCC: 0xFF8C,\n    0xCD: 0xFF8D,\n    0xCE: 0xFF8E,\n    0xCF: 0xFF8F,\n    0xD0: 0xFF90,\n    0xD1: 0xFF91,\n    0xD2: 0xFF92,\n    0xD3: 0xFF93,\n    0xD4: 0xFF94,\n    0xD5: 0xFF95,\n    0xD6: 0xFF96,\n    0xD7: 0xFF97,\n    0xD8: 0xFF98,\n    0xD9: 0xFF99,\n    0xDA: 0xFF9A,\n    0xDB: 0xFF9B,\n    0xDC: 0xFF9C,\n    0xDD: 0xFF9D,\n    0xDE: 0xFF9E,\n    0xDF: 0xFF9F,\n    0xE040: 0x6F3E,\n    0xE041: 0x6F13,\n    0xE042: 0x6EF7,\n    0xE043: 0x6F86,\n    0xE044: 0x6F7A,\n    0xE045: 0x6F78,\n    0xE046: 0x6F81,\n    0xE047: 0x6F80,\n    0xE048: 0x6F6F,\n    0xE049: 0x6F5B,\n    0xE04A: 0x6FF3,\n    0xE04B: 0x6F6D,\n    0xE04C: 0x6F82,\n    0xE04D: 0x6F7C,\n    0xE04E: 0x6F58,\n    0xE04F: 0x6F8E,\n    0xE050: 0x6F91,\n    0xE051: 0x6FC2,\n    0xE052: 0x6F66,\n    0xE053: 0x6FB3,\n    0xE054: 0x6FA3,\n    0xE055: 0x6FA1,\n    0xE056: 0x6FA4,\n    0xE057: 0x6FB9,\n    0xE058: 0x6FC6,\n    0xE059: 0x6FAA,\n    0xE05A: 0x6FDF,\n    0xE05B: 0x6FD5,\n    0xE05C: 0x6FEC,\n    0xE05D: 0x6FD4,\n    0xE05E: 0x6FD8,\n    0xE05F: 0x6FF1,\n    0xE060: 0x6FEE,\n    0xE061: 0x6FDB,\n    0xE062: 0x7009,\n    0xE063: 0x700B,\n    0xE064: 0x6FFA,\n    0xE065: 0x7011,\n    0xE066: 0x7001,\n    0xE067: 0x700F,\n    0xE068: 0x6FFE,\n    0xE069: 0x701B,\n    0xE06A: 0x701A,\n    0xE06B: 0x6F74,\n    0xE06C: 0x701D,\n    0xE06D: 0x7018,\n    0xE06E: 0x701F,\n    0xE06F: 0x7030,\n    0xE070: 0x703E,\n    0xE071: 0x7032,\n    0xE072: 0x7051,\n    0xE073: 0x7063,\n    0xE074: 0x7099,\n    0xE075: 0x7092,\n    0xE076: 0x70AF,\n    0xE077: 0x70F1,\n    0xE078: 0x70AC,\n    0xE079: 0x70B8,\n    0xE07A: 0x70B3,\n    0xE07B: 0x70AE,\n    0xE07C: 0x70DF,\n    0xE07D: 0x70CB,\n    0xE07E: 0x70DD,\n    0xE080: 0x70D9,\n    0xE081: 0x7109,\n    0xE082: 0x70FD,\n    0xE083: 0x711C,\n    0xE084: 0x7119,\n    0xE085: 0x7165,\n    0xE086: 0x7155,\n    0xE087: 0x7188,\n    0xE088: 0x7166,\n    0xE089: 0x7162,\n    0xE08A: 0x714C,\n    0xE08B: 0x7156,\n    0xE08C: 0x716C,\n    0xE08D: 0x718F,\n    0xE08E: 0x71FB,\n    0xE08F: 0x7184,\n    0xE090: 0x7195,\n    0xE091: 0x71A8,\n    0xE092: 0x71AC,\n    0xE093: 0x71D7,\n    0xE094: 0x71B9,\n    0xE095: 0x71BE,\n    0xE096: 0x71D2,\n    0xE097: 0x71C9,\n    0xE098: 0x71D4,\n    0xE099: 0x71CE,\n    0xE09A: 0x71E0,\n    0xE09B: 0x71EC,\n    0xE09C: 0x71E7,\n    0xE09D: 0x71F5,\n    0xE09E: 0x71FC,\n    0xE09F: 0x71F9,\n    0xE0A0: 0x71FF,\n    0xE0A1: 0x720D,\n    0xE0A2: 0x7210,\n    0xE0A3: 0x721B,\n    0xE0A4: 0x7228,\n    0xE0A5: 0x722D,\n    0xE0A6: 0x722C,\n    0xE0A7: 0x7230,\n    0xE0A8: 0x7232,\n    0xE0A9: 0x723B,\n    0xE0AA: 0x723C,\n    0xE0AB: 0x723F,\n    0xE0AC: 0x7240,\n    0xE0AD: 0x7246,\n    0xE0AE: 0x724B,\n    0xE0AF: 0x7258,\n    0xE0B0: 0x7274,\n    0xE0B1: 0x727E,\n    0xE0B2: 0x7282,\n    0xE0B3: 0x7281,\n    0xE0B4: 0x7287,\n    0xE0B5: 0x7292,\n    0xE0B6: 0x7296,\n    0xE0B7: 0x72A2,\n    0xE0B8: 0x72A7,\n    0xE0B9: 0x72B9,\n    0xE0BA: 0x72B2,\n    0xE0BB: 0x72C3,\n    0xE0BC: 0x72C6,\n    0xE0BD: 0x72C4,\n    0xE0BE: 0x72CE,\n    0xE0BF: 0x72D2,\n    0xE0C0: 0x72E2,\n    0xE0C1: 0x72E0,\n    0xE0C2: 0x72E1,\n    0xE0C3: 0x72F9,\n    0xE0C4: 0x72F7,\n    0xE0C5: 0x500F,\n    0xE0C6: 0x7317,\n    0xE0C7: 0x730A,\n    0xE0C8: 0x731C,\n    0xE0C9: 0x7316,\n    0xE0CA: 0x731D,\n    0xE0CB: 0x7334,\n    0xE0CC: 0x732F,\n    0xE0CD: 0x7329,\n    0xE0CE: 0x7325,\n    0xE0CF: 0x733E,\n    0xE0D0: 0x734E,\n    0xE0D1: 0x734F,\n    0xE0D2: 0x9ED8,\n    0xE0D3: 0x7357,\n    0xE0D4: 0x736A,\n    0xE0D5: 0x7368,\n    0xE0D6: 0x7370,\n    0xE0D7: 0x7378,\n    0xE0D8: 0x7375,\n    0xE0D9: 0x737B,\n    0xE0DA: 0x737A,\n    0xE0DB: 0x73C8,\n    0xE0DC: 0x73B3,\n    0xE0DD: 0x73CE,\n    0xE0DE: 0x73BB,\n    0xE0DF: 0x73C0,\n    0xE0E0: 0x73E5,\n    0xE0E1: 0x73EE,\n    0xE0E2: 0x73DE,\n    0xE0E3: 0x74A2,\n    0xE0E4: 0x7405,\n    0xE0E5: 0x746F,\n    0xE0E6: 0x7425,\n    0xE0E7: 0x73F8,\n    0xE0E8: 0x7432,\n    0xE0E9: 0x743A,\n    0xE0EA: 0x7455,\n    0xE0EB: 0x743F,\n    0xE0EC: 0x745F,\n    0xE0ED: 0x7459,\n    0xE0EE: 0x7441,\n    0xE0EF: 0x745C,\n    0xE0F0: 0x7469,\n    0xE0F1: 0x7470,\n    0xE0F2: 0x7463,\n    0xE0F3: 0x746A,\n    0xE0F4: 0x7476,\n    0xE0F5: 0x747E,\n    0xE0F6: 0x748B,\n    0xE0F7: 0x749E,\n    0xE0F8: 0x74A7,\n    0xE0F9: 0x74CA,\n    0xE0FA: 0x74CF,\n    0xE0FB: 0x74D4,\n    0xE0FC: 0x73F1,\n    0xE140: 0x74E0,\n    0xE141: 0x74E3,\n    0xE142: 0x74E7,\n    0xE143: 0x74E9,\n    0xE144: 0x74EE,\n    0xE145: 0x74F2,\n    0xE146: 0x74F0,\n    0xE147: 0x74F1,\n    0xE148: 0x74F8,\n    0xE149: 0x74F7,\n    0xE14A: 0x7504,\n    0xE14B: 0x7503,\n    0xE14C: 0x7505,\n    0xE14D: 0x750C,\n    0xE14E: 0x750E,\n    0xE14F: 0x750D,\n    0xE150: 0x7515,\n    0xE151: 0x7513,\n    0xE152: 0x751E,\n    0xE153: 0x7526,\n    0xE154: 0x752C,\n    0xE155: 0x753C,\n    0xE156: 0x7544,\n    0xE157: 0x754D,\n    0xE158: 0x754A,\n    0xE159: 0x7549,\n    0xE15A: 0x755B,\n    0xE15B: 0x7546,\n    0xE15C: 0x755A,\n    0xE15D: 0x7569,\n    0xE15E: 0x7564,\n    0xE15F: 0x7567,\n    0xE160: 0x756B,\n    0xE161: 0x756D,\n    0xE162: 0x7578,\n    0xE163: 0x7576,\n    0xE164: 0x7586,\n    0xE165: 0x7587,\n    0xE166: 0x7574,\n    0xE167: 0x758A,\n    0xE168: 0x7589,\n    0xE169: 0x7582,\n    0xE16A: 0x7594,\n    0xE16B: 0x759A,\n    0xE16C: 0x759D,\n    0xE16D: 0x75A5,\n    0xE16E: 0x75A3,\n    0xE16F: 0x75C2,\n    0xE170: 0x75B3,\n    0xE171: 0x75C3,\n    0xE172: 0x75B5,\n    0xE173: 0x75BD,\n    0xE174: 0x75B8,\n    0xE175: 0x75BC,\n    0xE176: 0x75B1,\n    0xE177: 0x75CD,\n    0xE178: 0x75CA,\n    0xE179: 0x75D2,\n    0xE17A: 0x75D9,\n    0xE17B: 0x75E3,\n    0xE17C: 0x75DE,\n    0xE17D: 0x75FE,\n    0xE17E: 0x75FF,\n    0xE180: 0x75FC,\n    0xE181: 0x7601,\n    0xE182: 0x75F0,\n    0xE183: 0x75FA,\n    0xE184: 0x75F2,\n    0xE185: 0x75F3,\n    0xE186: 0x760B,\n    0xE187: 0x760D,\n    0xE188: 0x7609,\n    0xE189: 0x761F,\n    0xE18A: 0x7627,\n    0xE18B: 0x7620,\n    0xE18C: 0x7621,\n    0xE18D: 0x7622,\n    0xE18E: 0x7624,\n    0xE18F: 0x7634,\n    0xE190: 0x7630,\n    0xE191: 0x763B,\n    0xE192: 0x7647,\n    0xE193: 0x7648,\n    0xE194: 0x7646,\n    0xE195: 0x765C,\n    0xE196: 0x7658,\n    0xE197: 0x7661,\n    0xE198: 0x7662,\n    0xE199: 0x7668,\n    0xE19A: 0x7669,\n    0xE19B: 0x766A,\n    0xE19C: 0x7667,\n    0xE19D: 0x766C,\n    0xE19E: 0x7670,\n    0xE19F: 0x7672,\n    0xE1A0: 0x7676,\n    0xE1A1: 0x7678,\n    0xE1A2: 0x767C,\n    0xE1A3: 0x7680,\n    0xE1A4: 0x7683,\n    0xE1A5: 0x7688,\n    0xE1A6: 0x768B,\n    0xE1A7: 0x768E,\n    0xE1A8: 0x7696,\n    0xE1A9: 0x7693,\n    0xE1AA: 0x7699,\n    0xE1AB: 0x769A,\n    0xE1AC: 0x76B0,\n    0xE1AD: 0x76B4,\n    0xE1AE: 0x76B8,\n    0xE1AF: 0x76B9,\n    0xE1B0: 0x76BA,\n    0xE1B1: 0x76C2,\n    0xE1B2: 0x76CD,\n    0xE1B3: 0x76D6,\n    0xE1B4: 0x76D2,\n    0xE1B5: 0x76DE,\n    0xE1B6: 0x76E1,\n    0xE1B7: 0x76E5,\n    0xE1B8: 0x76E7,\n    0xE1B9: 0x76EA,\n    0xE1BA: 0x862F,\n    0xE1BB: 0x76FB,\n    0xE1BC: 0x7708,\n    0xE1BD: 0x7707,\n    0xE1BE: 0x7704,\n    0xE1BF: 0x7729,\n    0xE1C0: 0x7724,\n    0xE1C1: 0x771E,\n    0xE1C2: 0x7725,\n    0xE1C3: 0x7726,\n    0xE1C4: 0x771B,\n    0xE1C5: 0x7737,\n    0xE1C6: 0x7738,\n    0xE1C7: 0x7747,\n    0xE1C8: 0x775A,\n    0xE1C9: 0x7768,\n    0xE1CA: 0x776B,\n    0xE1CB: 0x775B,\n    0xE1CC: 0x7765,\n    0xE1CD: 0x777F,\n    0xE1CE: 0x777E,\n    0xE1CF: 0x7779,\n    0xE1D0: 0x778E,\n    0xE1D1: 0x778B,\n    0xE1D2: 0x7791,\n    0xE1D3: 0x77A0,\n    0xE1D4: 0x779E,\n    0xE1D5: 0x77B0,\n    0xE1D6: 0x77B6,\n    0xE1D7: 0x77B9,\n    0xE1D8: 0x77BF,\n    0xE1D9: 0x77BC,\n    0xE1DA: 0x77BD,\n    0xE1DB: 0x77BB,\n    0xE1DC: 0x77C7,\n    0xE1DD: 0x77CD,\n    0xE1DE: 0x77D7,\n    0xE1DF: 0x77DA,\n    0xE1E0: 0x77DC,\n    0xE1E1: 0x77E3,\n    0xE1E2: 0x77EE,\n    0xE1E3: 0x77FC,\n    0xE1E4: 0x780C,\n    0xE1E5: 0x7812,\n    0xE1E6: 0x7926,\n    0xE1E7: 0x7820,\n    0xE1E8: 0x792A,\n    0xE1E9: 0x7845,\n    0xE1EA: 0x788E,\n    0xE1EB: 0x7874,\n    0xE1EC: 0x7886,\n    0xE1ED: 0x787C,\n    0xE1EE: 0x789A,\n    0xE1EF: 0x788C,\n    0xE1F0: 0x78A3,\n    0xE1F1: 0x78B5,\n    0xE1F2: 0x78AA,\n    0xE1F3: 0x78AF,\n    0xE1F4: 0x78D1,\n    0xE1F5: 0x78C6,\n    0xE1F6: 0x78CB,\n    0xE1F7: 0x78D4,\n    0xE1F8: 0x78BE,\n    0xE1F9: 0x78BC,\n    0xE1FA: 0x78C5,\n    0xE1FB: 0x78CA,\n    0xE1FC: 0x78EC,\n    0xE240: 0x78E7,\n    0xE241: 0x78DA,\n    0xE242: 0x78FD,\n    0xE243: 0x78F4,\n    0xE244: 0x7907,\n    0xE245: 0x7912,\n    0xE246: 0x7911,\n    0xE247: 0x7919,\n    0xE248: 0x792C,\n    0xE249: 0x792B,\n    0xE24A: 0x7940,\n    0xE24B: 0x7960,\n    0xE24C: 0x7957,\n    0xE24D: 0x795F,\n    0xE24E: 0x795A,\n    0xE24F: 0x7955,\n    0xE250: 0x7953,\n    0xE251: 0x797A,\n    0xE252: 0x797F,\n    0xE253: 0x798A,\n    0xE254: 0x799D,\n    0xE255: 0x79A7,\n    0xE256: 0x9F4B,\n    0xE257: 0x79AA,\n    0xE258: 0x79AE,\n    0xE259: 0x79B3,\n    0xE25A: 0x79B9,\n    0xE25B: 0x79BA,\n    0xE25C: 0x79C9,\n    0xE25D: 0x79D5,\n    0xE25E: 0x79E7,\n    0xE25F: 0x79EC,\n    0xE260: 0x79E1,\n    0xE261: 0x79E3,\n    0xE262: 0x7A08,\n    0xE263: 0x7A0D,\n    0xE264: 0x7A18,\n    0xE265: 0x7A19,\n    0xE266: 0x7A20,\n    0xE267: 0x7A1F,\n    0xE268: 0x7980,\n    0xE269: 0x7A31,\n    0xE26A: 0x7A3B,\n    0xE26B: 0x7A3E,\n    0xE26C: 0x7A37,\n    0xE26D: 0x7A43,\n    0xE26E: 0x7A57,\n    0xE26F: 0x7A49,\n    0xE270: 0x7A61,\n    0xE271: 0x7A62,\n    0xE272: 0x7A69,\n    0xE273: 0x9F9D,\n    0xE274: 0x7A70,\n    0xE275: 0x7A79,\n    0xE276: 0x7A7D,\n    0xE277: 0x7A88,\n    0xE278: 0x7A97,\n    0xE279: 0x7A95,\n    0xE27A: 0x7A98,\n    0xE27B: 0x7A96,\n    0xE27C: 0x7AA9,\n    0xE27D: 0x7AC8,\n    0xE27E: 0x7AB0,\n    0xE280: 0x7AB6,\n    0xE281: 0x7AC5,\n    0xE282: 0x7AC4,\n    0xE283: 0x7ABF,\n    0xE284: 0x9083,\n    0xE285: 0x7AC7,\n    0xE286: 0x7ACA,\n    0xE287: 0x7ACD,\n    0xE288: 0x7ACF,\n    0xE289: 0x7AD5,\n    0xE28A: 0x7AD3,\n    0xE28B: 0x7AD9,\n    0xE28C: 0x7ADA,\n    0xE28D: 0x7ADD,\n    0xE28E: 0x7AE1,\n    0xE28F: 0x7AE2,\n    0xE290: 0x7AE6,\n    0xE291: 0x7AED,\n    0xE292: 0x7AF0,\n    0xE293: 0x7B02,\n    0xE294: 0x7B0F,\n    0xE295: 0x7B0A,\n    0xE296: 0x7B06,\n    0xE297: 0x7B33,\n    0xE298: 0x7B18,\n    0xE299: 0x7B19,\n    0xE29A: 0x7B1E,\n    0xE29B: 0x7B35,\n    0xE29C: 0x7B28,\n    0xE29D: 0x7B36,\n    0xE29E: 0x7B50,\n    0xE29F: 0x7B7A,\n    0xE2A0: 0x7B04,\n    0xE2A1: 0x7B4D,\n    0xE2A2: 0x7B0B,\n    0xE2A3: 0x7B4C,\n    0xE2A4: 0x7B45,\n    0xE2A5: 0x7B75,\n    0xE2A6: 0x7B65,\n    0xE2A7: 0x7B74,\n    0xE2A8: 0x7B67,\n    0xE2A9: 0x7B70,\n    0xE2AA: 0x7B71,\n    0xE2AB: 0x7B6C,\n    0xE2AC: 0x7B6E,\n    0xE2AD: 0x7B9D,\n    0xE2AE: 0x7B98,\n    0xE2AF: 0x7B9F,\n    0xE2B0: 0x7B8D,\n    0xE2B1: 0x7B9C,\n    0xE2B2: 0x7B9A,\n    0xE2B3: 0x7B8B,\n    0xE2B4: 0x7B92,\n    0xE2B5: 0x7B8F,\n    0xE2B6: 0x7B5D,\n    0xE2B7: 0x7B99,\n    0xE2B8: 0x7BCB,\n    0xE2B9: 0x7BC1,\n    0xE2BA: 0x7BCC,\n    0xE2BB: 0x7BCF,\n    0xE2BC: 0x7BB4,\n    0xE2BD: 0x7BC6,\n    0xE2BE: 0x7BDD,\n    0xE2BF: 0x7BE9,\n    0xE2C0: 0x7C11,\n    0xE2C1: 0x7C14,\n    0xE2C2: 0x7BE6,\n    0xE2C3: 0x7BE5,\n    0xE2C4: 0x7C60,\n    0xE2C5: 0x7C00,\n    0xE2C6: 0x7C07,\n    0xE2C7: 0x7C13,\n    0xE2C8: 0x7BF3,\n    0xE2C9: 0x7BF7,\n    0xE2CA: 0x7C17,\n    0xE2CB: 0x7C0D,\n    0xE2CC: 0x7BF6,\n    0xE2CD: 0x7C23,\n    0xE2CE: 0x7C27,\n    0xE2CF: 0x7C2A,\n    0xE2D0: 0x7C1F,\n    0xE2D1: 0x7C37,\n    0xE2D2: 0x7C2B,\n    0xE2D3: 0x7C3D,\n    0xE2D4: 0x7C4C,\n    0xE2D5: 0x7C43,\n    0xE2D6: 0x7C54,\n    0xE2D7: 0x7C4F,\n    0xE2D8: 0x7C40,\n    0xE2D9: 0x7C50,\n    0xE2DA: 0x7C58,\n    0xE2DB: 0x7C5F,\n    0xE2DC: 0x7C64,\n    0xE2DD: 0x7C56,\n    0xE2DE: 0x7C65,\n    0xE2DF: 0x7C6C,\n    0xE2E0: 0x7C75,\n    0xE2E1: 0x7C83,\n    0xE2E2: 0x7C90,\n    0xE2E3: 0x7CA4,\n    0xE2E4: 0x7CAD,\n    0xE2E5: 0x7CA2,\n    0xE2E6: 0x7CAB,\n    0xE2E7: 0x7CA1,\n    0xE2E8: 0x7CA8,\n    0xE2E9: 0x7CB3,\n    0xE2EA: 0x7CB2,\n    0xE2EB: 0x7CB1,\n    0xE2EC: 0x7CAE,\n    0xE2ED: 0x7CB9,\n    0xE2EE: 0x7CBD,\n    0xE2EF: 0x7CC0,\n    0xE2F0: 0x7CC5,\n    0xE2F1: 0x7CC2,\n    0xE2F2: 0x7CD8,\n    0xE2F3: 0x7CD2,\n    0xE2F4: 0x7CDC,\n    0xE2F5: 0x7CE2,\n    0xE2F6: 0x9B3B,\n    0xE2F7: 0x7CEF,\n    0xE2F8: 0x7CF2,\n    0xE2F9: 0x7CF4,\n    0xE2FA: 0x7CF6,\n    0xE2FB: 0x7CFA,\n    0xE2FC: 0x7D06,\n    0xE340: 0x7D02,\n    0xE341: 0x7D1C,\n    0xE342: 0x7D15,\n    0xE343: 0x7D0A,\n    0xE344: 0x7D45,\n    0xE345: 0x7D4B,\n    0xE346: 0x7D2E,\n    0xE347: 0x7D32,\n    0xE348: 0x7D3F,\n    0xE349: 0x7D35,\n    0xE34A: 0x7D46,\n    0xE34B: 0x7D73,\n    0xE34C: 0x7D56,\n    0xE34D: 0x7D4E,\n    0xE34E: 0x7D72,\n    0xE34F: 0x7D68,\n    0xE350: 0x7D6E,\n    0xE351: 0x7D4F,\n    0xE352: 0x7D63,\n    0xE353: 0x7D93,\n    0xE354: 0x7D89,\n    0xE355: 0x7D5B,\n    0xE356: 0x7D8F,\n    0xE357: 0x7D7D,\n    0xE358: 0x7D9B,\n    0xE359: 0x7DBA,\n    0xE35A: 0x7DAE,\n    0xE35B: 0x7DA3,\n    0xE35C: 0x7DB5,\n    0xE35D: 0x7DC7,\n    0xE35E: 0x7DBD,\n    0xE35F: 0x7DAB,\n    0xE360: 0x7E3D,\n    0xE361: 0x7DA2,\n    0xE362: 0x7DAF,\n    0xE363: 0x7DDC,\n    0xE364: 0x7DB8,\n    0xE365: 0x7D9F,\n    0xE366: 0x7DB0,\n    0xE367: 0x7DD8,\n    0xE368: 0x7DDD,\n    0xE369: 0x7DE4,\n    0xE36A: 0x7DDE,\n    0xE36B: 0x7DFB,\n    0xE36C: 0x7DF2,\n    0xE36D: 0x7DE1,\n    0xE36E: 0x7E05,\n    0xE36F: 0x7E0A,\n    0xE370: 0x7E23,\n    0xE371: 0x7E21,\n    0xE372: 0x7E12,\n    0xE373: 0x7E31,\n    0xE374: 0x7E1F,\n    0xE375: 0x7E09,\n    0xE376: 0x7E0B,\n    0xE377: 0x7E22,\n    0xE378: 0x7E46,\n    0xE379: 0x7E66,\n    0xE37A: 0x7E3B,\n    0xE37B: 0x7E35,\n    0xE37C: 0x7E39,\n    0xE37D: 0x7E43,\n    0xE37E: 0x7E37,\n    0xE380: 0x7E32,\n    0xE381: 0x7E3A,\n    0xE382: 0x7E67,\n    0xE383: 0x7E5D,\n    0xE384: 0x7E56,\n    0xE385: 0x7E5E,\n    0xE386: 0x7E59,\n    0xE387: 0x7E5A,\n    0xE388: 0x7E79,\n    0xE389: 0x7E6A,\n    0xE38A: 0x7E69,\n    0xE38B: 0x7E7C,\n    0xE38C: 0x7E7B,\n    0xE38D: 0x7E83,\n    0xE38E: 0x7DD5,\n    0xE38F: 0x7E7D,\n    0xE390: 0x8FAE,\n    0xE391: 0x7E7F,\n    0xE392: 0x7E88,\n    0xE393: 0x7E89,\n    0xE394: 0x7E8C,\n    0xE395: 0x7E92,\n    0xE396: 0x7E90,\n    0xE397: 0x7E93,\n    0xE398: 0x7E94,\n    0xE399: 0x7E96,\n    0xE39A: 0x7E8E,\n    0xE39B: 0x7E9B,\n    0xE39C: 0x7E9C,\n    0xE39D: 0x7F38,\n    0xE39E: 0x7F3A,\n    0xE39F: 0x7F45,\n    0xE3A0: 0x7F4C,\n    0xE3A1: 0x7F4D,\n    0xE3A2: 0x7F4E,\n    0xE3A3: 0x7F50,\n    0xE3A4: 0x7F51,\n    0xE3A5: 0x7F55,\n    0xE3A6: 0x7F54,\n    0xE3A7: 0x7F58,\n    0xE3A8: 0x7F5F,\n    0xE3A9: 0x7F60,\n    0xE3AA: 0x7F68,\n    0xE3AB: 0x7F69,\n    0xE3AC: 0x7F67,\n    0xE3AD: 0x7F78,\n    0xE3AE: 0x7F82,\n    0xE3AF: 0x7F86,\n    0xE3B0: 0x7F83,\n    0xE3B1: 0x7F88,\n    0xE3B2: 0x7F87,\n    0xE3B3: 0x7F8C,\n    0xE3B4: 0x7F94,\n    0xE3B5: 0x7F9E,\n    0xE3B6: 0x7F9D,\n    0xE3B7: 0x7F9A,\n    0xE3B8: 0x7FA3,\n    0xE3B9: 0x7FAF,\n    0xE3BA: 0x7FB2,\n    0xE3BB: 0x7FB9,\n    0xE3BC: 0x7FAE,\n    0xE3BD: 0x7FB6,\n    0xE3BE: 0x7FB8,\n    0xE3BF: 0x8B71,\n    0xE3C0: 0x7FC5,\n    0xE3C1: 0x7FC6,\n    0xE3C2: 0x7FCA,\n    0xE3C3: 0x7FD5,\n    0xE3C4: 0x7FD4,\n    0xE3C5: 0x7FE1,\n    0xE3C6: 0x7FE6,\n    0xE3C7: 0x7FE9,\n    0xE3C8: 0x7FF3,\n    0xE3C9: 0x7FF9,\n    0xE3CA: 0x98DC,\n    0xE3CB: 0x8006,\n    0xE3CC: 0x8004,\n    0xE3CD: 0x800B,\n    0xE3CE: 0x8012,\n    0xE3CF: 0x8018,\n    0xE3D0: 0x8019,\n    0xE3D1: 0x801C,\n    0xE3D2: 0x8021,\n    0xE3D3: 0x8028,\n    0xE3D4: 0x803F,\n    0xE3D5: 0x803B,\n    0xE3D6: 0x804A,\n    0xE3D7: 0x8046,\n    0xE3D8: 0x8052,\n    0xE3D9: 0x8058,\n    0xE3DA: 0x805A,\n    0xE3DB: 0x805F,\n    0xE3DC: 0x8062,\n    0xE3DD: 0x8068,\n    0xE3DE: 0x8073,\n    0xE3DF: 0x8072,\n    0xE3E0: 0x8070,\n    0xE3E1: 0x8076,\n    0xE3E2: 0x8079,\n    0xE3E3: 0x807D,\n    0xE3E4: 0x807F,\n    0xE3E5: 0x8084,\n    0xE3E6: 0x8086,\n    0xE3E7: 0x8085,\n    0xE3E8: 0x809B,\n    0xE3E9: 0x8093,\n    0xE3EA: 0x809A,\n    0xE3EB: 0x80AD,\n    0xE3EC: 0x5190,\n    0xE3ED: 0x80AC,\n    0xE3EE: 0x80DB,\n    0xE3EF: 0x80E5,\n    0xE3F0: 0x80D9,\n    0xE3F1: 0x80DD,\n    0xE3F2: 0x80C4,\n    0xE3F3: 0x80DA,\n    0xE3F4: 0x80D6,\n    0xE3F5: 0x8109,\n    0xE3F6: 0x80EF,\n    0xE3F7: 0x80F1,\n    0xE3F8: 0x811B,\n    0xE3F9: 0x8129,\n    0xE3FA: 0x8123,\n    0xE3FB: 0x812F,\n    0xE3FC: 0x814B,\n    0xE440: 0x968B,\n    0xE441: 0x8146,\n    0xE442: 0x813E,\n    0xE443: 0x8153,\n    0xE444: 0x8151,\n    0xE445: 0x80FC,\n    0xE446: 0x8171,\n    0xE447: 0x816E,\n    0xE448: 0x8165,\n    0xE449: 0x8166,\n    0xE44A: 0x8174,\n    0xE44B: 0x8183,\n    0xE44C: 0x8188,\n    0xE44D: 0x818A,\n    0xE44E: 0x8180,\n    0xE44F: 0x8182,\n    0xE450: 0x81A0,\n    0xE451: 0x8195,\n    0xE452: 0x81A4,\n    0xE453: 0x81A3,\n    0xE454: 0x815F,\n    0xE455: 0x8193,\n    0xE456: 0x81A9,\n    0xE457: 0x81B0,\n    0xE458: 0x81B5,\n    0xE459: 0x81BE,\n    0xE45A: 0x81B8,\n    0xE45B: 0x81BD,\n    0xE45C: 0x81C0,\n    0xE45D: 0x81C2,\n    0xE45E: 0x81BA,\n    0xE45F: 0x81C9,\n    0xE460: 0x81CD,\n    0xE461: 0x81D1,\n    0xE462: 0x81D9,\n    0xE463: 0x81D8,\n    0xE464: 0x81C8,\n    0xE465: 0x81DA,\n    0xE466: 0x81DF,\n    0xE467: 0x81E0,\n    0xE468: 0x81E7,\n    0xE469: 0x81FA,\n    0xE46A: 0x81FB,\n    0xE46B: 0x81FE,\n    0xE46C: 0x8201,\n    0xE46D: 0x8202,\n    0xE46E: 0x8205,\n    0xE46F: 0x8207,\n    0xE470: 0x820A,\n    0xE471: 0x820D,\n    0xE472: 0x8210,\n    0xE473: 0x8216,\n    0xE474: 0x8229,\n    0xE475: 0x822B,\n    0xE476: 0x8238,\n    0xE477: 0x8233,\n    0xE478: 0x8240,\n    0xE479: 0x8259,\n    0xE47A: 0x8258,\n    0xE47B: 0x825D,\n    0xE47C: 0x825A,\n    0xE47D: 0x825F,\n    0xE47E: 0x8264,\n    0xE480: 0x8262,\n    0xE481: 0x8268,\n    0xE482: 0x826A,\n    0xE483: 0x826B,\n    0xE484: 0x822E,\n    0xE485: 0x8271,\n    0xE486: 0x8277,\n    0xE487: 0x8278,\n    0xE488: 0x827E,\n    0xE489: 0x828D,\n    0xE48A: 0x8292,\n    0xE48B: 0x82AB,\n    0xE48C: 0x829F,\n    0xE48D: 0x82BB,\n    0xE48E: 0x82AC,\n    0xE48F: 0x82E1,\n    0xE490: 0x82E3,\n    0xE491: 0x82DF,\n    0xE492: 0x82D2,\n    0xE493: 0x82F4,\n    0xE494: 0x82F3,\n    0xE495: 0x82FA,\n    0xE496: 0x8393,\n    0xE497: 0x8303,\n    0xE498: 0x82FB,\n    0xE499: 0x82F9,\n    0xE49A: 0x82DE,\n    0xE49B: 0x8306,\n    0xE49C: 0x82DC,\n    0xE49D: 0x8309,\n    0xE49E: 0x82D9,\n    0xE49F: 0x8335,\n    0xE4A0: 0x8334,\n    0xE4A1: 0x8316,\n    0xE4A2: 0x8332,\n    0xE4A3: 0x8331,\n    0xE4A4: 0x8340,\n    0xE4A5: 0x8339,\n    0xE4A6: 0x8350,\n    0xE4A7: 0x8345,\n    0xE4A8: 0x832F,\n    0xE4A9: 0x832B,\n    0xE4AA: 0x8317,\n    0xE4AB: 0x8318,\n    0xE4AC: 0x8385,\n    0xE4AD: 0x839A,\n    0xE4AE: 0x83AA,\n    0xE4AF: 0x839F,\n    0xE4B0: 0x83A2,\n    0xE4B1: 0x8396,\n    0xE4B2: 0x8323,\n    0xE4B3: 0x838E,\n    0xE4B4: 0x8387,\n    0xE4B5: 0x838A,\n    0xE4B6: 0x837C,\n    0xE4B7: 0x83B5,\n    0xE4B8: 0x8373,\n    0xE4B9: 0x8375,\n    0xE4BA: 0x83A0,\n    0xE4BB: 0x8389,\n    0xE4BC: 0x83A8,\n    0xE4BD: 0x83F4,\n    0xE4BE: 0x8413,\n    0xE4BF: 0x83EB,\n    0xE4C0: 0x83CE,\n    0xE4C1: 0x83FD,\n    0xE4C2: 0x8403,\n    0xE4C3: 0x83D8,\n    0xE4C4: 0x840B,\n    0xE4C5: 0x83C1,\n    0xE4C6: 0x83F7,\n    0xE4C7: 0x8407,\n    0xE4C8: 0x83E0,\n    0xE4C9: 0x83F2,\n    0xE4CA: 0x840D,\n    0xE4CB: 0x8422,\n    0xE4CC: 0x8420,\n    0xE4CD: 0x83BD,\n    0xE4CE: 0x8438,\n    0xE4CF: 0x8506,\n    0xE4D0: 0x83FB,\n    0xE4D1: 0x846D,\n    0xE4D2: 0x842A,\n    0xE4D3: 0x843C,\n    0xE4D4: 0x855A,\n    0xE4D5: 0x8484,\n    0xE4D6: 0x8477,\n    0xE4D7: 0x846B,\n    0xE4D8: 0x84AD,\n    0xE4D9: 0x846E,\n    0xE4DA: 0x8482,\n    0xE4DB: 0x8469,\n    0xE4DC: 0x8446,\n    0xE4DD: 0x842C,\n    0xE4DE: 0x846F,\n    0xE4DF: 0x8479,\n    0xE4E0: 0x8435,\n    0xE4E1: 0x84CA,\n    0xE4E2: 0x8462,\n    0xE4E3: 0x84B9,\n    0xE4E4: 0x84BF,\n    0xE4E5: 0x849F,\n    0xE4E6: 0x84D9,\n    0xE4E7: 0x84CD,\n    0xE4E8: 0x84BB,\n    0xE4E9: 0x84DA,\n    0xE4EA: 0x84D0,\n    0xE4EB: 0x84C1,\n    0xE4EC: 0x84C6,\n    0xE4ED: 0x84D6,\n    0xE4EE: 0x84A1,\n    0xE4EF: 0x8521,\n    0xE4F0: 0x84FF,\n    0xE4F1: 0x84F4,\n    0xE4F2: 0x8517,\n    0xE4F3: 0x8518,\n    0xE4F4: 0x852C,\n    0xE4F5: 0x851F,\n    0xE4F6: 0x8515,\n    0xE4F7: 0x8514,\n    0xE4F8: 0x84FC,\n    0xE4F9: 0x8540,\n    0xE4FA: 0x8563,\n    0xE4FB: 0x8558,\n    0xE4FC: 0x8548,\n    0xE540: 0x8541,\n    0xE541: 0x8602,\n    0xE542: 0x854B,\n    0xE543: 0x8555,\n    0xE544: 0x8580,\n    0xE545: 0x85A4,\n    0xE546: 0x8588,\n    0xE547: 0x8591,\n    0xE548: 0x858A,\n    0xE549: 0x85A8,\n    0xE54A: 0x856D,\n    0xE54B: 0x8594,\n    0xE54C: 0x859B,\n    0xE54D: 0x85EA,\n    0xE54E: 0x8587,\n    0xE54F: 0x859C,\n    0xE550: 0x8577,\n    0xE551: 0x857E,\n    0xE552: 0x8590,\n    0xE553: 0x85C9,\n    0xE554: 0x85BA,\n    0xE555: 0x85CF,\n    0xE556: 0x85B9,\n    0xE557: 0x85D0,\n    0xE558: 0x85D5,\n    0xE559: 0x85DD,\n    0xE55A: 0x85E5,\n    0xE55B: 0x85DC,\n    0xE55C: 0x85F9,\n    0xE55D: 0x860A,\n    0xE55E: 0x8613,\n    0xE55F: 0x860B,\n    0xE560: 0x85FE,\n    0xE561: 0x85FA,\n    0xE562: 0x8606,\n    0xE563: 0x8622,\n    0xE564: 0x861A,\n    0xE565: 0x8630,\n    0xE566: 0x863F,\n    0xE567: 0x864D,\n    0xE568: 0x4E55,\n    0xE569: 0x8654,\n    0xE56A: 0x865F,\n    0xE56B: 0x8667,\n    0xE56C: 0x8671,\n    0xE56D: 0x8693,\n    0xE56E: 0x86A3,\n    0xE56F: 0x86A9,\n    0xE570: 0x86AA,\n    0xE571: 0x868B,\n    0xE572: 0x868C,\n    0xE573: 0x86B6,\n    0xE574: 0x86AF,\n    0xE575: 0x86C4,\n    0xE576: 0x86C6,\n    0xE577: 0x86B0,\n    0xE578: 0x86C9,\n    0xE579: 0x8823,\n    0xE57A: 0x86AB,\n    0xE57B: 0x86D4,\n    0xE57C: 0x86DE,\n    0xE57D: 0x86E9,\n    0xE57E: 0x86EC,\n    0xE580: 0x86DF,\n    0xE581: 0x86DB,\n    0xE582: 0x86EF,\n    0xE583: 0x8712,\n    0xE584: 0x8706,\n    0xE585: 0x8708,\n    0xE586: 0x8700,\n    0xE587: 0x8703,\n    0xE588: 0x86FB,\n    0xE589: 0x8711,\n    0xE58A: 0x8709,\n    0xE58B: 0x870D,\n    0xE58C: 0x86F9,\n    0xE58D: 0x870A,\n    0xE58E: 0x8734,\n    0xE58F: 0x873F,\n    0xE590: 0x8737,\n    0xE591: 0x873B,\n    0xE592: 0x8725,\n    0xE593: 0x8729,\n    0xE594: 0x871A,\n    0xE595: 0x8760,\n    0xE596: 0x875F,\n    0xE597: 0x8778,\n    0xE598: 0x874C,\n    0xE599: 0x874E,\n    0xE59A: 0x8774,\n    0xE59B: 0x8757,\n    0xE59C: 0x8768,\n    0xE59D: 0x876E,\n    0xE59E: 0x8759,\n    0xE59F: 0x8753,\n    0xE5A0: 0x8763,\n    0xE5A1: 0x876A,\n    0xE5A2: 0x8805,\n    0xE5A3: 0x87A2,\n    0xE5A4: 0x879F,\n    0xE5A5: 0x8782,\n    0xE5A6: 0x87AF,\n    0xE5A7: 0x87CB,\n    0xE5A8: 0x87BD,\n    0xE5A9: 0x87C0,\n    0xE5AA: 0x87D0,\n    0xE5AB: 0x96D6,\n    0xE5AC: 0x87AB,\n    0xE5AD: 0x87C4,\n    0xE5AE: 0x87B3,\n    0xE5AF: 0x87C7,\n    0xE5B0: 0x87C6,\n    0xE5B1: 0x87BB,\n    0xE5B2: 0x87EF,\n    0xE5B3: 0x87F2,\n    0xE5B4: 0x87E0,\n    0xE5B5: 0x880F,\n    0xE5B6: 0x880D,\n    0xE5B7: 0x87FE,\n    0xE5B8: 0x87F6,\n    0xE5B9: 0x87F7,\n    0xE5BA: 0x880E,\n    0xE5BB: 0x87D2,\n    0xE5BC: 0x8811,\n    0xE5BD: 0x8816,\n    0xE5BE: 0x8815,\n    0xE5BF: 0x8822,\n    0xE5C0: 0x8821,\n    0xE5C1: 0x8831,\n    0xE5C2: 0x8836,\n    0xE5C3: 0x8839,\n    0xE5C4: 0x8827,\n    0xE5C5: 0x883B,\n    0xE5C6: 0x8844,\n    0xE5C7: 0x8842,\n    0xE5C8: 0x8852,\n    0xE5C9: 0x8859,\n    0xE5CA: 0x885E,\n    0xE5CB: 0x8862,\n    0xE5CC: 0x886B,\n    0xE5CD: 0x8881,\n    0xE5CE: 0x887E,\n    0xE5CF: 0x889E,\n    0xE5D0: 0x8875,\n    0xE5D1: 0x887D,\n    0xE5D2: 0x88B5,\n    0xE5D3: 0x8872,\n    0xE5D4: 0x8882,\n    0xE5D5: 0x8897,\n    0xE5D6: 0x8892,\n    0xE5D7: 0x88AE,\n    0xE5D8: 0x8899,\n    0xE5D9: 0x88A2,\n    0xE5DA: 0x888D,\n    0xE5DB: 0x88A4,\n    0xE5DC: 0x88B0,\n    0xE5DD: 0x88BF,\n    0xE5DE: 0x88B1,\n    0xE5DF: 0x88C3,\n    0xE5E0: 0x88C4,\n    0xE5E1: 0x88D4,\n    0xE5E2: 0x88D8,\n    0xE5E3: 0x88D9,\n    0xE5E4: 0x88DD,\n    0xE5E5: 0x88F9,\n    0xE5E6: 0x8902,\n    0xE5E7: 0x88FC,\n    0xE5E8: 0x88F4,\n    0xE5E9: 0x88E8,\n    0xE5EA: 0x88F2,\n    0xE5EB: 0x8904,\n    0xE5EC: 0x890C,\n    0xE5ED: 0x890A,\n    0xE5EE: 0x8913,\n    0xE5EF: 0x8943,\n    0xE5F0: 0x891E,\n    0xE5F1: 0x8925,\n    0xE5F2: 0x892A,\n    0xE5F3: 0x892B,\n    0xE5F4: 0x8941,\n    0xE5F5: 0x8944,\n    0xE5F6: 0x893B,\n    0xE5F7: 0x8936,\n    0xE5F8: 0x8938,\n    0xE5F9: 0x894C,\n    0xE5FA: 0x891D,\n    0xE5FB: 0x8960,\n    0xE5FC: 0x895E,\n    0xE640: 0x8966,\n    0xE641: 0x8964,\n    0xE642: 0x896D,\n    0xE643: 0x896A,\n    0xE644: 0x896F,\n    0xE645: 0x8974,\n    0xE646: 0x8977,\n    0xE647: 0x897E,\n    0xE648: 0x8983,\n    0xE649: 0x8988,\n    0xE64A: 0x898A,\n    0xE64B: 0x8993,\n    0xE64C: 0x8998,\n    0xE64D: 0x89A1,\n    0xE64E: 0x89A9,\n    0xE64F: 0x89A6,\n    0xE650: 0x89AC,\n    0xE651: 0x89AF,\n    0xE652: 0x89B2,\n    0xE653: 0x89BA,\n    0xE654: 0x89BD,\n    0xE655: 0x89BF,\n    0xE656: 0x89C0,\n    0xE657: 0x89DA,\n    0xE658: 0x89DC,\n    0xE659: 0x89DD,\n    0xE65A: 0x89E7,\n    0xE65B: 0x89F4,\n    0xE65C: 0x89F8,\n    0xE65D: 0x8A03,\n    0xE65E: 0x8A16,\n    0xE65F: 0x8A10,\n    0xE660: 0x8A0C,\n    0xE661: 0x8A1B,\n    0xE662: 0x8A1D,\n    0xE663: 0x8A25,\n    0xE664: 0x8A36,\n    0xE665: 0x8A41,\n    0xE666: 0x8A5B,\n    0xE667: 0x8A52,\n    0xE668: 0x8A46,\n    0xE669: 0x8A48,\n    0xE66A: 0x8A7C,\n    0xE66B: 0x8A6D,\n    0xE66C: 0x8A6C,\n    0xE66D: 0x8A62,\n    0xE66E: 0x8A85,\n    0xE66F: 0x8A82,\n    0xE670: 0x8A84,\n    0xE671: 0x8AA8,\n    0xE672: 0x8AA1,\n    0xE673: 0x8A91,\n    0xE674: 0x8AA5,\n    0xE675: 0x8AA6,\n    0xE676: 0x8A9A,\n    0xE677: 0x8AA3,\n    0xE678: 0x8AC4,\n    0xE679: 0x8ACD,\n    0xE67A: 0x8AC2,\n    0xE67B: 0x8ADA,\n    0xE67C: 0x8AEB,\n    0xE67D: 0x8AF3,\n    0xE67E: 0x8AE7,\n    0xE680: 0x8AE4,\n    0xE681: 0x8AF1,\n    0xE682: 0x8B14,\n    0xE683: 0x8AE0,\n    0xE684: 0x8AE2,\n    0xE685: 0x8AF7,\n    0xE686: 0x8ADE,\n    0xE687: 0x8ADB,\n    0xE688: 0x8B0C,\n    0xE689: 0x8B07,\n    0xE68A: 0x8B1A,\n    0xE68B: 0x8AE1,\n    0xE68C: 0x8B16,\n    0xE68D: 0x8B10,\n    0xE68E: 0x8B17,\n    0xE68F: 0x8B20,\n    0xE690: 0x8B33,\n    0xE691: 0x97AB,\n    0xE692: 0x8B26,\n    0xE693: 0x8B2B,\n    0xE694: 0x8B3E,\n    0xE695: 0x8B28,\n    0xE696: 0x8B41,\n    0xE697: 0x8B4C,\n    0xE698: 0x8B4F,\n    0xE699: 0x8B4E,\n    0xE69A: 0x8B49,\n    0xE69B: 0x8B56,\n    0xE69C: 0x8B5B,\n    0xE69D: 0x8B5A,\n    0xE69E: 0x8B6B,\n    0xE69F: 0x8B5F,\n    0xE6A0: 0x8B6C,\n    0xE6A1: 0x8B6F,\n    0xE6A2: 0x8B74,\n    0xE6A3: 0x8B7D,\n    0xE6A4: 0x8B80,\n    0xE6A5: 0x8B8C,\n    0xE6A6: 0x8B8E,\n    0xE6A7: 0x8B92,\n    0xE6A8: 0x8B93,\n    0xE6A9: 0x8B96,\n    0xE6AA: 0x8B99,\n    0xE6AB: 0x8B9A,\n    0xE6AC: 0x8C3A,\n    0xE6AD: 0x8C41,\n    0xE6AE: 0x8C3F,\n    0xE6AF: 0x8C48,\n    0xE6B0: 0x8C4C,\n    0xE6B1: 0x8C4E,\n    0xE6B2: 0x8C50,\n    0xE6B3: 0x8C55,\n    0xE6B4: 0x8C62,\n    0xE6B5: 0x8C6C,\n    0xE6B6: 0x8C78,\n    0xE6B7: 0x8C7A,\n    0xE6B8: 0x8C82,\n    0xE6B9: 0x8C89,\n    0xE6BA: 0x8C85,\n    0xE6BB: 0x8C8A,\n    0xE6BC: 0x8C8D,\n    0xE6BD: 0x8C8E,\n    0xE6BE: 0x8C94,\n    0xE6BF: 0x8C7C,\n    0xE6C0: 0x8C98,\n    0xE6C1: 0x621D,\n    0xE6C2: 0x8CAD,\n    0xE6C3: 0x8CAA,\n    0xE6C4: 0x8CBD,\n    0xE6C5: 0x8CB2,\n    0xE6C6: 0x8CB3,\n    0xE6C7: 0x8CAE,\n    0xE6C8: 0x8CB6,\n    0xE6C9: 0x8CC8,\n    0xE6CA: 0x8CC1,\n    0xE6CB: 0x8CE4,\n    0xE6CC: 0x8CE3,\n    0xE6CD: 0x8CDA,\n    0xE6CE: 0x8CFD,\n    0xE6CF: 0x8CFA,\n    0xE6D0: 0x8CFB,\n    0xE6D1: 0x8D04,\n    0xE6D2: 0x8D05,\n    0xE6D3: 0x8D0A,\n    0xE6D4: 0x8D07,\n    0xE6D5: 0x8D0F,\n    0xE6D6: 0x8D0D,\n    0xE6D7: 0x8D10,\n    0xE6D8: 0x9F4E,\n    0xE6D9: 0x8D13,\n    0xE6DA: 0x8CCD,\n    0xE6DB: 0x8D14,\n    0xE6DC: 0x8D16,\n    0xE6DD: 0x8D67,\n    0xE6DE: 0x8D6D,\n    0xE6DF: 0x8D71,\n    0xE6E0: 0x8D73,\n    0xE6E1: 0x8D81,\n    0xE6E2: 0x8D99,\n    0xE6E3: 0x8DC2,\n    0xE6E4: 0x8DBE,\n    0xE6E5: 0x8DBA,\n    0xE6E6: 0x8DCF,\n    0xE6E7: 0x8DDA,\n    0xE6E8: 0x8DD6,\n    0xE6E9: 0x8DCC,\n    0xE6EA: 0x8DDB,\n    0xE6EB: 0x8DCB,\n    0xE6EC: 0x8DEA,\n    0xE6ED: 0x8DEB,\n    0xE6EE: 0x8DDF,\n    0xE6EF: 0x8DE3,\n    0xE6F0: 0x8DFC,\n    0xE6F1: 0x8E08,\n    0xE6F2: 0x8E09,\n    0xE6F3: 0x8DFF,\n    0xE6F4: 0x8E1D,\n    0xE6F5: 0x8E1E,\n    0xE6F6: 0x8E10,\n    0xE6F7: 0x8E1F,\n    0xE6F8: 0x8E42,\n    0xE6F9: 0x8E35,\n    0xE6FA: 0x8E30,\n    0xE6FB: 0x8E34,\n    0xE6FC: 0x8E4A,\n    0xE740: 0x8E47,\n    0xE741: 0x8E49,\n    0xE742: 0x8E4C,\n    0xE743: 0x8E50,\n    0xE744: 0x8E48,\n    0xE745: 0x8E59,\n    0xE746: 0x8E64,\n    0xE747: 0x8E60,\n    0xE748: 0x8E2A,\n    0xE749: 0x8E63,\n    0xE74A: 0x8E55,\n    0xE74B: 0x8E76,\n    0xE74C: 0x8E72,\n    0xE74D: 0x8E7C,\n    0xE74E: 0x8E81,\n    0xE74F: 0x8E87,\n    0xE750: 0x8E85,\n    0xE751: 0x8E84,\n    0xE752: 0x8E8B,\n    0xE753: 0x8E8A,\n    0xE754: 0x8E93,\n    0xE755: 0x8E91,\n    0xE756: 0x8E94,\n    0xE757: 0x8E99,\n    0xE758: 0x8EAA,\n    0xE759: 0x8EA1,\n    0xE75A: 0x8EAC,\n    0xE75B: 0x8EB0,\n    0xE75C: 0x8EC6,\n    0xE75D: 0x8EB1,\n    0xE75E: 0x8EBE,\n    0xE75F: 0x8EC5,\n    0xE760: 0x8EC8,\n    0xE761: 0x8ECB,\n    0xE762: 0x8EDB,\n    0xE763: 0x8EE3,\n    0xE764: 0x8EFC,\n    0xE765: 0x8EFB,\n    0xE766: 0x8EEB,\n    0xE767: 0x8EFE,\n    0xE768: 0x8F0A,\n    0xE769: 0x8F05,\n    0xE76A: 0x8F15,\n    0xE76B: 0x8F12,\n    0xE76C: 0x8F19,\n    0xE76D: 0x8F13,\n    0xE76E: 0x8F1C,\n    0xE76F: 0x8F1F,\n    0xE770: 0x8F1B,\n    0xE771: 0x8F0C,\n    0xE772: 0x8F26,\n    0xE773: 0x8F33,\n    0xE774: 0x8F3B,\n    0xE775: 0x8F39,\n    0xE776: 0x8F45,\n    0xE777: 0x8F42,\n    0xE778: 0x8F3E,\n    0xE779: 0x8F4C,\n    0xE77A: 0x8F49,\n    0xE77B: 0x8F46,\n    0xE77C: 0x8F4E,\n    0xE77D: 0x8F57,\n    0xE77E: 0x8F5C,\n    0xE780: 0x8F62,\n    0xE781: 0x8F63,\n    0xE782: 0x8F64,\n    0xE783: 0x8F9C,\n    0xE784: 0x8F9F,\n    0xE785: 0x8FA3,\n    0xE786: 0x8FAD,\n    0xE787: 0x8FAF,\n    0xE788: 0x8FB7,\n    0xE789: 0x8FDA,\n    0xE78A: 0x8FE5,\n    0xE78B: 0x8FE2,\n    0xE78C: 0x8FEA,\n    0xE78D: 0x8FEF,\n    0xE78E: 0x9087,\n    0xE78F: 0x8FF4,\n    0xE790: 0x9005,\n    0xE791: 0x8FF9,\n    0xE792: 0x8FFA,\n    0xE793: 0x9011,\n    0xE794: 0x9015,\n    0xE795: 0x9021,\n    0xE796: 0x900D,\n    0xE797: 0x901E,\n    0xE798: 0x9016,\n    0xE799: 0x900B,\n    0xE79A: 0x9027,\n    0xE79B: 0x9036,\n    0xE79C: 0x9035,\n    0xE79D: 0x9039,\n    0xE79E: 0x8FF8,\n    0xE79F: 0x904F,\n    0xE7A0: 0x9050,\n    0xE7A1: 0x9051,\n    0xE7A2: 0x9052,\n    0xE7A3: 0x900E,\n    0xE7A4: 0x9049,\n    0xE7A5: 0x903E,\n    0xE7A6: 0x9056,\n    0xE7A7: 0x9058,\n    0xE7A8: 0x905E,\n    0xE7A9: 0x9068,\n    0xE7AA: 0x906F,\n    0xE7AB: 0x9076,\n    0xE7AC: 0x96A8,\n    0xE7AD: 0x9072,\n    0xE7AE: 0x9082,\n    0xE7AF: 0x907D,\n    0xE7B0: 0x9081,\n    0xE7B1: 0x9080,\n    0xE7B2: 0x908A,\n    0xE7B3: 0x9089,\n    0xE7B4: 0x908F,\n    0xE7B5: 0x90A8,\n    0xE7B6: 0x90AF,\n    0xE7B7: 0x90B1,\n    0xE7B8: 0x90B5,\n    0xE7B9: 0x90E2,\n    0xE7BA: 0x90E4,\n    0xE7BB: 0x6248,\n    0xE7BC: 0x90DB,\n    0xE7BD: 0x9102,\n    0xE7BE: 0x9112,\n    0xE7BF: 0x9119,\n    0xE7C0: 0x9132,\n    0xE7C1: 0x9130,\n    0xE7C2: 0x914A,\n    0xE7C3: 0x9156,\n    0xE7C4: 0x9158,\n    0xE7C5: 0x9163,\n    0xE7C6: 0x9165,\n    0xE7C7: 0x9169,\n    0xE7C8: 0x9173,\n    0xE7C9: 0x9172,\n    0xE7CA: 0x918B,\n    0xE7CB: 0x9189,\n    0xE7CC: 0x9182,\n    0xE7CD: 0x91A2,\n    0xE7CE: 0x91AB,\n    0xE7CF: 0x91AF,\n    0xE7D0: 0x91AA,\n    0xE7D1: 0x91B5,\n    0xE7D2: 0x91B4,\n    0xE7D3: 0x91BA,\n    0xE7D4: 0x91C0,\n    0xE7D5: 0x91C1,\n    0xE7D6: 0x91C9,\n    0xE7D7: 0x91CB,\n    0xE7D8: 0x91D0,\n    0xE7D9: 0x91D6,\n    0xE7DA: 0x91DF,\n    0xE7DB: 0x91E1,\n    0xE7DC: 0x91DB,\n    0xE7DD: 0x91FC,\n    0xE7DE: 0x91F5,\n    0xE7DF: 0x91F6,\n    0xE7E0: 0x921E,\n    0xE7E1: 0x91FF,\n    0xE7E2: 0x9214,\n    0xE7E3: 0x922C,\n    0xE7E4: 0x9215,\n    0xE7E5: 0x9211,\n    0xE7E6: 0x925E,\n    0xE7E7: 0x9257,\n    0xE7E8: 0x9245,\n    0xE7E9: 0x9249,\n    0xE7EA: 0x9264,\n    0xE7EB: 0x9248,\n    0xE7EC: 0x9295,\n    0xE7ED: 0x923F,\n    0xE7EE: 0x924B,\n    0xE7EF: 0x9250,\n    0xE7F0: 0x929C,\n    0xE7F1: 0x9296,\n    0xE7F2: 0x9293,\n    0xE7F3: 0x929B,\n    0xE7F4: 0x925A,\n    0xE7F5: 0x92CF,\n    0xE7F6: 0x92B9,\n    0xE7F7: 0x92B7,\n    0xE7F8: 0x92E9,\n    0xE7F9: 0x930F,\n    0xE7FA: 0x92FA,\n    0xE7FB: 0x9344,\n    0xE7FC: 0x932E,\n    0xE840: 0x9319,\n    0xE841: 0x9322,\n    0xE842: 0x931A,\n    0xE843: 0x9323,\n    0xE844: 0x933A,\n    0xE845: 0x9335,\n    0xE846: 0x933B,\n    0xE847: 0x935C,\n    0xE848: 0x9360,\n    0xE849: 0x937C,\n    0xE84A: 0x936E,\n    0xE84B: 0x9356,\n    0xE84C: 0x93B0,\n    0xE84D: 0x93AC,\n    0xE84E: 0x93AD,\n    0xE84F: 0x9394,\n    0xE850: 0x93B9,\n    0xE851: 0x93D6,\n    0xE852: 0x93D7,\n    0xE853: 0x93E8,\n    0xE854: 0x93E5,\n    0xE855: 0x93D8,\n    0xE856: 0x93C3,\n    0xE857: 0x93DD,\n    0xE858: 0x93D0,\n    0xE859: 0x93C8,\n    0xE85A: 0x93E4,\n    0xE85B: 0x941A,\n    0xE85C: 0x9414,\n    0xE85D: 0x9413,\n    0xE85E: 0x9403,\n    0xE85F: 0x9407,\n    0xE860: 0x9410,\n    0xE861: 0x9436,\n    0xE862: 0x942B,\n    0xE863: 0x9435,\n    0xE864: 0x9421,\n    0xE865: 0x943A,\n    0xE866: 0x9441,\n    0xE867: 0x9452,\n    0xE868: 0x9444,\n    0xE869: 0x945B,\n    0xE86A: 0x9460,\n    0xE86B: 0x9462,\n    0xE86C: 0x945E,\n    0xE86D: 0x946A,\n    0xE86E: 0x9229,\n    0xE86F: 0x9470,\n    0xE870: 0x9475,\n    0xE871: 0x9477,\n    0xE872: 0x947D,\n    0xE873: 0x945A,\n    0xE874: 0x947C,\n    0xE875: 0x947E,\n    0xE876: 0x9481,\n    0xE877: 0x947F,\n    0xE878: 0x9582,\n    0xE879: 0x9587,\n    0xE87A: 0x958A,\n    0xE87B: 0x9594,\n    0xE87C: 0x9596,\n    0xE87D: 0x9598,\n    0xE87E: 0x9599,\n    0xE880: 0x95A0,\n    0xE881: 0x95A8,\n    0xE882: 0x95A7,\n    0xE883: 0x95AD,\n    0xE884: 0x95BC,\n    0xE885: 0x95BB,\n    0xE886: 0x95B9,\n    0xE887: 0x95BE,\n    0xE888: 0x95CA,\n    0xE889: 0x6FF6,\n    0xE88A: 0x95C3,\n    0xE88B: 0x95CD,\n    0xE88C: 0x95CC,\n    0xE88D: 0x95D5,\n    0xE88E: 0x95D4,\n    0xE88F: 0x95D6,\n    0xE890: 0x95DC,\n    0xE891: 0x95E1,\n    0xE892: 0x95E5,\n    0xE893: 0x95E2,\n    0xE894: 0x9621,\n    0xE895: 0x9628,\n    0xE896: 0x962E,\n    0xE897: 0x962F,\n    0xE898: 0x9642,\n    0xE899: 0x964C,\n    0xE89A: 0x964F,\n    0xE89B: 0x964B,\n    0xE89C: 0x9677,\n    0xE89D: 0x965C,\n    0xE89E: 0x965E,\n    0xE89F: 0x965D,\n    0xE8A0: 0x965F,\n    0xE8A1: 0x9666,\n    0xE8A2: 0x9672,\n    0xE8A3: 0x966C,\n    0xE8A4: 0x968D,\n    0xE8A5: 0x9698,\n    0xE8A6: 0x9695,\n    0xE8A7: 0x9697,\n    0xE8A8: 0x96AA,\n    0xE8A9: 0x96A7,\n    0xE8AA: 0x96B1,\n    0xE8AB: 0x96B2,\n    0xE8AC: 0x96B0,\n    0xE8AD: 0x96B4,\n    0xE8AE: 0x96B6,\n    0xE8AF: 0x96B8,\n    0xE8B0: 0x96B9,\n    0xE8B1: 0x96CE,\n    0xE8B2: 0x96CB,\n    0xE8B3: 0x96C9,\n    0xE8B4: 0x96CD,\n    0xE8B5: 0x894D,\n    0xE8B6: 0x96DC,\n    0xE8B7: 0x970D,\n    0xE8B8: 0x96D5,\n    0xE8B9: 0x96F9,\n    0xE8BA: 0x9704,\n    0xE8BB: 0x9706,\n    0xE8BC: 0x9708,\n    0xE8BD: 0x9713,\n    0xE8BE: 0x970E,\n    0xE8BF: 0x9711,\n    0xE8C0: 0x970F,\n    0xE8C1: 0x9716,\n    0xE8C2: 0x9719,\n    0xE8C3: 0x9724,\n    0xE8C4: 0x972A,\n    0xE8C5: 0x9730,\n    0xE8C6: 0x9739,\n    0xE8C7: 0x973D,\n    0xE8C8: 0x973E,\n    0xE8C9: 0x9744,\n    0xE8CA: 0x9746,\n    0xE8CB: 0x9748,\n    0xE8CC: 0x9742,\n    0xE8CD: 0x9749,\n    0xE8CE: 0x975C,\n    0xE8CF: 0x9760,\n    0xE8D0: 0x9764,\n    0xE8D1: 0x9766,\n    0xE8D2: 0x9768,\n    0xE8D3: 0x52D2,\n    0xE8D4: 0x976B,\n    0xE8D5: 0x9771,\n    0xE8D6: 0x9779,\n    0xE8D7: 0x9785,\n    0xE8D8: 0x977C,\n    0xE8D9: 0x9781,\n    0xE8DA: 0x977A,\n    0xE8DB: 0x9786,\n    0xE8DC: 0x978B,\n    0xE8DD: 0x978F,\n    0xE8DE: 0x9790,\n    0xE8DF: 0x979C,\n    0xE8E0: 0x97A8,\n    0xE8E1: 0x97A6,\n    0xE8E2: 0x97A3,\n    0xE8E3: 0x97B3,\n    0xE8E4: 0x97B4,\n    0xE8E5: 0x97C3,\n    0xE8E6: 0x97C6,\n    0xE8E7: 0x97C8,\n    0xE8E8: 0x97CB,\n    0xE8E9: 0x97DC,\n    0xE8EA: 0x97ED,\n    0xE8EB: 0x9F4F,\n    0xE8EC: 0x97F2,\n    0xE8ED: 0x7ADF,\n    0xE8EE: 0x97F6,\n    0xE8EF: 0x97F5,\n    0xE8F0: 0x980F,\n    0xE8F1: 0x980C,\n    0xE8F2: 0x9838,\n    0xE8F3: 0x9824,\n    0xE8F4: 0x9821,\n    0xE8F5: 0x9837,\n    0xE8F6: 0x983D,\n    0xE8F7: 0x9846,\n    0xE8F8: 0x984F,\n    0xE8F9: 0x984B,\n    0xE8FA: 0x986B,\n    0xE8FB: 0x986F,\n    0xE8FC: 0x9870,\n    0xE940: 0x9871,\n    0xE941: 0x9874,\n    0xE942: 0x9873,\n    0xE943: 0x98AA,\n    0xE944: 0x98AF,\n    0xE945: 0x98B1,\n    0xE946: 0x98B6,\n    0xE947: 0x98C4,\n    0xE948: 0x98C3,\n    0xE949: 0x98C6,\n    0xE94A: 0x98E9,\n    0xE94B: 0x98EB,\n    0xE94C: 0x9903,\n    0xE94D: 0x9909,\n    0xE94E: 0x9912,\n    0xE94F: 0x9914,\n    0xE950: 0x9918,\n    0xE951: 0x9921,\n    0xE952: 0x991D,\n    0xE953: 0x991E,\n    0xE954: 0x9924,\n    0xE955: 0x9920,\n    0xE956: 0x992C,\n    0xE957: 0x992E,\n    0xE958: 0x993D,\n    0xE959: 0x993E,\n    0xE95A: 0x9942,\n    0xE95B: 0x9949,\n    0xE95C: 0x9945,\n    0xE95D: 0x9950,\n    0xE95E: 0x994B,\n    0xE95F: 0x9951,\n    0xE960: 0x9952,\n    0xE961: 0x994C,\n    0xE962: 0x9955,\n    0xE963: 0x9997,\n    0xE964: 0x9998,\n    0xE965: 0x99A5,\n    0xE966: 0x99AD,\n    0xE967: 0x99AE,\n    0xE968: 0x99BC,\n    0xE969: 0x99DF,\n    0xE96A: 0x99DB,\n    0xE96B: 0x99DD,\n    0xE96C: 0x99D8,\n    0xE96D: 0x99D1,\n    0xE96E: 0x99ED,\n    0xE96F: 0x99EE,\n    0xE970: 0x99F1,\n    0xE971: 0x99F2,\n    0xE972: 0x99FB,\n    0xE973: 0x99F8,\n    0xE974: 0x9A01,\n    0xE975: 0x9A0F,\n    0xE976: 0x9A05,\n    0xE977: 0x99E2,\n    0xE978: 0x9A19,\n    0xE979: 0x9A2B,\n    0xE97A: 0x9A37,\n    0xE97B: 0x9A45,\n    0xE97C: 0x9A42,\n    0xE97D: 0x9A40,\n    0xE97E: 0x9A43,\n    0xE980: 0x9A3E,\n    0xE981: 0x9A55,\n    0xE982: 0x9A4D,\n    0xE983: 0x9A5B,\n    0xE984: 0x9A57,\n    0xE985: 0x9A5F,\n    0xE986: 0x9A62,\n    0xE987: 0x9A65,\n    0xE988: 0x9A64,\n    0xE989: 0x9A69,\n    0xE98A: 0x9A6B,\n    0xE98B: 0x9A6A,\n    0xE98C: 0x9AAD,\n    0xE98D: 0x9AB0,\n    0xE98E: 0x9ABC,\n    0xE98F: 0x9AC0,\n    0xE990: 0x9ACF,\n    0xE991: 0x9AD1,\n    0xE992: 0x9AD3,\n    0xE993: 0x9AD4,\n    0xE994: 0x9ADE,\n    0xE995: 0x9ADF,\n    0xE996: 0x9AE2,\n    0xE997: 0x9AE3,\n    0xE998: 0x9AE6,\n    0xE999: 0x9AEF,\n    0xE99A: 0x9AEB,\n    0xE99B: 0x9AEE,\n    0xE99C: 0x9AF4,\n    0xE99D: 0x9AF1,\n    0xE99E: 0x9AF7,\n    0xE99F: 0x9AFB,\n    0xE9A0: 0x9B06,\n    0xE9A1: 0x9B18,\n    0xE9A2: 0x9B1A,\n    0xE9A3: 0x9B1F,\n    0xE9A4: 0x9B22,\n    0xE9A5: 0x9B23,\n    0xE9A6: 0x9B25,\n    0xE9A7: 0x9B27,\n    0xE9A8: 0x9B28,\n    0xE9A9: 0x9B29,\n    0xE9AA: 0x9B2A,\n    0xE9AB: 0x9B2E,\n    0xE9AC: 0x9B2F,\n    0xE9AD: 0x9B32,\n    0xE9AE: 0x9B44,\n    0xE9AF: 0x9B43,\n    0xE9B0: 0x9B4F,\n    0xE9B1: 0x9B4D,\n    0xE9B2: 0x9B4E,\n    0xE9B3: 0x9B51,\n    0xE9B4: 0x9B58,\n    0xE9B5: 0x9B74,\n    0xE9B6: 0x9B93,\n    0xE9B7: 0x9B83,\n    0xE9B8: 0x9B91,\n    0xE9B9: 0x9B96,\n    0xE9BA: 0x9B97,\n    0xE9BB: 0x9B9F,\n    0xE9BC: 0x9BA0,\n    0xE9BD: 0x9BA8,\n    0xE9BE: 0x9BB4,\n    0xE9BF: 0x9BC0,\n    0xE9C0: 0x9BCA,\n    0xE9C1: 0x9BB9,\n    0xE9C2: 0x9BC6,\n    0xE9C3: 0x9BCF,\n    0xE9C4: 0x9BD1,\n    0xE9C5: 0x9BD2,\n    0xE9C6: 0x9BE3,\n    0xE9C7: 0x9BE2,\n    0xE9C8: 0x9BE4,\n    0xE9C9: 0x9BD4,\n    0xE9CA: 0x9BE1,\n    0xE9CB: 0x9C3A,\n    0xE9CC: 0x9BF2,\n    0xE9CD: 0x9BF1,\n    0xE9CE: 0x9BF0,\n    0xE9CF: 0x9C15,\n    0xE9D0: 0x9C14,\n    0xE9D1: 0x9C09,\n    0xE9D2: 0x9C13,\n    0xE9D3: 0x9C0C,\n    0xE9D4: 0x9C06,\n    0xE9D5: 0x9C08,\n    0xE9D6: 0x9C12,\n    0xE9D7: 0x9C0A,\n    0xE9D8: 0x9C04,\n    0xE9D9: 0x9C2E,\n    0xE9DA: 0x9C1B,\n    0xE9DB: 0x9C25,\n    0xE9DC: 0x9C24,\n    0xE9DD: 0x9C21,\n    0xE9DE: 0x9C30,\n    0xE9DF: 0x9C47,\n    0xE9E0: 0x9C32,\n    0xE9E1: 0x9C46,\n    0xE9E2: 0x9C3E,\n    0xE9E3: 0x9C5A,\n    0xE9E4: 0x9C60,\n    0xE9E5: 0x9C67,\n    0xE9E6: 0x9C76,\n    0xE9E7: 0x9C78,\n    0xE9E8: 0x9CE7,\n    0xE9E9: 0x9CEC,\n    0xE9EA: 0x9CF0,\n    0xE9EB: 0x9D09,\n    0xE9EC: 0x9D08,\n    0xE9ED: 0x9CEB,\n    0xE9EE: 0x9D03,\n    0xE9EF: 0x9D06,\n    0xE9F0: 0x9D2A,\n    0xE9F1: 0x9D26,\n    0xE9F2: 0x9DAF,\n    0xE9F3: 0x9D23,\n    0xE9F4: 0x9D1F,\n    0xE9F5: 0x9D44,\n    0xE9F6: 0x9D15,\n    0xE9F7: 0x9D12,\n    0xE9F8: 0x9D41,\n    0xE9F9: 0x9D3F,\n    0xE9FA: 0x9D3E,\n    0xE9FB: 0x9D46,\n    0xE9FC: 0x9D48,\n    0xEA40: 0x9D5D,\n    0xEA41: 0x9D5E,\n    0xEA42: 0x9D64,\n    0xEA43: 0x9D51,\n    0xEA44: 0x9D50,\n    0xEA45: 0x9D59,\n    0xEA46: 0x9D72,\n    0xEA47: 0x9D89,\n    0xEA48: 0x9D87,\n    0xEA49: 0x9DAB,\n    0xEA4A: 0x9D6F,\n    0xEA4B: 0x9D7A,\n    0xEA4C: 0x9D9A,\n    0xEA4D: 0x9DA4,\n    0xEA4E: 0x9DA9,\n    0xEA4F: 0x9DB2,\n    0xEA50: 0x9DC4,\n    0xEA51: 0x9DC1,\n    0xEA52: 0x9DBB,\n    0xEA53: 0x9DB8,\n    0xEA54: 0x9DBA,\n    0xEA55: 0x9DC6,\n    0xEA56: 0x9DCF,\n    0xEA57: 0x9DC2,\n    0xEA58: 0x9DD9,\n    0xEA59: 0x9DD3,\n    0xEA5A: 0x9DF8,\n    0xEA5B: 0x9DE6,\n    0xEA5C: 0x9DED,\n    0xEA5D: 0x9DEF,\n    0xEA5E: 0x9DFD,\n    0xEA5F: 0x9E1A,\n    0xEA60: 0x9E1B,\n    0xEA61: 0x9E1E,\n    0xEA62: 0x9E75,\n    0xEA63: 0x9E79,\n    0xEA64: 0x9E7D,\n    0xEA65: 0x9E81,\n    0xEA66: 0x9E88,\n    0xEA67: 0x9E8B,\n    0xEA68: 0x9E8C,\n    0xEA69: 0x9E92,\n    0xEA6A: 0x9E95,\n    0xEA6B: 0x9E91,\n    0xEA6C: 0x9E9D,\n    0xEA6D: 0x9EA5,\n    0xEA6E: 0x9EA9,\n    0xEA6F: 0x9EB8,\n    0xEA70: 0x9EAA,\n    0xEA71: 0x9EAD,\n    0xEA72: 0x9761,\n    0xEA73: 0x9ECC,\n    0xEA74: 0x9ECE,\n    0xEA75: 0x9ECF,\n    0xEA76: 0x9ED0,\n    0xEA77: 0x9ED4,\n    0xEA78: 0x9EDC,\n    0xEA79: 0x9EDE,\n    0xEA7A: 0x9EDD,\n    0xEA7B: 0x9EE0,\n    0xEA7C: 0x9EE5,\n    0xEA7D: 0x9EE8,\n    0xEA7E: 0x9EEF,\n    0xEA80: 0x9EF4,\n    0xEA81: 0x9EF6,\n    0xEA82: 0x9EF7,\n    0xEA83: 0x9EF9,\n    0xEA84: 0x9EFB,\n    0xEA85: 0x9EFC,\n    0xEA86: 0x9EFD,\n    0xEA87: 0x9F07,\n    0xEA88: 0x9F08,\n    0xEA89: 0x76B7,\n    0xEA8A: 0x9F15,\n    0xEA8B: 0x9F21,\n    0xEA8C: 0x9F2C,\n    0xEA8D: 0x9F3E,\n    0xEA8E: 0x9F4A,\n    0xEA8F: 0x9F52,\n    0xEA90: 0x9F54,\n    0xEA91: 0x9F63,\n    0xEA92: 0x9F5F,\n    0xEA93: 0x9F60,\n    0xEA94: 0x9F61,\n    0xEA95: 0x9F66,\n    0xEA96: 0x9F67,\n    0xEA97: 0x9F6C,\n    0xEA98: 0x9F6A,\n    0xEA99: 0x9F77,\n    0xEA9A: 0x9F72,\n    0xEA9B: 0x9F76,\n    0xEA9C: 0x9F95,\n    0xEA9D: 0x9F9C,\n    0xEA9E: 0x9FA0,\n    0xEA9F: 0x582F,\n    0xEAA0: 0x69C7,\n    0xEAA1: 0x9059,\n    0xEAA2: 0x7464,\n    0xEAA3: 0x51DC,\n    0xEAA4: 0x7199,\n};\n\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar GenericGF_1 = __webpack_require__(1);\nvar GenericGFPoly_1 = __webpack_require__(2);\nfunction runEuclideanAlgorithm(field, a, b, R) {\n    var _a;\n    // Assume a's degree is >= b's\n    if (a.degree() < b.degree()) {\n        _a = [b, a], a = _a[0], b = _a[1];\n    }\n    var rLast = a;\n    var r = b;\n    var tLast = field.zero;\n    var t = field.one;\n    // Run Euclidean algorithm until r's degree is less than R/2\n    while (r.degree() >= R / 2) {\n        var rLastLast = rLast;\n        var tLastLast = tLast;\n        rLast = r;\n        tLast = t;\n        // Divide rLastLast by rLast, with quotient in q and remainder in r\n        if (rLast.isZero()) {\n            // Euclidean algorithm already terminated?\n            return null;\n        }\n        r = rLastLast;\n        var q = field.zero;\n        var denominatorLeadingTerm = rLast.getCoefficient(rLast.degree());\n        var dltInverse = field.inverse(denominatorLeadingTerm);\n        while (r.degree() >= rLast.degree() && !r.isZero()) {\n            var degreeDiff = r.degree() - rLast.degree();\n            var scale = field.multiply(r.getCoefficient(r.degree()), dltInverse);\n            q = q.addOrSubtract(field.buildMonomial(degreeDiff, scale));\n            r = r.addOrSubtract(rLast.multiplyByMonomial(degreeDiff, scale));\n        }\n        t = q.multiplyPoly(tLast).addOrSubtract(tLastLast);\n        if (r.degree() >= rLast.degree()) {\n            return null;\n        }\n    }\n    var sigmaTildeAtZero = t.getCoefficient(0);\n    if (sigmaTildeAtZero === 0) {\n        return null;\n    }\n    var inverse = field.inverse(sigmaTildeAtZero);\n    return [t.multiply(inverse), r.multiply(inverse)];\n}\nfunction findErrorLocations(field, errorLocator) {\n    // This is a direct application of Chien's search\n    var numErrors = errorLocator.degree();\n    if (numErrors === 1) {\n        return [errorLocator.getCoefficient(1)];\n    }\n    var result = new Array(numErrors);\n    var errorCount = 0;\n    for (var i = 1; i < field.size && errorCount < numErrors; i++) {\n        if (errorLocator.evaluateAt(i) === 0) {\n            result[errorCount] = field.inverse(i);\n            errorCount++;\n        }\n    }\n    if (errorCount !== numErrors) {\n        return null;\n    }\n    return result;\n}\nfunction findErrorMagnitudes(field, errorEvaluator, errorLocations) {\n    // This is directly applying Forney's Formula\n    var s = errorLocations.length;\n    var result = new Array(s);\n    for (var i = 0; i < s; i++) {\n        var xiInverse = field.inverse(errorLocations[i]);\n        var denominator = 1;\n        for (var j = 0; j < s; j++) {\n            if (i !== j) {\n                denominator = field.multiply(denominator, GenericGF_1.addOrSubtractGF(1, field.multiply(errorLocations[j], xiInverse)));\n            }\n        }\n        result[i] = field.multiply(errorEvaluator.evaluateAt(xiInverse), field.inverse(denominator));\n        if (field.generatorBase !== 0) {\n            result[i] = field.multiply(result[i], xiInverse);\n        }\n    }\n    return result;\n}\nfunction decode(bytes, twoS) {\n    var outputBytes = new Uint8ClampedArray(bytes.length);\n    outputBytes.set(bytes);\n    var field = new GenericGF_1.default(0x011D, 256, 0); // x^8 + x^4 + x^3 + x^2 + 1\n    var poly = new GenericGFPoly_1.default(field, outputBytes);\n    var syndromeCoefficients = new Uint8ClampedArray(twoS);\n    var error = false;\n    for (var s = 0; s < twoS; s++) {\n        var evaluation = poly.evaluateAt(field.exp(s + field.generatorBase));\n        syndromeCoefficients[syndromeCoefficients.length - 1 - s] = evaluation;\n        if (evaluation !== 0) {\n            error = true;\n        }\n    }\n    if (!error) {\n        return outputBytes;\n    }\n    var syndrome = new GenericGFPoly_1.default(field, syndromeCoefficients);\n    var sigmaOmega = runEuclideanAlgorithm(field, field.buildMonomial(twoS, 1), syndrome, twoS);\n    if (sigmaOmega === null) {\n        return null;\n    }\n    var errorLocations = findErrorLocations(field, sigmaOmega[0]);\n    if (errorLocations == null) {\n        return null;\n    }\n    var errorMagnitudes = findErrorMagnitudes(field, sigmaOmega[1], errorLocations);\n    for (var i = 0; i < errorLocations.length; i++) {\n        var position = outputBytes.length - 1 - field.log(errorLocations[i]);\n        if (position < 0) {\n            return null;\n        }\n        outputBytes[position] = GenericGF_1.addOrSubtractGF(outputBytes[position], errorMagnitudes[i]);\n    }\n    return outputBytes;\n}\nexports.decode = decode;\n\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.VERSIONS = [\n    {\n        infoBits: null,\n        versionNumber: 1,\n        alignmentPatternCenters: [],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 7,\n                ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 19 }],\n            },\n            {\n                ecCodewordsPerBlock: 10,\n                ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 16 }],\n            },\n            {\n                ecCodewordsPerBlock: 13,\n                ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 13 }],\n            },\n            {\n                ecCodewordsPerBlock: 17,\n                ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 9 }],\n            },\n        ],\n    },\n    {\n        infoBits: null,\n        versionNumber: 2,\n        alignmentPatternCenters: [6, 18],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 10,\n                ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 34 }],\n            },\n            {\n                ecCodewordsPerBlock: 16,\n                ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 28 }],\n            },\n            {\n                ecCodewordsPerBlock: 22,\n                ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 22 }],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 16 }],\n            },\n        ],\n    },\n    {\n        infoBits: null,\n        versionNumber: 3,\n        alignmentPatternCenters: [6, 22],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 15,\n                ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 55 }],\n            },\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 44 }],\n            },\n            {\n                ecCodewordsPerBlock: 18,\n                ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 17 }],\n            },\n            {\n                ecCodewordsPerBlock: 22,\n                ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 13 }],\n            },\n        ],\n    },\n    {\n        infoBits: null,\n        versionNumber: 4,\n        alignmentPatternCenters: [6, 26],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 20,\n                ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 80 }],\n            },\n            {\n                ecCodewordsPerBlock: 18,\n                ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 32 }],\n            },\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 24 }],\n            },\n            {\n                ecCodewordsPerBlock: 16,\n                ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 9 }],\n            },\n        ],\n    },\n    {\n        infoBits: null,\n        versionNumber: 5,\n        alignmentPatternCenters: [6, 30],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 108 }],\n            },\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 43 }],\n            },\n            {\n                ecCodewordsPerBlock: 18,\n                ecBlocks: [\n                    { numBlocks: 2, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 16 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 22,\n                ecBlocks: [\n                    { numBlocks: 2, dataCodewordsPerBlock: 11 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 12 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: null,\n        versionNumber: 6,\n        alignmentPatternCenters: [6, 34],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 18,\n                ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 68 }],\n            },\n            {\n                ecCodewordsPerBlock: 16,\n                ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 27 }],\n            },\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 19 }],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 15 }],\n            },\n        ],\n    },\n    {\n        infoBits: 0x07C94,\n        versionNumber: 7,\n        alignmentPatternCenters: [6, 22, 38],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 20,\n                ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 78 }],\n            },\n            {\n                ecCodewordsPerBlock: 18,\n                ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 31 }],\n            },\n            {\n                ecCodewordsPerBlock: 18,\n                ecBlocks: [\n                    { numBlocks: 2, dataCodewordsPerBlock: 14 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 15 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 13 },\n                    { numBlocks: 1, dataCodewordsPerBlock: 14 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x085BC,\n        versionNumber: 8,\n        alignmentPatternCenters: [6, 24, 42],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 97 }],\n            },\n            {\n                ecCodewordsPerBlock: 22,\n                ecBlocks: [\n                    { numBlocks: 2, dataCodewordsPerBlock: 38 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 39 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 22,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 18 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 19 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 14 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 15 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x09A99,\n        versionNumber: 9,\n        alignmentPatternCenters: [6, 26, 46],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 116 }],\n            },\n            {\n                ecCodewordsPerBlock: 22,\n                ecBlocks: [\n                    { numBlocks: 3, dataCodewordsPerBlock: 36 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 37 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 20,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 16 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 17 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 12 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 13 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x0A4D3,\n        versionNumber: 10,\n        alignmentPatternCenters: [6, 28, 50],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 18,\n                ecBlocks: [\n                    { numBlocks: 2, dataCodewordsPerBlock: 68 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 69 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 43 },\n                    { numBlocks: 1, dataCodewordsPerBlock: 44 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [\n                    { numBlocks: 6, dataCodewordsPerBlock: 19 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 20 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 6, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x0BBF6,\n        versionNumber: 11,\n        alignmentPatternCenters: [6, 30, 54],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 20,\n                ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 81 }],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 1, dataCodewordsPerBlock: 50 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 51 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 22 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 23 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [\n                    { numBlocks: 3, dataCodewordsPerBlock: 12 },\n                    { numBlocks: 8, dataCodewordsPerBlock: 13 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x0C762,\n        versionNumber: 12,\n        alignmentPatternCenters: [6, 32, 58],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [\n                    { numBlocks: 2, dataCodewordsPerBlock: 92 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 93 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 22,\n                ecBlocks: [\n                    { numBlocks: 6, dataCodewordsPerBlock: 36 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 37 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 20 },\n                    { numBlocks: 6, dataCodewordsPerBlock: 21 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 7, dataCodewordsPerBlock: 14 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 15 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x0D847,\n        versionNumber: 13,\n        alignmentPatternCenters: [6, 34, 62],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 107 }],\n            },\n            {\n                ecCodewordsPerBlock: 22,\n                ecBlocks: [\n                    { numBlocks: 8, dataCodewordsPerBlock: 37 },\n                    { numBlocks: 1, dataCodewordsPerBlock: 38 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [\n                    { numBlocks: 8, dataCodewordsPerBlock: 20 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 21 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 22,\n                ecBlocks: [\n                    { numBlocks: 12, dataCodewordsPerBlock: 11 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 12 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x0E60D,\n        versionNumber: 14,\n        alignmentPatternCenters: [6, 26, 46, 66],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 3, dataCodewordsPerBlock: 115 },\n                    { numBlocks: 1, dataCodewordsPerBlock: 116 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 40 },\n                    { numBlocks: 5, dataCodewordsPerBlock: 41 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 20,\n                ecBlocks: [\n                    { numBlocks: 11, dataCodewordsPerBlock: 16 },\n                    { numBlocks: 5, dataCodewordsPerBlock: 17 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [\n                    { numBlocks: 11, dataCodewordsPerBlock: 12 },\n                    { numBlocks: 5, dataCodewordsPerBlock: 13 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x0F928,\n        versionNumber: 15,\n        alignmentPatternCenters: [6, 26, 48, 70],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 22,\n                ecBlocks: [\n                    { numBlocks: 5, dataCodewordsPerBlock: 87 },\n                    { numBlocks: 1, dataCodewordsPerBlock: 88 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [\n                    { numBlocks: 5, dataCodewordsPerBlock: 41 },\n                    { numBlocks: 5, dataCodewordsPerBlock: 42 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 5, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 7, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [\n                    { numBlocks: 11, dataCodewordsPerBlock: 12 },\n                    { numBlocks: 7, dataCodewordsPerBlock: 13 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x10B78,\n        versionNumber: 16,\n        alignmentPatternCenters: [6, 26, 50, 74],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [\n                    { numBlocks: 5, dataCodewordsPerBlock: 98 },\n                    { numBlocks: 1, dataCodewordsPerBlock: 99 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 7, dataCodewordsPerBlock: 45 },\n                    { numBlocks: 3, dataCodewordsPerBlock: 46 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [\n                    { numBlocks: 15, dataCodewordsPerBlock: 19 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 20 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 3, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 13, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x1145D,\n        versionNumber: 17,\n        alignmentPatternCenters: [6, 30, 54, 78],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 1, dataCodewordsPerBlock: 107 },\n                    { numBlocks: 5, dataCodewordsPerBlock: 108 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 10, dataCodewordsPerBlock: 46 },\n                    { numBlocks: 1, dataCodewordsPerBlock: 47 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 1, dataCodewordsPerBlock: 22 },\n                    { numBlocks: 15, dataCodewordsPerBlock: 23 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 2, dataCodewordsPerBlock: 14 },\n                    { numBlocks: 17, dataCodewordsPerBlock: 15 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x12A17,\n        versionNumber: 18,\n        alignmentPatternCenters: [6, 30, 56, 82],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 5, dataCodewordsPerBlock: 120 },\n                    { numBlocks: 1, dataCodewordsPerBlock: 121 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [\n                    { numBlocks: 9, dataCodewordsPerBlock: 43 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 44 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 17, dataCodewordsPerBlock: 22 },\n                    { numBlocks: 1, dataCodewordsPerBlock: 23 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 2, dataCodewordsPerBlock: 14 },\n                    { numBlocks: 19, dataCodewordsPerBlock: 15 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x13532,\n        versionNumber: 19,\n        alignmentPatternCenters: [6, 30, 58, 86],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 3, dataCodewordsPerBlock: 113 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 114 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [\n                    { numBlocks: 3, dataCodewordsPerBlock: 44 },\n                    { numBlocks: 11, dataCodewordsPerBlock: 45 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [\n                    { numBlocks: 17, dataCodewordsPerBlock: 21 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 22 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [\n                    { numBlocks: 9, dataCodewordsPerBlock: 13 },\n                    { numBlocks: 16, dataCodewordsPerBlock: 14 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x149A6,\n        versionNumber: 20,\n        alignmentPatternCenters: [6, 34, 62, 90],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 3, dataCodewordsPerBlock: 107 },\n                    { numBlocks: 5, dataCodewordsPerBlock: 108 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [\n                    { numBlocks: 3, dataCodewordsPerBlock: 41 },\n                    { numBlocks: 13, dataCodewordsPerBlock: 42 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 15, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 5, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 15, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 10, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x15683,\n        versionNumber: 21,\n        alignmentPatternCenters: [6, 28, 50, 72, 94],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 116 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 117 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [{ numBlocks: 17, dataCodewordsPerBlock: 42 }],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 17, dataCodewordsPerBlock: 22 },\n                    { numBlocks: 6, dataCodewordsPerBlock: 23 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 19, dataCodewordsPerBlock: 16 },\n                    { numBlocks: 6, dataCodewordsPerBlock: 17 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x168C9,\n        versionNumber: 22,\n        alignmentPatternCenters: [6, 26, 50, 74, 98],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 2, dataCodewordsPerBlock: 111 },\n                    { numBlocks: 7, dataCodewordsPerBlock: 112 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [{ numBlocks: 17, dataCodewordsPerBlock: 46 }],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 7, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 16, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 24,\n                ecBlocks: [{ numBlocks: 34, dataCodewordsPerBlock: 13 }],\n            },\n        ],\n    },\n    {\n        infoBits: 0x177EC,\n        versionNumber: 23,\n        alignmentPatternCenters: [6, 30, 54, 74, 102],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 121 },\n                    { numBlocks: 5, dataCodewordsPerBlock: 122 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 47 },\n                    { numBlocks: 14, dataCodewordsPerBlock: 48 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 11, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 14, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 16, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 14, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x18EC4,\n        versionNumber: 24,\n        alignmentPatternCenters: [6, 28, 54, 80, 106],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 6, dataCodewordsPerBlock: 117 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 118 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 6, dataCodewordsPerBlock: 45 },\n                    { numBlocks: 14, dataCodewordsPerBlock: 46 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 11, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 16, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 30, dataCodewordsPerBlock: 16 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 17 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x191E1,\n        versionNumber: 25,\n        alignmentPatternCenters: [6, 32, 58, 84, 110],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 26,\n                ecBlocks: [\n                    { numBlocks: 8, dataCodewordsPerBlock: 106 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 107 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 8, dataCodewordsPerBlock: 47 },\n                    { numBlocks: 13, dataCodewordsPerBlock: 48 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 7, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 22, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 22, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 13, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x1AFAB,\n        versionNumber: 26,\n        alignmentPatternCenters: [6, 30, 58, 86, 114],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 10, dataCodewordsPerBlock: 114 },\n                    { numBlocks: 2, dataCodewordsPerBlock: 115 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 19, dataCodewordsPerBlock: 46 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 47 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 28, dataCodewordsPerBlock: 22 },\n                    { numBlocks: 6, dataCodewordsPerBlock: 23 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 33, dataCodewordsPerBlock: 16 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 17 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x1B08E,\n        versionNumber: 27,\n        alignmentPatternCenters: [6, 34, 62, 90, 118],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 8, dataCodewordsPerBlock: 122 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 123 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 22, dataCodewordsPerBlock: 45 },\n                    { numBlocks: 3, dataCodewordsPerBlock: 46 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 8, dataCodewordsPerBlock: 23 },\n                    { numBlocks: 26, dataCodewordsPerBlock: 24 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 12, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 28, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x1CC1A,\n        versionNumber: 28,\n        alignmentPatternCenters: [6, 26, 50, 74, 98, 122],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 3, dataCodewordsPerBlock: 117 },\n                    { numBlocks: 10, dataCodewordsPerBlock: 118 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 3, dataCodewordsPerBlock: 45 },\n                    { numBlocks: 23, dataCodewordsPerBlock: 46 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 31, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 11, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 31, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x1D33F,\n        versionNumber: 29,\n        alignmentPatternCenters: [6, 30, 54, 78, 102, 126],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 7, dataCodewordsPerBlock: 116 },\n                    { numBlocks: 7, dataCodewordsPerBlock: 117 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 21, dataCodewordsPerBlock: 45 },\n                    { numBlocks: 7, dataCodewordsPerBlock: 46 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 1, dataCodewordsPerBlock: 23 },\n                    { numBlocks: 37, dataCodewordsPerBlock: 24 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 19, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 26, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x1ED75,\n        versionNumber: 30,\n        alignmentPatternCenters: [6, 26, 52, 78, 104, 130],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 5, dataCodewordsPerBlock: 115 },\n                    { numBlocks: 10, dataCodewordsPerBlock: 116 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 19, dataCodewordsPerBlock: 47 },\n                    { numBlocks: 10, dataCodewordsPerBlock: 48 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 15, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 25, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 23, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 25, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x1F250,\n        versionNumber: 31,\n        alignmentPatternCenters: [6, 30, 56, 82, 108, 134],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 13, dataCodewordsPerBlock: 115 },\n                    { numBlocks: 3, dataCodewordsPerBlock: 116 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 2, dataCodewordsPerBlock: 46 },\n                    { numBlocks: 29, dataCodewordsPerBlock: 47 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 42, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 1, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 23, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 28, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x209D5,\n        versionNumber: 32,\n        alignmentPatternCenters: [6, 34, 60, 86, 112, 138],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [{ numBlocks: 17, dataCodewordsPerBlock: 115 }],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 10, dataCodewordsPerBlock: 46 },\n                    { numBlocks: 23, dataCodewordsPerBlock: 47 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 10, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 35, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 19, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 35, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x216F0,\n        versionNumber: 33,\n        alignmentPatternCenters: [6, 30, 58, 86, 114, 142],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 17, dataCodewordsPerBlock: 115 },\n                    { numBlocks: 1, dataCodewordsPerBlock: 116 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 14, dataCodewordsPerBlock: 46 },\n                    { numBlocks: 21, dataCodewordsPerBlock: 47 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 29, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 19, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 11, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 46, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x228BA,\n        versionNumber: 34,\n        alignmentPatternCenters: [6, 34, 62, 90, 118, 146],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 13, dataCodewordsPerBlock: 115 },\n                    { numBlocks: 6, dataCodewordsPerBlock: 116 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 14, dataCodewordsPerBlock: 46 },\n                    { numBlocks: 23, dataCodewordsPerBlock: 47 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 44, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 7, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 59, dataCodewordsPerBlock: 16 },\n                    { numBlocks: 1, dataCodewordsPerBlock: 17 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x2379F,\n        versionNumber: 35,\n        alignmentPatternCenters: [6, 30, 54, 78, 102, 126, 150],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 12, dataCodewordsPerBlock: 121 },\n                    { numBlocks: 7, dataCodewordsPerBlock: 122 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 12, dataCodewordsPerBlock: 47 },\n                    { numBlocks: 26, dataCodewordsPerBlock: 48 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 39, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 14, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 22, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 41, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x24B0B,\n        versionNumber: 36,\n        alignmentPatternCenters: [6, 24, 50, 76, 102, 128, 154],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 6, dataCodewordsPerBlock: 121 },\n                    { numBlocks: 14, dataCodewordsPerBlock: 122 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 6, dataCodewordsPerBlock: 47 },\n                    { numBlocks: 34, dataCodewordsPerBlock: 48 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 46, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 10, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 2, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 64, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x2542E,\n        versionNumber: 37,\n        alignmentPatternCenters: [6, 28, 54, 80, 106, 132, 158],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 17, dataCodewordsPerBlock: 122 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 123 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 29, dataCodewordsPerBlock: 46 },\n                    { numBlocks: 14, dataCodewordsPerBlock: 47 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 49, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 10, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 24, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 46, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x26A64,\n        versionNumber: 38,\n        alignmentPatternCenters: [6, 32, 58, 84, 110, 136, 162],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 4, dataCodewordsPerBlock: 122 },\n                    { numBlocks: 18, dataCodewordsPerBlock: 123 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 13, dataCodewordsPerBlock: 46 },\n                    { numBlocks: 32, dataCodewordsPerBlock: 47 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 48, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 14, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 42, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 32, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x27541,\n        versionNumber: 39,\n        alignmentPatternCenters: [6, 26, 54, 82, 110, 138, 166],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 20, dataCodewordsPerBlock: 117 },\n                    { numBlocks: 4, dataCodewordsPerBlock: 118 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 40, dataCodewordsPerBlock: 47 },\n                    { numBlocks: 7, dataCodewordsPerBlock: 48 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 43, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 22, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 10, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 67, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n    {\n        infoBits: 0x28C69,\n        versionNumber: 40,\n        alignmentPatternCenters: [6, 30, 58, 86, 114, 142, 170],\n        errorCorrectionLevels: [\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 19, dataCodewordsPerBlock: 118 },\n                    { numBlocks: 6, dataCodewordsPerBlock: 119 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 28,\n                ecBlocks: [\n                    { numBlocks: 18, dataCodewordsPerBlock: 47 },\n                    { numBlocks: 31, dataCodewordsPerBlock: 48 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 34, dataCodewordsPerBlock: 24 },\n                    { numBlocks: 34, dataCodewordsPerBlock: 25 },\n                ],\n            },\n            {\n                ecCodewordsPerBlock: 30,\n                ecBlocks: [\n                    { numBlocks: 20, dataCodewordsPerBlock: 15 },\n                    { numBlocks: 61, dataCodewordsPerBlock: 16 },\n                ],\n            },\n        ],\n    },\n];\n\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar BitMatrix_1 = __webpack_require__(0);\nfunction squareToQuadrilateral(p1, p2, p3, p4) {\n    var dx3 = p1.x - p2.x + p3.x - p4.x;\n    var dy3 = p1.y - p2.y + p3.y - p4.y;\n    if (dx3 === 0 && dy3 === 0) { // Affine\n        return {\n            a11: p2.x - p1.x,\n            a12: p2.y - p1.y,\n            a13: 0,\n            a21: p3.x - p2.x,\n            a22: p3.y - p2.y,\n            a23: 0,\n            a31: p1.x,\n            a32: p1.y,\n            a33: 1,\n        };\n    }\n    else {\n        var dx1 = p2.x - p3.x;\n        var dx2 = p4.x - p3.x;\n        var dy1 = p2.y - p3.y;\n        var dy2 = p4.y - p3.y;\n        var denominator = dx1 * dy2 - dx2 * dy1;\n        var a13 = (dx3 * dy2 - dx2 * dy3) / denominator;\n        var a23 = (dx1 * dy3 - dx3 * dy1) / denominator;\n        return {\n            a11: p2.x - p1.x + a13 * p2.x,\n            a12: p2.y - p1.y + a13 * p2.y,\n            a13: a13,\n            a21: p4.x - p1.x + a23 * p4.x,\n            a22: p4.y - p1.y + a23 * p4.y,\n            a23: a23,\n            a31: p1.x,\n            a32: p1.y,\n            a33: 1,\n        };\n    }\n}\nfunction quadrilateralToSquare(p1, p2, p3, p4) {\n    // Here, the adjoint serves as the inverse:\n    var sToQ = squareToQuadrilateral(p1, p2, p3, p4);\n    return {\n        a11: sToQ.a22 * sToQ.a33 - sToQ.a23 * sToQ.a32,\n        a12: sToQ.a13 * sToQ.a32 - sToQ.a12 * sToQ.a33,\n        a13: sToQ.a12 * sToQ.a23 - sToQ.a13 * sToQ.a22,\n        a21: sToQ.a23 * sToQ.a31 - sToQ.a21 * sToQ.a33,\n        a22: sToQ.a11 * sToQ.a33 - sToQ.a13 * sToQ.a31,\n        a23: sToQ.a13 * sToQ.a21 - sToQ.a11 * sToQ.a23,\n        a31: sToQ.a21 * sToQ.a32 - sToQ.a22 * sToQ.a31,\n        a32: sToQ.a12 * sToQ.a31 - sToQ.a11 * sToQ.a32,\n        a33: sToQ.a11 * sToQ.a22 - sToQ.a12 * sToQ.a21,\n    };\n}\nfunction times(a, b) {\n    return {\n        a11: a.a11 * b.a11 + a.a21 * b.a12 + a.a31 * b.a13,\n        a12: a.a12 * b.a11 + a.a22 * b.a12 + a.a32 * b.a13,\n        a13: a.a13 * b.a11 + a.a23 * b.a12 + a.a33 * b.a13,\n        a21: a.a11 * b.a21 + a.a21 * b.a22 + a.a31 * b.a23,\n        a22: a.a12 * b.a21 + a.a22 * b.a22 + a.a32 * b.a23,\n        a23: a.a13 * b.a21 + a.a23 * b.a22 + a.a33 * b.a23,\n        a31: a.a11 * b.a31 + a.a21 * b.a32 + a.a31 * b.a33,\n        a32: a.a12 * b.a31 + a.a22 * b.a32 + a.a32 * b.a33,\n        a33: a.a13 * b.a31 + a.a23 * b.a32 + a.a33 * b.a33,\n    };\n}\nfunction extract(image, location) {\n    var qToS = quadrilateralToSquare({ x: 3.5, y: 3.5 }, { x: location.dimension - 3.5, y: 3.5 }, { x: location.dimension - 6.5, y: location.dimension - 6.5 }, { x: 3.5, y: location.dimension - 3.5 });\n    var sToQ = squareToQuadrilateral(location.topLeft, location.topRight, location.alignmentPattern, location.bottomLeft);\n    var transform = times(sToQ, qToS);\n    var matrix = BitMatrix_1.BitMatrix.createEmpty(location.dimension, location.dimension);\n    var mappingFunction = function (x, y) {\n        var denominator = transform.a13 * x + transform.a23 * y + transform.a33;\n        return {\n            x: (transform.a11 * x + transform.a21 * y + transform.a31) / denominator,\n            y: (transform.a12 * x + transform.a22 * y + transform.a32) / denominator,\n        };\n    };\n    for (var y = 0; y < location.dimension; y++) {\n        for (var x = 0; x < location.dimension; x++) {\n            var xValue = x + 0.5;\n            var yValue = y + 0.5;\n            var sourcePixel = mappingFunction(xValue, yValue);\n            matrix.set(x, y, image.get(Math.floor(sourcePixel.x), Math.floor(sourcePixel.y)));\n        }\n    }\n    return {\n        matrix: matrix,\n        mappingFunction: mappingFunction,\n    };\n}\nexports.extract = extract;\n\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar MAX_FINDERPATTERNS_TO_SEARCH = 4;\nvar MIN_QUAD_RATIO = 0.5;\nvar MAX_QUAD_RATIO = 1.5;\nvar distance = function (a, b) { return Math.sqrt(Math.pow((b.x - a.x), 2) + Math.pow((b.y - a.y), 2)); };\nfunction sum(values) {\n    return values.reduce(function (a, b) { return a + b; });\n}\n// Takes three finder patterns and organizes them into topLeft, topRight, etc\nfunction reorderFinderPatterns(pattern1, pattern2, pattern3) {\n    var _a, _b, _c, _d;\n    // Find distances between pattern centers\n    var oneTwoDistance = distance(pattern1, pattern2);\n    var twoThreeDistance = distance(pattern2, pattern3);\n    var oneThreeDistance = distance(pattern1, pattern3);\n    var bottomLeft;\n    var topLeft;\n    var topRight;\n    // Assume one closest to other two is B; A and C will just be guesses at first\n    if (twoThreeDistance >= oneTwoDistance && twoThreeDistance >= oneThreeDistance) {\n        _a = [pattern2, pattern1, pattern3], bottomLeft = _a[0], topLeft = _a[1], topRight = _a[2];\n    }\n    else if (oneThreeDistance >= twoThreeDistance && oneThreeDistance >= oneTwoDistance) {\n        _b = [pattern1, pattern2, pattern3], bottomLeft = _b[0], topLeft = _b[1], topRight = _b[2];\n    }\n    else {\n        _c = [pattern1, pattern3, pattern2], bottomLeft = _c[0], topLeft = _c[1], topRight = _c[2];\n    }\n    // Use cross product to figure out whether bottomLeft (A) and topRight (C) are correct or flipped in relation to topLeft (B)\n    // This asks whether BC x BA has a positive z component, which is the arrangement we want. If it's negative, then\n    // we've got it flipped around and should swap topRight and bottomLeft.\n    if (((topRight.x - topLeft.x) * (bottomLeft.y - topLeft.y)) - ((topRight.y - topLeft.y) * (bottomLeft.x - topLeft.x)) < 0) {\n        _d = [topRight, bottomLeft], bottomLeft = _d[0], topRight = _d[1];\n    }\n    return { bottomLeft: bottomLeft, topLeft: topLeft, topRight: topRight };\n}\n// Computes the dimension (number of modules on a side) of the QR Code based on the position of the finder patterns\nfunction computeDimension(topLeft, topRight, bottomLeft, matrix) {\n    var moduleSize = (sum(countBlackWhiteRun(topLeft, bottomLeft, matrix, 5)) / 7 + // Divide by 7 since the ratio is 1:1:3:1:1\n        sum(countBlackWhiteRun(topLeft, topRight, matrix, 5)) / 7 +\n        sum(countBlackWhiteRun(bottomLeft, topLeft, matrix, 5)) / 7 +\n        sum(countBlackWhiteRun(topRight, topLeft, matrix, 5)) / 7) / 4;\n    if (moduleSize < 1) {\n        throw new Error(\"Invalid module size\");\n    }\n    var topDimension = Math.round(distance(topLeft, topRight) / moduleSize);\n    var sideDimension = Math.round(distance(topLeft, bottomLeft) / moduleSize);\n    var dimension = Math.floor((topDimension + sideDimension) / 2) + 7;\n    switch (dimension % 4) {\n        case 0:\n            dimension++;\n            break;\n        case 2:\n            dimension--;\n            break;\n    }\n    return { dimension: dimension, moduleSize: moduleSize };\n}\n// Takes an origin point and an end point and counts the sizes of the black white run from the origin towards the end point.\n// Returns an array of elements, representing the pixel size of the black white run.\n// Uses a variant of http://en.wikipedia.org/wiki/Bresenham's_line_algorithm\nfunction countBlackWhiteRunTowardsPoint(origin, end, matrix, length) {\n    var switchPoints = [{ x: Math.floor(origin.x), y: Math.floor(origin.y) }];\n    var steep = Math.abs(end.y - origin.y) > Math.abs(end.x - origin.x);\n    var fromX;\n    var fromY;\n    var toX;\n    var toY;\n    if (steep) {\n        fromX = Math.floor(origin.y);\n        fromY = Math.floor(origin.x);\n        toX = Math.floor(end.y);\n        toY = Math.floor(end.x);\n    }\n    else {\n        fromX = Math.floor(origin.x);\n        fromY = Math.floor(origin.y);\n        toX = Math.floor(end.x);\n        toY = Math.floor(end.y);\n    }\n    var dx = Math.abs(toX - fromX);\n    var dy = Math.abs(toY - fromY);\n    var error = Math.floor(-dx / 2);\n    var xStep = fromX < toX ? 1 : -1;\n    var yStep = fromY < toY ? 1 : -1;\n    var currentPixel = true;\n    // Loop up until x == toX, but not beyond\n    for (var x = fromX, y = fromY; x !== toX + xStep; x += xStep) {\n        // Does current pixel mean we have moved white to black or vice versa?\n        // Scanning black in state 0,2 and white in state 1, so if we find the wrong\n        // color, advance to next state or end if we are in state 2 already\n        var realX = steep ? y : x;\n        var realY = steep ? x : y;\n        if (matrix.get(realX, realY) !== currentPixel) {\n            currentPixel = !currentPixel;\n            switchPoints.push({ x: realX, y: realY });\n            if (switchPoints.length === length + 1) {\n                break;\n            }\n        }\n        error += dy;\n        if (error > 0) {\n            if (y === toY) {\n                break;\n            }\n            y += yStep;\n            error -= dx;\n        }\n    }\n    var distances = [];\n    for (var i = 0; i < length; i++) {\n        if (switchPoints[i] && switchPoints[i + 1]) {\n            distances.push(distance(switchPoints[i], switchPoints[i + 1]));\n        }\n        else {\n            distances.push(0);\n        }\n    }\n    return distances;\n}\n// Takes an origin point and an end point and counts the sizes of the black white run in the origin point\n// along the line that intersects with the end point. Returns an array of elements, representing the pixel sizes\n// of the black white run. Takes a length which represents the number of switches from black to white to look for.\nfunction countBlackWhiteRun(origin, end, matrix, length) {\n    var _a;\n    var rise = end.y - origin.y;\n    var run = end.x - origin.x;\n    var towardsEnd = countBlackWhiteRunTowardsPoint(origin, end, matrix, Math.ceil(length / 2));\n    var awayFromEnd = countBlackWhiteRunTowardsPoint(origin, { x: origin.x - run, y: origin.y - rise }, matrix, Math.ceil(length / 2));\n    var middleValue = towardsEnd.shift() + awayFromEnd.shift() - 1; // Substract one so we don't double count a pixel\n    return (_a = awayFromEnd.concat(middleValue)).concat.apply(_a, towardsEnd);\n}\n// Takes in a black white run and an array of expected ratios. Returns the average size of the run as well as the \"error\" -\n// that is the amount the run diverges from the expected ratio\nfunction scoreBlackWhiteRun(sequence, ratios) {\n    var averageSize = sum(sequence) / sum(ratios);\n    var error = 0;\n    ratios.forEach(function (ratio, i) {\n        error += Math.pow((sequence[i] - ratio * averageSize), 2);\n    });\n    return { averageSize: averageSize, error: error };\n}\n// Takes an X,Y point and an array of sizes and scores the point against those ratios.\n// For example for a finder pattern takes the ratio list of 1:1:3:1:1 and checks horizontal, vertical and diagonal ratios\n// against that.\nfunction scorePattern(point, ratios, matrix) {\n    try {\n        var horizontalRun = countBlackWhiteRun(point, { x: -1, y: point.y }, matrix, ratios.length);\n        var verticalRun = countBlackWhiteRun(point, { x: point.x, y: -1 }, matrix, ratios.length);\n        var topLeftPoint = {\n            x: Math.max(0, point.x - point.y) - 1,\n            y: Math.max(0, point.y - point.x) - 1,\n        };\n        var topLeftBottomRightRun = countBlackWhiteRun(point, topLeftPoint, matrix, ratios.length);\n        var bottomLeftPoint = {\n            x: Math.min(matrix.width, point.x + point.y) + 1,\n            y: Math.min(matrix.height, point.y + point.x) + 1,\n        };\n        var bottomLeftTopRightRun = countBlackWhiteRun(point, bottomLeftPoint, matrix, ratios.length);\n        var horzError = scoreBlackWhiteRun(horizontalRun, ratios);\n        var vertError = scoreBlackWhiteRun(verticalRun, ratios);\n        var diagDownError = scoreBlackWhiteRun(topLeftBottomRightRun, ratios);\n        var diagUpError = scoreBlackWhiteRun(bottomLeftTopRightRun, ratios);\n        var ratioError = Math.sqrt(horzError.error * horzError.error +\n            vertError.error * vertError.error +\n            diagDownError.error * diagDownError.error +\n            diagUpError.error * diagUpError.error);\n        var avgSize = (horzError.averageSize + vertError.averageSize + diagDownError.averageSize + diagUpError.averageSize) / 4;\n        var sizeError = (Math.pow((horzError.averageSize - avgSize), 2) +\n            Math.pow((vertError.averageSize - avgSize), 2) +\n            Math.pow((diagDownError.averageSize - avgSize), 2) +\n            Math.pow((diagUpError.averageSize - avgSize), 2)) / avgSize;\n        return ratioError + sizeError;\n    }\n    catch (_a) {\n        return Infinity;\n    }\n}\nfunction recenterLocation(matrix, p) {\n    var leftX = Math.round(p.x);\n    while (matrix.get(leftX, Math.round(p.y))) {\n        leftX--;\n    }\n    var rightX = Math.round(p.x);\n    while (matrix.get(rightX, Math.round(p.y))) {\n        rightX++;\n    }\n    var x = (leftX + rightX) / 2;\n    var topY = Math.round(p.y);\n    while (matrix.get(Math.round(x), topY)) {\n        topY--;\n    }\n    var bottomY = Math.round(p.y);\n    while (matrix.get(Math.round(x), bottomY)) {\n        bottomY++;\n    }\n    var y = (topY + bottomY) / 2;\n    return { x: x, y: y };\n}\nfunction locate(matrix) {\n    var finderPatternQuads = [];\n    var activeFinderPatternQuads = [];\n    var alignmentPatternQuads = [];\n    var activeAlignmentPatternQuads = [];\n    var _loop_1 = function (y) {\n        var length_1 = 0;\n        var lastBit = false;\n        var scans = [0, 0, 0, 0, 0];\n        var _loop_2 = function (x) {\n            var v = matrix.get(x, y);\n            if (v === lastBit) {\n                length_1++;\n            }\n            else {\n                scans = [scans[1], scans[2], scans[3], scans[4], length_1];\n                length_1 = 1;\n                lastBit = v;\n                // Do the last 5 color changes ~ match the expected ratio for a finder pattern? 1:1:3:1:1 of b:w:b:w:b\n                var averageFinderPatternBlocksize = sum(scans) / 7;\n                var validFinderPattern = Math.abs(scans[0] - averageFinderPatternBlocksize) < averageFinderPatternBlocksize &&\n                    Math.abs(scans[1] - averageFinderPatternBlocksize) < averageFinderPatternBlocksize &&\n                    Math.abs(scans[2] - 3 * averageFinderPatternBlocksize) < 3 * averageFinderPatternBlocksize &&\n                    Math.abs(scans[3] - averageFinderPatternBlocksize) < averageFinderPatternBlocksize &&\n                    Math.abs(scans[4] - averageFinderPatternBlocksize) < averageFinderPatternBlocksize &&\n                    !v; // And make sure the current pixel is white since finder patterns are bordered in white\n                // Do the last 3 color changes ~ match the expected ratio for an alignment pattern? 1:1:1 of w:b:w\n                var averageAlignmentPatternBlocksize = sum(scans.slice(-3)) / 3;\n                var validAlignmentPattern = Math.abs(scans[2] - averageAlignmentPatternBlocksize) < averageAlignmentPatternBlocksize &&\n                    Math.abs(scans[3] - averageAlignmentPatternBlocksize) < averageAlignmentPatternBlocksize &&\n                    Math.abs(scans[4] - averageAlignmentPatternBlocksize) < averageAlignmentPatternBlocksize &&\n                    v; // Is the current pixel black since alignment patterns are bordered in black\n                if (validFinderPattern) {\n                    // Compute the start and end x values of the large center black square\n                    var endX_1 = x - scans[3] - scans[4];\n                    var startX_1 = endX_1 - scans[2];\n                    var line = { startX: startX_1, endX: endX_1, y: y };\n                    // Is there a quad directly above the current spot? If so, extend it with the new line. Otherwise, create a new quad with\n                    // that line as the starting point.\n                    var matchingQuads = activeFinderPatternQuads.filter(function (q) {\n                        return (startX_1 >= q.bottom.startX && startX_1 <= q.bottom.endX) ||\n                            (endX_1 >= q.bottom.startX && startX_1 <= q.bottom.endX) ||\n                            (startX_1 <= q.bottom.startX && endX_1 >= q.bottom.endX && ((scans[2] / (q.bottom.endX - q.bottom.startX)) < MAX_QUAD_RATIO &&\n                                (scans[2] / (q.bottom.endX - q.bottom.startX)) > MIN_QUAD_RATIO));\n                    });\n                    if (matchingQuads.length > 0) {\n                        matchingQuads[0].bottom = line;\n                    }\n                    else {\n                        activeFinderPatternQuads.push({ top: line, bottom: line });\n                    }\n                }\n                if (validAlignmentPattern) {\n                    // Compute the start and end x values of the center black square\n                    var endX_2 = x - scans[4];\n                    var startX_2 = endX_2 - scans[3];\n                    var line = { startX: startX_2, y: y, endX: endX_2 };\n                    // Is there a quad directly above the current spot? If so, extend it with the new line. Otherwise, create a new quad with\n                    // that line as the starting point.\n                    var matchingQuads = activeAlignmentPatternQuads.filter(function (q) {\n                        return (startX_2 >= q.bottom.startX && startX_2 <= q.bottom.endX) ||\n                            (endX_2 >= q.bottom.startX && startX_2 <= q.bottom.endX) ||\n                            (startX_2 <= q.bottom.startX && endX_2 >= q.bottom.endX && ((scans[2] / (q.bottom.endX - q.bottom.startX)) < MAX_QUAD_RATIO &&\n                                (scans[2] / (q.bottom.endX - q.bottom.startX)) > MIN_QUAD_RATIO));\n                    });\n                    if (matchingQuads.length > 0) {\n                        matchingQuads[0].bottom = line;\n                    }\n                    else {\n                        activeAlignmentPatternQuads.push({ top: line, bottom: line });\n                    }\n                }\n            }\n        };\n        for (var x = -1; x <= matrix.width; x++) {\n            _loop_2(x);\n        }\n        finderPatternQuads.push.apply(finderPatternQuads, activeFinderPatternQuads.filter(function (q) { return q.bottom.y !== y && q.bottom.y - q.top.y >= 2; }));\n        activeFinderPatternQuads = activeFinderPatternQuads.filter(function (q) { return q.bottom.y === y; });\n        alignmentPatternQuads.push.apply(alignmentPatternQuads, activeAlignmentPatternQuads.filter(function (q) { return q.bottom.y !== y; }));\n        activeAlignmentPatternQuads = activeAlignmentPatternQuads.filter(function (q) { return q.bottom.y === y; });\n    };\n    for (var y = 0; y <= matrix.height; y++) {\n        _loop_1(y);\n    }\n    finderPatternQuads.push.apply(finderPatternQuads, activeFinderPatternQuads.filter(function (q) { return q.bottom.y - q.top.y >= 2; }));\n    alignmentPatternQuads.push.apply(alignmentPatternQuads, activeAlignmentPatternQuads);\n    var finderPatternGroups = finderPatternQuads\n        .filter(function (q) { return q.bottom.y - q.top.y >= 2; }) // All quads must be at least 2px tall since the center square is larger than a block\n        .map(function (q) {\n        var x = (q.top.startX + q.top.endX + q.bottom.startX + q.bottom.endX) / 4;\n        var y = (q.top.y + q.bottom.y + 1) / 2;\n        if (!matrix.get(Math.round(x), Math.round(y))) {\n            return;\n        }\n        var lengths = [q.top.endX - q.top.startX, q.bottom.endX - q.bottom.startX, q.bottom.y - q.top.y + 1];\n        var size = sum(lengths) / lengths.length;\n        var score = scorePattern({ x: Math.round(x), y: Math.round(y) }, [1, 1, 3, 1, 1], matrix);\n        return { score: score, x: x, y: y, size: size };\n    })\n        .filter(function (q) { return !!q; }) // Filter out any rejected quads from above\n        .sort(function (a, b) { return a.score - b.score; })\n        // Now take the top finder pattern options and try to find 2 other options with a similar size.\n        .map(function (point, i, finderPatterns) {\n        if (i > MAX_FINDERPATTERNS_TO_SEARCH) {\n            return null;\n        }\n        var otherPoints = finderPatterns\n            .filter(function (p, ii) { return i !== ii; })\n            .map(function (p) { return ({ x: p.x, y: p.y, score: p.score + (Math.pow((p.size - point.size), 2)) / point.size, size: p.size }); })\n            .sort(function (a, b) { return a.score - b.score; });\n        if (otherPoints.length < 2) {\n            return null;\n        }\n        var score = point.score + otherPoints[0].score + otherPoints[1].score;\n        return { points: [point].concat(otherPoints.slice(0, 2)), score: score };\n    })\n        .filter(function (q) { return !!q; }) // Filter out any rejected finder patterns from above\n        .sort(function (a, b) { return a.score - b.score; });\n    if (finderPatternGroups.length === 0) {\n        return null;\n    }\n    var _a = reorderFinderPatterns(finderPatternGroups[0].points[0], finderPatternGroups[0].points[1], finderPatternGroups[0].points[2]), topRight = _a.topRight, topLeft = _a.topLeft, bottomLeft = _a.bottomLeft;\n    var alignment = findAlignmentPattern(matrix, alignmentPatternQuads, topRight, topLeft, bottomLeft);\n    var result = [];\n    if (alignment) {\n        result.push({\n            alignmentPattern: { x: alignment.alignmentPattern.x, y: alignment.alignmentPattern.y },\n            bottomLeft: { x: bottomLeft.x, y: bottomLeft.y },\n            dimension: alignment.dimension,\n            topLeft: { x: topLeft.x, y: topLeft.y },\n            topRight: { x: topRight.x, y: topRight.y },\n        });\n    }\n    // We normally use the center of the quads as the location of the tracking points, which is optimal for most cases and will account\n    // for a skew in the image. However, In some cases, a slight skew might not be real and instead be caused by image compression\n    // errors and/or low resolution. For those cases, we'd be better off centering the point exactly in the middle of the black area. We\n    // compute and return the location data for the naively centered points as it is little additional work and allows for multiple\n    // attempts at decoding harder images.\n    var midTopRight = recenterLocation(matrix, topRight);\n    var midTopLeft = recenterLocation(matrix, topLeft);\n    var midBottomLeft = recenterLocation(matrix, bottomLeft);\n    var centeredAlignment = findAlignmentPattern(matrix, alignmentPatternQuads, midTopRight, midTopLeft, midBottomLeft);\n    if (centeredAlignment) {\n        result.push({\n            alignmentPattern: { x: centeredAlignment.alignmentPattern.x, y: centeredAlignment.alignmentPattern.y },\n            bottomLeft: { x: midBottomLeft.x, y: midBottomLeft.y },\n            topLeft: { x: midTopLeft.x, y: midTopLeft.y },\n            topRight: { x: midTopRight.x, y: midTopRight.y },\n            dimension: centeredAlignment.dimension,\n        });\n    }\n    if (result.length === 0) {\n        return null;\n    }\n    return result;\n}\nexports.locate = locate;\nfunction findAlignmentPattern(matrix, alignmentPatternQuads, topRight, topLeft, bottomLeft) {\n    var _a;\n    // Now that we've found the three finder patterns we can determine the blockSize and the size of the QR code.\n    // We'll use these to help find the alignment pattern but also later when we do the extraction.\n    var dimension;\n    var moduleSize;\n    try {\n        (_a = computeDimension(topLeft, topRight, bottomLeft, matrix), dimension = _a.dimension, moduleSize = _a.moduleSize);\n    }\n    catch (e) {\n        return null;\n    }\n    // Now find the alignment pattern\n    var bottomRightFinderPattern = {\n        x: topRight.x - topLeft.x + bottomLeft.x,\n        y: topRight.y - topLeft.y + bottomLeft.y,\n    };\n    var modulesBetweenFinderPatterns = ((distance(topLeft, bottomLeft) + distance(topLeft, topRight)) / 2 / moduleSize);\n    var correctionToTopLeft = 1 - (3 / modulesBetweenFinderPatterns);\n    var expectedAlignmentPattern = {\n        x: topLeft.x + correctionToTopLeft * (bottomRightFinderPattern.x - topLeft.x),\n        y: topLeft.y + correctionToTopLeft * (bottomRightFinderPattern.y - topLeft.y),\n    };\n    var alignmentPatterns = alignmentPatternQuads\n        .map(function (q) {\n        var x = (q.top.startX + q.top.endX + q.bottom.startX + q.bottom.endX) / 4;\n        var y = (q.top.y + q.bottom.y + 1) / 2;\n        if (!matrix.get(Math.floor(x), Math.floor(y))) {\n            return;\n        }\n        var lengths = [q.top.endX - q.top.startX, q.bottom.endX - q.bottom.startX, (q.bottom.y - q.top.y + 1)];\n        var size = sum(lengths) / lengths.length;\n        var sizeScore = scorePattern({ x: Math.floor(x), y: Math.floor(y) }, [1, 1, 1], matrix);\n        var score = sizeScore + distance({ x: x, y: y }, expectedAlignmentPattern);\n        return { x: x, y: y, score: score };\n    })\n        .filter(function (v) { return !!v; })\n        .sort(function (a, b) { return a.score - b.score; });\n    // If there are less than 15 modules between finder patterns it's a version 1 QR code and as such has no alignmemnt pattern\n    // so we can only use our best guess.\n    var alignmentPattern = modulesBetweenFinderPatterns >= 15 && alignmentPatterns.length ? alignmentPatterns[0] : expectedAlignmentPattern;\n    return { alignmentPattern: alignmentPattern, dimension: dimension };\n}\n\n\n/***/ })\n/******/ ])[\"default\"];\n});"], "names": [], "mappings": "AAAA,CAAC,SAAS,iCAAiC,IAAI,EAAE,OAAO;IACvD,wCACC,OAAO,OAAO,GAAG;SACb;;IAKoB;AAC1B,CAAC,EAAE,OAAO,SAAS,cAAc,OAAO,IAAI,EAAE;IAC9C,OAAO,MAAM,GAAG,AAAC,SAAS,OAAO;QACjC,MAAM,GAAI,mBAAmB;QAC7B,MAAM,GAAI,IAAI,mBAAmB,CAAC;QAClC,MAAM,GACN,MAAM,GAAI,uBAAuB;QACjC,MAAM,GAAI,SAAS,oBAAoB,QAAQ;YAC/C,MAAM,GACN,MAAM,GAAK,8BAA8B;YACzC,MAAM,GAAK,IAAG,gBAAgB,CAAC,SAAS,EAAE;gBAC1C,MAAM,GAAM,OAAO,gBAAgB,CAAC,SAAS,CAAC,OAAO;YACrD,MAAM,GAAK;YACX,MAAM,GAAK,kDAAkD;YAC7D,MAAM,GAAK,IAAI,UAAS,gBAAgB,CAAC,SAAS,GAAG;gBACrD,MAAM,GAAM,GAAG;gBACf,MAAM,GAAM,GAAG;gBACf,MAAM,GAAM,SAAS,CAAC;YACX;YACX,MAAM,GACN,MAAM,GAAK,8BAA8B;YACzC,MAAM,GAAK,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAO,OAAO,EAAE,SAAQ,QAAO,OAAO,EAAE;YAC1E,MAAM,GACN,MAAM,GAAK,4BAA4B;YACvC,MAAM,GAAK,QAAO,CAAC,GAAG;YACtB,MAAM,GACN,MAAM,GAAK,mCAAmC;YAC9C,MAAM,GAAK,OAAO,QAAO,OAAO;QAChC,MAAM,GAAI;QACV,MAAM,GACN,MAAM,GACN,MAAM,GAAI,kDAAkD;QAC5D,MAAM,GAAI,oBAAoB,CAAC,GAAG;QAClC,MAAM,GACN,MAAM,GAAI,0BAA0B;QACpC,MAAM,GAAI,oBAAoB,CAAC,GAAG;QAClC,MAAM,GACN,MAAM,GAAI,6CAA6C;QACvD,MAAM,GAAI,oBAAoB,CAAC,GAAG,SAAS,OAAO,EAAE,IAAI,EAAE,MAAM;YAChE,MAAM,GAAK,IAAG,CAAC,oBAAoB,CAAC,CAAC,SAAS,OAAO;gBACrD,MAAM,GAAM,OAAO,cAAc,CAAC,SAAS,MAAM;oBACjD,MAAM,GAAO,cAAc;oBAC3B,MAAM,GAAO,YAAY;oBACzB,MAAM,GAAO,KAAK;gBACN;YACZ,MAAM,GAAK;QACX,MAAM,GAAI;QACV,MAAM,GACN,MAAM,GAAI,uEAAuE;QACjF,MAAM,GAAI,oBAAoB,CAAC,GAAG,SAAS,OAAM;YACjD,MAAM,GAAK,IAAI,SAAS,WAAU,QAAO,UAAU,GACnD,MAAM,GAAM,SAAS;gBAAe,OAAO,OAAM,CAAC,UAAU;YAAE,IAC9D,MAAM,GAAM,SAAS;gBAAqB,OAAO;YAAQ;YACzD,MAAM,GAAK,oBAAoB,CAAC,CAAC,QAAQ,KAAK;YAC9C,MAAM,GAAK,OAAO;QAClB,MAAM,GAAI;QACV,MAAM,GACN,MAAM,GAAI,uCAAuC;QACjD,MAAM,GAAI,oBAAoB,CAAC,GAAG,SAAS,MAAM,EAAE,QAAQ;YAAI,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ;QAAW;QAC9H,MAAM,GACN,MAAM,GAAI,0BAA0B;QACpC,MAAM,GAAI,oBAAoB,CAAC,GAAG;QAClC,MAAM,GACN,MAAM,GAAI,uCAAuC;QACjD,MAAM,GAAI,OAAO,oBAAoB,oBAAoB,CAAC,GAAG;IAC7D,MAAM,GAAG,EAEC;QAEH,SAAS,OAAM,EAAE,OAAO,EAAE,mBAAmB;YAEpD;YAEA,OAAO,cAAc,CAAC,SAAS,cAAc;gBAAE,OAAO;YAAK;YAC3D,IAAI,YAA2B;gBAC3B,SAAS,UAAU,IAAI,EAAE,KAAK;oBAC1B,IAAI,CAAC,KAAK,GAAG;oBACb,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,GAAG;oBAC5B,IAAI,CAAC,IAAI,GAAG;gBAChB;gBACA,UAAU,WAAW,GAAG,SAAU,KAAK,EAAE,MAAM;oBAC3C,OAAO,IAAI,UAAU,IAAI,kBAAkB,QAAQ,SAAS;gBAChE;gBACA,UAAU,SAAS,CAAC,GAAG,GAAG,SAAU,CAAC,EAAE,CAAC;oBACpC,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE;wBACvD,OAAO;oBACX;oBACA,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE;gBAC1C;gBACA,UAAU,SAAS,CAAC,GAAG,GAAG,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;oBACvC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,GAAG,IAAI,IAAI;gBAC5C;gBACA,UAAU,SAAS,CAAC,SAAS,GAAG,SAAU,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;oBACjE,IAAK,IAAI,IAAI,KAAK,IAAI,MAAM,QAAQ,IAAK;wBACrC,IAAK,IAAI,IAAI,MAAM,IAAI,OAAO,OAAO,IAAK;4BACtC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;wBACrB;oBACJ;gBACJ;gBACA,OAAO;YACX;YACA,QAAQ,SAAS,GAAG;QAGpB,GAAG,GAAG;QAEC,SAAS,OAAM,EAAE,OAAO,EAAE,mBAAmB;YAEpD;YAEA,OAAO,cAAc,CAAC,SAAS,cAAc;gBAAE,OAAO;YAAK;YAC3D,IAAI,kBAAkB,oBAAoB;YAC1C,SAAS,gBAAgB,CAAC,EAAE,CAAC;gBACzB,OAAO,IAAI,GAAG,iCAAiC;YACnD;YACA,QAAQ,eAAe,GAAG;YAC1B,IAAI,YAA2B;gBAC3B,SAAS,UAAU,SAAS,EAAE,IAAI,EAAE,OAAO;oBACvC,IAAI,CAAC,SAAS,GAAG;oBACjB,IAAI,CAAC,IAAI,GAAG;oBACZ,IAAI,CAAC,aAAa,GAAG;oBACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,IAAI,CAAC,IAAI;oBACnC,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,IAAI,CAAC,IAAI;oBACnC,IAAI,IAAI;oBACR,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAK;wBAChC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG;wBACnB,IAAI,IAAI;wBACR,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;4BAChB,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,IAAK,IAAI,CAAC,IAAI,GAAG,GAAI,iCAAiC;wBACjF;oBACJ;oBACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,IAAK;wBACpC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG;oBACtC;oBACA,IAAI,CAAC,IAAI,GAAG,IAAI,gBAAgB,OAAO,CAAC,IAAI,EAAE,kBAAkB,IAAI,CAAC;wBAAC;qBAAE;oBACxE,IAAI,CAAC,GAAG,GAAG,IAAI,gBAAgB,OAAO,CAAC,IAAI,EAAE,kBAAkB,IAAI,CAAC;wBAAC;qBAAE;gBAC3E;gBACA,UAAU,SAAS,CAAC,QAAQ,GAAG,SAAU,CAAC,EAAE,CAAC;oBACzC,IAAI,MAAM,KAAK,MAAM,GAAG;wBACpB,OAAO;oBACX;oBACA,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;gBACjF;gBACA,UAAU,SAAS,CAAC,OAAO,GAAG,SAAU,CAAC;oBACrC,IAAI,MAAM,GAAG;wBACT,MAAM,IAAI,MAAM;oBACpB;oBACA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE;gBAC1D;gBACA,UAAU,SAAS,CAAC,aAAa,GAAG,SAAU,MAAM,EAAE,WAAW;oBAC7D,IAAI,SAAS,GAAG;wBACZ,MAAM,IAAI,MAAM;oBACpB;oBACA,IAAI,gBAAgB,GAAG;wBACnB,OAAO,IAAI,CAAC,IAAI;oBACpB;oBACA,IAAI,eAAe,IAAI,kBAAkB,SAAS;oBAClD,YAAY,CAAC,EAAE,GAAG;oBAClB,OAAO,IAAI,gBAAgB,OAAO,CAAC,IAAI,EAAE;gBAC7C;gBACA,UAAU,SAAS,CAAC,GAAG,GAAG,SAAU,CAAC;oBACjC,IAAI,MAAM,GAAG;wBACT,MAAM,IAAI,MAAM;oBACpB;oBACA,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC3B;gBACA,UAAU,SAAS,CAAC,GAAG,GAAG,SAAU,CAAC;oBACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAC3B;gBACA,OAAO;YACX;YACA,QAAQ,OAAO,GAAG;QAGlB,GAAG,GAAG;QAEC,SAAS,OAAM,EAAE,OAAO,EAAE,mBAAmB;YAEpD;YAEA,OAAO,cAAc,CAAC,SAAS,cAAc;gBAAE,OAAO;YAAK;YAC3D,IAAI,cAAc,oBAAoB;YACtC,IAAI,gBAA+B;gBAC/B,SAAS,cAAc,KAAK,EAAE,YAAY;oBACtC,IAAI,aAAa,MAAM,KAAK,GAAG;wBAC3B,MAAM,IAAI,MAAM;oBACpB;oBACA,IAAI,CAAC,KAAK,GAAG;oBACb,IAAI,qBAAqB,aAAa,MAAM;oBAC5C,IAAI,qBAAqB,KAAK,YAAY,CAAC,EAAE,KAAK,GAAG;wBACjD,gFAAgF;wBAChF,IAAI,eAAe;wBACnB,MAAO,eAAe,sBAAsB,YAAY,CAAC,aAAa,KAAK,EAAG;4BAC1E;wBACJ;wBACA,IAAI,iBAAiB,oBAAoB;4BACrC,IAAI,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY;wBAC/C,OACK;4BACD,IAAI,CAAC,YAAY,GAAG,IAAI,kBAAkB,qBAAqB;4BAC/D,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAK;gCAC/C,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,YAAY,CAAC,eAAe,EAAE;4BACzD;wBACJ;oBACJ,OACK;wBACD,IAAI,CAAC,YAAY,GAAG;oBACxB;gBACJ;gBACA,cAAc,SAAS,CAAC,MAAM,GAAG;oBAC7B,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;gBACtC;gBACA,cAAc,SAAS,CAAC,MAAM,GAAG;oBAC7B,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,KAAK;gBACpC;gBACA,cAAc,SAAS,CAAC,cAAc,GAAG,SAAU,MAAM;oBACrD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,OAAO;gBACnE;gBACA,cAAc,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK;oBACnD,IAAI;oBACJ,IAAI,IAAI,CAAC,MAAM,IAAI;wBACf,OAAO;oBACX;oBACA,IAAI,MAAM,MAAM,IAAI;wBAChB,OAAO,IAAI;oBACf;oBACA,IAAI,sBAAsB,IAAI,CAAC,YAAY;oBAC3C,IAAI,qBAAqB,MAAM,YAAY;oBAC3C,IAAI,oBAAoB,MAAM,GAAG,mBAAmB,MAAM,EAAE;wBACxD,KAAK;4BAAC;4BAAoB;yBAAoB,EAAE,sBAAsB,EAAE,CAAC,EAAE,EAAE,qBAAqB,EAAE,CAAC,EAAE;oBAC3G;oBACA,IAAI,UAAU,IAAI,kBAAkB,mBAAmB,MAAM;oBAC7D,IAAI,aAAa,mBAAmB,MAAM,GAAG,oBAAoB,MAAM;oBACvE,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;wBACjC,OAAO,CAAC,EAAE,GAAG,kBAAkB,CAAC,EAAE;oBACtC;oBACA,IAAK,IAAI,IAAI,YAAY,IAAI,mBAAmB,MAAM,EAAE,IAAK;wBACzD,OAAO,CAAC,EAAE,GAAG,YAAY,eAAe,CAAC,mBAAmB,CAAC,IAAI,WAAW,EAAE,kBAAkB,CAAC,EAAE;oBACvG;oBACA,OAAO,IAAI,cAAc,IAAI,CAAC,KAAK,EAAE;gBACzC;gBACA,cAAc,SAAS,CAAC,QAAQ,GAAG,SAAU,MAAM;oBAC/C,IAAI,WAAW,GAAG;wBACd,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;oBAC1B;oBACA,IAAI,WAAW,GAAG;wBACd,OAAO,IAAI;oBACf;oBACA,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;oBACnC,IAAI,UAAU,IAAI,kBAAkB;oBACpC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;wBAC3B,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE;oBAC3D;oBACA,OAAO,IAAI,cAAc,IAAI,CAAC,KAAK,EAAE;gBACzC;gBACA,cAAc,SAAS,CAAC,YAAY,GAAG,SAAU,KAAK;oBAClD,IAAI,IAAI,CAAC,MAAM,MAAM,MAAM,MAAM,IAAI;wBACjC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;oBAC1B;oBACA,IAAI,gBAAgB,IAAI,CAAC,YAAY;oBACrC,IAAI,UAAU,cAAc,MAAM;oBAClC,IAAI,gBAAgB,MAAM,YAAY;oBACtC,IAAI,UAAU,cAAc,MAAM;oBAClC,IAAI,UAAU,IAAI,kBAAkB,UAAU,UAAU;oBACxD,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;wBAC9B,IAAI,SAAS,aAAa,CAAC,EAAE;wBAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;4BAC9B,OAAO,CAAC,IAAI,EAAE,GAAG,YAAY,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,aAAa,CAAC,EAAE;wBAC7G;oBACJ;oBACA,OAAO,IAAI,cAAc,IAAI,CAAC,KAAK,EAAE;gBACzC;gBACA,cAAc,SAAS,CAAC,kBAAkB,GAAG,SAAU,MAAM,EAAE,WAAW;oBACtE,IAAI,SAAS,GAAG;wBACZ,MAAM,IAAI,MAAM;oBACpB;oBACA,IAAI,gBAAgB,GAAG;wBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;oBAC1B;oBACA,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;oBACnC,IAAI,UAAU,IAAI,kBAAkB,OAAO;oBAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;wBAC3B,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE;oBAC3D;oBACA,OAAO,IAAI,cAAc,IAAI,CAAC,KAAK,EAAE;gBACzC;gBACA,cAAc,SAAS,CAAC,UAAU,GAAG,SAAU,CAAC;oBAC5C,IAAI,SAAS;oBACb,IAAI,MAAM,GAAG;wBACT,kCAAkC;wBAClC,OAAO,IAAI,CAAC,cAAc,CAAC;oBAC/B;oBACA,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;oBACnC,IAAI,MAAM,GAAG;wBACT,mCAAmC;wBACnC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAU,WAAW;4BAC3C,SAAS,YAAY,eAAe,CAAC,QAAQ;wBACjD;wBACA,OAAO;oBACX;oBACA,SAAS,IAAI,CAAC,YAAY,CAAC,EAAE;oBAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;wBAC3B,SAAS,YAAY,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,SAAS,IAAI,CAAC,YAAY,CAAC,EAAE;oBAC7F;oBACA,OAAO;gBACX;gBACA,OAAO;YACX;YACA,QAAQ,OAAO,GAAG;QAGlB,GAAG,GAAG;QAEC,SAAS,OAAM,EAAE,OAAO,EAAE,mBAAmB;YAEpD;YAEA,OAAO,cAAc,CAAC,SAAS,cAAc;gBAAE,OAAO;YAAK;YAC3D,IAAI,cAAc,oBAAoB;YACtC,IAAI,YAAY,oBAAoB;YACpC,IAAI,cAAc,oBAAoB;YACtC,IAAI,YAAY,oBAAoB;YACpC,SAAS,KAAK,MAAM;gBAChB,IAAI,YAAY,UAAU,MAAM,CAAC;gBACjC,IAAI,CAAC,WAAW;oBACZ,OAAO;gBACX;gBACA,IAAK,IAAI,KAAK,GAAG,cAAc,WAAW,KAAK,YAAY,MAAM,EAAE,KAAM;oBACrE,IAAI,aAAa,WAAW,CAAC,GAAG;oBAChC,IAAI,YAAY,YAAY,OAAO,CAAC,QAAQ;oBAC5C,IAAI,UAAU,UAAU,MAAM,CAAC,UAAU,MAAM;oBAC/C,IAAI,SAAS;wBACT,OAAO;4BACH,YAAY,QAAQ,KAAK;4BACzB,MAAM,QAAQ,IAAI;4BAClB,QAAQ,QAAQ,MAAM;4BACtB,SAAS,QAAQ,OAAO;4BACxB,UAAU;gCACN,gBAAgB,UAAU,eAAe,CAAC,WAAW,SAAS,EAAE;gCAChE,eAAe,UAAU,eAAe,CAAC,GAAG;gCAC5C,mBAAmB,UAAU,eAAe,CAAC,WAAW,SAAS,EAAE,WAAW,SAAS;gCACvF,kBAAkB,UAAU,eAAe,CAAC,GAAG,WAAW,SAAS;gCACnE,uBAAuB,WAAW,QAAQ;gCAC1C,sBAAsB,WAAW,OAAO;gCACxC,yBAAyB,WAAW,UAAU;gCAC9C,6BAA6B,WAAW,gBAAgB;4BAC5D;wBACJ;oBACJ;gBACJ;gBACA,OAAO;YACX;YACA,IAAI,iBAAiB;gBACjB,mBAAmB;YACvB;YACA,SAAS,KAAK,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe;gBAC9C,IAAI,oBAAoB,KAAK,GAAG;oBAAE,kBAAkB,CAAC;gBAAG;gBACxD,IAAI,UAAU;gBACd,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,SAAU,GAAG;oBAC5C,OAAO,CAAC,IAAI,GAAG,eAAe,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI;gBACvD;gBACA,IAAI,eAAe,QAAQ,iBAAiB,KAAK,iBAAiB,QAAQ,iBAAiB,KAAK;gBAChG,IAAI,mBAAmB,QAAQ,iBAAiB,KAAK,gBAAgB,QAAQ,iBAAiB,KAAK;gBACnG,IAAI,KAAK,YAAY,QAAQ,CAAC,MAAM,OAAO,QAAQ,eAAe,YAAY,GAAG,SAAS,EAAE,WAAW,GAAG,QAAQ;gBAClH,IAAI,SAAS,KAAK,mBAAmB,WAAW;gBAChD,IAAI,CAAC,UAAU,CAAC,QAAQ,iBAAiB,KAAK,iBAAiB,QAAQ,iBAAiB,KAAK,aAAa,GAAG;oBACzG,SAAS,KAAK,mBAAmB,YAAY;gBACjD;gBACA,OAAO;YACX;YACA,KAAK,OAAO,GAAG;YACf,QAAQ,OAAO,GAAG;QAGlB,GAAG,GAAG;QAEC,SAAS,OAAM,EAAE,OAAO,EAAE,mBAAmB;YAEpD;YAEA,OAAO,cAAc,CAAC,SAAS,cAAc;gBAAE,OAAO;YAAK;YAC3D,IAAI,cAAc,oBAAoB;YACtC,IAAI,cAAc;YAClB,IAAI,oBAAoB;YACxB,SAAS,WAAW,KAAK,EAAE,GAAG,EAAE,GAAG;gBAC/B,OAAO,QAAQ,MAAM,MAAM,QAAQ,MAAM,MAAM;YACnD;YACA,kDAAkD;YAClD,IAAI,SAAwB;gBACxB,SAAS,OAAO,KAAK,EAAE,MAAM;oBACzB,IAAI,CAAC,KAAK,GAAG;oBACb,IAAI,CAAC,IAAI,GAAG,IAAI,kBAAkB,QAAQ;gBAC9C;gBACA,OAAO,SAAS,CAAC,GAAG,GAAG,SAAU,CAAC,EAAE,CAAC;oBACjC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE;gBACxC;gBACA,OAAO,SAAS,CAAC,GAAG,GAAG,SAAU,CAAC,EAAE,CAAC,EAAE,KAAK;oBACxC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,GAAG;gBACpC;gBACA,OAAO;YACX;YACA,SAAS,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc;gBACjD,IAAI,KAAK,MAAM,KAAK,QAAQ,SAAS,GAAG;oBACpC,MAAM,IAAI,MAAM;gBACpB;gBACA,6BAA6B;gBAC7B,IAAI,kBAAkB,IAAI,OAAO,OAAO;gBACxC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;oBAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;wBAC7B,IAAI,IAAI,IAAI,CAAC,AAAC,CAAC,IAAI,QAAQ,CAAC,IAAI,IAAK,EAAE;wBACvC,IAAI,IAAI,IAAI,CAAC,AAAC,CAAC,IAAI,QAAQ,CAAC,IAAI,IAAK,EAAE;wBACvC,IAAI,IAAI,IAAI,CAAC,AAAC,CAAC,IAAI,QAAQ,CAAC,IAAI,IAAK,EAAE;wBACvC,gBAAgB,GAAG,CAAC,GAAG,GAAG,SAAS,IAAI,SAAS,IAAI,SAAS;oBACjE;gBACJ;gBACA,IAAI,wBAAwB,KAAK,IAAI,CAAC,QAAQ;gBAC9C,IAAI,sBAAsB,KAAK,IAAI,CAAC,SAAS;gBAC7C,IAAI,cAAc,IAAI,OAAO,uBAAuB;gBACpD,IAAK,IAAI,iBAAiB,GAAG,iBAAiB,qBAAqB,iBAAkB;oBACjF,IAAK,IAAI,oBAAoB,GAAG,oBAAoB,uBAAuB,oBAAqB;wBAC5F,IAAI,MAAM;wBACV,IAAI,MAAM;wBACV,IAAI,MAAM;wBACV,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;4BAClC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;gCAClC,IAAI,gBAAgB,gBAAgB,GAAG,CAAC,oBAAoB,cAAc,GAAG,iBAAiB,cAAc;gCAC5G,OAAO;gCACP,MAAM,KAAK,GAAG,CAAC,KAAK;gCACpB,MAAM,KAAK,GAAG,CAAC,KAAK;4BACxB;wBACJ;wBACA,IAAI,UAAU,MAAO,KAAK,GAAG,CAAC,aAAa;wBAC3C,IAAI,MAAM,OAAO,mBAAmB;4BAChC,uFAAuF;4BACvF,uFAAuF;4BACvF,yFAAyF;4BACzF,EAAE;4BACF,0FAA0F;4BAC1F,UAAU,MAAM;4BAChB,IAAI,iBAAiB,KAAK,oBAAoB,GAAG;gCAC7C,wFAAwF;gCACxF,uFAAuF;gCACvF,oFAAoF;gCACpF,uFAAuF;gCACvF,2CAA2C;gCAC3C,sFAAsF;gCACtF,IAAI,4BAA4B,CAAC,YAAY,GAAG,CAAC,mBAAmB,iBAAiB,KAChF,IAAI,YAAY,GAAG,CAAC,oBAAoB,GAAG,kBAC5C,YAAY,GAAG,CAAC,oBAAoB,GAAG,iBAAiB,EAAE,IAAI;gCAClE,IAAI,MAAM,2BAA2B;oCACjC,UAAU;gCACd;4BACJ;wBACJ;wBACA,YAAY,GAAG,CAAC,mBAAmB,gBAAgB;oBACvD;gBACJ;gBACA,IAAI,YAAY,YAAY,SAAS,CAAC,WAAW,CAAC,OAAO;gBACzD,IAAI,WAAW;gBACf,IAAI,gBAAgB;oBAChB,WAAW,YAAY,SAAS,CAAC,WAAW,CAAC,OAAO;gBACxD;gBACA,IAAK,IAAI,iBAAiB,GAAG,iBAAiB,qBAAqB,iBAAkB;oBACjF,IAAK,IAAI,oBAAoB,GAAG,oBAAoB,uBAAuB,oBAAqB;wBAC5F,IAAI,OAAO,WAAW,mBAAmB,GAAG,wBAAwB;wBACpE,IAAI,QAAQ,WAAW,gBAAgB,GAAG,sBAAsB;wBAChE,IAAI,MAAM;wBACV,IAAK,IAAI,UAAU,CAAC,GAAG,WAAW,GAAG,UAAW;4BAC5C,IAAK,IAAI,UAAU,CAAC,GAAG,WAAW,GAAG,UAAW;gCAC5C,OAAO,YAAY,GAAG,CAAC,OAAO,SAAS,QAAQ;4BACnD;wBACJ;wBACA,IAAI,YAAY,MAAM;wBACtB,IAAK,IAAI,UAAU,GAAG,UAAU,aAAa,UAAW;4BACpD,IAAK,IAAI,UAAU,GAAG,UAAU,aAAa,UAAW;gCACpD,IAAI,IAAI,oBAAoB,cAAc;gCAC1C,IAAI,IAAI,iBAAiB,cAAc;gCACvC,IAAI,MAAM,gBAAgB,GAAG,CAAC,GAAG;gCACjC,UAAU,GAAG,CAAC,GAAG,GAAG,OAAO;gCAC3B,IAAI,gBAAgB;oCAChB,SAAS,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,SAAS;gCACzC;4BACJ;wBACJ;oBACJ;gBACJ;gBACA,IAAI,gBAAgB;oBAChB,OAAO;wBAAE,WAAW;wBAAW,UAAU;oBAAS;gBACtD;gBACA,OAAO;oBAAE,WAAW;gBAAU;YAClC;YACA,QAAQ,QAAQ,GAAG;QAGnB,GAAG,GAAG;QAEC,SAAS,OAAM,EAAE,OAAO,EAAE,mBAAmB;YAEpD;YAEA,OAAO,cAAc,CAAC,SAAS,cAAc;gBAAE,OAAO;YAAK;YAC3D,IAAI,cAAc,oBAAoB;YACtC,IAAI,eAAe,oBAAoB;YACvC,IAAI,gBAAgB,oBAAoB;YACxC,IAAI,YAAY,oBAAoB;YACpC,4BAA4B;YAC5B,SAAS,iBAAiB,CAAC,EAAE,CAAC;gBAC1B,IAAI,IAAI,IAAI;gBACZ,IAAI,WAAW;gBACf,MAAO,EAAG;oBACN;oBACA,KAAK,IAAI;gBACb;gBACA,OAAO;YACX;YACA,SAAS,QAAQ,GAAG,EAAE,IAAI;gBACtB,OAAO,AAAC,QAAQ,IAAK;YACzB;YACA,2BAA2B;YAC3B,IAAI,oBAAoB;gBACpB;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;gBACrE;oBAAE,MAAM;oBAAQ,YAAY;wBAAE,sBAAsB;wBAAG,UAAU;oBAAE;gBAAE;aACxE;YACD,IAAI,aAAa;gBACb,SAAU,CAAC;oBAAI,OAAO,AAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,MAAO;gBAAG;gBAC/C,SAAU,CAAC;oBAAI,OAAO,AAAC,EAAE,CAAC,GAAG,MAAO;gBAAG;gBACvC,SAAU,CAAC;oBAAI,OAAO,EAAE,CAAC,GAAG,MAAM;gBAAG;gBACrC,SAAU,CAAC;oBAAI,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,MAAM;gBAAG;gBAC7C,SAAU,CAAC;oBAAI,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,MAAM;gBAAG;gBAC7E,SAAU,CAAC;oBAAI,OAAO,AAAE,EAAE,CAAC,GAAG,EAAE,CAAC,GAAI,IAAM,AAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAI,MAAO;gBAAG;gBACnE,SAAU,CAAC;oBAAI,OAAO,AAAC,CAAC,AAAE,EAAE,CAAC,GAAG,EAAE,CAAC,GAAI,IAAK,AAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAI,CAAC,IAAI,MAAO;gBAAG;gBACzE,SAAU,CAAC;oBAAI,OAAO,AAAC,CAAC,AAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,IAAK,AAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAI,CAAC,IAAI,MAAO;gBAAG;aAC5E;YACD,SAAS,yBAAyB,OAAO;gBACrC,IAAI,YAAY,KAAK,IAAI,QAAQ,aAAa;gBAC9C,IAAI,SAAS,YAAY,SAAS,CAAC,WAAW,CAAC,WAAW;gBAC1D,OAAO,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,OAAO,+CAA+C;gBACnF,OAAO,SAAS,CAAC,YAAY,GAAG,GAAG,GAAG,GAAG,OAAO,gDAAgD;gBAChG,OAAO,SAAS,CAAC,GAAG,YAAY,GAAG,GAAG,GAAG,OAAO,kDAAkD;gBAClG,qBAAqB;gBACrB,IAAK,IAAI,KAAK,GAAG,KAAK,QAAQ,uBAAuB,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;oBACzE,IAAI,IAAI,EAAE,CAAC,GAAG;oBACd,IAAK,IAAI,KAAK,GAAG,KAAK,QAAQ,uBAAuB,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;wBACzE,IAAI,IAAI,EAAE,CAAC,GAAG;wBACd,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,YAAY,KAAK,MAAM,YAAY,KAAK,MAAM,CAAC,GAAG;4BAC3F,OAAO,SAAS,CAAC,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG;wBACzC;oBACJ;gBACJ;gBACA,OAAO,SAAS,CAAC,GAAG,GAAG,GAAG,YAAY,IAAI,OAAO,0BAA0B;gBAC3E,OAAO,SAAS,CAAC,GAAG,GAAG,YAAY,IAAI,GAAG,OAAO,4BAA4B;gBAC7E,IAAI,QAAQ,aAAa,GAAG,GAAG;oBAC3B,OAAO,SAAS,CAAC,YAAY,IAAI,GAAG,GAAG,GAAG,OAAO,0BAA0B;oBAC3E,OAAO,SAAS,CAAC,GAAG,YAAY,IAAI,GAAG,GAAG,OAAO,4BAA4B;gBACjF;gBACA,OAAO;YACX;YACA,SAAS,cAAc,MAAM,EAAE,OAAO,EAAE,UAAU;gBAC9C,IAAI,WAAW,UAAU,CAAC,WAAW,QAAQ,CAAC;gBAC9C,IAAI,YAAY,OAAO,MAAM;gBAC7B,IAAI,sBAAsB,yBAAyB;gBACnD,IAAI,YAAY,EAAE;gBAClB,IAAI,cAAc;gBAClB,IAAI,WAAW;gBACf,4CAA4C;gBAC5C,IAAI,YAAY;gBAChB,IAAK,IAAI,cAAc,YAAY,GAAG,cAAc,GAAG,eAAe,EAAG;oBACrE,IAAI,gBAAgB,GAAG;wBACnB;oBACJ;oBACA,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;wBAChC,IAAI,IAAI,YAAY,YAAY,IAAI,IAAI;wBACxC,IAAK,IAAI,eAAe,GAAG,eAAe,GAAG,eAAgB;4BACzD,IAAI,IAAI,cAAc;4BACtB,IAAI,CAAC,oBAAoB,GAAG,CAAC,GAAG,IAAI;gCAChC;gCACA,IAAI,MAAM,OAAO,GAAG,CAAC,GAAG;gCACxB,IAAI,SAAS;oCAAE,GAAG;oCAAG,GAAG;gCAAE,IAAI;oCAC1B,MAAM,CAAC;gCACX;gCACA,cAAc,QAAQ,KAAK;gCAC3B,IAAI,aAAa,GAAG;oCAChB,UAAU,IAAI,CAAC;oCACf,WAAW;oCACX,cAAc;gCAClB;4BACJ;wBACJ;oBACJ;oBACA,YAAY,CAAC;gBACjB;gBACA,OAAO;YACX;YACA,SAAS,YAAY,MAAM;gBACvB,IAAI,YAAY,OAAO,MAAM;gBAC7B,IAAI,qBAAqB,KAAK,KAAK,CAAC,CAAC,YAAY,EAAE,IAAI;gBACvD,IAAI,sBAAsB,GAAG;oBACzB,OAAO,UAAU,QAAQ,CAAC,qBAAqB,EAAE;gBACrD;gBACA,IAAI,sBAAsB;gBAC1B,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;oBACzB,IAAK,IAAI,IAAI,YAAY,GAAG,KAAK,YAAY,IAAI,IAAK;wBAClD,sBAAsB,QAAQ,OAAO,GAAG,CAAC,GAAG,IAAI;oBACpD;gBACJ;gBACA,IAAI,wBAAwB;gBAC5B,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;oBACzB,IAAK,IAAI,IAAI,YAAY,GAAG,KAAK,YAAY,IAAI,IAAK;wBAClD,wBAAwB,QAAQ,OAAO,GAAG,CAAC,GAAG,IAAI;oBACtD;gBACJ;gBACA,IAAI,iBAAiB;gBACrB,IAAI;gBACJ,IAAK,IAAI,KAAK,GAAG,aAAa,UAAU,QAAQ,EAAE,KAAK,WAAW,MAAM,EAAE,KAAM;oBAC5E,IAAI,UAAU,UAAU,CAAC,GAAG;oBAC5B,IAAI,QAAQ,QAAQ,KAAK,uBAAuB,QAAQ,QAAQ,KAAK,uBAAuB;wBACxF,OAAO;oBACX;oBACA,IAAI,aAAa,iBAAiB,qBAAqB,QAAQ,QAAQ;oBACvE,IAAI,aAAa,gBAAgB;wBAC7B,cAAc;wBACd,iBAAiB;oBACrB;oBACA,aAAa,iBAAiB,uBAAuB,QAAQ,QAAQ;oBACrE,IAAI,aAAa,gBAAgB;wBAC7B,cAAc;wBACd,iBAAiB;oBACrB;gBACJ;gBACA,iFAAiF;gBACjF,8BAA8B;gBAC9B,IAAI,kBAAkB,GAAG;oBACrB,OAAO;gBACX;YACJ;YACA,SAAS,sBAAsB,MAAM;gBACjC,IAAI,wBAAwB;gBAC5B,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;oBACzB,IAAI,MAAM,GAAG;wBACT,wBAAwB,QAAQ,OAAO,GAAG,CAAC,GAAG,IAAI;oBACtD;gBACJ;gBACA,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;oBACzB,IAAI,MAAM,GAAG;wBACT,wBAAwB,QAAQ,OAAO,GAAG,CAAC,GAAG,IAAI;oBACtD;gBACJ;gBACA,IAAI,YAAY,OAAO,MAAM;gBAC7B,IAAI,oCAAoC;gBACxC,IAAK,IAAI,IAAI,YAAY,GAAG,KAAK,YAAY,GAAG,IAAK;oBACjD,oCAAoC,QAAQ,OAAO,GAAG,CAAC,GAAG,IAAI;gBAClE;gBACA,IAAK,IAAI,IAAI,YAAY,GAAG,IAAI,WAAW,IAAK;oBAC5C,oCAAoC,QAAQ,OAAO,GAAG,CAAC,GAAG,IAAI;gBAClE;gBACA,IAAI,iBAAiB;gBACrB,IAAI,iBAAiB;gBACrB,IAAK,IAAI,KAAK,GAAG,sBAAsB,mBAAmB,KAAK,oBAAoB,MAAM,EAAE,KAAM;oBAC7F,IAAI,KAAK,mBAAmB,CAAC,GAAG,EAAE,OAAO,GAAG,IAAI,EAAE,aAAa,GAAG,UAAU;oBAC5E,IAAI,SAAS,yBAAyB,SAAS,mCAAmC;wBAC9E,OAAO;oBACX;oBACA,IAAI,aAAa,iBAAiB,uBAAuB;oBACzD,IAAI,aAAa,gBAAgB;wBAC7B,iBAAiB;wBACjB,iBAAiB;oBACrB;oBACA,IAAI,0BAA0B,mCAAmC;wBAC7D,aAAa,iBAAiB,mCAAmC;wBACjE,IAAI,aAAa,gBAAgB;4BAC7B,iBAAiB;4BACjB,iBAAiB;wBACrB;oBACJ;gBACJ;gBACA,+GAA+G;gBAC/G,IAAI,kBAAkB,GAAG;oBACrB,OAAO;gBACX;gBACA,OAAO;YACX;YACA,SAAS,cAAc,SAAS,EAAE,OAAO,EAAE,OAAO;gBAC9C,IAAI,SAAS,QAAQ,qBAAqB,CAAC,QAAQ;gBACnD,IAAI,aAAa,EAAE;gBACnB,IAAI,iBAAiB;gBACrB,OAAO,QAAQ,CAAC,OAAO,CAAC,SAAU,KAAK;oBACnC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,SAAS,EAAE,IAAK;wBACtC,WAAW,IAAI,CAAC;4BAAE,kBAAkB,MAAM,qBAAqB;4BAAE,WAAW,EAAE;wBAAC;wBAC/E,kBAAkB,MAAM,qBAAqB,GAAG,OAAO,mBAAmB;oBAC9E;gBACJ;gBACA,mGAAmG;gBACnG,iDAAiD;gBACjD,6CAA6C;gBAC7C,IAAI,UAAU,MAAM,GAAG,gBAAgB;oBACnC,OAAO;gBACX;gBACA,YAAY,UAAU,KAAK,CAAC,GAAG;gBAC/B,IAAI,iBAAiB,OAAO,QAAQ,CAAC,EAAE,CAAC,qBAAqB;gBAC7D,2DAA2D;gBAC3D,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,IAAK;oBACrC,IAAK,IAAI,KAAK,GAAG,eAAe,YAAY,KAAK,aAAa,MAAM,EAAE,KAAM;wBACxE,IAAI,YAAY,YAAY,CAAC,GAAG;wBAChC,UAAU,SAAS,CAAC,IAAI,CAAC,UAAU,KAAK;oBAC5C;gBACJ;gBACA,kFAAkF;gBAClF,IAAI,OAAO,QAAQ,CAAC,MAAM,GAAG,GAAG;oBAC5B,IAAI,kBAAkB,OAAO,QAAQ,CAAC,EAAE,CAAC,SAAS;oBAClD,IAAI,kBAAkB,OAAO,QAAQ,CAAC,EAAE,CAAC,SAAS;oBAClD,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;wBACtC,UAAU,CAAC,kBAAkB,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,KAAK;oBAClE;gBACJ;gBACA,yFAAyF;gBACzF,MAAO,UAAU,MAAM,GAAG,EAAG;oBACzB,IAAK,IAAI,KAAK,GAAG,eAAe,YAAY,KAAK,aAAa,MAAM,EAAE,KAAM;wBACxE,IAAI,YAAY,YAAY,CAAC,GAAG;wBAChC,UAAU,SAAS,CAAC,IAAI,CAAC,UAAU,KAAK;oBAC5C;gBACJ;gBACA,OAAO;YACX;YACA,SAAS,aAAa,MAAM;gBACxB,IAAI,UAAU,YAAY;gBAC1B,IAAI,CAAC,SAAS;oBACV,OAAO;gBACX;gBACA,IAAI,aAAa,sBAAsB;gBACvC,IAAI,CAAC,YAAY;oBACb,OAAO;gBACX;gBACA,IAAI,YAAY,cAAc,QAAQ,SAAS;gBAC/C,IAAI,aAAa,cAAc,WAAW,SAAS,WAAW,oBAAoB;gBAClF,IAAI,CAAC,YAAY;oBACb,OAAO;gBACX;gBACA,mCAAmC;gBACnC,IAAI,aAAa,WAAW,MAAM,CAAC,SAAU,CAAC,EAAE,CAAC;oBAAI,OAAO,IAAI,EAAE,gBAAgB;gBAAE,GAAG;gBACvF,IAAI,cAAc,IAAI,kBAAkB;gBACxC,IAAI,cAAc;gBAClB,IAAK,IAAI,KAAK,GAAG,eAAe,YAAY,KAAK,aAAa,MAAM,EAAE,KAAM;oBACxE,IAAI,YAAY,YAAY,CAAC,GAAG;oBAChC,IAAI,iBAAiB,cAAc,MAAM,CAAC,UAAU,SAAS,EAAE,UAAU,SAAS,CAAC,MAAM,GAAG,UAAU,gBAAgB;oBACtH,IAAI,CAAC,gBAAgB;wBACjB,OAAO;oBACX;oBACA,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,gBAAgB,EAAE,IAAK;wBACjD,WAAW,CAAC,cAAc,GAAG,cAAc,CAAC,EAAE;oBAClD;gBACJ;gBACA,IAAI;oBACA,OAAO,aAAa,MAAM,CAAC,aAAa,QAAQ,aAAa;gBACjE,EACA,OAAO,IAAI;oBACP,OAAO;gBACX;YACJ;YACA,SAAS,OAAO,MAAM;gBAClB,IAAI,UAAU,MAAM;oBAChB,OAAO;gBACX;gBACA,IAAI,SAAS,aAAa;gBAC1B,IAAI,QAAQ;oBACR,OAAO;gBACX;gBACA,qFAAqF;gBACrF,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,KAAK,EAAE,IAAK;oBACnC,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;wBACxC,IAAI,OAAO,GAAG,CAAC,GAAG,OAAO,OAAO,GAAG,CAAC,GAAG,IAAI;4BACvC,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,OAAO,GAAG,CAAC,GAAG;4BAChC,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,OAAO,GAAG,CAAC,GAAG;wBACpC;oBACJ;gBACJ;gBACA,OAAO,aAAa;YACxB;YACA,QAAQ,MAAM,GAAG;QAGjB,GAAG,GAAG;QAEC,SAAS,OAAM,EAAE,OAAO,EAAE,mBAAmB;YAEpD;YAEA,OAAO,cAAc,CAAC,SAAS,cAAc;gBAAE,OAAO;YAAK;YAC3D,4BAA4B;YAC5B,IAAI,cAAc,oBAAoB;YACtC,IAAI,kBAAkB,oBAAoB;YAC1C,IAAI;YACJ,CAAC,SAAU,IAAI;gBACX,IAAI,CAAC,UAAU,GAAG;gBAClB,IAAI,CAAC,eAAe,GAAG;gBACvB,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,MAAM,GAAG;YAClB,CAAC,EAAE,OAAO,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,GAAG,CAAC,CAAC;YAC5C,IAAI;YACJ,CAAC,SAAU,QAAQ;gBACf,QAAQ,CAAC,QAAQ,CAAC,aAAa,GAAG,EAAE,GAAG;gBACvC,QAAQ,CAAC,QAAQ,CAAC,UAAU,GAAG,EAAE,GAAG;gBACpC,QAAQ,CAAC,QAAQ,CAAC,eAAe,GAAG,EAAE,GAAG;gBACzC,QAAQ,CAAC,QAAQ,CAAC,OAAO,GAAG,EAAE,GAAG;gBACjC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,GAAG,EAAE,GAAG;gBAClC,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,GAAG;YAChC,0BAA0B;YAC1B,2BAA2B;YAC3B,4BAA4B;YAChC,CAAC,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;YAC7B,SAAS,cAAc,MAAM,EAAE,IAAI;gBAC/B,IAAI,QAAQ,EAAE;gBACd,IAAI,OAAO;gBACX,IAAI,qBAAqB;oBAAC;oBAAI;oBAAI;iBAAG,CAAC,KAAK;gBAC3C,IAAI,SAAS,OAAO,QAAQ,CAAC;gBAC7B,6BAA6B;gBAC7B,MAAO,UAAU,EAAG;oBAChB,IAAI,MAAM,OAAO,QAAQ,CAAC;oBAC1B,IAAI,OAAO,MAAM;wBACb,MAAM,IAAI,MAAM;oBACpB;oBACA,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM;oBACzB,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM,MAAM;oBAC/B,IAAI,IAAI,MAAM;oBACd,MAAM,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK;oBAChC,QAAQ,EAAE,QAAQ,KAAK,EAAE,QAAQ,KAAK,EAAE,QAAQ;oBAChD,UAAU;gBACd;gBACA,0FAA0F;gBAC1F,IAAI,WAAW,GAAG;oBACd,IAAI,MAAM,OAAO,QAAQ,CAAC;oBAC1B,IAAI,OAAO,KAAK;wBACZ,MAAM,IAAI,MAAM;oBACpB;oBACA,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM;oBACzB,IAAI,IAAI,MAAM;oBACd,MAAM,IAAI,CAAC,KAAK,GAAG,KAAK;oBACxB,QAAQ,EAAE,QAAQ,KAAK,EAAE,QAAQ;gBACrC,OACK,IAAI,WAAW,GAAG;oBACnB,IAAI,MAAM,OAAO,QAAQ,CAAC;oBAC1B,IAAI,OAAO,IAAI;wBACX,MAAM,IAAI,MAAM;oBACpB;oBACA,MAAM,IAAI,CAAC,KAAK;oBAChB,QAAQ,IAAI,QAAQ;gBACxB;gBACA,OAAO;oBAAE,OAAO;oBAAO,MAAM;gBAAK;YACtC;YACA,IAAI,6BAA6B;gBAC7B;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBACxC;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBACxC;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBACxC;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBACxC;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;gBAAK;aAC3C;YACD,SAAS,mBAAmB,MAAM,EAAE,IAAI;gBACpC,IAAI,QAAQ,EAAE;gBACd,IAAI,OAAO;gBACX,IAAI,qBAAqB;oBAAC;oBAAG;oBAAI;iBAAG,CAAC,KAAK;gBAC1C,IAAI,SAAS,OAAO,QAAQ,CAAC;gBAC7B,MAAO,UAAU,EAAG;oBAChB,IAAI,IAAI,OAAO,QAAQ,CAAC;oBACxB,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI;oBACvB,IAAI,IAAI,IAAI;oBACZ,MAAM,IAAI,CAAC,0BAA0B,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,0BAA0B,CAAC,EAAE,CAAC,UAAU,CAAC;oBACjG,QAAQ,0BAA0B,CAAC,EAAE,GAAG,0BAA0B,CAAC,EAAE;oBACrE,UAAU;gBACd;gBACA,IAAI,WAAW,GAAG;oBACd,IAAI,IAAI,OAAO,QAAQ,CAAC;oBACxB,MAAM,IAAI,CAAC,0BAA0B,CAAC,EAAE,CAAC,UAAU,CAAC;oBACpD,QAAQ,0BAA0B,CAAC,EAAE;gBACzC;gBACA,OAAO;oBAAE,OAAO;oBAAO,MAAM;gBAAK;YACtC;YACA,SAAS,WAAW,MAAM,EAAE,IAAI;gBAC5B,IAAI,QAAQ,EAAE;gBACd,IAAI,OAAO;gBACX,IAAI,qBAAqB;oBAAC;oBAAG;oBAAI;iBAAG,CAAC,KAAK;gBAC1C,IAAI,SAAS,OAAO,QAAQ,CAAC;gBAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;oBAC7B,IAAI,IAAI,OAAO,QAAQ,CAAC;oBACxB,MAAM,IAAI,CAAC;gBACf;gBACA,IAAI;oBACA,QAAQ,mBAAmB,MAAM,GAAG,CAAC,SAAU,CAAC;wBAAI,OAAO,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;oBAAI,GAAG,IAAI,CAAC;gBAChH,EACA,OAAO,IAAI;gBACP,mBAAmB;gBACvB;gBACA,OAAO;oBAAE,OAAO;oBAAO,MAAM;gBAAK;YACtC;YACA,SAAS,YAAY,MAAM,EAAE,IAAI;gBAC7B,IAAI,QAAQ,EAAE;gBACd,IAAI,OAAO;gBACX,IAAI,qBAAqB;oBAAC;oBAAG;oBAAI;iBAAG,CAAC,KAAK;gBAC1C,IAAI,SAAS,OAAO,QAAQ,CAAC;gBAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;oBAC7B,IAAI,IAAI,OAAO,QAAQ,CAAC;oBACxB,IAAI,IAAI,AAAC,KAAK,KAAK,CAAC,IAAI,SAAS,IAAM,IAAI;oBAC3C,IAAI,IAAI,QAAQ;wBACZ,KAAK;oBACT,OACK;wBACD,KAAK;oBACT;oBACA,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI;oBACvB,QAAQ,OAAO,YAAY,CAAC,gBAAgB,aAAa,CAAC,EAAE;gBAChE;gBACA,OAAO;oBAAE,OAAO;oBAAO,MAAM;gBAAK;YACtC;YACA,SAAS,OAAO,IAAI,EAAE,OAAO;gBACzB,IAAI,IAAI,IAAI,IAAI;gBAChB,IAAI,SAAS,IAAI,YAAY,SAAS,CAAC;gBACvC,0GAA0G;gBAC1G,IAAI,OAAO,WAAW,IAAI,IAAI,WAAW,KAAK,IAAI;gBAClD,IAAI,SAAS;oBACT,MAAM;oBACN,OAAO,EAAE;oBACT,QAAQ,EAAE;oBACV,SAAS;gBACb;gBACA,MAAO,OAAO,SAAS,MAAM,EAAG;oBAC5B,IAAI,OAAO,OAAO,QAAQ,CAAC;oBAC3B,IAAI,SAAS,SAAS,UAAU,EAAE;wBAC9B,OAAO;oBACX,OACK,IAAI,SAAS,SAAS,GAAG,EAAE;wBAC5B,IAAI,OAAO,QAAQ,CAAC,OAAO,GAAG;4BAC1B,OAAO,MAAM,CAAC,IAAI,CAAC;gCACf,MAAM,KAAK,GAAG;gCACd,kBAAkB,OAAO,QAAQ,CAAC;4BACtC;wBACJ,OACK,IAAI,OAAO,QAAQ,CAAC,OAAO,GAAG;4BAC/B,OAAO,MAAM,CAAC,IAAI,CAAC;gCACf,MAAM,KAAK,GAAG;gCACd,kBAAkB,OAAO,QAAQ,CAAC;4BACtC;wBACJ,OACK,IAAI,OAAO,QAAQ,CAAC,OAAO,GAAG;4BAC/B,OAAO,MAAM,CAAC,IAAI,CAAC;gCACf,MAAM,KAAK,GAAG;gCACd,kBAAkB,OAAO,QAAQ,CAAC;4BACtC;wBACJ,OACK;4BACD,2BAA2B;4BAC3B,OAAO,MAAM,CAAC,IAAI,CAAC;gCACf,MAAM,KAAK,GAAG;gCACd,kBAAkB,CAAC;4BACvB;wBACJ;oBACJ,OACK,IAAI,SAAS,SAAS,OAAO,EAAE;wBAChC,IAAI,gBAAgB,cAAc,QAAQ;wBAC1C,OAAO,IAAI,IAAI,cAAc,IAAI;wBACjC,CAAC,KAAK,OAAO,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,cAAc,KAAK;wBACtD,OAAO,MAAM,CAAC,IAAI,CAAC;4BACf,MAAM,KAAK,OAAO;4BAClB,MAAM,cAAc,IAAI;wBAC5B;oBACJ,OACK,IAAI,SAAS,SAAS,YAAY,EAAE;wBACrC,IAAI,qBAAqB,mBAAmB,QAAQ;wBACpD,OAAO,IAAI,IAAI,mBAAmB,IAAI;wBACtC,CAAC,KAAK,OAAO,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,mBAAmB,KAAK;wBAC3D,OAAO,MAAM,CAAC,IAAI,CAAC;4BACf,MAAM,KAAK,YAAY;4BACvB,MAAM,mBAAmB,IAAI;wBACjC;oBACJ,OACK,IAAI,SAAS,SAAS,IAAI,EAAE;wBAC7B,IAAI,aAAa,WAAW,QAAQ;wBACpC,OAAO,IAAI,IAAI,WAAW,IAAI;wBAC9B,CAAC,KAAK,OAAO,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,WAAW,KAAK;wBACnD,OAAO,MAAM,CAAC,IAAI,CAAC;4BACf,MAAM,KAAK,IAAI;4BACf,OAAO,WAAW,KAAK;4BACvB,MAAM,WAAW,IAAI;wBACzB;oBACJ,OACK,IAAI,SAAS,SAAS,KAAK,EAAE;wBAC9B,IAAI,cAAc,YAAY,QAAQ;wBACtC,OAAO,IAAI,IAAI,YAAY,IAAI;wBAC/B,CAAC,KAAK,OAAO,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,YAAY,KAAK;wBACpD,OAAO,MAAM,CAAC,IAAI,CAAC;4BACf,MAAM,KAAK,KAAK;4BAChB,OAAO,YAAY,KAAK;4BACxB,MAAM,YAAY,IAAI;wBAC1B;oBACJ;gBACJ;gBACA,sGAAsG;gBACtG,IAAI,OAAO,SAAS,OAAO,KAAK,OAAO,QAAQ,CAAC,OAAO,SAAS,QAAQ,GAAG;oBACvE,OAAO;gBACX;YACJ;YACA,QAAQ,MAAM,GAAG;QAGjB,GAAG,GAAG;QAEC,SAAS,OAAM,EAAE,OAAO,EAAE,mBAAmB;YAEpD;YAEA,4BAA4B;YAC5B,OAAO,cAAc,CAAC,SAAS,cAAc;gBAAE,OAAO;YAAK;YAC3D,IAAI,YAA2B;gBAC3B,SAAS,UAAU,KAAK;oBACpB,IAAI,CAAC,UAAU,GAAG;oBAClB,IAAI,CAAC,SAAS,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG;gBACjB;gBACA,UAAU,SAAS,CAAC,QAAQ,GAAG,SAAU,OAAO;oBAC5C,IAAI,UAAU,KAAK,UAAU,MAAM,UAAU,IAAI,CAAC,SAAS,IAAI;wBAC3D,MAAM,IAAI,MAAM,iBAAiB,QAAQ,QAAQ,KAAK;oBAC1D;oBACA,IAAI,SAAS;oBACb,0CAA0C;oBAC1C,IAAI,IAAI,CAAC,SAAS,GAAG,GAAG;wBACpB,IAAI,WAAW,IAAI,IAAI,CAAC,SAAS;wBACjC,IAAI,SAAS,UAAU,WAAW,UAAU;wBAC5C,IAAI,gBAAgB,WAAW;wBAC/B,IAAI,OAAO,AAAC,QAAS,IAAI,UAAY;wBACrC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,KAAK;wBACjD,WAAW;wBACX,IAAI,CAAC,SAAS,IAAI;wBAClB,IAAI,IAAI,CAAC,SAAS,KAAK,GAAG;4BACtB,IAAI,CAAC,SAAS,GAAG;4BACjB,IAAI,CAAC,UAAU;wBACnB;oBACJ;oBACA,wBAAwB;oBACxB,IAAI,UAAU,GAAG;wBACb,MAAO,WAAW,EAAG;4BACjB,SAAS,AAAC,UAAU,IAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG;4BACxD,IAAI,CAAC,UAAU;4BACf,WAAW;wBACf;wBACA,8BAA8B;wBAC9B,IAAI,UAAU,GAAG;4BACb,IAAI,gBAAgB,IAAI;4BACxB,IAAI,OAAO,AAAC,QAAQ,iBAAkB;4BACtC,SAAS,AAAC,UAAU,UAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,KAAK;4BACxE,IAAI,CAAC,SAAS,IAAI;wBACtB;oBACJ;oBACA,OAAO;gBACX;gBACA,UAAU,SAAS,CAAC,SAAS,GAAG;oBAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS;gBACrE;gBACA,OAAO;YACX;YACA,QAAQ,SAAS,GAAG;QAGpB,GAAG,GAAG;QAEC,SAAS,OAAM,EAAE,OAAO,EAAE,mBAAmB;YAEpD;YAEA,OAAO,cAAc,CAAC,SAAS,cAAc;gBAAE,OAAO;YAAK;YAC3D,QAAQ,aAAa,GAAG;gBACpB,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR,QAAQ;YACZ;QAGA,GAAG,GAAG;QAEC,SAAS,OAAM,EAAE,OAAO,EAAE,mBAAmB;YAEpD;YAEA,OAAO,cAAc,CAAC,SAAS,cAAc;gBAAE,OAAO;YAAK;YAC3D,IAAI,cAAc,oBAAoB;YACtC,IAAI,kBAAkB,oBAAoB;YAC1C,SAAS,sBAAsB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBACzC,IAAI;gBACJ,8BAA8B;gBAC9B,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,IAAI;oBACzB,KAAK;wBAAC;wBAAG;qBAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;gBACrC;gBACA,IAAI,QAAQ;gBACZ,IAAI,IAAI;gBACR,IAAI,QAAQ,MAAM,IAAI;gBACtB,IAAI,IAAI,MAAM,GAAG;gBACjB,4DAA4D;gBAC5D,MAAO,EAAE,MAAM,MAAM,IAAI,EAAG;oBACxB,IAAI,YAAY;oBAChB,IAAI,YAAY;oBAChB,QAAQ;oBACR,QAAQ;oBACR,mEAAmE;oBACnE,IAAI,MAAM,MAAM,IAAI;wBAChB,0CAA0C;wBAC1C,OAAO;oBACX;oBACA,IAAI;oBACJ,IAAI,IAAI,MAAM,IAAI;oBAClB,IAAI,yBAAyB,MAAM,cAAc,CAAC,MAAM,MAAM;oBAC9D,IAAI,aAAa,MAAM,OAAO,CAAC;oBAC/B,MAAO,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,CAAC,EAAE,MAAM,GAAI;wBAChD,IAAI,aAAa,EAAE,MAAM,KAAK,MAAM,MAAM;wBAC1C,IAAI,QAAQ,MAAM,QAAQ,CAAC,EAAE,cAAc,CAAC,EAAE,MAAM,KAAK;wBACzD,IAAI,EAAE,aAAa,CAAC,MAAM,aAAa,CAAC,YAAY;wBACpD,IAAI,EAAE,aAAa,CAAC,MAAM,kBAAkB,CAAC,YAAY;oBAC7D;oBACA,IAAI,EAAE,YAAY,CAAC,OAAO,aAAa,CAAC;oBACxC,IAAI,EAAE,MAAM,MAAM,MAAM,MAAM,IAAI;wBAC9B,OAAO;oBACX;gBACJ;gBACA,IAAI,mBAAmB,EAAE,cAAc,CAAC;gBACxC,IAAI,qBAAqB,GAAG;oBACxB,OAAO;gBACX;gBACA,IAAI,UAAU,MAAM,OAAO,CAAC;gBAC5B,OAAO;oBAAC,EAAE,QAAQ,CAAC;oBAAU,EAAE,QAAQ,CAAC;iBAAS;YACrD;YACA,SAAS,mBAAmB,KAAK,EAAE,YAAY;gBAC3C,iDAAiD;gBACjD,IAAI,YAAY,aAAa,MAAM;gBACnC,IAAI,cAAc,GAAG;oBACjB,OAAO;wBAAC,aAAa,cAAc,CAAC;qBAAG;gBAC3C;gBACA,IAAI,SAAS,IAAI,MAAM;gBACvB,IAAI,aAAa;gBACjB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,aAAa,WAAW,IAAK;oBAC3D,IAAI,aAAa,UAAU,CAAC,OAAO,GAAG;wBAClC,MAAM,CAAC,WAAW,GAAG,MAAM,OAAO,CAAC;wBACnC;oBACJ;gBACJ;gBACA,IAAI,eAAe,WAAW;oBAC1B,OAAO;gBACX;gBACA,OAAO;YACX;YACA,SAAS,oBAAoB,KAAK,EAAE,cAAc,EAAE,cAAc;gBAC9D,6CAA6C;gBAC7C,IAAI,IAAI,eAAe,MAAM;gBAC7B,IAAI,SAAS,IAAI,MAAM;gBACvB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;oBACxB,IAAI,YAAY,MAAM,OAAO,CAAC,cAAc,CAAC,EAAE;oBAC/C,IAAI,cAAc;oBAClB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;wBACxB,IAAI,MAAM,GAAG;4BACT,cAAc,MAAM,QAAQ,CAAC,aAAa,YAAY,eAAe,CAAC,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,EAAE,EAAE;wBAC/G;oBACJ;oBACA,MAAM,CAAC,EAAE,GAAG,MAAM,QAAQ,CAAC,eAAe,UAAU,CAAC,YAAY,MAAM,OAAO,CAAC;oBAC/E,IAAI,MAAM,aAAa,KAAK,GAAG;wBAC3B,MAAM,CAAC,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE;oBAC1C;gBACJ;gBACA,OAAO;YACX;YACA,SAAS,OAAO,KAAK,EAAE,IAAI;gBACvB,IAAI,cAAc,IAAI,kBAAkB,MAAM,MAAM;gBACpD,YAAY,GAAG,CAAC;gBAChB,IAAI,QAAQ,IAAI,YAAY,OAAO,CAAC,QAAQ,KAAK,IAAI,4BAA4B;gBACjF,IAAI,OAAO,IAAI,gBAAgB,OAAO,CAAC,OAAO;gBAC9C,IAAI,uBAAuB,IAAI,kBAAkB;gBACjD,IAAI,QAAQ;gBACZ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;oBAC3B,IAAI,aAAa,KAAK,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,aAAa;oBAClE,oBAAoB,CAAC,qBAAqB,MAAM,GAAG,IAAI,EAAE,GAAG;oBAC5D,IAAI,eAAe,GAAG;wBAClB,QAAQ;oBACZ;gBACJ;gBACA,IAAI,CAAC,OAAO;oBACR,OAAO;gBACX;gBACA,IAAI,WAAW,IAAI,gBAAgB,OAAO,CAAC,OAAO;gBAClD,IAAI,aAAa,sBAAsB,OAAO,MAAM,aAAa,CAAC,MAAM,IAAI,UAAU;gBACtF,IAAI,eAAe,MAAM;oBACrB,OAAO;gBACX;gBACA,IAAI,iBAAiB,mBAAmB,OAAO,UAAU,CAAC,EAAE;gBAC5D,IAAI,kBAAkB,MAAM;oBACxB,OAAO;gBACX;gBACA,IAAI,kBAAkB,oBAAoB,OAAO,UAAU,CAAC,EAAE,EAAE;gBAChE,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;oBAC5C,IAAI,WAAW,YAAY,MAAM,GAAG,IAAI,MAAM,GAAG,CAAC,cAAc,CAAC,EAAE;oBACnE,IAAI,WAAW,GAAG;wBACd,OAAO;oBACX;oBACA,WAAW,CAAC,SAAS,GAAG,YAAY,eAAe,CAAC,WAAW,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE;gBACjG;gBACA,OAAO;YACX;YACA,QAAQ,MAAM,GAAG;QAGjB,GAAG,GAAG;QAEC,SAAS,OAAM,EAAE,OAAO,EAAE,mBAAmB;YAEpD;YAEA,OAAO,cAAc,CAAC,SAAS,cAAc;gBAAE,OAAO;YAAK;YAC3D,QAAQ,QAAQ,GAAG;gBACf;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB,EAAE;oBAC3B,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAE;6BAAE;wBAC1D;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;qBAAG;oBAChC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;qBAAG;oBAChC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;qBAAG;oBAChC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAE;6BAAE;wBAC1D;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;qBAAG;oBAChC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAAE;wBAC5D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;qBAAG;oBAChC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;qBAAG;oBACpC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;qBAAG;oBACpC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;qBAAG;oBACpC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAAE;wBAC5D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;qBAAG;oBACpC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;qBAAG;oBACpC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAAE;wBAC3D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;qBAAG;oBACpC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;qBAAG;oBACpC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAAE;wBAC5D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;qBAAG;oBACxC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;qBAAG;oBACxC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;qBAAG;oBACxC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;qBAAG;oBACxC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;qBAAG;oBACxC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;qBAAG;oBACxC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;qBAAG;oBACxC,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;qBAAG;oBAC5C,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAAE;wBAC5D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;qBAAG;oBAC5C,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAAE;wBAC5D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAAE;wBAC5D;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;qBAAI;oBAC7C,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;qBAAI;oBAC7C,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;qBAAI;oBAC7C,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;qBAAI;oBAC7C,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAI;gCAC5C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;qBAAI;oBAC7C,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;wBAAI;qBAAI;oBACjD,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAI;6BAC/C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;wBAAK;qBAAI;oBAClD,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;wBAAK;qBAAI;oBAClD,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAI;6BAC/C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;wBAAK;qBAAI;oBAClD,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAI;gCAC5C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;wBAAK;qBAAI;oBAClD,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCAAC;oCAAE,WAAW;oCAAI,uBAAuB;gCAAI;6BAAE;wBAC7D;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;wBAAK;qBAAI;oBAClD,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAI;gCAC5C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;wBAAK;qBAAI;oBAClD,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAI;gCAC5C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;wBAAK;wBAAK;qBAAI;oBACvD,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAI;gCAC5C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;wBAAK;wBAAK;qBAAI;oBACvD,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAI;6BAC/C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;gCAC1C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;wBAAK;wBAAK;qBAAI;oBACvD,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAI;gCAC5C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;wBAAK;wBAAK;qBAAI;oBACvD,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAI;6BAC/C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;wBAAK;wBAAK;qBAAI;oBACvD,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAI;gCAC5C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAG;6BAC7C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;gBACA;oBACI,UAAU;oBACV,eAAe;oBACf,yBAAyB;wBAAC;wBAAG;wBAAI;wBAAI;wBAAI;wBAAK;wBAAK;qBAAI;oBACvD,uBAAuB;wBACnB;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAI;gCAC5C;oCAAE,WAAW;oCAAG,uBAAuB;gCAAI;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;wBACA;4BACI,qBAAqB;4BACrB,UAAU;gCACN;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;gCAC3C;oCAAE,WAAW;oCAAI,uBAAuB;gCAAG;6BAC9C;wBACL;qBACH;gBACL;aACH;QAGD,GAAG,GAAG;QAEC,SAAS,OAAM,EAAE,OAAO,EAAE,mBAAmB;YAEpD;YAEA,OAAO,cAAc,CAAC,SAAS,cAAc;gBAAE,OAAO;YAAK;YAC3D,IAAI,cAAc,oBAAoB;YACtC,SAAS,sBAAsB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBACzC,IAAI,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;gBACnC,IAAI,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;gBACnC,IAAI,QAAQ,KAAK,QAAQ,GAAG;oBACxB,OAAO;wBACH,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC;wBAChB,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC;wBAChB,KAAK;wBACL,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC;wBAChB,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC;wBAChB,KAAK;wBACL,KAAK,GAAG,CAAC;wBACT,KAAK,GAAG,CAAC;wBACT,KAAK;oBACT;gBACJ,OACK;oBACD,IAAI,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;oBACrB,IAAI,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;oBACrB,IAAI,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;oBACrB,IAAI,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;oBACrB,IAAI,cAAc,MAAM,MAAM,MAAM;oBACpC,IAAI,MAAM,CAAC,MAAM,MAAM,MAAM,GAAG,IAAI;oBACpC,IAAI,MAAM,CAAC,MAAM,MAAM,MAAM,GAAG,IAAI;oBACpC,OAAO;wBACH,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC;wBAC7B,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC;wBAC7B,KAAK;wBACL,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC;wBAC7B,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC;wBAC7B,KAAK;wBACL,KAAK,GAAG,CAAC;wBACT,KAAK,GAAG,CAAC;wBACT,KAAK;oBACT;gBACJ;YACJ;YACA,SAAS,sBAAsB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBACzC,2CAA2C;gBAC3C,IAAI,OAAO,sBAAsB,IAAI,IAAI,IAAI;gBAC7C,OAAO;oBACH,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG;oBAC9C,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG;oBAC9C,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG;oBAC9C,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG;oBAC9C,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG;oBAC9C,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG;oBAC9C,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG;oBAC9C,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG;oBAC9C,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG;gBAClD;YACJ;YACA,SAAS,MAAM,CAAC,EAAE,CAAC;gBACf,OAAO;oBACH,KAAK,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG;oBAClD,KAAK,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG;oBAClD,KAAK,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG;oBAClD,KAAK,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG;oBAClD,KAAK,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG;oBAClD,KAAK,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG;oBAClD,KAAK,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG;oBAClD,KAAK,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG;oBAClD,KAAK,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG;gBACtD;YACJ;YACA,SAAS,QAAQ,KAAK,EAAE,QAAQ;gBAC5B,IAAI,OAAO,sBAAsB;oBAAE,GAAG;oBAAK,GAAG;gBAAI,GAAG;oBAAE,GAAG,SAAS,SAAS,GAAG;oBAAK,GAAG;gBAAI,GAAG;oBAAE,GAAG,SAAS,SAAS,GAAG;oBAAK,GAAG,SAAS,SAAS,GAAG;gBAAI,GAAG;oBAAE,GAAG;oBAAK,GAAG,SAAS,SAAS,GAAG;gBAAI;gBAClM,IAAI,OAAO,sBAAsB,SAAS,OAAO,EAAE,SAAS,QAAQ,EAAE,SAAS,gBAAgB,EAAE,SAAS,UAAU;gBACpH,IAAI,YAAY,MAAM,MAAM;gBAC5B,IAAI,SAAS,YAAY,SAAS,CAAC,WAAW,CAAC,SAAS,SAAS,EAAE,SAAS,SAAS;gBACrF,IAAI,kBAAkB,SAAU,CAAC,EAAE,CAAC;oBAChC,IAAI,cAAc,UAAU,GAAG,GAAG,IAAI,UAAU,GAAG,GAAG,IAAI,UAAU,GAAG;oBACvE,OAAO;wBACH,GAAG,CAAC,UAAU,GAAG,GAAG,IAAI,UAAU,GAAG,GAAG,IAAI,UAAU,GAAG,IAAI;wBAC7D,GAAG,CAAC,UAAU,GAAG,GAAG,IAAI,UAAU,GAAG,GAAG,IAAI,UAAU,GAAG,IAAI;oBACjE;gBACJ;gBACA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,SAAS,EAAE,IAAK;oBACzC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,SAAS,EAAE,IAAK;wBACzC,IAAI,SAAS,IAAI;wBACjB,IAAI,SAAS,IAAI;wBACjB,IAAI,cAAc,gBAAgB,QAAQ;wBAC1C,OAAO,GAAG,CAAC,GAAG,GAAG,MAAM,GAAG,CAAC,KAAK,KAAK,CAAC,YAAY,CAAC,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC;oBAClF;gBACJ;gBACA,OAAO;oBACH,QAAQ;oBACR,iBAAiB;gBACrB;YACJ;YACA,QAAQ,OAAO,GAAG;QAGlB,GAAG,GAAG;QAEC,SAAS,OAAM,EAAE,OAAO,EAAE,mBAAmB;YAEpD;YAEA,OAAO,cAAc,CAAC,SAAS,cAAc;gBAAE,OAAO;YAAK;YAC3D,IAAI,+BAA+B;YACnC,IAAI,iBAAiB;YACrB,IAAI,iBAAiB;YACrB,IAAI,WAAW,SAAU,CAAC,EAAE,CAAC;gBAAI,OAAO,KAAK,IAAI,CAAC,KAAK,GAAG,CAAE,EAAE,CAAC,GAAG,EAAE,CAAC,EAAG,KAAK,KAAK,GAAG,CAAE,EAAE,CAAC,GAAG,EAAE,CAAC,EAAG;YAAK;YACxG,SAAS,IAAI,MAAM;gBACf,OAAO,OAAO,MAAM,CAAC,SAAU,CAAC,EAAE,CAAC;oBAAI,OAAO,IAAI;gBAAG;YACzD;YACA,6EAA6E;YAC7E,SAAS,sBAAsB,QAAQ,EAAE,QAAQ,EAAE,QAAQ;gBACvD,IAAI,IAAI,IAAI,IAAI;gBAChB,yCAAyC;gBACzC,IAAI,iBAAiB,SAAS,UAAU;gBACxC,IAAI,mBAAmB,SAAS,UAAU;gBAC1C,IAAI,mBAAmB,SAAS,UAAU;gBAC1C,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,8EAA8E;gBAC9E,IAAI,oBAAoB,kBAAkB,oBAAoB,kBAAkB;oBAC5E,KAAK;wBAAC;wBAAU;wBAAU;qBAAS,EAAE,aAAa,EAAE,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE;gBAC9F,OACK,IAAI,oBAAoB,oBAAoB,oBAAoB,gBAAgB;oBACjF,KAAK;wBAAC;wBAAU;wBAAU;qBAAS,EAAE,aAAa,EAAE,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE;gBAC9F,OACK;oBACD,KAAK;wBAAC;wBAAU;wBAAU;qBAAS,EAAE,aAAa,EAAE,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE;gBAC9F;gBACA,4HAA4H;gBAC5H,iHAAiH;gBACjH,uEAAuE;gBACvE,IAAI,AAAC,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,IAAM,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,IAAK,GAAG;oBACvH,KAAK;wBAAC;wBAAU;qBAAW,EAAE,aAAa,EAAE,CAAC,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE;gBACrE;gBACA,OAAO;oBAAE,YAAY;oBAAY,SAAS;oBAAS,UAAU;gBAAS;YAC1E;YACA,mHAAmH;YACnH,SAAS,iBAAiB,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM;gBAC3D,IAAI,aAAa,CAAC,IAAI,mBAAmB,SAAS,YAAY,QAAQ,MAAM,IAAI,2CAA2C;gBACvH,IAAI,mBAAmB,SAAS,UAAU,QAAQ,MAAM,IACxD,IAAI,mBAAmB,YAAY,SAAS,QAAQ,MAAM,IAC1D,IAAI,mBAAmB,UAAU,SAAS,QAAQ,MAAM,CAAC,IAAI;gBACjE,IAAI,aAAa,GAAG;oBAChB,MAAM,IAAI,MAAM;gBACpB;gBACA,IAAI,eAAe,KAAK,KAAK,CAAC,SAAS,SAAS,YAAY;gBAC5D,IAAI,gBAAgB,KAAK,KAAK,CAAC,SAAS,SAAS,cAAc;gBAC/D,IAAI,YAAY,KAAK,KAAK,CAAC,CAAC,eAAe,aAAa,IAAI,KAAK;gBACjE,OAAQ,YAAY;oBAChB,KAAK;wBACD;wBACA;oBACJ,KAAK;wBACD;wBACA;gBACR;gBACA,OAAO;oBAAE,WAAW;oBAAW,YAAY;gBAAW;YAC1D;YACA,4HAA4H;YAC5H,oFAAoF;YACpF,4EAA4E;YAC5E,SAAS,+BAA+B,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM;gBAC/D,IAAI,eAAe;oBAAC;wBAAE,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;wBAAG,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;oBAAE;iBAAE;gBACzE,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;gBAClE,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI,OAAO;oBACP,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC;oBAC3B,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC;oBAC3B,MAAM,KAAK,KAAK,CAAC,IAAI,CAAC;oBACtB,MAAM,KAAK,KAAK,CAAC,IAAI,CAAC;gBAC1B,OACK;oBACD,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC;oBAC3B,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC;oBAC3B,MAAM,KAAK,KAAK,CAAC,IAAI,CAAC;oBACtB,MAAM,KAAK,KAAK,CAAC,IAAI,CAAC;gBAC1B;gBACA,IAAI,KAAK,KAAK,GAAG,CAAC,MAAM;gBACxB,IAAI,KAAK,KAAK,GAAG,CAAC,MAAM;gBACxB,IAAI,QAAQ,KAAK,KAAK,CAAC,CAAC,KAAK;gBAC7B,IAAI,QAAQ,QAAQ,MAAM,IAAI,CAAC;gBAC/B,IAAI,QAAQ,QAAQ,MAAM,IAAI,CAAC;gBAC/B,IAAI,eAAe;gBACnB,yCAAyC;gBACzC,IAAK,IAAI,IAAI,OAAO,IAAI,OAAO,MAAM,MAAM,OAAO,KAAK,MAAO;oBAC1D,sEAAsE;oBACtE,4EAA4E;oBAC5E,mEAAmE;oBACnE,IAAI,QAAQ,QAAQ,IAAI;oBACxB,IAAI,QAAQ,QAAQ,IAAI;oBACxB,IAAI,OAAO,GAAG,CAAC,OAAO,WAAW,cAAc;wBAC3C,eAAe,CAAC;wBAChB,aAAa,IAAI,CAAC;4BAAE,GAAG;4BAAO,GAAG;wBAAM;wBACvC,IAAI,aAAa,MAAM,KAAK,SAAS,GAAG;4BACpC;wBACJ;oBACJ;oBACA,SAAS;oBACT,IAAI,QAAQ,GAAG;wBACX,IAAI,MAAM,KAAK;4BACX;wBACJ;wBACA,KAAK;wBACL,SAAS;oBACb;gBACJ;gBACA,IAAI,YAAY,EAAE;gBAClB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;oBAC7B,IAAI,YAAY,CAAC,EAAE,IAAI,YAAY,CAAC,IAAI,EAAE,EAAE;wBACxC,UAAU,IAAI,CAAC,SAAS,YAAY,CAAC,EAAE,EAAE,YAAY,CAAC,IAAI,EAAE;oBAChE,OACK;wBACD,UAAU,IAAI,CAAC;oBACnB;gBACJ;gBACA,OAAO;YACX;YACA,yGAAyG;YACzG,gHAAgH;YAChH,kHAAkH;YAClH,SAAS,mBAAmB,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM;gBACnD,IAAI;gBACJ,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC;gBAC3B,IAAI,MAAM,IAAI,CAAC,GAAG,OAAO,CAAC;gBAC1B,IAAI,aAAa,+BAA+B,QAAQ,KAAK,QAAQ,KAAK,IAAI,CAAC,SAAS;gBACxF,IAAI,cAAc,+BAA+B,QAAQ;oBAAE,GAAG,OAAO,CAAC,GAAG;oBAAK,GAAG,OAAO,CAAC,GAAG;gBAAK,GAAG,QAAQ,KAAK,IAAI,CAAC,SAAS;gBAC/H,IAAI,cAAc,WAAW,KAAK,KAAK,YAAY,KAAK,KAAK,GAAG,iDAAiD;gBACjH,OAAO,CAAC,KAAK,YAAY,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;YACnE;YACA,2HAA2H;YAC3H,8DAA8D;YAC9D,SAAS,mBAAmB,QAAQ,EAAE,MAAM;gBACxC,IAAI,cAAc,IAAI,YAAY,IAAI;gBACtC,IAAI,QAAQ;gBACZ,OAAO,OAAO,CAAC,SAAU,KAAK,EAAE,CAAC;oBAC7B,SAAS,KAAK,GAAG,CAAE,QAAQ,CAAC,EAAE,GAAG,QAAQ,aAAc;gBAC3D;gBACA,OAAO;oBAAE,aAAa;oBAAa,OAAO;gBAAM;YACpD;YACA,sFAAsF;YACtF,yHAAyH;YACzH,gBAAgB;YAChB,SAAS,aAAa,KAAK,EAAE,MAAM,EAAE,MAAM;gBACvC,IAAI;oBACA,IAAI,gBAAgB,mBAAmB,OAAO;wBAAE,GAAG,CAAC;wBAAG,GAAG,MAAM,CAAC;oBAAC,GAAG,QAAQ,OAAO,MAAM;oBAC1F,IAAI,cAAc,mBAAmB,OAAO;wBAAE,GAAG,MAAM,CAAC;wBAAE,GAAG,CAAC;oBAAE,GAAG,QAAQ,OAAO,MAAM;oBACxF,IAAI,eAAe;wBACf,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI;wBACpC,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI;oBACxC;oBACA,IAAI,wBAAwB,mBAAmB,OAAO,cAAc,QAAQ,OAAO,MAAM;oBACzF,IAAI,kBAAkB;wBAClB,GAAG,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI;wBAC/C,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI;oBACpD;oBACA,IAAI,wBAAwB,mBAAmB,OAAO,iBAAiB,QAAQ,OAAO,MAAM;oBAC5F,IAAI,YAAY,mBAAmB,eAAe;oBAClD,IAAI,YAAY,mBAAmB,aAAa;oBAChD,IAAI,gBAAgB,mBAAmB,uBAAuB;oBAC9D,IAAI,cAAc,mBAAmB,uBAAuB;oBAC5D,IAAI,aAAa,KAAK,IAAI,CAAC,UAAU,KAAK,GAAG,UAAU,KAAK,GACxD,UAAU,KAAK,GAAG,UAAU,KAAK,GACjC,cAAc,KAAK,GAAG,cAAc,KAAK,GACzC,YAAY,KAAK,GAAG,YAAY,KAAK;oBACzC,IAAI,UAAU,CAAC,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG,cAAc,WAAW,GAAG,YAAY,WAAW,IAAI;oBACtH,IAAI,YAAY,CAAC,KAAK,GAAG,CAAE,UAAU,WAAW,GAAG,SAAU,KACzD,KAAK,GAAG,CAAE,UAAU,WAAW,GAAG,SAAU,KAC5C,KAAK,GAAG,CAAE,cAAc,WAAW,GAAG,SAAU,KAChD,KAAK,GAAG,CAAE,YAAY,WAAW,GAAG,SAAU,EAAE,IAAI;oBACxD,OAAO,aAAa;gBACxB,EACA,OAAO,IAAI;oBACP,OAAO;gBACX;YACJ;YACA,SAAS,iBAAiB,MAAM,EAAE,CAAC;gBAC/B,IAAI,QAAQ,KAAK,KAAK,CAAC,EAAE,CAAC;gBAC1B,MAAO,OAAO,GAAG,CAAC,OAAO,KAAK,KAAK,CAAC,EAAE,CAAC,GAAI;oBACvC;gBACJ;gBACA,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,CAAC;gBAC3B,MAAO,OAAO,GAAG,CAAC,QAAQ,KAAK,KAAK,CAAC,EAAE,CAAC,GAAI;oBACxC;gBACJ;gBACA,IAAI,IAAI,CAAC,QAAQ,MAAM,IAAI;gBAC3B,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,CAAC;gBACzB,MAAO,OAAO,GAAG,CAAC,KAAK,KAAK,CAAC,IAAI,MAAO;oBACpC;gBACJ;gBACA,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,CAAC;gBAC5B,MAAO,OAAO,GAAG,CAAC,KAAK,KAAK,CAAC,IAAI,SAAU;oBACvC;gBACJ;gBACA,IAAI,IAAI,CAAC,OAAO,OAAO,IAAI;gBAC3B,OAAO;oBAAE,GAAG;oBAAG,GAAG;gBAAE;YACxB;YACA,SAAS,OAAO,MAAM;gBAClB,IAAI,qBAAqB,EAAE;gBAC3B,IAAI,2BAA2B,EAAE;gBACjC,IAAI,wBAAwB,EAAE;gBAC9B,IAAI,8BAA8B,EAAE;gBACpC,IAAI,UAAU,SAAU,CAAC;oBACrB,IAAI,WAAW;oBACf,IAAI,UAAU;oBACd,IAAI,QAAQ;wBAAC;wBAAG;wBAAG;wBAAG;wBAAG;qBAAE;oBAC3B,IAAI,UAAU,SAAU,CAAC;wBACrB,IAAI,IAAI,OAAO,GAAG,CAAC,GAAG;wBACtB,IAAI,MAAM,SAAS;4BACf;wBACJ,OACK;4BACD,QAAQ;gCAAC,KAAK,CAAC,EAAE;gCAAE,KAAK,CAAC,EAAE;gCAAE,KAAK,CAAC,EAAE;gCAAE,KAAK,CAAC,EAAE;gCAAE;6BAAS;4BAC1D,WAAW;4BACX,UAAU;4BACV,sGAAsG;4BACtG,IAAI,gCAAgC,IAAI,SAAS;4BACjD,IAAI,qBAAqB,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,iCAAiC,iCAC1E,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,iCAAiC,iCACrD,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,iCAAiC,IAAI,iCAC7D,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,iCAAiC,iCACrD,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,iCAAiC,iCACrD,CAAC,GAAG,uFAAuF;4BAC/F,kGAAkG;4BAClG,IAAI,mCAAmC,IAAI,MAAM,KAAK,CAAC,CAAC,MAAM;4BAC9D,IAAI,wBAAwB,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,oCAAoC,oCAChF,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,oCAAoC,oCACxD,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,oCAAoC,oCACxD,GAAG,4EAA4E;4BACnF,IAAI,oBAAoB;gCACpB,sEAAsE;gCACtE,IAAI,SAAS,IAAI,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;gCACpC,IAAI,WAAW,SAAS,KAAK,CAAC,EAAE;gCAChC,IAAI,OAAO;oCAAE,QAAQ;oCAAU,MAAM;oCAAQ,GAAG;gCAAE;gCAClD,yHAAyH;gCACzH,mCAAmC;gCACnC,IAAI,gBAAgB,yBAAyB,MAAM,CAAC,SAAU,CAAC;oCAC3D,OAAO,AAAC,YAAY,EAAE,MAAM,CAAC,MAAM,IAAI,YAAY,EAAE,MAAM,CAAC,IAAI,IAC3D,UAAU,EAAE,MAAM,CAAC,MAAM,IAAI,YAAY,EAAE,MAAM,CAAC,IAAI,IACtD,YAAY,EAAE,MAAM,CAAC,MAAM,IAAI,UAAU,EAAE,MAAM,CAAC,IAAI,IAAK,AAAC,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,GAAG,EAAE,MAAM,CAAC,MAAM,IAAK,kBACzG,AAAC,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,GAAG,EAAE,MAAM,CAAC,MAAM,IAAK;gCAC7D;gCACA,IAAI,cAAc,MAAM,GAAG,GAAG;oCAC1B,aAAa,CAAC,EAAE,CAAC,MAAM,GAAG;gCAC9B,OACK;oCACD,yBAAyB,IAAI,CAAC;wCAAE,KAAK;wCAAM,QAAQ;oCAAK;gCAC5D;4BACJ;4BACA,IAAI,uBAAuB;gCACvB,gEAAgE;gCAChE,IAAI,SAAS,IAAI,KAAK,CAAC,EAAE;gCACzB,IAAI,WAAW,SAAS,KAAK,CAAC,EAAE;gCAChC,IAAI,OAAO;oCAAE,QAAQ;oCAAU,GAAG;oCAAG,MAAM;gCAAO;gCAClD,yHAAyH;gCACzH,mCAAmC;gCACnC,IAAI,gBAAgB,4BAA4B,MAAM,CAAC,SAAU,CAAC;oCAC9D,OAAO,AAAC,YAAY,EAAE,MAAM,CAAC,MAAM,IAAI,YAAY,EAAE,MAAM,CAAC,IAAI,IAC3D,UAAU,EAAE,MAAM,CAAC,MAAM,IAAI,YAAY,EAAE,MAAM,CAAC,IAAI,IACtD,YAAY,EAAE,MAAM,CAAC,MAAM,IAAI,UAAU,EAAE,MAAM,CAAC,IAAI,IAAK,AAAC,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,GAAG,EAAE,MAAM,CAAC,MAAM,IAAK,kBACzG,AAAC,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,GAAG,EAAE,MAAM,CAAC,MAAM,IAAK;gCAC7D;gCACA,IAAI,cAAc,MAAM,GAAG,GAAG;oCAC1B,aAAa,CAAC,EAAE,CAAC,MAAM,GAAG;gCAC9B,OACK;oCACD,4BAA4B,IAAI,CAAC;wCAAE,KAAK;wCAAM,QAAQ;oCAAK;gCAC/D;4BACJ;wBACJ;oBACJ;oBACA,IAAK,IAAI,IAAI,CAAC,GAAG,KAAK,OAAO,KAAK,EAAE,IAAK;wBACrC,QAAQ;oBACZ;oBACA,mBAAmB,IAAI,CAAC,KAAK,CAAC,oBAAoB,yBAAyB,MAAM,CAAC,SAAU,CAAC;wBAAI,OAAO,EAAE,MAAM,CAAC,CAAC,KAAK,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI;oBAAG;oBACvJ,2BAA2B,yBAAyB,MAAM,CAAC,SAAU,CAAC;wBAAI,OAAO,EAAE,MAAM,CAAC,CAAC,KAAK;oBAAG;oBACnG,sBAAsB,IAAI,CAAC,KAAK,CAAC,uBAAuB,4BAA4B,MAAM,CAAC,SAAU,CAAC;wBAAI,OAAO,EAAE,MAAM,CAAC,CAAC,KAAK;oBAAG;oBACnI,8BAA8B,4BAA4B,MAAM,CAAC,SAAU,CAAC;wBAAI,OAAO,EAAE,MAAM,CAAC,CAAC,KAAK;oBAAG;gBAC7G;gBACA,IAAK,IAAI,IAAI,GAAG,KAAK,OAAO,MAAM,EAAE,IAAK;oBACrC,QAAQ;gBACZ;gBACA,mBAAmB,IAAI,CAAC,KAAK,CAAC,oBAAoB,yBAAyB,MAAM,CAAC,SAAU,CAAC;oBAAI,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI;gBAAG;gBACnI,sBAAsB,IAAI,CAAC,KAAK,CAAC,uBAAuB;gBACxD,IAAI,sBAAsB,mBACrB,MAAM,CAAC,SAAU,CAAC;oBAAI,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI;gBAAG,GAAG,qFAAqF;iBAChJ,GAAG,CAAC,SAAU,CAAC;oBAChB,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,MAAM,CAAC,MAAM,GAAG,EAAE,MAAM,CAAC,IAAI,IAAI;oBACxE,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI;oBACrC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,KAAK;wBAC3C;oBACJ;oBACA,IAAI,UAAU;wBAAC,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,MAAM;wBAAE,EAAE,MAAM,CAAC,IAAI,GAAG,EAAE,MAAM,CAAC,MAAM;wBAAE,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG;qBAAE;oBACpG,IAAI,OAAO,IAAI,WAAW,QAAQ,MAAM;oBACxC,IAAI,QAAQ,aAAa;wBAAE,GAAG,KAAK,KAAK,CAAC;wBAAI,GAAG,KAAK,KAAK,CAAC;oBAAG,GAAG;wBAAC;wBAAG;wBAAG;wBAAG;wBAAG;qBAAE,EAAE;oBAClF,OAAO;wBAAE,OAAO;wBAAO,GAAG;wBAAG,GAAG;wBAAG,MAAM;oBAAK;gBAClD,GACK,MAAM,CAAC,SAAU,CAAC;oBAAI,OAAO,CAAC,CAAC;gBAAG,GAAG,2CAA2C;iBAChF,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;oBAAI,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;gBAAE,EAClD,+FAA+F;iBAC9F,GAAG,CAAC,SAAU,KAAK,EAAE,CAAC,EAAE,cAAc;oBACvC,IAAI,IAAI,8BAA8B;wBAClC,OAAO;oBACX;oBACA,IAAI,cAAc,eACb,MAAM,CAAC,SAAU,CAAC,EAAE,EAAE;wBAAI,OAAO,MAAM;oBAAI,GAC3C,GAAG,CAAC,SAAU,CAAC;wBAAI,OAAQ;4BAAE,GAAG,EAAE,CAAC;4BAAE,GAAG,EAAE,CAAC;4BAAE,OAAO,EAAE,KAAK,GAAG,AAAC,KAAK,GAAG,CAAE,EAAE,IAAI,GAAG,MAAM,IAAI,EAAG,KAAM,MAAM,IAAI;4BAAE,MAAM,EAAE,IAAI;wBAAC;oBAAI,GAClI,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;wBAAI,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;oBAAE;oBACtD,IAAI,YAAY,MAAM,GAAG,GAAG;wBACxB,OAAO;oBACX;oBACA,IAAI,QAAQ,MAAM,KAAK,GAAG,WAAW,CAAC,EAAE,CAAC,KAAK,GAAG,WAAW,CAAC,EAAE,CAAC,KAAK;oBACrE,OAAO;wBAAE,QAAQ;4BAAC;yBAAM,CAAC,MAAM,CAAC,YAAY,KAAK,CAAC,GAAG;wBAAK,OAAO;oBAAM;gBAC3E,GACK,MAAM,CAAC,SAAU,CAAC;oBAAI,OAAO,CAAC,CAAC;gBAAG,GAAG,qDAAqD;iBAC1F,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;oBAAI,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;gBAAE;gBACtD,IAAI,oBAAoB,MAAM,KAAK,GAAG;oBAClC,OAAO;gBACX;gBACA,IAAI,KAAK,sBAAsB,mBAAmB,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,mBAAmB,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,mBAAmB,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,GAAG,WAAW,GAAG,QAAQ,EAAE,UAAU,GAAG,OAAO,EAAE,aAAa,GAAG,UAAU;gBAC9M,IAAI,YAAY,qBAAqB,QAAQ,uBAAuB,UAAU,SAAS;gBACvF,IAAI,SAAS,EAAE;gBACf,IAAI,WAAW;oBACX,OAAO,IAAI,CAAC;wBACR,kBAAkB;4BAAE,GAAG,UAAU,gBAAgB,CAAC,CAAC;4BAAE,GAAG,UAAU,gBAAgB,CAAC,CAAC;wBAAC;wBACrF,YAAY;4BAAE,GAAG,WAAW,CAAC;4BAAE,GAAG,WAAW,CAAC;wBAAC;wBAC/C,WAAW,UAAU,SAAS;wBAC9B,SAAS;4BAAE,GAAG,QAAQ,CAAC;4BAAE,GAAG,QAAQ,CAAC;wBAAC;wBACtC,UAAU;4BAAE,GAAG,SAAS,CAAC;4BAAE,GAAG,SAAS,CAAC;wBAAC;oBAC7C;gBACJ;gBACA,mIAAmI;gBACnI,8HAA8H;gBAC9H,oIAAoI;gBACpI,+HAA+H;gBAC/H,sCAAsC;gBACtC,IAAI,cAAc,iBAAiB,QAAQ;gBAC3C,IAAI,aAAa,iBAAiB,QAAQ;gBAC1C,IAAI,gBAAgB,iBAAiB,QAAQ;gBAC7C,IAAI,oBAAoB,qBAAqB,QAAQ,uBAAuB,aAAa,YAAY;gBACrG,IAAI,mBAAmB;oBACnB,OAAO,IAAI,CAAC;wBACR,kBAAkB;4BAAE,GAAG,kBAAkB,gBAAgB,CAAC,CAAC;4BAAE,GAAG,kBAAkB,gBAAgB,CAAC,CAAC;wBAAC;wBACrG,YAAY;4BAAE,GAAG,cAAc,CAAC;4BAAE,GAAG,cAAc,CAAC;wBAAC;wBACrD,SAAS;4BAAE,GAAG,WAAW,CAAC;4BAAE,GAAG,WAAW,CAAC;wBAAC;wBAC5C,UAAU;4BAAE,GAAG,YAAY,CAAC;4BAAE,GAAG,YAAY,CAAC;wBAAC;wBAC/C,WAAW,kBAAkB,SAAS;oBAC1C;gBACJ;gBACA,IAAI,OAAO,MAAM,KAAK,GAAG;oBACrB,OAAO;gBACX;gBACA,OAAO;YACX;YACA,QAAQ,MAAM,GAAG;YACjB,SAAS,qBAAqB,MAAM,EAAE,qBAAqB,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU;gBACtF,IAAI;gBACJ,6GAA6G;gBAC7G,+FAA+F;gBAC/F,IAAI;gBACJ,IAAI;gBACJ,IAAI;oBACC,KAAK,iBAAiB,SAAS,UAAU,YAAY,SAAS,YAAY,GAAG,SAAS,EAAE,aAAa,GAAG,UAAU;gBACvH,EACA,OAAO,GAAG;oBACN,OAAO;gBACX;gBACA,iCAAiC;gBACjC,IAAI,2BAA2B;oBAC3B,GAAG,SAAS,CAAC,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC;oBACxC,GAAG,SAAS,CAAC,GAAG,QAAQ,CAAC,GAAG,WAAW,CAAC;gBAC5C;gBACA,IAAI,+BAAgC,CAAC,SAAS,SAAS,cAAc,SAAS,SAAS,SAAS,IAAI,IAAI;gBACxG,IAAI,sBAAsB,IAAK,IAAI;gBACnC,IAAI,2BAA2B;oBAC3B,GAAG,QAAQ,CAAC,GAAG,sBAAsB,CAAC,yBAAyB,CAAC,GAAG,QAAQ,CAAC;oBAC5E,GAAG,QAAQ,CAAC,GAAG,sBAAsB,CAAC,yBAAyB,CAAC,GAAG,QAAQ,CAAC;gBAChF;gBACA,IAAI,oBAAoB,sBACnB,GAAG,CAAC,SAAU,CAAC;oBAChB,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,MAAM,CAAC,MAAM,GAAG,EAAE,MAAM,CAAC,IAAI,IAAI;oBACxE,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI;oBACrC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,KAAK;wBAC3C;oBACJ;oBACA,IAAI,UAAU;wBAAC,EAAE,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,MAAM;wBAAE,EAAE,MAAM,CAAC,IAAI,GAAG,EAAE,MAAM,CAAC,MAAM;wBAAG,EAAE,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG;qBAAG;oBACtG,IAAI,OAAO,IAAI,WAAW,QAAQ,MAAM;oBACxC,IAAI,YAAY,aAAa;wBAAE,GAAG,KAAK,KAAK,CAAC;wBAAI,GAAG,KAAK,KAAK,CAAC;oBAAG,GAAG;wBAAC;wBAAG;wBAAG;qBAAE,EAAE;oBAChF,IAAI,QAAQ,YAAY,SAAS;wBAAE,GAAG;wBAAG,GAAG;oBAAE,GAAG;oBACjD,OAAO;wBAAE,GAAG;wBAAG,GAAG;wBAAG,OAAO;oBAAM;gBACtC,GACK,MAAM,CAAC,SAAU,CAAC;oBAAI,OAAO,CAAC,CAAC;gBAAG,GAClC,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;oBAAI,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;gBAAE;gBACtD,2HAA2H;gBAC3H,qCAAqC;gBACrC,IAAI,mBAAmB,gCAAgC,MAAM,kBAAkB,MAAM,GAAG,iBAAiB,CAAC,EAAE,GAAG;gBAC/G,OAAO;oBAAE,kBAAkB;oBAAkB,WAAW;gBAAU;YACtE;QAGA,GAAG,GAAG;KACI,CAAC,CAAC,UAAU;AACtB", "ignoreList": [0], "debugId": null}}]}