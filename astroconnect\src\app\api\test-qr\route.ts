import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Get all users with their QR tokens
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        qrToken: true,
        zodiacSign: true
      }
    });

    // Get QR mappings
    const qrMappings = await prisma.qrCodeMapping.findMany({
      select: {
        qrToken: true,
        userId: true,
        scanCount: true,
        lastScanned: true
      }
    });

    // Create test URLs for each user
    const testData = users.map(user => {
      const mapping = qrMappings.find(m => m.userId === user.id);
      return {
        user: {
          id: user.id,
          name: user.name,
          zodiacSign: user.zodiacSign
        },
        qrToken: user.qrToken,
        qrUrl: `http://localhost:3000/qr/${user.qrToken}`,
        mapping: mapping ? {
          scanCount: mapping.scanCount,
          lastScanned: mapping.lastScanned
        } : null
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        users: testData,
        totalUsers: users.length,
        totalMappings: qrMappings.length
      },
      message: 'QR test data retrieved successfully'
    });

  } catch (error) {
    console.error('QR test data error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
