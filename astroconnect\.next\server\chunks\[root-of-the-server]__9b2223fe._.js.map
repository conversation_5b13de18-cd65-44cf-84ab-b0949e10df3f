{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Helper function to disconnect Prisma (useful for serverless)\nexport async function disconnectPrisma() {\n  await prisma.$disconnect();\n}\n\n// Helper function to check database connection\nexport async function checkDatabaseConnection() {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\n\nexport interface AdminTokenPayload {\n  adminId: string;\n  email: string;\n  name: string;\n  role: string;\n}\n\n// Hash password\nexport async function hashPassword(password: string): Promise<string> {\n  const saltRounds = 12;\n  return bcrypt.hash(password, saltRounds);\n}\n\n// Verify password\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Generate JWT token for admin\nexport function generateAdminToken(payload: AdminTokenPayload): string {\n  return jwt.sign(payload, JWT_SECRET, {\n    expiresIn: '24h',\n    issuer: 'astroconnect-admin'\n  });\n}\n\n// Verify JWT token\nexport function verifyAdminToken(token: string): AdminTokenPayload | null {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET, {\n      issuer: 'astroconnect-admin'\n    }) as AdminTokenPayload;\n    return decoded;\n  } catch (error) {\n    console.error('Token verification failed:', error);\n    return null;\n  }\n}\n\n// Extract admin token from request\nexport function getAdminFromRequest(request: NextRequest): AdminTokenPayload | null {\n  try {\n    // Check Authorization header\n    const authHeader = request.headers.get('authorization');\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n      const token = authHeader.substring(7);\n      return verifyAdminToken(token);\n    }\n\n    // Check cookie\n    const tokenCookie = request.cookies.get('admin-token');\n    if (tokenCookie) {\n      return verifyAdminToken(tokenCookie.value);\n    }\n\n    return null;\n  } catch (error) {\n    console.error('Error extracting admin from request:', error);\n    return null;\n  }\n}\n\n// Validate admin permissions\nexport function requireAdminAuth(admin: AdminTokenPayload | null): boolean {\n  return admin !== null && admin.role === 'admin';\n}\n\n// Generate secure random password for demo\nexport function generateSecurePassword(length: number = 12): string {\n  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';\n  let password = '';\n  for (let i = 0; i < length; i++) {\n    password += charset.charAt(Math.floor(Math.random() * charset.length));\n  }\n  return password;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAUtC,eAAe,aAAa,QAAgB;IACjD,MAAM,aAAa;IACnB,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,mBAAmB,OAA0B;IAC3D,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QACnC,WAAW;QACX,QAAQ;IACV;AACF;AAGO,SAAS,iBAAiB,KAAa;IAC5C,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,YAAY;YAC5C,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,OAAoB;IACtD,IAAI;QACF,6BAA6B;QAC7B,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;YAClD,MAAM,QAAQ,WAAW,SAAS,CAAC;YACnC,OAAO,iBAAiB;QAC1B;QAEA,eAAe;QACf,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QACxC,IAAI,aAAa;YACf,OAAO,iBAAiB,YAAY,KAAK;QAC3C;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;IACT;AACF;AAGO,SAAS,iBAAiB,KAA+B;IAC9D,OAAO,UAAU,QAAQ,MAAM,IAAI,KAAK;AAC1C;AAGO,SAAS,uBAAuB,SAAiB,EAAE;IACxD,MAAM,UAAU;IAChB,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,YAAY,QAAQ,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM;IACtE;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/admin/activity/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { prisma } from '@/lib/prisma';\nimport { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';\nimport { ApiResponse } from '@/types';\n\ninterface ActivityItem {\n  id: string;\n  action: string;\n  user: string;\n  time: string;\n  type: 'user_registered' | 'qr_scanned' | 'horoscope_updated' | 'content_added';\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Check admin authentication\n    const admin = getAdminFromRequest(request);\n    if (!requireAdminAuth(admin)) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Unauthorized - Admin access required'\n      }, { status: 401 });\n    }\n\n    const activities: ActivityItem[] = [];\n\n    // Get recent user registrations (last 10)\n    const recentUsers = await prisma.user.findMany({\n      orderBy: { createdAt: 'desc' },\n      take: 5,\n      select: {\n        id: true,\n        name: true,\n        email: true,\n        createdAt: true\n      }\n    });\n\n    recentUsers.forEach(user => {\n      activities.push({\n        id: `user_${user.id}`,\n        action: 'New user registered',\n        user: user.email || user.name,\n        time: formatTimeAgo(user.createdAt),\n        type: 'user_registered'\n      });\n    });\n\n    // Get recent QR code scans (last 5)\n    const recentScans = await prisma.qrCodeMapping.findMany({\n      where: {\n        lastScanned: {\n          not: null\n        }\n      },\n      orderBy: { lastScanned: 'desc' },\n      take: 5,\n      include: {\n        user: {\n          select: {\n            name: true,\n            email: true\n          }\n        }\n      }\n    });\n\n    recentScans.forEach(scan => {\n      if (scan.lastScanned) {\n        activities.push({\n          id: `scan_${scan.id}`,\n          action: 'QR code scanned',\n          user: scan.user.email || scan.user.name,\n          time: formatTimeAgo(scan.lastScanned),\n          type: 'qr_scanned'\n        });\n      }\n    });\n\n    // Get recent horoscope updates (last 3)\n    const recentHoroscopes = await prisma.horoscope.findMany({\n      orderBy: { createdAt: 'desc' },\n      take: 3,\n      select: {\n        id: true,\n        zodiacSign: true,\n        type: true,\n        createdAt: true\n      }\n    });\n\n    recentHoroscopes.forEach(horoscope => {\n      activities.push({\n        id: `horoscope_${horoscope.id}`,\n        action: `${horoscope.type} horoscope updated`,\n        user: 'Admin',\n        time: formatTimeAgo(horoscope.createdAt),\n        type: 'horoscope_updated'\n      });\n    });\n\n    // Sort all activities by time (most recent first)\n    activities.sort((a, b) => {\n      const timeA = parseTimeAgo(a.time);\n      const timeB = parseTimeAgo(b.time);\n      return timeA - timeB;\n    });\n\n    // Return top 8 activities\n    const topActivities = activities.slice(0, 8);\n\n    return NextResponse.json<ApiResponse<ActivityItem[]>>({\n      success: true,\n      data: topActivities,\n      message: 'Recent activities retrieved successfully'\n    });\n\n  } catch (error) {\n    console.error('Recent activity error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n\nfunction formatTimeAgo(date: Date): string {\n  const now = new Date();\n  const diffInMs = now.getTime() - date.getTime();\n  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));\n  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));\n  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\n\n  if (diffInMinutes < 1) {\n    return 'Just now';\n  } else if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;\n  } else if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;\n  } else {\n    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;\n  }\n}\n\nfunction parseTimeAgo(timeStr: string): number {\n  if (timeStr === 'Just now') return 0;\n  \n  const match = timeStr.match(/(\\d+)\\s+(minute|hour|day)s?\\s+ago/);\n  if (!match) return 0;\n  \n  const value = parseInt(match[1]);\n  const unit = match[2];\n  \n  switch (unit) {\n    case 'minute': return value;\n    case 'hour': return value * 60;\n    case 'day': return value * 60 * 24;\n    default: return 0;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAWO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,6BAA6B;QAC7B,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;QAClC,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,aAA6B,EAAE;QAErC,0CAA0C;QAC1C,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC7C,SAAS;gBAAE,WAAW;YAAO;YAC7B,MAAM;YACN,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,WAAW;YACb;QACF;QAEA,YAAY,OAAO,CAAC,CAAA;YAClB,WAAW,IAAI,CAAC;gBACd,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACrB,QAAQ;gBACR,MAAM,KAAK,KAAK,IAAI,KAAK,IAAI;gBAC7B,MAAM,cAAc,KAAK,SAAS;gBAClC,MAAM;YACR;QACF;QAEA,oCAAoC;QACpC,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;YACtD,OAAO;gBACL,aAAa;oBACX,KAAK;gBACP;YACF;YACA,SAAS;gBAAE,aAAa;YAAO;YAC/B,MAAM;YACN,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;QACF;QAEA,YAAY,OAAO,CAAC,CAAA;YAClB,IAAI,KAAK,WAAW,EAAE;gBACpB,WAAW,IAAI,CAAC;oBACd,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;oBACrB,QAAQ;oBACR,MAAM,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,CAAC,IAAI;oBACvC,MAAM,cAAc,KAAK,WAAW;oBACpC,MAAM;gBACR;YACF;QACF;QAEA,wCAAwC;QACxC,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACvD,SAAS;gBAAE,WAAW;YAAO;YAC7B,MAAM;YACN,QAAQ;gBACN,IAAI;gBACJ,YAAY;gBACZ,MAAM;gBACN,WAAW;YACb;QACF;QAEA,iBAAiB,OAAO,CAAC,CAAA;YACvB,WAAW,IAAI,CAAC;gBACd,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE;gBAC/B,QAAQ,GAAG,UAAU,IAAI,CAAC,kBAAkB,CAAC;gBAC7C,MAAM;gBACN,MAAM,cAAc,UAAU,SAAS;gBACvC,MAAM;YACR;QACF;QAEA,kDAAkD;QAClD,WAAW,IAAI,CAAC,CAAC,GAAG;YAClB,MAAM,QAAQ,aAAa,EAAE,IAAI;YACjC,MAAM,QAAQ,aAAa,EAAE,IAAI;YACjC,OAAO,QAAQ;QACjB;QAEA,0BAA0B;QAC1B,MAAM,gBAAgB,WAAW,KAAK,CAAC,GAAG;QAE1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAA8B;YACpD,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEA,SAAS,cAAc,IAAU;IAC/B,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;IAC7C,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE;IACtD,MAAM,cAAc,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,EAAE;IACzD,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAE7D,IAAI,gBAAgB,GAAG;QACrB,OAAO;IACT,OAAO,IAAI,gBAAgB,IAAI;QAC7B,OAAO,GAAG,cAAc,OAAO,EAAE,kBAAkB,IAAI,KAAK,IAAI,IAAI,CAAC;IACvE,OAAO,IAAI,cAAc,IAAI;QAC3B,OAAO,GAAG,YAAY,KAAK,EAAE,gBAAgB,IAAI,KAAK,IAAI,IAAI,CAAC;IACjE,OAAO;QACL,OAAO,GAAG,WAAW,IAAI,EAAE,eAAe,IAAI,KAAK,IAAI,IAAI,CAAC;IAC9D;AACF;AAEA,SAAS,aAAa,OAAe;IACnC,IAAI,YAAY,YAAY,OAAO;IAEnC,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,QAAQ,SAAS,KAAK,CAAC,EAAE;IAC/B,MAAM,OAAO,KAAK,CAAC,EAAE;IAErB,OAAQ;QACN,KAAK;YAAU,OAAO;QACtB,KAAK;YAAQ,OAAO,QAAQ;QAC5B,KAAK;YAAO,OAAO,QAAQ,KAAK;QAChC;YAAS,OAAO;IAClB;AACF", "debugId": null}}]}