{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/scan/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Html5Qrcode } from 'html5-qrcode';\nimport { Camera, Upload, ArrowLeft, RotateCcw } from 'lucide-react';\n\nexport default function ScanPage() {\n  const router = useRouter();\n  const scannerRef = useRef<Html5Qrcode | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const [isScanning, setIsScanning] = useState(false);\n  const [cameras, setCameras] = useState<any[]>([]);\n  const [selectedCamera, setSelectedCamera] = useState<string>('');\n  const [error, setError] = useState<string>('');\n  const [scanResult, setScanResult] = useState<string>('');\n\n  useEffect(() => {\n    // Initialize camera list on component mount\n    initializeCameras();\n    \n    return () => {\n      // Cleanup on unmount\n      stopScanning();\n    };\n  }, []);\n\n  const initializeCameras = async () => {\n    try {\n      console.log('🎥 Initializing cameras...');\n      \n      // Request permission first\n      const stream = await navigator.mediaDevices.getUserMedia({ video: true });\n      stream.getTracks().forEach(track => track.stop());\n      \n      // Get available cameras\n      const availableCameras = await Html5Qrcode.getCameras();\n      console.log('📷 Available cameras:', availableCameras);\n      \n      setCameras(availableCameras);\n      \n      if (availableCameras.length > 0) {\n        // Prefer back camera\n        const backCamera = availableCameras.find(camera => \n          camera.label.toLowerCase().includes('back') ||\n          camera.label.toLowerCase().includes('rear') ||\n          camera.label.toLowerCase().includes('environment')\n        ) || availableCameras[0];\n        \n        setSelectedCamera(backCamera.id);\n      }\n    } catch (error: any) {\n      console.error('❌ Camera initialization failed:', error);\n      setError(`Camera access failed: ${error.message}`);\n    }\n  };\n\n  const startScanning = async () => {\n    if (!selectedCamera) {\n      setError('No camera selected');\n      return;\n    }\n\n    try {\n      setError('');\n      setIsScanning(true);\n      \n      // Create scanner instance\n      scannerRef.current = new Html5Qrcode('qr-reader');\n      \n      const config = {\n        fps: 10,\n        qrbox: { width: 250, height: 250 },\n        aspectRatio: 1.0\n      };\n\n      await scannerRef.current.start(\n        selectedCamera,\n        config,\n        (decodedText) => {\n          console.log('✅ QR Code scanned:', decodedText);\n          setScanResult(decodedText);\n          handleScanSuccess(decodedText);\n        },\n        (errorMessage) => {\n          // Ignore scan errors - they're normal when no QR code is visible\n        }\n      );\n\n      console.log('✅ Camera scanning started');\n    } catch (error: any) {\n      console.error('❌ Failed to start scanning:', error);\n      setError(`Failed to start camera: ${error.message}`);\n      setIsScanning(false);\n    }\n  };\n\n  const stopScanning = async () => {\n    if (scannerRef.current) {\n      try {\n        await scannerRef.current.stop();\n        scannerRef.current = null;\n        console.log('🛑 Camera scanning stopped');\n      } catch (error) {\n        console.log('ℹ️ Scanner was already stopped');\n      }\n    }\n    setIsScanning(false);\n  };\n\n  const handleScanSuccess = async (qrData: string) => {\n    console.log('🎯 Processing QR code:', qrData);\n\n    try {\n      // Stop scanning\n      await stopScanning();\n\n      // Check if we came from the main page\n      const hasCallback = sessionStorage.getItem('qr-scan-callback');\n      if (hasCallback) {\n        sessionStorage.removeItem('qr-scan-callback');\n        // Go back to main page and let it handle the result\n        router.push(`/?qr=${encodeURIComponent(qrData)}`);\n        return;\n      }\n\n      // Extract token from QR data\n      let token = qrData;\n      if (qrData.includes('/qr/')) {\n        token = qrData.split('/qr/')[1];\n      }\n\n      // Redirect to dashboard with token\n      router.push(`/qr/${token}`);\n    } catch (error) {\n      console.error('❌ Error processing QR code:', error);\n      setError('Failed to process QR code');\n    }\n  };\n\n  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    try {\n      setError('');\n      \n      const html5QrCode = new Html5Qrcode('qr-reader');\n      const result = await html5QrCode.scanFile(file, true);\n      \n      console.log('✅ QR Code from file:', result);\n      setScanResult(result);\n      handleScanSuccess(result);\n    } catch (error: any) {\n      console.error('❌ File scan failed:', error);\n      setError('Could not read QR code from image. Please try a clearer image.');\n    }\n  };\n\n  const switchCamera = async () => {\n    if (cameras.length <= 1) return;\n    \n    const currentIndex = cameras.findIndex(cam => cam.id === selectedCamera);\n    const nextIndex = (currentIndex + 1) % cameras.length;\n    const nextCamera = cameras[nextIndex];\n    \n    if (isScanning) {\n      await stopScanning();\n      setSelectedCamera(nextCamera.id);\n      setTimeout(() => startScanning(), 500);\n    } else {\n      setSelectedCamera(nextCamera.id);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"flex items-center gap-4 mb-8\">\n          <button\n            onClick={() => router.push('/')}\n            className=\"p-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors\"\n          >\n            <ArrowLeft className=\"text-white\" size={20} />\n          </button>\n          <h1 className=\"text-2xl font-bold text-white\">Scan QR Code</h1>\n        </div>\n\n        {/* Scanner Container */}\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"bg-gray-800 rounded-lg p-6 mb-6\">\n            <div id=\"qr-reader\" className=\"w-full mb-4\"></div>\n            \n            {error && (\n              <div className=\"bg-red-900/50 border border-red-500 rounded-lg p-4 mb-4\">\n                <p className=\"text-red-200 text-sm\">{error}</p>\n              </div>\n            )}\n\n            {scanResult && (\n              <div className=\"bg-green-900/50 border border-green-500 rounded-lg p-4 mb-4\">\n                <p className=\"text-green-200 text-sm\">Scanned: {scanResult}</p>\n              </div>\n            )}\n\n            {/* Camera Controls */}\n            <div className=\"space-y-4\">\n              {cameras.length > 0 && (\n                <div>\n                  <label className=\"block text-white text-sm font-medium mb-2\">\n                    Select Camera\n                  </label>\n                  <select\n                    value={selectedCamera}\n                    onChange={(e) => setSelectedCamera(e.target.value)}\n                    className=\"w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600\"\n                    disabled={isScanning}\n                  >\n                    {cameras.map((camera) => (\n                      <option key={camera.id} value={camera.id}>\n                        {camera.label || `Camera ${camera.id.substring(0, 8)}`}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              )}\n\n              <div className=\"flex gap-3\">\n                <button\n                  onClick={isScanning ? stopScanning : startScanning}\n                  disabled={!selectedCamera}\n                  className={`flex-1 py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 ${\n                    isScanning\n                      ? 'bg-red-600 hover:bg-red-700 text-white'\n                      : 'bg-purple-600 hover:bg-purple-700 text-white disabled:bg-gray-600 disabled:text-gray-400'\n                  }`}\n                >\n                  <Camera size={20} />\n                  {isScanning ? 'Stop Camera' : 'Start Camera'}\n                </button>\n\n                {cameras.length > 1 && (\n                  <button\n                    onClick={switchCamera}\n                    className=\"p-3 bg-gray-600 hover:bg-gray-500 rounded-lg transition-colors\"\n                    title=\"Switch Camera\"\n                  >\n                    <RotateCcw className=\"text-white\" size={20} />\n                  </button>\n                )}\n              </div>\n\n              {/* File Upload */}\n              <div className=\"border-t border-gray-600 pt-4\">\n                <input\n                  ref={fileInputRef}\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleFileUpload}\n                  className=\"hidden\"\n                />\n                <button\n                  onClick={() => fileInputRef.current?.click()}\n                  className=\"w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center gap-2\"\n                >\n                  <Upload size={20} />\n                  Upload QR Image\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Instructions */}\n          <div className=\"bg-blue-900/20 border border-blue-500/30 rounded-lg p-4\">\n            <h3 className=\"font-medium text-blue-300 mb-2\">Instructions</h3>\n            <ul className=\"text-sm text-blue-200 space-y-1\">\n              <li>• Point your camera at the QR code</li>\n              <li>• Make sure the QR code is well-lit and in focus</li>\n              <li>• Or upload an image containing a QR code</li>\n              <li>• You'll be automatically redirected after scanning</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAsB;IAC9C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,4CAA4C;YAC5C;YAEA;sCAAO;oBACL,qBAAqB;oBACrB;gBACF;;QACF;6BAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,2BAA2B;YAC3B,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAK;YACvE,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAE9C,wBAAwB;YACxB,MAAM,mBAAmB,MAAM,4JAAA,CAAA,cAAW,CAAC,UAAU;YACrD,QAAQ,GAAG,CAAC,yBAAyB;YAErC,WAAW;YAEX,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,qBAAqB;gBACrB,MAAM,aAAa,iBAAiB,IAAI,CAAC,CAAA,SACvC,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WACpC,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WACpC,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACjC,gBAAgB,CAAC,EAAE;gBAExB,kBAAkB,WAAW,EAAE;YACjC;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,mCAAmC;YACjD,SAAS,CAAC,sBAAsB,EAAE,MAAM,OAAO,EAAE;QACnD;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,gBAAgB;YACnB,SAAS;YACT;QACF;QAEA,IAAI;YACF,SAAS;YACT,cAAc;YAEd,0BAA0B;YAC1B,WAAW,OAAO,GAAG,IAAI,4JAAA,CAAA,cAAW,CAAC;YAErC,MAAM,SAAS;gBACb,KAAK;gBACL,OAAO;oBAAE,OAAO;oBAAK,QAAQ;gBAAI;gBACjC,aAAa;YACf;YAEA,MAAM,WAAW,OAAO,CAAC,KAAK,CAC5B,gBACA,QACA,CAAC;gBACC,QAAQ,GAAG,CAAC,sBAAsB;gBAClC,cAAc;gBACd,kBAAkB;YACpB,GACA,CAAC;YACC,iEAAiE;YACnE;YAGF,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;YACnD,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW,OAAO,EAAE;YACtB,IAAI;gBACF,MAAM,WAAW,OAAO,CAAC,IAAI;gBAC7B,WAAW,OAAO,GAAG;gBACrB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC;YACd;QACF;QACA,cAAc;IAChB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,QAAQ,GAAG,CAAC,0BAA0B;QAEtC,IAAI;YACF,gBAAgB;YAChB,MAAM;YAEN,sCAAsC;YACtC,MAAM,cAAc,eAAe,OAAO,CAAC;YAC3C,IAAI,aAAa;gBACf,eAAe,UAAU,CAAC;gBAC1B,oDAAoD;gBACpD,OAAO,IAAI,CAAC,CAAC,KAAK,EAAE,mBAAmB,SAAS;gBAChD;YACF;YAEA,6BAA6B;YAC7B,IAAI,QAAQ;YACZ,IAAI,OAAO,QAAQ,CAAC,SAAS;gBAC3B,QAAQ,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE;YACjC;YAEA,mCAAmC;YACnC,OAAO,IAAI,CAAC,CAAC,IAAI,EAAE,OAAO;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS;QACX;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,SAAS;YAET,MAAM,cAAc,IAAI,4JAAA,CAAA,cAAW,CAAC;YACpC,MAAM,SAAS,MAAM,YAAY,QAAQ,CAAC,MAAM;YAEhD,QAAQ,GAAG,CAAC,wBAAwB;YACpC,cAAc;YACd,kBAAkB;QACpB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS;QACX;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,QAAQ,MAAM,IAAI,GAAG;QAEzB,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QACzD,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,QAAQ,MAAM;QACrD,MAAM,aAAa,OAAO,CAAC,UAAU;QAErC,IAAI,YAAY;YACd,MAAM;YACN,kBAAkB,WAAW,EAAE;YAC/B,WAAW,IAAM,iBAAiB;QACpC,OAAO;YACL,kBAAkB,WAAW,EAAE;QACjC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;gCAAa,MAAM;;;;;;;;;;;sCAE1C,6LAAC;4BAAG,WAAU;sCAAgC;;;;;;;;;;;;8BAIhD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,IAAG;oCAAY,WAAU;;;;;;gCAE7B,uBACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;gCAIxC,4BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;4CAAyB;4CAAU;;;;;;;;;;;;8CAKpD,6LAAC;oCAAI,WAAU;;wCACZ,QAAQ,MAAM,GAAG,mBAChB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA4C;;;;;;8DAG7D,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,WAAU;oDACV,UAAU;8DAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;4DAAuB,OAAO,OAAO,EAAE;sEACrC,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,SAAS,CAAC,GAAG,IAAI;2DAD3C,OAAO,EAAE;;;;;;;;;;;;;;;;sDAQ9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,aAAa,eAAe;oDACrC,UAAU,CAAC;oDACX,WAAW,CAAC,iGAAiG,EAC3G,aACI,2CACA,4FACJ;;sEAEF,6LAAC,yMAAA,CAAA,SAAM;4DAAC,MAAM;;;;;;wDACb,aAAa,gBAAgB;;;;;;;gDAG/B,QAAQ,MAAM,GAAG,mBAChB,6LAAC;oDACC,SAAS;oDACT,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;wDAAa,MAAM;;;;;;;;;;;;;;;;;sDAM9C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,KAAK;oDACL,MAAK;oDACL,QAAO;oDACP,UAAU;oDACV,WAAU;;;;;;8DAEZ,6LAAC;oDACC,SAAS,IAAM,aAAa,OAAO,EAAE;oDACrC,WAAU;;sEAEV,6LAAC,yMAAA,CAAA,SAAM;4DAAC,MAAM;;;;;;wDAAM;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAxRwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}