const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testUserCreation() {
  try {
    // First, let's test the simple user creation endpoint
    console.log('Testing simple user creation...');
    
    const testUserResponse = await fetch('http://localhost:3000/api/test-user', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: 'Test User',
        birthDate: '1990-05-15'
      })
    });

    const testUserData = await testUserResponse.json();
    console.log('Test user creation result:', testUserData);

    if (testUserData.success) {
      console.log('✅ Simple user creation works!');
    } else {
      console.log('❌ Simple user creation failed:', testUserData.error);
    }

    // Now test the full admin user creation
    console.log('\nTesting admin user creation...');
    
    // You'll need to get a valid admin token first
    // For now, let's just test without auth to see the error
    const adminUserResponse = await fetch('http://localhost:3000/api/admin/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer invalid-token' // This will fail auth but we can see other errors
      },
      body: JSON.stringify({
        name: 'Admin Test User',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        address: '123 Test St',
        birthDate: '1990-05-15',
        birthTime: '12:00',
        zodiacSign: 'taurus',
        languagePreference: 'en'
      })
    });

    const adminUserData = await adminUserResponse.json();
    console.log('Admin user creation result:', adminUserData);

  } catch (error) {
    console.error('Test error:', error);
  }
}

testUserCreation();
