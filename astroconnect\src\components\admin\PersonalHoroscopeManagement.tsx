'use client';

import { useState, useEffect } from 'react';
import { User } from '@/types';
import { Search, Plus, Edit, Trash2, Star, Calendar, User as UserIcon } from 'lucide-react';

interface PersonalHoroscope {
  id: string;
  userId: string;
  title: string;
  content: string;
  isActive: boolean;
  language: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    zodiacSign: string;
  };
}

export default function PersonalHoroscopeManagement() {
  const [horoscopes, setHoroscopes] = useState<PersonalHoroscope[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedHoroscope, setSelectedHoroscope] = useState<PersonalHoroscope | null>(null);
  const [newHoroscope, setNewHoroscope] = useState({
    userId: '',
    title: '',
    content: '',
    language: 'en'
  });

  useEffect(() => {
    fetchHoroscopes();
    fetchUsers();
  }, []);

  const fetchHoroscopes = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/personal-horoscopes', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const data = await response.json();
      
      if (data.success) {
        setHoroscopes(data.data.horoscopes);
      }
    } catch (error) {
      console.error('Error fetching horoscopes:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/users', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const data = await response.json();
      
      if (data.success) {
        setUsers(data.data.users);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const handleAddHoroscope = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/personal-horoscopes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(newHoroscope)
      });

      const data = await response.json();

      if (data.success) {
        setHoroscopes([data.data.horoscope, ...horoscopes]);
        setNewHoroscope({
          userId: '',
          title: '',
          content: '',
          language: 'en'
        });
        setShowAddModal(false);
        alert('Personal horoscope created successfully!');
      } else {
        alert('Error creating horoscope: ' + data.error);
      }
    } catch (error) {
      console.error('Error creating horoscope:', error);
      alert('Error creating horoscope. Please try again.');
    }
  };

  const handleEditHoroscope = (horoscope: PersonalHoroscope) => {
    setSelectedHoroscope(horoscope);
    setNewHoroscope({
      userId: horoscope.userId,
      title: horoscope.title,
      content: horoscope.content,
      language: horoscope.language
    });
    setShowEditModal(true);
  };

  const handleUpdateHoroscope = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedHoroscope) return;

    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/personal-horoscopes', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          id: selectedHoroscope.id,
          title: newHoroscope.title,
          content: newHoroscope.content,
          isActive: selectedHoroscope.isActive
        })
      });

      const data = await response.json();

      if (data.success) {
        setHoroscopes(horoscopes.map(h => 
          h.id === selectedHoroscope.id ? data.data.horoscope : h
        ));
        setShowEditModal(false);
        setSelectedHoroscope(null);
        alert('Personal horoscope updated successfully!');
      } else {
        alert('Error updating horoscope: ' + data.error);
      }
    } catch (error) {
      console.error('Error updating horoscope:', error);
      alert('Error updating horoscope. Please try again.');
    }
  };

  const handleDeleteHoroscope = async (id: string) => {
    if (!confirm('Are you sure you want to delete this horoscope?')) return;

    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/personal-horoscopes?id=${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (data.success) {
        setHoroscopes(horoscopes.filter(h => h.id !== id));
        alert('Personal horoscope deleted successfully!');
      } else {
        alert('Error deleting horoscope: ' + data.error);
      }
    } catch (error) {
      console.error('Error deleting horoscope:', error);
      alert('Error deleting horoscope. Please try again.');
    }
  };

  const filteredHoroscopes = horoscopes.filter(horoscope =>
    horoscope.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    horoscope.user.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search horoscopes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>
        <button 
          onClick={() => setShowAddModal(true)}
          className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
        >
          <Plus size={16} />
          <span>Add Personal Horoscope</span>
        </button>
      </div>

      {/* Horoscopes List */}
      <div className="space-y-4">
        {filteredHoroscopes.length > 0 ? (
          filteredHoroscopes.map((horoscope) => (
            <div key={horoscope.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <Star className="text-yellow-400" size={20} />
                    <h3 className="text-lg font-bold text-white">{horoscope.title}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      horoscope.isActive 
                        ? 'bg-green-500/20 text-green-300' 
                        : 'bg-red-500/20 text-red-300'
                    }`}>
                      {horoscope.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-300 mb-3">
                    <span className="flex items-center">
                      <UserIcon size={16} className="mr-1" />
                      {horoscope.user.name}
                    </span>
                    <span className="flex items-center">
                      <Calendar size={16} className="mr-1" />
                      {new Date(horoscope.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  
                  <p className="text-gray-200 leading-relaxed line-clamp-3">
                    {horoscope.content}
                  </p>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => handleEditHoroscope(horoscope)}
                    className="text-purple-400 hover:text-purple-300 hover:bg-purple-400/10 p-2 rounded transition-all duration-200"
                    title="Edit"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => handleDeleteHoroscope(horoscope.id)}
                    className="text-red-400 hover:text-red-300 hover:bg-red-400/10 p-2 rounded transition-all duration-200"
                    title="Delete"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center">
            <Star className="mx-auto mb-4 text-gray-400" size={48} />
            <h3 className="text-xl font-semibold text-white mb-2">No Personal Horoscopes</h3>
            <p className="text-gray-300">Create personalized horoscope content for your users.</p>
          </div>
        )}
      </div>

      {/* Add/Edit Modal */}
      {(showAddModal || showEditModal) && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-xl font-bold text-white mb-4">
              {showEditModal ? 'Edit Personal Horoscope' : 'Add Personal Horoscope'}
            </h3>

            <form onSubmit={showEditModal ? handleUpdateHoroscope : handleAddHoroscope} className="space-y-4">
              {!showEditModal && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">Select User</label>
                  <select
                    value={newHoroscope.userId}
                    onChange={(e) => setNewHoroscope({ ...newHoroscope, userId: e.target.value })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    required
                  >
                    <option value="">Choose a user...</option>
                    {users.map((user) => (
                      <option key={user.id} value={user.id}>
                        {user.name} ({user.zodiacSign})
                      </option>
                    ))}
                  </select>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Title</label>
                <input
                  type="text"
                  value={newHoroscope.title}
                  onChange={(e) => setNewHoroscope({ ...newHoroscope, title: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="e.g., Your Weekly Cosmic Guidance"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Content</label>
                <textarea
                  value={newHoroscope.content}
                  onChange={(e) => setNewHoroscope({ ...newHoroscope, content: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  rows={8}
                  placeholder="Write the personalized horoscope content..."
                  required
                />
              </div>

              <div className="flex items-center space-x-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  {showEditModal ? 'Update Horoscope' : 'Create Horoscope'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowAddModal(false);
                    setShowEditModal(false);
                    setSelectedHoroscope(null);
                    setNewHoroscope({
                      userId: '',
                      title: '',
                      content: '',
                      language: 'en'
                    });
                  }}
                  className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
