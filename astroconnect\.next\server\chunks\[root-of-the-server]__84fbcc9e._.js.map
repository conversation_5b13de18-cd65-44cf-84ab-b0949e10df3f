{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Helper function to disconnect Prisma (useful for serverless)\nexport async function disconnectPrisma() {\n  await prisma.$disconnect();\n}\n\n// Helper function to check database connection\nexport async function checkDatabaseConnection() {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\n\nexport interface AdminTokenPayload {\n  adminId: string;\n  email: string;\n  name: string;\n  role: string;\n}\n\n// Hash password\nexport async function hashPassword(password: string): Promise<string> {\n  const saltRounds = 12;\n  return bcrypt.hash(password, saltRounds);\n}\n\n// Verify password\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Generate JWT token for admin\nexport function generateAdminToken(payload: AdminTokenPayload): string {\n  return jwt.sign(payload, JWT_SECRET, {\n    expiresIn: '24h',\n    issuer: 'astroconnect-admin'\n  });\n}\n\n// Verify JWT token\nexport function verifyAdminToken(token: string): AdminTokenPayload | null {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET, {\n      issuer: 'astroconnect-admin'\n    }) as AdminTokenPayload;\n    return decoded;\n  } catch (error) {\n    console.error('Token verification failed:', error);\n    return null;\n  }\n}\n\n// Extract admin token from request\nexport function getAdminFromRequest(request: NextRequest): AdminTokenPayload | null {\n  try {\n    // Check Authorization header\n    const authHeader = request.headers.get('authorization');\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n      const token = authHeader.substring(7);\n      return verifyAdminToken(token);\n    }\n\n    // Check cookie\n    const tokenCookie = request.cookies.get('admin-token');\n    if (tokenCookie) {\n      return verifyAdminToken(tokenCookie.value);\n    }\n\n    return null;\n  } catch (error) {\n    console.error('Error extracting admin from request:', error);\n    return null;\n  }\n}\n\n// Validate admin permissions\nexport function requireAdminAuth(admin: AdminTokenPayload | null): boolean {\n  return admin !== null && admin.role === 'admin';\n}\n\n// Generate secure random password for demo\nexport function generateSecurePassword(length: number = 12): string {\n  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';\n  let password = '';\n  for (let i = 0; i < length; i++) {\n    password += charset.charAt(Math.floor(Math.random() * charset.length));\n  }\n  return password;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAUtC,eAAe,aAAa,QAAgB;IACjD,MAAM,aAAa;IACnB,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,mBAAmB,OAA0B;IAC3D,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QACnC,WAAW;QACX,QAAQ;IACV;AACF;AAGO,SAAS,iBAAiB,KAAa;IAC5C,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,YAAY;YAC5C,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,OAAoB;IACtD,IAAI;QACF,6BAA6B;QAC7B,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;YAClD,MAAM,QAAQ,WAAW,SAAS,CAAC;YACnC,OAAO,iBAAiB;QAC1B;QAEA,eAAe;QACf,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QACxC,IAAI,aAAa;YACf,OAAO,iBAAiB,YAAY,KAAK;QAC3C;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;IACT;AACF;AAGO,SAAS,iBAAiB,KAA+B;IAC9D,OAAO,UAAU,QAAQ,MAAM,IAAI,KAAK;AAC1C;AAGO,SAAS,uBAAuB,SAAiB,EAAE;IACxD,MAAM,UAAU;IAChB,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,YAAY,QAAQ,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM;IACtE;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/admin/analytics/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { prisma } from '@/lib/prisma';\nimport { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';\nimport { ApiResponse } from '@/types';\n\ninterface AnalyticsData {\n  totalUsers: number;\n  activeUsers: number;\n  totalScans: number;\n  totalHoroscopes: number;\n  userGrowth: number;\n  scanGrowth: number;\n  topZodiacSigns: Array<{\n    sign: string;\n    count: number;\n    percentage: number;\n  }>;\n  languageDistribution: Array<{\n    language: string;\n    count: number;\n    percentage: number;\n  }>;\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Check admin authentication\n    const admin = getAdminFromRequest(request);\n    if (!requireAdminAuth(admin)) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Unauthorized - Admin access required'\n      }, { status: 401 });\n    }\n\n    const { searchParams } = new URL(request.url);\n    const range = searchParams.get('range') || '30d';\n\n    // Calculate date range\n    const now = new Date();\n    const daysBack = range === '7d' ? 7 : range === '30d' ? 30 : 90;\n    const startDate = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000));\n\n    // Get basic stats\n    const totalUsers = await prisma.user.count();\n    const totalHoroscopes = await prisma.horoscope.count();\n    \n    // Get total scans\n    const scanStats = await prisma.qrCodeMapping.aggregate({\n      _sum: {\n        scanCount: true\n      }\n    });\n    const totalScans = scanStats._sum.scanCount || 0;\n\n    // Get active users (users who have scanned their QR code at least once)\n    const activeUsers = await prisma.qrCodeMapping.count({\n      where: {\n        scanCount: {\n          gt: 0\n        }\n      }\n    });\n\n    // Calculate growth rates (simplified - comparing with previous period)\n    const previousPeriodStart = new Date(startDate.getTime() - (daysBack * 24 * 60 * 60 * 1000));\n    \n    const previousUsers = await prisma.user.count({\n      where: {\n        createdAt: {\n          gte: previousPeriodStart,\n          lt: startDate\n        }\n      }\n    });\n\n    const currentUsers = await prisma.user.count({\n      where: {\n        createdAt: {\n          gte: startDate\n        }\n      }\n    });\n\n    const userGrowth = previousUsers > 0 ? Math.round(((currentUsers - previousUsers) / previousUsers) * 100) : 0;\n\n    // Get previous scans for growth calculation\n    const previousScans = await prisma.qrCodeMapping.aggregate({\n      where: {\n        lastScanned: {\n          gte: previousPeriodStart,\n          lt: startDate\n        }\n      },\n      _sum: {\n        scanCount: true\n      }\n    });\n\n    const currentScans = await prisma.qrCodeMapping.aggregate({\n      where: {\n        lastScanned: {\n          gte: startDate\n        }\n      },\n      _sum: {\n        scanCount: true\n      }\n    });\n\n    const prevScanCount = previousScans._sum.scanCount || 0;\n    const currScanCount = currentScans._sum.scanCount || 0;\n    const scanGrowth = prevScanCount > 0 ? Math.round(((currScanCount - prevScanCount) / prevScanCount) * 100) : 0;\n\n    // Get zodiac sign distribution\n    const zodiacStats = await prisma.user.groupBy({\n      by: ['zodiacSign'],\n      _count: {\n        zodiacSign: true\n      },\n      orderBy: {\n        _count: {\n          zodiacSign: 'desc'\n        }\n      },\n      take: 5\n    });\n\n    const topZodiacSigns = zodiacStats.map(stat => ({\n      sign: stat.zodiacSign,\n      count: stat._count.zodiacSign,\n      percentage: Math.round((stat._count.zodiacSign / totalUsers) * 100)\n    }));\n\n    // Get language distribution\n    const languageStats = await prisma.user.groupBy({\n      by: ['languagePreference'],\n      _count: {\n        languagePreference: true\n      }\n    });\n\n    const languageDistribution = languageStats.map(stat => ({\n      language: stat.languagePreference,\n      count: stat._count.languagePreference,\n      percentage: Math.round((stat._count.languagePreference / totalUsers) * 100)\n    }));\n\n    const analytics: AnalyticsData = {\n      totalUsers,\n      activeUsers,\n      totalScans,\n      totalHoroscopes,\n      userGrowth,\n      scanGrowth,\n      topZodiacSigns,\n      languageDistribution\n    };\n\n    return NextResponse.json<ApiResponse<AnalyticsData>>({\n      success: true,\n      data: analytics,\n      message: 'Analytics data retrieved successfully'\n    });\n\n  } catch (error) {\n    console.error('Analytics error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAsBO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,6BAA6B;QAC7B,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;QAClC,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC,YAAY;QAE3C,uBAAuB;QACvB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,UAAU,OAAO,IAAI,UAAU,QAAQ,KAAK;QAC7D,MAAM,YAAY,IAAI,KAAK,IAAI,OAAO,KAAM,WAAW,KAAK,KAAK,KAAK;QAEtE,kBAAkB;QAClB,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK;QAC1C,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,KAAK;QAEpD,kBAAkB;QAClB,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,SAAS,CAAC;YACrD,MAAM;gBACJ,WAAW;YACb;QACF;QACA,MAAM,aAAa,UAAU,IAAI,CAAC,SAAS,IAAI;QAE/C,wEAAwE;QACxE,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,KAAK,CAAC;YACnD,OAAO;gBACL,WAAW;oBACT,IAAI;gBACN;YACF;QACF;QAEA,uEAAuE;QACvE,MAAM,sBAAsB,IAAI,KAAK,UAAU,OAAO,KAAM,WAAW,KAAK,KAAK,KAAK;QAEtF,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC5C,OAAO;gBACL,WAAW;oBACT,KAAK;oBACL,IAAI;gBACN;YACF;QACF;QAEA,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC3C,OAAO;gBACL,WAAW;oBACT,KAAK;gBACP;YACF;QACF;QAEA,MAAM,aAAa,gBAAgB,IAAI,KAAK,KAAK,CAAC,AAAC,CAAC,eAAe,aAAa,IAAI,gBAAiB,OAAO;QAE5G,4CAA4C;QAC5C,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,SAAS,CAAC;YACzD,OAAO;gBACL,aAAa;oBACX,KAAK;oBACL,IAAI;gBACN;YACF;YACA,MAAM;gBACJ,WAAW;YACb;QACF;QAEA,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,SAAS,CAAC;YACxD,OAAO;gBACL,aAAa;oBACX,KAAK;gBACP;YACF;YACA,MAAM;gBACJ,WAAW;YACb;QACF;QAEA,MAAM,gBAAgB,cAAc,IAAI,CAAC,SAAS,IAAI;QACtD,MAAM,gBAAgB,aAAa,IAAI,CAAC,SAAS,IAAI;QACrD,MAAM,aAAa,gBAAgB,IAAI,KAAK,KAAK,CAAC,AAAC,CAAC,gBAAgB,aAAa,IAAI,gBAAiB,OAAO;QAE7G,+BAA+B;QAC/B,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,CAAC;YAC5C,IAAI;gBAAC;aAAa;YAClB,QAAQ;gBACN,YAAY;YACd;YACA,SAAS;gBACP,QAAQ;oBACN,YAAY;gBACd;YACF;YACA,MAAM;QACR;QAEA,MAAM,iBAAiB,YAAY,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC9C,MAAM,KAAK,UAAU;gBACrB,OAAO,KAAK,MAAM,CAAC,UAAU;gBAC7B,YAAY,KAAK,KAAK,CAAC,AAAC,KAAK,MAAM,CAAC,UAAU,GAAG,aAAc;YACjE,CAAC;QAED,4BAA4B;QAC5B,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,CAAC;YAC9C,IAAI;gBAAC;aAAqB;YAC1B,QAAQ;gBACN,oBAAoB;YACtB;QACF;QAEA,MAAM,uBAAuB,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACtD,UAAU,KAAK,kBAAkB;gBACjC,OAAO,KAAK,MAAM,CAAC,kBAAkB;gBACrC,YAAY,KAAK,KAAK,CAAC,AAAC,KAAK,MAAM,CAAC,kBAAkB,GAAG,aAAc;YACzE,CAAC;QAED,MAAM,YAA2B;YAC/B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAA6B;YACnD,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}