// This file initializes background services when the app starts
import { dailyReadingsScheduler } from './scheduler';

let isInitialized = false;

export function initializeServices() {
  if (isInitialized) {
    console.log('🔄 Services already initialized');
    return;
  }

  console.log('🚀 Initializing AstroConnect services...');
  
  try {
    // Initialize the daily readings scheduler
    // The scheduler will start automatically when imported
    console.log('📅 Daily readings scheduler initialized');
    
    isInitialized = true;
    console.log('✅ All services initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing services:', error);
  }
}

// Auto-initialize when this module is imported
if (typeof window === 'undefined') {
  // Only run on server side
  initializeServices();
}
