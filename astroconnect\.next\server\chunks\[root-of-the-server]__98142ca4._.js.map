{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Helper function to disconnect Prisma (useful for serverless)\nexport async function disconnectPrisma() {\n  await prisma.$disconnect();\n}\n\n// Helper function to check database connection\nexport async function checkDatabaseConnection() {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/admin/horoscopes/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { prisma } from '@/lib/prisma';\nimport { ApiResponse, Horoscope, ZodiacSign, LanguageCode, HoroscopeType } from '@/types';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { zodiacSign, type, content, date, language = 'en' } = await request.json();\n\n    if (!zodiacSign || !type || !content || !date) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Missing required fields: zodiacSign, type, content, date'\n      }, { status: 400 });\n    }\n\n    const horoscope = await prisma.horoscope.create({\n      data: {\n        zodiacSign: zodiacSign as ZodiacSign,\n        type: type as HoroscopeType,\n        content,\n        date: new Date(date),\n        language: language as LanguageCode\n      }\n    });\n\n    return NextResponse.json<ApiResponse<Horoscope>>({\n      success: true,\n      data: horoscope as Horoscope,\n      message: 'Horoscope created successfully'\n    });\n\n  } catch (error) {\n    console.error('Horoscope creation error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  const { searchParams } = new URL(request.url);\n  const zodiacSign = searchParams.get('zodiacSign');\n  const type = searchParams.get('type');\n  const date = searchParams.get('date');\n  const language = searchParams.get('language') || 'en';\n  const page = parseInt(searchParams.get('page') || '1');\n  const limit = parseInt(searchParams.get('limit') || '20');\n  const offset = (page - 1) * limit;\n\n  try {\n    // Build where clause\n    const where: any = {\n      language: language as LanguageCode\n    };\n\n    if (zodiacSign) {\n      where.zodiacSign = zodiacSign as ZodiacSign;\n    }\n\n    if (type) {\n      where.type = type as HoroscopeType;\n    }\n\n    if (date) {\n      where.date = new Date(date);\n    }\n\n    const horoscopes = await prisma.horoscope.findMany({\n      where,\n      orderBy: [\n        { date: 'desc' },\n        { zodiacSign: 'asc' }\n      ],\n      skip: offset,\n      take: limit\n    });\n\n    return NextResponse.json<ApiResponse<Horoscope[]>>({\n      success: true,\n      data: horoscopes,\n      message: 'Horoscopes retrieved successfully'\n    });\n\n  } catch (error) {\n    console.error('Horoscopes fetch error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n\nexport async function PUT(request: NextRequest) {\n  try {\n    const { id, zodiacSign, type, content, date, language } = await request.json();\n\n    if (!id) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Horoscope ID is required'\n      }, { status: 400 });\n    }\n\n    const updateData: any = {};\n    if (zodiacSign) updateData.zodiacSign = zodiacSign as ZodiacSign;\n    if (type) updateData.type = type as HoroscopeType;\n    if (content) updateData.content = content;\n    if (date) updateData.date = new Date(date);\n    if (language) updateData.language = language as LanguageCode;\n\n    const horoscope = await prisma.horoscope.update({\n      where: { id },\n      data: updateData\n    });\n\n    return NextResponse.json<ApiResponse<Horoscope>>({\n      success: true,\n      data: horoscope,\n      message: 'Horoscope updated successfully'\n    });\n\n  } catch (error) {\n    console.error('Horoscope update error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n\nexport async function DELETE(request: NextRequest) {\n  const { searchParams } = new URL(request.url);\n  const id = searchParams.get('id');\n\n  if (!id) {\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Horoscope ID is required'\n    }, { status: 400 });\n  }\n\n  try {\n    await prisma.horoscope.delete({\n      where: { id }\n    });\n\n    return NextResponse.json<ApiResponse<{ deleted: boolean }>>({\n      success: true,\n      data: { deleted: true },\n      message: 'Horoscope deleted successfully'\n    });\n\n  } catch (error) {\n    console.error('Horoscope deletion error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE/E,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YAC9C,MAAM;gBACJ,YAAY;gBACZ,MAAM;gBACN;gBACA,MAAM,IAAI,KAAK;gBACf,UAAU;YACZ;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAyB;YAC/C,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,aAAa,aAAa,GAAG,CAAC;IACpC,MAAM,OAAO,aAAa,GAAG,CAAC;IAC9B,MAAM,OAAO,aAAa,GAAG,CAAC;IAC9B,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;IACjD,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;IAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;IACpD,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;IAE5B,IAAI;QACF,qBAAqB;QACrB,MAAM,QAAa;YACjB,UAAU;QACZ;QAEA,IAAI,YAAY;YACd,MAAM,UAAU,GAAG;QACrB;QAEA,IAAI,MAAM;YACR,MAAM,IAAI,GAAG;QACf;QAEA,IAAI,MAAM;YACR,MAAM,IAAI,GAAG,IAAI,KAAK;QACxB;QAEA,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACjD;YACA,SAAS;gBACP;oBAAE,MAAM;gBAAO;gBACf;oBAAE,YAAY;gBAAM;aACrB;YACD,MAAM;YACN,MAAM;QACR;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAA2B;YACjD,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE5E,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,aAAkB,CAAC;QACzB,IAAI,YAAY,WAAW,UAAU,GAAG;QACxC,IAAI,MAAM,WAAW,IAAI,GAAG;QAC5B,IAAI,SAAS,WAAW,OAAO,GAAG;QAClC,IAAI,MAAM,WAAW,IAAI,GAAG,IAAI,KAAK;QACrC,IAAI,UAAU,WAAW,QAAQ,GAAG;QAEpC,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YAC9C,OAAO;gBAAE;YAAG;YACZ,MAAM;QACR;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAyB;YAC/C,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,OAAO,OAAoB;IAC/C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,KAAK,aAAa,GAAG,CAAC;IAE5B,IAAI,CAAC,IAAI;QACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;IAEA,IAAI;QACF,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YAC5B,OAAO;gBAAE;YAAG;QACd;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoC;YAC1D,SAAS;YACT,MAAM;gBAAE,SAAS;YAAK;YACtB,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}