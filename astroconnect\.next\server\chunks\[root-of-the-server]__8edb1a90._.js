module.exports = {

"[project]/.next-internal/server/app/api/auth/qr/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkDatabaseConnection": (()=>checkDatabaseConnection),
    "disconnectPrisma": (()=>disconnectPrisma),
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
async function disconnectPrisma() {
    await prisma.$disconnect();
}
async function checkDatabaseConnection() {
    try {
        await prisma.$queryRaw`SELECT 1`;
        return true;
    } catch (error) {
        console.error('Database connection failed:', error);
        return false;
    }
}
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/utils/qr.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "extractTokenFromUrl": (()=>extractTokenFromUrl),
    "generateQRCodeImage": (()=>generateQRCodeImage),
    "generateQRToken": (()=>generateQRToken),
    "generateQRUrl": (()=>generateQRUrl),
    "isValidQRToken": (()=>isValidQRToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_context__.i("[project]/node_modules/uuid/dist/esm/v4.js [app-route] (ecmascript) <export default as v4>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$qrcode$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/qrcode/lib/index.js [app-route] (ecmascript)");
;
;
function generateQRToken() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
}
function generateQRUrl(token, baseUrl = ("TURBOPACK compile-time value", "http://localhost:3000") || 'http://localhost:3000') {
    return `${baseUrl}/qr/${token}`;
}
async function generateQRCodeImage(token, baseUrl) {
    const url = generateQRUrl(token, baseUrl);
    try {
        const qrCodeDataUrl = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$qrcode$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].toDataURL(url, {
            width: 300,
            margin: 2,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        });
        return qrCodeDataUrl;
    } catch (error) {
        console.error('Error generating QR code:', error);
        throw new Error('Failed to generate QR code');
    }
}
function extractTokenFromUrl(url) {
    try {
        const urlObj = new URL(url);
        const pathParts = urlObj.pathname.split('/');
        const qrIndex = pathParts.indexOf('qr');
        if (qrIndex !== -1 && qrIndex < pathParts.length - 1) {
            return pathParts[qrIndex + 1];
        }
        return null;
    } catch (error) {
        console.error('Error extracting token from URL:', error);
        return null;
    }
}
function isValidQRToken(token) {
    // UUID v4 regex pattern
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(token);
}
}}),
"[project]/src/utils/zodiac.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ZODIAC_INFO": (()=>ZODIAC_INFO),
    "ZODIAC_SIGNS": (()=>ZODIAC_SIGNS),
    "getZodiacColors": (()=>getZodiacColors),
    "getZodiacFromDate": (()=>getZodiacFromDate)
});
const ZODIAC_SIGNS = [
    'aries',
    'taurus',
    'gemini',
    'cancer',
    'leo',
    'virgo',
    'libra',
    'scorpio',
    'sagittarius',
    'capricorn',
    'aquarius',
    'pisces'
];
const ZODIAC_INFO = {
    aries: {
        name: 'Aries',
        symbol: '♈',
        dates: 'Mar 21 - Apr 19',
        element: 'Fire'
    },
    taurus: {
        name: 'Taurus',
        symbol: '♉',
        dates: 'Apr 20 - May 20',
        element: 'Earth'
    },
    gemini: {
        name: 'Gemini',
        symbol: '♊',
        dates: 'May 21 - Jun 20',
        element: 'Air'
    },
    cancer: {
        name: 'Cancer',
        symbol: '♋',
        dates: 'Jun 21 - Jul 22',
        element: 'Water'
    },
    leo: {
        name: 'Leo',
        symbol: '♌',
        dates: 'Jul 23 - Aug 22',
        element: 'Fire'
    },
    virgo: {
        name: 'Virgo',
        symbol: '♍',
        dates: 'Aug 23 - Sep 22',
        element: 'Earth'
    },
    libra: {
        name: 'Libra',
        symbol: '♎',
        dates: 'Sep 23 - Oct 22',
        element: 'Air'
    },
    scorpio: {
        name: 'Scorpio',
        symbol: '♏',
        dates: 'Oct 23 - Nov 21',
        element: 'Water'
    },
    sagittarius: {
        name: 'Sagittarius',
        symbol: '♐',
        dates: 'Nov 22 - Dec 21',
        element: 'Fire'
    },
    capricorn: {
        name: 'Capricorn',
        symbol: '♑',
        dates: 'Dec 22 - Jan 19',
        element: 'Earth'
    },
    aquarius: {
        name: 'Aquarius',
        symbol: '♒',
        dates: 'Jan 20 - Feb 18',
        element: 'Air'
    },
    pisces: {
        name: 'Pisces',
        symbol: '♓',
        dates: 'Feb 19 - Mar 20',
        element: 'Water'
    }
};
function getZodiacFromDate(birthDate) {
    const date = new Date(birthDate);
    const month = date.getMonth() + 1; // 1-12
    const day = date.getDate();
    if (month === 3 && day >= 21 || month === 4 && day <= 19) return 'aries';
    if (month === 4 && day >= 20 || month === 5 && day <= 20) return 'taurus';
    if (month === 5 && day >= 21 || month === 6 && day <= 20) return 'gemini';
    if (month === 6 && day >= 21 || month === 7 && day <= 22) return 'cancer';
    if (month === 7 && day >= 23 || month === 8 && day <= 22) return 'leo';
    if (month === 8 && day >= 23 || month === 9 && day <= 22) return 'virgo';
    if (month === 9 && day >= 23 || month === 10 && day <= 22) return 'libra';
    if (month === 10 && day >= 23 || month === 11 && day <= 21) return 'scorpio';
    if (month === 11 && day >= 22 || month === 12 && day <= 21) return 'sagittarius';
    if (month === 12 && day >= 22 || month === 1 && day <= 19) return 'capricorn';
    if (month === 1 && day >= 20 || month === 2 && day <= 18) return 'aquarius';
    return 'pisces';
}
function getZodiacColors(sign) {
    const colorMap = {
        aries: [
            '#FF6B6B',
            '#FF4757'
        ],
        taurus: [
            '#2ECC71',
            '#27AE60'
        ],
        gemini: [
            '#F39C12',
            '#E67E22'
        ],
        cancer: [
            '#3498DB',
            '#2980B9'
        ],
        leo: [
            '#E74C3C',
            '#C0392B'
        ],
        virgo: [
            '#1ABC9C',
            '#16A085'
        ],
        libra: [
            '#9B59B6',
            '#8E44AD'
        ],
        scorpio: [
            '#34495E',
            '#2C3E50'
        ],
        sagittarius: [
            '#E67E22',
            '#D35400'
        ],
        capricorn: [
            '#95A5A6',
            '#7F8C8D'
        ],
        aquarius: [
            '#3498DB',
            '#2980B9'
        ],
        pisces: [
            '#1ABC9C',
            '#16A085'
        ]
    };
    return colorMap[sign];
}
}}),
"[project]/src/utils/validation.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "escapeSpecialChars": (()=>escapeSpecialChars),
    "generateRateLimitKey": (()=>generateRateLimitKey),
    "isStrongPassword": (()=>isStrongPassword),
    "isValidContent": (()=>isValidContent),
    "isValidDate": (()=>isValidDate),
    "isValidEmail": (()=>isValidEmail),
    "isValidHoroscopeType": (()=>isValidHoroscopeType),
    "isValidLanguage": (()=>isValidLanguage),
    "isValidName": (()=>isValidName),
    "isValidSearchQuery": (()=>isValidSearchQuery),
    "isValidUUID": (()=>isValidUUID),
    "isValidZodiacSign": (()=>isValidZodiacSign),
    "sanitizeHTML": (()=>sanitizeHTML),
    "sanitizeString": (()=>sanitizeString),
    "validatePagination": (()=>validatePagination),
    "validateRequestBody": (()=>validateRequestBody)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$zodiac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/zodiac.ts [app-route] (ecmascript)");
;
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
}
function isValidName(name) {
    if (!name || typeof name !== 'string') return false;
    // Remove extra whitespace and check length
    const trimmedName = name.trim();
    if (trimmedName.length < 2 || trimmedName.length > 100) return false;
    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    const nameRegex = /^[a-zA-Z\s\-']+$/;
    return nameRegex.test(trimmedName);
}
function isValidDate(dateString) {
    if (!dateString || typeof dateString !== 'string') return false;
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return false;
    // Check if date is reasonable (not in future, not too far in past)
    const now = new Date();
    const minDate = new Date('1900-01-01');
    return date <= now && date >= minDate;
}
function isValidZodiacSign(sign) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$zodiac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ZODIAC_SIGNS"].includes(sign);
}
function isValidLanguage(lang) {
    return lang === 'en' || lang === 'si';
}
function isValidHoroscopeType(type) {
    return [
        'daily',
        'weekly',
        'monthly'
    ].includes(type);
}
function isValidContent(content) {
    if (!content || typeof content !== 'string') return false;
    const trimmedContent = content.trim();
    if (trimmedContent.length < 10 || trimmedContent.length > 5000) return false;
    // Check for potentially malicious content
    const suspiciousPatterns = [
        /<script/i,
        /javascript:/i,
        /on\w+\s*=/i,
        /<iframe/i,
        /<object/i,
        /<embed/i
    ];
    return !suspiciousPatterns.some((pattern)=>pattern.test(content));
}
function isValidUUID(uuid) {
    if (!uuid || typeof uuid !== 'string') return false;
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
}
function sanitizeString(input) {
    if (!input || typeof input !== 'string') return '';
    return input.trim().replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .substring(0, 1000); // Limit length
}
function sanitizeHTML(html) {
    if (!html || typeof html !== 'string') return '';
    // Allow only basic formatting tags
    const allowedTags = [
        'p',
        'br',
        'strong',
        'em',
        'u'
    ];
    const tagRegex = /<\/?(\w+)[^>]*>/g;
    return html.replace(tagRegex, (match, tagName)=>{
        if (allowedTags.includes(tagName.toLowerCase())) {
            return match;
        }
        return '';
    });
}
function validatePagination(page, limit) {
    const parsedPage = parseInt(page || '1', 10);
    const parsedLimit = parseInt(limit || '10', 10);
    return {
        page: Math.max(1, Math.min(parsedPage, 1000)),
        limit: Math.max(1, Math.min(parsedLimit, 100)) // Max 100 items per page
    };
}
function isValidSearchQuery(query) {
    if (!query || typeof query !== 'string') return false;
    const trimmedQuery = query.trim();
    if (trimmedQuery.length < 1 || trimmedQuery.length > 100) return false;
    // Check for SQL injection patterns
    const sqlPatterns = [
        /union\s+select/i,
        /drop\s+table/i,
        /delete\s+from/i,
        /insert\s+into/i,
        /update\s+set/i,
        /--/,
        /\/\*/,
        /\*\//
    ];
    return !sqlPatterns.some((pattern)=>pattern.test(query));
}
function generateRateLimitKey(ip, endpoint, userId) {
    const baseKey = `${ip}:${endpoint}`;
    return userId ? `${baseKey}:${userId}` : baseKey;
}
function isStrongPassword(password) {
    if (!password || typeof password !== 'string') return false;
    // At least 8 characters, contains uppercase, lowercase, number, and special character
    const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return strongPasswordRegex.test(password) && password.length <= 128;
}
function validateRequestBody(body, requiredFields) {
    const errors = [];
    if (!body || typeof body !== 'object') {
        return {
            isValid: false,
            errors: [
                'Invalid request body'
            ]
        };
    }
    // Check required fields
    for (const field of requiredFields){
        if (!(field in body) || body[field] === null || body[field] === undefined) {
            errors.push(`Missing required field: ${field}`);
        }
    }
    return {
        isValid: errors.length === 0,
        errors
    };
}
function escapeSpecialChars(input) {
    if (!input || typeof input !== 'string') return '';
    return input.replace(/'/g, "''") // Escape single quotes
    .replace(/\\/g, '\\\\') // Escape backslashes
    .replace(/\0/g, '\\0') // Escape null bytes
    .replace(/\n/g, '\\n') // Escape newlines
    .replace(/\r/g, '\\r') // Escape carriage returns
    .replace(/\x1a/g, '\\Z'); // Escape ctrl+Z
}
}}),
"[project]/src/utils/security.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SECURITY_HEADERS": (()=>SECURITY_HEADERS),
    "SlidingWindowRateLimit": (()=>SlidingWindowRateLimit),
    "decryptData": (()=>decryptData),
    "encryptData": (()=>encryptData),
    "generateCSPNonce": (()=>generateCSPNonce),
    "generateHMAC": (()=>generateHMAC),
    "generateSecureQRToken": (()=>generateSecureQRToken),
    "generateSecureRandomString": (()=>generateSecureRandomString),
    "generateSecureToken": (()=>generateSecureToken),
    "generateSessionToken": (()=>generateSessionToken),
    "hashData": (()=>hashData),
    "isIPAllowed": (()=>isIPAllowed),
    "normalizeIP": (()=>normalizeIP),
    "sanitizeForHTML": (()=>sanitizeForHTML),
    "sanitizeForJS": (()=>sanitizeForJS),
    "sanitizeForSQL": (()=>sanitizeForSQL),
    "validateSessionToken": (()=>validateSessionToken),
    "verifyHMAC": (()=>verifyHMAC),
    "verifyHash": (()=>verifyHash)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
function generateSecureToken(length = 32) {
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomBytes(length).toString('hex');
}
function generateSecureQRToken() {
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomUUID();
}
function hashData(data, salt) {
    const actualSalt = salt || __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomBytes(16).toString('hex');
    const hash = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].pbkdf2Sync(data, actualSalt, 10000, 64, 'sha512').toString('hex');
    return {
        hash,
        salt: actualSalt
    };
}
function verifyHash(data, hash, salt) {
    const verifyHash = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].pbkdf2Sync(data, salt, 10000, 64, 'sha512').toString('hex');
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].timingSafeEqual(Buffer.from(hash, 'hex'), Buffer.from(verifyHash, 'hex'));
}
function encryptData(data, key) {
    const actualKey = key || __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomBytes(32).toString('hex');
    const iv = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomBytes(16);
    const cipher = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createCipher('aes-256-cbc', actualKey);
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return {
        encrypted,
        iv: iv.toString('hex'),
        key: actualKey
    };
}
function decryptData(encryptedData, key, iv) {
    const decipher = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createDecipher('aes-256-cbc', key);
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}
function generateHMAC(data, secret) {
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createHmac('sha256', secret).update(data).digest('hex');
}
function verifyHMAC(data, signature, secret) {
    const expectedSignature = generateHMAC(data, secret);
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].timingSafeEqual(Buffer.from(signature, 'hex'), Buffer.from(expectedSignature, 'hex'));
}
function generateSessionToken() {
    const timestamp = Date.now().toString();
    const randomBytes = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomBytes(16).toString('hex');
    return `${timestamp}.${randomBytes}`;
}
function validateSessionToken(token, maxAge = 24 * 60 * 60 * 1000) {
    try {
        const [timestamp, randomPart] = token.split('.');
        if (!timestamp || !randomPart) return false;
        const tokenTime = parseInt(timestamp, 10);
        const now = Date.now();
        return now - tokenTime <= maxAge;
    } catch  {
        return false;
    }
}
class SlidingWindowRateLimit {
    maxRequests;
    windowSizeMs;
    windows;
    constructor(maxRequests, windowSizeMs){
        this.maxRequests = maxRequests;
        this.windowSizeMs = windowSizeMs;
        this.windows = new Map();
    }
    isAllowed(key) {
        const now = Date.now();
        const windowStart = now - this.windowSizeMs;
        // Get or create window for this key
        let requests = this.windows.get(key) || [];
        // Remove old requests outside the window
        requests = requests.filter((timestamp)=>timestamp > windowStart);
        // Check if we're within the limit
        if (requests.length >= this.maxRequests) {
            return false;
        }
        // Add current request
        requests.push(now);
        this.windows.set(key, requests);
        return true;
    }
    getRemainingRequests(key) {
        const now = Date.now();
        const windowStart = now - this.windowSizeMs;
        const requests = this.windows.get(key) || [];
        const validRequests = requests.filter((timestamp)=>timestamp > windowStart);
        return Math.max(0, this.maxRequests - validRequests.length);
    }
    getResetTime(key) {
        const requests = this.windows.get(key) || [];
        if (requests.length === 0) return 0;
        const oldestRequest = Math.min(...requests);
        return oldestRequest + this.windowSizeMs;
    }
    cleanup() {
        const now = Date.now();
        for (const [key, requests] of this.windows.entries()){
            const windowStart = now - this.windowSizeMs;
            const validRequests = requests.filter((timestamp)=>timestamp > windowStart);
            if (validRequests.length === 0) {
                this.windows.delete(key);
            } else {
                this.windows.set(key, validRequests);
            }
        }
    }
}
function normalizeIP(ip) {
    // Handle IPv6 mapped IPv4 addresses
    if (ip.startsWith('::ffff:')) {
        return ip.substring(7);
    }
    // Handle localhost variations
    if (ip === '::1' || ip === '127.0.0.1') {
        return 'localhost';
    }
    return ip;
}
function isIPAllowed(ip, allowedRanges) {
    const normalizedIP = normalizeIP(ip);
    // For development, allow localhost
    if (("TURBOPACK compile-time value", "development") === 'development' && normalizedIP === 'localhost') {
        return true;
    }
    // Check against allowed ranges
    return allowedRanges.some((range)=>{
        if (range === normalizedIP) return true;
        // Simple CIDR check (basic implementation)
        if (range.includes('/')) {
            const [network, prefixLength] = range.split('/');
            // This is a simplified check - in production, use a proper CIDR library
            return normalizedIP.startsWith(network.split('.').slice(0, parseInt(prefixLength) / 8).join('.'));
        }
        return false;
    });
}
function generateSecureRandomString(length, charset) {
    const defaultCharset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const actualCharset = charset || defaultCharset;
    let result = '';
    const bytes = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomBytes(length);
    for(let i = 0; i < length; i++){
        result += actualCharset[bytes[i] % actualCharset.length];
    }
    return result;
}
function generateCSPNonce() {
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomBytes(16).toString('base64');
}
const SECURITY_HEADERS = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
};
function sanitizeForHTML(input) {
    return input.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#x27;').replace(/\//g, '&#x2F;');
}
function sanitizeForSQL(input) {
    return input.replace(/'/g, "''");
}
function sanitizeForJS(input) {
    return input.replace(/\\/g, '\\\\').replace(/'/g, "\\'").replace(/"/g, '\\"').replace(/\n/g, '\\n').replace(/\r/g, '\\r').replace(/\t/g, '\\t');
}
}}),
"[project]/src/app/api/auth/qr/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$qr$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/qr.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/validation.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/security.ts [app-route] (ecmascript)");
;
;
;
;
;
async function POST(request) {
    try {
        // Get client IP for logging
        const clientIP = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$security$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["normalizeIP"])(request.headers.get('x-forwarded-for') || 'unknown');
        const body = await request.json();
        const { token } = body;
        // Input validation
        if (!token || typeof token !== 'string') {
            console.warn(`Invalid token format from IP: ${clientIP}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Invalid QR token format'
            }, {
                status: 400
            });
        }
        const sanitizedToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["sanitizeString"])(token);
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$validation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isValidUUID"])(sanitizedToken) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$qr$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isValidQRToken"])(sanitizedToken)) {
            console.warn(`Invalid QR token from IP: ${clientIP}, token: ${sanitizedToken.substring(0, 8)}...`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Invalid QR token format'
            }, {
                status: 400
            });
        }
        // Find user by QR token
        const mapping = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].qrCodeMapping.findUnique({
            where: {
                qrToken: sanitizedToken
            },
            include: {
                user: true
            }
        });
        if (!mapping) {
            console.warn(`QR token not found from IP: ${clientIP}, token: ${sanitizedToken.substring(0, 8)}...`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'QR code not found or invalid'
            }, {
                status: 404
            });
        }
        // Update scan count and last scanned time
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].qrCodeMapping.update({
            where: {
                qrToken: sanitizedToken
            },
            data: {
                scanCount: mapping.scanCount + 1,
                lastScanned: new Date()
            }
        });
        const user = mapping.user;
        // Add session information with 30-minute expiration
        const sessionExpiry = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes from now
        const userWithSession = {
            ...user,
            sessionExpiry: sessionExpiry.toISOString(),
            sessionStarted: new Date().toISOString()
        };
        // Log successful authentication
        console.log(`Successful QR authentication for user: ${user.id} from IP: ${clientIP}`);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: userWithSession,
            message: 'Authentication successful'
        });
    } catch (error) {
        console.error('QR authentication error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function GET(request) {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');
    if (!token || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$qr$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["isValidQRToken"])(token)) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Invalid or missing QR token'
        }, {
            status: 400
        });
    }
    try {
        // Check if QR token exists
        const mapping = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].qrCodeMapping.findUnique({
            where: {
                qrToken: token
            }
        });
        if (!mapping) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'QR code not found'
            }, {
                status: 404
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                exists: true
            },
            message: 'QR token is valid'
        });
    } catch (error) {
        console.error('QR validation error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__8edb1a90._.js.map