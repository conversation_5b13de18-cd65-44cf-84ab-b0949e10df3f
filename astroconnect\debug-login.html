<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Login</title>
</head>
<body>
    <h1>Debug Login Test</h1>
    <button onclick="testLogin()">Test Login</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing login...';
            
            try {
                console.log('Starting login test...');
                
                const response = await fetch('/api/admin/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    }),
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                if (data.success) {
                    console.log('Login successful, storing token...');
                    localStorage.setItem('admin-token', data.data.token);
                    localStorage.setItem('admin-user', JSON.stringify(data.data.admin));
                    
                    resultDiv.innerHTML = `
                        <h2>Login Successful!</h2>
                        <p>Token: ${data.data.token.substring(0, 50)}...</p>
                        <p>Admin: ${data.data.admin.name}</p>
                        <button onclick="testRedirect()">Test Redirect to Admin</button>
                    `;
                } else {
                    resultDiv.innerHTML = `<h2>Login Failed!</h2><p>Error: ${data.error}</p>`;
                }
            } catch (error) {
                console.error('Login error:', error);
                resultDiv.innerHTML = `<h2>Network Error!</h2><p>${error.message}</p>`;
            }
        }
        
        function testRedirect() {
            console.log('Testing redirect to admin dashboard...');
            window.location.href = '/admin';
        }
    </script>
</body>
</html>
