module.exports = {

"[project]/node_modules/html5-qrcode/esm/index.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_html5-qrcode_esm_ed65c5e2._.js",
  "server/chunks/ssr/node_modules_html5-qrcode_third_party_zxing-js_umd_e9f276bd.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/html5-qrcode/esm/index.js [app-ssr] (ecmascript)");
    });
});
}}),

};