{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/admin/auth/logout/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { ApiResponse } from '@/types';\n\nexport async function POST() {\n  try {\n    const response = NextResponse.json<ApiResponse<{ success: boolean }>>({\n      success: true,\n      data: { success: true },\n      message: 'Logged out successfully'\n    });\n\n    // Clear the admin token cookie\n    response.cookies.set('admin-token', '', {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === 'production',\n      sameSite: 'strict',\n      maxAge: 0 // Expire immediately\n    });\n\n    return response;\n\n  } catch (error) {\n    console.error('Admin logout error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoC;YACpE,SAAS;YACT,MAAM;gBAAE,SAAS;YAAK;YACtB,SAAS;QACX;QAEA,+BAA+B;QAC/B,SAAS,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI;YACtC,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,EAAE,qBAAqB;QACjC;QAEA,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}