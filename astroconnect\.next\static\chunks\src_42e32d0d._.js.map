{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/QRScanner.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { Html5QrcodeScanner, Html5QrcodeScannerConfig, Html5QrcodeResult, Html5Qrcode, Html5QrcodeScanType } from 'html5-qrcode';\n\nimport { QRScannerProps } from '@/types';\nimport { Camera, Upload, X } from 'lucide-react';\n\nexport default function QRScanner({\n  onScanSuccess,\n  onScanError,\n  width = 300,\n  height = 300\n}: QRScannerProps) {\n  const scannerRef = useRef<Html5Qrcode | null>(null);\n  const [isScanning, setIsScanning] = useState(false);\n  const [scanMode, setScanMode] = useState<'camera' | 'file' | null>(null);\n  const [cameras, setCameras] = useState<any[]>([]);\n  const [permissionGranted, setPermissionGranted] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const config: Html5QrcodeScannerConfig = {\n    fps: 10,\n    qrbox: { width: 250, height: 250 },\n    aspectRatio: 1.0,\n    disableFlip: false,\n    supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],\n    showTorchButtonIfSupported: true,\n    showZoomSliderIfSupported: true,\n  };\n\n  const handleScanSuccess = (decodedText: string, decodedResult: Html5QrcodeResult) => {\n    console.log('QR Code scanned:', decodedText);\n    onScanSuccess(decodedText);\n    stopScanning();\n  };\n\n  const handleScanError = (error: string) => {\n    // Only log actual errors, not the constant \"No QR code found\" messages\n    if (!error.includes('No QR code found')) {\n      console.error('QR scan error:', error);\n      onScanError?.(error);\n    }\n  };\n\n  const requestCameraPermission = async () => {\n    try {\n      console.log('🎥 Starting camera permission request...');\n\n      // Check browser support\n      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n        throw new Error('Camera API not supported in this browser');\n      }\n\n      // Check if we're in a secure context (HTTPS or localhost)\n      if (!window.isSecureContext) {\n        console.warn('⚠️ Not in secure context, camera may not work');\n      }\n\n      console.log('🔐 Requesting camera access...');\n\n      // Request camera permission with multiple fallback strategies\n      let stream: MediaStream | null = null;\n\n      try {\n        // Strategy 1: Try with environment camera (back camera)\n        stream = await navigator.mediaDevices.getUserMedia({\n          video: {\n            facingMode: { ideal: 'environment' },\n            width: { ideal: 1280 },\n            height: { ideal: 720 }\n          }\n        });\n        console.log('✅ Got camera stream with environment facing mode');\n      } catch (envError) {\n        console.log('⚠️ Environment camera failed, trying user camera:', envError);\n\n        try {\n          // Strategy 2: Try with user camera (front camera)\n          stream = await navigator.mediaDevices.getUserMedia({\n            video: {\n              facingMode: 'user',\n              width: { ideal: 1280 },\n              height: { ideal: 720 }\n            }\n          });\n          console.log('✅ Got camera stream with user facing mode');\n        } catch (userError) {\n          console.log('⚠️ User camera failed, trying basic video:', userError);\n\n          // Strategy 3: Try with basic video constraints\n          stream = await navigator.mediaDevices.getUserMedia({\n            video: true\n          });\n          console.log('✅ Got camera stream with basic constraints');\n        }\n      }\n\n      if (!stream) {\n        throw new Error('Failed to get camera stream');\n      }\n\n      // Stop the stream immediately - we just needed permission\n      stream.getTracks().forEach(track => {\n        track.stop();\n        console.log('🛑 Stopped track:', track.label);\n      });\n\n      console.log('✅ Camera permission granted, getting available cameras...');\n\n      // Small delay to ensure camera is released\n      await new Promise(resolve => setTimeout(resolve, 100));\n\n      // Now get the list of cameras using Html5Qrcode\n      const cameras = await Html5Qrcode.getCameras();\n      console.log('📷 Available cameras:', cameras);\n\n      if (!cameras || cameras.length === 0) {\n        throw new Error('No cameras found on this device');\n      }\n\n      setCameras(cameras);\n      setPermissionGranted(true);\n\n      // Select the best camera for QR scanning\n      const backCamera = cameras.find(camera => {\n        const label = camera.label.toLowerCase();\n        return label.includes('back') ||\n               label.includes('rear') ||\n               label.includes('environment') ||\n               label.includes('facing back');\n      });\n\n      const selectedCamera = backCamera || cameras[0];\n      console.log('🎯 Selected camera:', selectedCamera);\n\n      await startCameraScanning(selectedCamera.id);\n\n    } catch (error: any) {\n      console.error('❌ Camera permission error:', error);\n      setPermissionGranted(false);\n\n      let errorMessage = 'Camera access failed. ';\n\n      switch (error.name) {\n        case 'NotAllowedError':\n          errorMessage += 'Please allow camera access in your browser and try again.';\n          break;\n        case 'NotFoundError':\n          errorMessage += 'No camera found on this device.';\n          break;\n        case 'NotSupportedError':\n          errorMessage += 'Camera not supported in this browser. Try Chrome, Firefox, or Safari.';\n          break;\n        case 'NotReadableError':\n          errorMessage += 'Camera is being used by another application. Please close other apps using the camera.';\n          break;\n        case 'OverconstrainedError':\n          errorMessage += 'Camera constraints not supported. Trying with basic settings...';\n          // Try again with basic constraints\n          setTimeout(() => requestBasicCameraPermission(), 1000);\n          return;\n        case 'SecurityError':\n          errorMessage += 'Camera access blocked by security policy. Please check your browser settings.';\n          break;\n        default:\n          errorMessage += error.message || 'Unknown error occurred.';\n      }\n\n      onScanError?.(errorMessage);\n    }\n  };\n\n  // Fallback method with minimal constraints\n  const requestBasicCameraPermission = async () => {\n    try {\n      console.log('🔄 Trying basic camera permission...');\n\n      const stream = await navigator.mediaDevices.getUserMedia({ video: true });\n      stream.getTracks().forEach(track => track.stop());\n\n      const cameras = await Html5Qrcode.getCameras();\n      if (cameras && cameras.length > 0) {\n        setCameras(cameras);\n        setPermissionGranted(true);\n        await startCameraScanning(cameras[0].id);\n      } else {\n        throw new Error('No cameras available');\n      }\n    } catch (error) {\n      console.error('❌ Basic camera permission failed:', error);\n      onScanError?.('Camera access failed completely. Please check your camera permissions and try refreshing the page.');\n    }\n  };\n\n  const startCameraScanning = async (cameraId: string) => {\n    console.log('🚀 Starting camera scanning with camera ID:', cameraId);\n\n    // Clean up any existing scanner\n    if (scannerRef.current) {\n      try {\n        await scannerRef.current.stop();\n        console.log('🛑 Stopped existing scanner');\n      } catch (error) {\n        console.log('ℹ️ Scanner was already stopped');\n      }\n      scannerRef.current = null;\n    }\n\n    setScanMode('camera');\n    setIsScanning(true);\n\n    try {\n      const element = document.getElementById('qr-scanner-container');\n      if (!element) {\n        throw new Error('QR scanner container element not found in DOM');\n      }\n\n      console.log('📦 Creating new Html5Qrcode instance...');\n      scannerRef.current = new Html5Qrcode('qr-scanner-container');\n\n      // Progressive configuration - start with optimal settings\n      const configs = [\n        // Config 1: Optimal for QR scanning\n        {\n          fps: 10,\n          qrbox: { width: 250, height: 250 },\n          aspectRatio: 1.0,\n          disableFlip: false,\n          videoConstraints: {\n            facingMode: 'environment',\n            width: { ideal: 1280, min: 640 },\n            height: { ideal: 720, min: 480 }\n          }\n        },\n        // Config 2: Fallback with basic constraints\n        {\n          fps: 8,\n          qrbox: { width: 200, height: 200 },\n          aspectRatio: 1.0,\n          disableFlip: false\n        },\n        // Config 3: Minimal configuration\n        {\n          fps: 5,\n          qrbox: { width: 150, height: 150 }\n        }\n      ];\n\n      let configIndex = 0;\n      let lastError: any = null;\n\n      const tryConfig = async (config: any): Promise<void> => {\n        console.log(`🔧 Trying camera config ${configIndex + 1}:`, config);\n\n        try {\n          await scannerRef.current!.start(\n            cameraId,\n            config,\n            handleScanSuccess,\n            handleScanError\n          );\n\n          console.log('✅ Camera scanner started successfully with config', configIndex + 1);\n          return;\n        } catch (error: any) {\n          console.warn(`⚠️ Config ${configIndex + 1} failed:`, error);\n          lastError = error;\n\n          if (configIndex < configs.length - 1) {\n            configIndex++;\n            await tryConfig(configs[configIndex]);\n          } else {\n            throw lastError;\n          }\n        }\n      };\n\n      await tryConfig(configs[0]);\n\n    } catch (error: any) {\n      console.error('❌ All camera configurations failed:', error);\n      setIsScanning(false);\n      setScanMode(null);\n\n      let errorMessage = 'Failed to start camera scanner. ';\n\n      switch (error.name) {\n        case 'NotAllowedError':\n          errorMessage += 'Camera permission was revoked. Please refresh the page and allow camera access.';\n          break;\n        case 'NotFoundError':\n          errorMessage += 'Camera not found or disconnected. Please check your camera connection.';\n          break;\n        case 'NotReadableError':\n          errorMessage += 'Camera is being used by another application. Please close other camera apps.';\n          break;\n        case 'OverconstrainedError':\n          errorMessage += 'Camera settings not supported. Please try a different camera if available.';\n          break;\n        case 'AbortError':\n          errorMessage += 'Camera operation was interrupted. Please try again.';\n          break;\n        default:\n          errorMessage += error.message || 'Unknown camera error occurred.';\n      }\n\n      onScanError?.(errorMessage);\n    }\n  };\n\n  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    setScanMode('file');\n    setIsScanning(true);\n\n    try {\n      // Use Html5Qrcode for file scanning\n      const html5QrCode = new Html5Qrcode(\"qr-reader-file\");\n\n      const result = await html5QrCode.scanFile(file, true);\n      console.log('QR Code from file:', result);\n      onScanSuccess(result);\n\n      // Clear the file input\n      if (fileInputRef.current) {\n        fileInputRef.current.value = '';\n      }\n    } catch (error: any) {\n      console.error('File scan error:', error);\n      onScanError?.('Failed to scan QR code from image. Please ensure the image contains a valid QR code.');\n    } finally {\n      setIsScanning(false);\n      setScanMode(null);\n    }\n  };\n\n  const stopScanning = async () => {\n    if (scannerRef.current) {\n      try {\n        await scannerRef.current.stop();\n        scannerRef.current = null;\n      } catch (error) {\n        console.error('Error stopping scanner:', error);\n      }\n    }\n    setIsScanning(false);\n    setScanMode(null);\n  };\n\n  // Check camera permission status on mount\n  const checkCameraPermission = async () => {\n    try {\n      if (navigator.permissions && navigator.permissions.query) {\n        const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });\n        console.log('Camera permission status:', permission.state);\n\n        if (permission.state === 'granted') {\n          setPermissionGranted(true);\n        } else if (permission.state === 'denied') {\n          setPermissionGranted(false);\n        }\n\n        // Listen for permission changes\n        permission.onchange = () => {\n          console.log('Camera permission changed to:', permission.state);\n          if (permission.state === 'granted') {\n            setPermissionGranted(true);\n          } else if (permission.state === 'denied') {\n            setPermissionGranted(false);\n            if (scannerRef.current) {\n              stopScanning();\n            }\n          }\n        };\n      }\n    } catch (error) {\n      console.log('Permission API not supported:', error);\n    }\n  };\n\n  useEffect(() => {\n    checkCameraPermission();\n\n    return () => {\n      if (scannerRef.current) {\n        scannerRef.current.stop().catch(console.error);\n      }\n    };\n  }, []);\n\n  if (!scanMode) {\n    return (\n      <div className=\"flex flex-col items-center space-y-4 p-6\">\n        <h3 className=\"text-lg font-semibold text-white mb-4\">Scan Your QR Code</h3>\n\n        {/* Permission Status Indicator */}\n        {permissionGranted === false && (\n          <div className=\"w-full max-w-md bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4\">\n            <div className=\"flex items-center gap-2 text-red-300 mb-2\">\n              <X size={16} />\n              <span className=\"text-sm font-medium\">Camera Permission Denied</span>\n            </div>\n            <p className=\"text-red-200 text-xs mb-3\">\n              Please allow camera access in your browser settings and try again.\n            </p>\n\n            {/* Troubleshooting Steps */}\n            <div className=\"text-xs text-red-200 space-y-2\">\n              <p className=\"font-medium\">Troubleshooting steps:</p>\n              <ul className=\"list-disc list-inside space-y-1 ml-2\">\n                <li>Click the camera icon in your browser's address bar</li>\n                <li>Select \"Allow\" for camera access</li>\n                <li>Refresh the page and try again</li>\n                <li>Check if another app is using your camera</li>\n              </ul>\n\n              <button\n                onClick={() => {\n                  setPermissionGranted(null);\n                  requestCameraPermission();\n                }}\n                className=\"mt-2 px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors\"\n              >\n                Try Again\n              </button>\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex flex-col sm:flex-row gap-4 w-full max-w-md\">\n          <button\n            onClick={requestCameraPermission}\n            disabled={isScanning}\n            className={`flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-colors ${\n              isScanning\n                ? 'bg-gray-600 cursor-not-allowed text-gray-400'\n                : 'bg-purple-600 hover:bg-purple-700 text-white'\n            }`}\n          >\n            <Camera size={20} />\n            {isScanning ? 'Starting Camera...' : 'Use Camera'}\n          </button>\n\n          <button\n            onClick={() => fileInputRef.current?.click()}\n            disabled={isScanning}\n            className={`flex items-center justify-center gap-2 px-6 py-3 rounded-lg transition-colors ${\n              isScanning\n                ? 'bg-gray-600 cursor-not-allowed text-gray-400'\n                : 'bg-blue-600 hover:bg-blue-700 text-white'\n            }`}\n          >\n            <Upload size={20} />\n            Upload Image\n          </button>\n        </div>\n\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          accept=\"image/*\"\n          onChange={handleFileUpload}\n          className=\"hidden\"\n        />\n\n        <div className=\"text-center mt-4\">\n          <p className=\"text-gray-300 text-sm\">\n            Point your camera at the QR code or upload an image containing the QR code\n          </p>\n\n          {/* Browser Compatibility Info */}\n          <div className=\"mt-3 text-xs text-gray-400\">\n            <p>📱 For best results, use your device's back camera</p>\n            <p>🔒 Camera access is required for QR scanning</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex flex-col items-center space-y-4 p-6\">\n      <div className=\"flex items-center justify-between w-full max-w-md\">\n        <h3 className=\"text-lg font-semibold text-white\">\n          {scanMode === 'camera' ? 'Camera Scanner' : 'Processing Image...'}\n        </h3>\n        <button\n          onClick={stopScanning}\n          className=\"text-gray-400 hover:text-white transition-colors\"\n        >\n          <X size={24} />\n        </button>\n      </div>\n\n      {scanMode === 'camera' && (\n        <div className=\"w-full max-w-md\">\n          <div\n            id=\"qr-scanner-container\"\n            className=\"w-full rounded-lg overflow-hidden border-2 border-purple-400\"\n            style={{ width, height }}\n          />\n          {cameras.length > 1 && (\n            <div className=\"mt-4\">\n              <label className=\"block text-white text-sm mb-2\">Select Camera:</label>\n              <select\n                onChange={(e) => startCameraScanning(e.target.value)}\n                className=\"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                style={{ colorScheme: 'dark' }}\n              >\n                {cameras.map((camera, index) => (\n                  <option key={camera.id} value={camera.id} className=\"bg-gray-800 text-white\">\n                    {camera.label || `Camera ${index + 1}`}\n                  </option>\n                ))}\n              </select>\n            </div>\n          )}\n        </div>\n      )}\n\n      {scanMode === 'file' && (\n        <div className=\"flex items-center justify-center w-64 h-64 border-2 border-dashed border-gray-400 rounded-lg\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2\"></div>\n            <p className=\"text-gray-300\">Scanning image...</p>\n          </div>\n        </div>\n      )}\n\n      {/* Hidden div for file scanner */}\n      <div id=\"qr-reader-file\" style={{ display: 'none' }}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAGA;AAAA;AAAA;;;AANA;;;;AAQe,SAAS,UAAU,EAChC,aAAa,EACb,WAAW,EACX,QAAQ,GAAG,EACX,SAAS,GAAG,EACG;;IACf,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAsB;IAC9C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAChD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,SAAmC;QACvC,KAAK;QACL,OAAO;YAAE,OAAO;YAAK,QAAQ;QAAI;QACjC,aAAa;QACb,aAAa;QACb,oBAAoB;YAAC,iJAAA,CAAA,sBAAmB,CAAC,gBAAgB;SAAC;QAC1D,4BAA4B;QAC5B,2BAA2B;IAC7B;IAEA,MAAM,oBAAoB,CAAC,aAAqB;QAC9C,QAAQ,GAAG,CAAC,oBAAoB;QAChC,cAAc;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,uEAAuE;QACvE,IAAI,CAAC,MAAM,QAAQ,CAAC,qBAAqB;YACvC,QAAQ,KAAK,CAAC,kBAAkB;YAChC,cAAc;QAChB;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,wBAAwB;YACxB,IAAI,CAAC,UAAU,YAAY,IAAI,CAAC,UAAU,YAAY,CAAC,YAAY,EAAE;gBACnE,MAAM,IAAI,MAAM;YAClB;YAEA,0DAA0D;YAC1D,IAAI,CAAC,OAAO,eAAe,EAAE;gBAC3B,QAAQ,IAAI,CAAC;YACf;YAEA,QAAQ,GAAG,CAAC;YAEZ,8DAA8D;YAC9D,IAAI,SAA6B;YAEjC,IAAI;gBACF,wDAAwD;gBACxD,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;oBACjD,OAAO;wBACL,YAAY;4BAAE,OAAO;wBAAc;wBACnC,OAAO;4BAAE,OAAO;wBAAK;wBACrB,QAAQ;4BAAE,OAAO;wBAAI;oBACvB;gBACF;gBACA,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,UAAU;gBACjB,QAAQ,GAAG,CAAC,qDAAqD;gBAEjE,IAAI;oBACF,kDAAkD;oBAClD,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;wBACjD,OAAO;4BACL,YAAY;4BACZ,OAAO;gCAAE,OAAO;4BAAK;4BACrB,QAAQ;gCAAE,OAAO;4BAAI;wBACvB;oBACF;oBACA,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,WAAW;oBAClB,QAAQ,GAAG,CAAC,8CAA8C;oBAE1D,+CAA+C;oBAC/C,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;wBACjD,OAAO;oBACT;oBACA,QAAQ,GAAG,CAAC;gBACd;YACF;YAEA,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,0DAA0D;YAC1D,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA;gBACzB,MAAM,IAAI;gBACV,QAAQ,GAAG,CAAC,qBAAqB,MAAM,KAAK;YAC9C;YAEA,QAAQ,GAAG,CAAC;YAEZ,2CAA2C;YAC3C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,gDAAgD;YAChD,MAAM,UAAU,MAAM,4JAAA,CAAA,cAAW,CAAC,UAAU;YAC5C,QAAQ,GAAG,CAAC,yBAAyB;YAErC,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;gBACpC,MAAM,IAAI,MAAM;YAClB;YAEA,WAAW;YACX,qBAAqB;YAErB,yCAAyC;YACzC,MAAM,aAAa,QAAQ,IAAI,CAAC,CAAA;gBAC9B,MAAM,QAAQ,OAAO,KAAK,CAAC,WAAW;gBACtC,OAAO,MAAM,QAAQ,CAAC,WACf,MAAM,QAAQ,CAAC,WACf,MAAM,QAAQ,CAAC,kBACf,MAAM,QAAQ,CAAC;YACxB;YAEA,MAAM,iBAAiB,cAAc,OAAO,CAAC,EAAE;YAC/C,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,MAAM,oBAAoB,eAAe,EAAE;QAE7C,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,qBAAqB;YAErB,IAAI,eAAe;YAEnB,OAAQ,MAAM,IAAI;gBAChB,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,gBAAgB;oBAChB,mCAAmC;oBACnC,WAAW,IAAM,gCAAgC;oBACjD;gBACF,KAAK;oBACH,gBAAgB;oBAChB;gBACF;oBACE,gBAAgB,MAAM,OAAO,IAAI;YACrC;YAEA,cAAc;QAChB;IACF;IAEA,2CAA2C;IAC3C,MAAM,+BAA+B;QACnC,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAK;YACvE,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAE9C,MAAM,UAAU,MAAM,4JAAA,CAAA,cAAW,CAAC,UAAU;YAC5C,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;gBACjC,WAAW;gBACX,qBAAqB;gBACrB,MAAM,oBAAoB,OAAO,CAAC,EAAE,CAAC,EAAE;YACzC,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,cAAc;QAChB;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,QAAQ,GAAG,CAAC,+CAA+C;QAE3D,gCAAgC;QAChC,IAAI,WAAW,OAAO,EAAE;YACtB,IAAI;gBACF,MAAM,WAAW,OAAO,CAAC,IAAI;gBAC7B,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC;YACd;YACA,WAAW,OAAO,GAAG;QACvB;QAEA,YAAY;QACZ,cAAc;QAEd,IAAI;YACF,MAAM,UAAU,SAAS,cAAc,CAAC;YACxC,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC;YACZ,WAAW,OAAO,GAAG,IAAI,4JAAA,CAAA,cAAW,CAAC;YAErC,0DAA0D;YAC1D,MAAM,UAAU;gBACd,oCAAoC;gBACpC;oBACE,KAAK;oBACL,OAAO;wBAAE,OAAO;wBAAK,QAAQ;oBAAI;oBACjC,aAAa;oBACb,aAAa;oBACb,kBAAkB;wBAChB,YAAY;wBACZ,OAAO;4BAAE,OAAO;4BAAM,KAAK;wBAAI;wBAC/B,QAAQ;4BAAE,OAAO;4BAAK,KAAK;wBAAI;oBACjC;gBACF;gBACA,4CAA4C;gBAC5C;oBACE,KAAK;oBACL,OAAO;wBAAE,OAAO;wBAAK,QAAQ;oBAAI;oBACjC,aAAa;oBACb,aAAa;gBACf;gBACA,kCAAkC;gBAClC;oBACE,KAAK;oBACL,OAAO;wBAAE,OAAO;wBAAK,QAAQ;oBAAI;gBACnC;aACD;YAED,IAAI,cAAc;YAClB,IAAI,YAAiB;YAErB,MAAM,YAAY,OAAO;gBACvB,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,cAAc,EAAE,CAAC,CAAC,EAAE;gBAE3D,IAAI;oBACF,MAAM,WAAW,OAAO,CAAE,KAAK,CAC7B,UACA,QACA,mBACA;oBAGF,QAAQ,GAAG,CAAC,qDAAqD,cAAc;oBAC/E;gBACF,EAAE,OAAO,OAAY;oBACnB,QAAQ,IAAI,CAAC,CAAC,UAAU,EAAE,cAAc,EAAE,QAAQ,CAAC,EAAE;oBACrD,YAAY;oBAEZ,IAAI,cAAc,QAAQ,MAAM,GAAG,GAAG;wBACpC;wBACA,MAAM,UAAU,OAAO,CAAC,YAAY;oBACtC,OAAO;wBACL,MAAM;oBACR;gBACF;YACF;YAEA,MAAM,UAAU,OAAO,CAAC,EAAE;QAE5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,uCAAuC;YACrD,cAAc;YACd,YAAY;YAEZ,IAAI,eAAe;YAEnB,OAAQ,MAAM,IAAI;gBAChB,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,gBAAgB;oBAChB;gBACF,KAAK;oBACH,gBAAgB;oBAChB;gBACF;oBACE,gBAAgB,MAAM,OAAO,IAAI;YACrC;YAEA,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,YAAY;QACZ,cAAc;QAEd,IAAI;YACF,oCAAoC;YACpC,MAAM,cAAc,IAAI,4JAAA,CAAA,cAAW,CAAC;YAEpC,MAAM,SAAS,MAAM,YAAY,QAAQ,CAAC,MAAM;YAChD,QAAQ,GAAG,CAAC,sBAAsB;YAClC,cAAc;YAEd,uBAAuB;YACvB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,cAAc;QAChB,SAAU;YACR,cAAc;YACd,YAAY;QACd;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW,OAAO,EAAE;YACtB,IAAI;gBACF,MAAM,WAAW,OAAO,CAAC,IAAI;gBAC7B,WAAW,OAAO,GAAG;YACvB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;QACA,cAAc;QACd,YAAY;IACd;IAEA,0CAA0C;IAC1C,MAAM,wBAAwB;QAC5B,IAAI;YACF,IAAI,UAAU,WAAW,IAAI,UAAU,WAAW,CAAC,KAAK,EAAE;gBACxD,MAAM,aAAa,MAAM,UAAU,WAAW,CAAC,KAAK,CAAC;oBAAE,MAAM;gBAA2B;gBACxF,QAAQ,GAAG,CAAC,6BAA6B,WAAW,KAAK;gBAEzD,IAAI,WAAW,KAAK,KAAK,WAAW;oBAClC,qBAAqB;gBACvB,OAAO,IAAI,WAAW,KAAK,KAAK,UAAU;oBACxC,qBAAqB;gBACvB;gBAEA,gCAAgC;gBAChC,WAAW,QAAQ,GAAG;oBACpB,QAAQ,GAAG,CAAC,iCAAiC,WAAW,KAAK;oBAC7D,IAAI,WAAW,KAAK,KAAK,WAAW;wBAClC,qBAAqB;oBACvB,OAAO,IAAI,WAAW,KAAK,KAAK,UAAU;wBACxC,qBAAqB;wBACrB,IAAI,WAAW,OAAO,EAAE;4BACtB;wBACF;oBACF;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,iCAAiC;QAC/C;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;YAEA;uCAAO;oBACL,IAAI,WAAW,OAAO,EAAE;wBACtB,WAAW,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,KAAK;oBAC/C;gBACF;;QACF;8BAAG,EAAE;IAEL,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;gBAGrD,sBAAsB,uBACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;8CACT,6LAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;sCAExC,6LAAC;4BAAE,WAAU;sCAA4B;;;;;;sCAKzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAc;;;;;;8CAC3B,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;8CAGN,6LAAC;oCACC,SAAS;wCACP,qBAAqB;wCACrB;oCACF;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAOP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAW,CAAC,8EAA8E,EACxF,aACI,iDACA,gDACJ;;8CAEF,6LAAC,yMAAA,CAAA,SAAM;oCAAC,MAAM;;;;;;gCACb,aAAa,uBAAuB;;;;;;;sCAGvC,6LAAC;4BACC,SAAS,IAAM,aAAa,OAAO,EAAE;4BACrC,UAAU;4BACV,WAAW,CAAC,8EAA8E,EACxF,aACI,iDACA,4CACJ;;8CAEF,6LAAC,yMAAA,CAAA,SAAM;oCAAC,MAAM;;;;;;gCAAM;;;;;;;;;;;;;8BAKxB,6LAAC;oBACC,KAAK;oBACL,MAAK;oBACL,QAAO;oBACP,UAAU;oBACV,WAAU;;;;;;8BAGZ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAKrC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAE;;;;;;8CACH,6LAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,aAAa,WAAW,mBAAmB;;;;;;kCAE9C,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,MAAM;;;;;;;;;;;;;;;;;YAIZ,aAAa,0BACZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,IAAG;wBACH,WAAU;wBACV,OAAO;4BAAE;4BAAO;wBAAO;;;;;;oBAExB,QAAQ,MAAM,GAAG,mBAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CAAgC;;;;;;0CACjD,6LAAC;gCACC,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAU;gCACV,OAAO;oCAAE,aAAa;gCAAO;0CAE5B,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;wCAAuB,OAAO,OAAO,EAAE;wCAAE,WAAU;kDACjD,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE,QAAQ,GAAG;uCAD3B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;YAUjC,aAAa,wBACZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAMnC,6LAAC;gBAAI,IAAG;gBAAiB,OAAO;oBAAE,SAAS;gBAAO;;;;;;;;;;;;AAGxD;GAhhBwB;KAAA", "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/PWAInstaller.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Download, X } from 'lucide-react';\n\ninterface BeforeInstallPromptEvent extends Event {\n  readonly platforms: string[];\n  readonly userChoice: Promise<{\n    outcome: 'accepted' | 'dismissed';\n    platform: string;\n  }>;\n  prompt(): Promise<void>;\n}\n\nexport default function PWAInstaller() {\n  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);\n  const [showInstallPrompt, setShowInstallPrompt] = useState(false);\n  const [isInstalled, setIsInstalled] = useState(false);\n\n  useEffect(() => {\n    // Check if app is already installed\n    if (window.matchMedia('(display-mode: standalone)').matches) {\n      setIsInstalled(true);\n      return;\n    }\n\n    // Listen for the beforeinstallprompt event\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault();\n      setDeferredPrompt(e as BeforeInstallPromptEvent);\n      setShowInstallPrompt(true);\n    };\n\n    // Listen for app installed event\n    const handleAppInstalled = () => {\n      setIsInstalled(true);\n      setShowInstallPrompt(false);\n      setDeferredPrompt(null);\n    };\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n    window.addEventListener('appinstalled', handleAppInstalled);\n\n    // Register service worker\n    if ('serviceWorker' in navigator) {\n      navigator.serviceWorker.register('/sw.js')\n        .then((registration) => {\n          console.log('Service Worker registered:', registration);\n        })\n        .catch((error) => {\n          console.error('Service Worker registration failed:', error);\n        });\n    }\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n      window.removeEventListener('appinstalled', handleAppInstalled);\n    };\n  }, []);\n\n  const handleInstallClick = async () => {\n    if (!deferredPrompt) return;\n\n    try {\n      await deferredPrompt.prompt();\n      const { outcome } = await deferredPrompt.userChoice;\n      \n      if (outcome === 'accepted') {\n        console.log('User accepted the install prompt');\n      } else {\n        console.log('User dismissed the install prompt');\n      }\n      \n      setDeferredPrompt(null);\n      setShowInstallPrompt(false);\n    } catch (error) {\n      console.error('Error during installation:', error);\n    }\n  };\n\n  const handleDismiss = () => {\n    setShowInstallPrompt(false);\n    // Don't show again for this session\n    sessionStorage.setItem('pwa-install-dismissed', 'true');\n  };\n\n  // Don't show if already installed or dismissed\n  if (isInstalled || !showInstallPrompt || sessionStorage.getItem('pwa-install-dismissed')) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4 shadow-lg z-50\">\n      <div className=\"flex items-start justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          <Download className=\"w-5 h-5 text-purple-400\" />\n          <h3 className=\"text-white font-semibold\">Install AstroConnect</h3>\n        </div>\n        <button\n          onClick={handleDismiss}\n          className=\"text-gray-400 hover:text-white transition-colors\"\n        >\n          <X size={20} />\n        </button>\n      </div>\n      \n      <p className=\"text-gray-300 text-sm mb-4\">\n        Install our app for quick access to your daily horoscope and cosmic insights!\n      </p>\n      \n      <div className=\"flex space-x-2\">\n        <button\n          onClick={handleInstallClick}\n          className=\"flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n        >\n          Install\n        </button>\n        <button\n          onClick={handleDismiss}\n          className=\"px-4 py-2 text-gray-300 hover:text-white text-sm transition-colors\"\n        >\n          Not now\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAce,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IACtF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,oCAAoC;YACpC,IAAI,OAAO,UAAU,CAAC,8BAA8B,OAAO,EAAE;gBAC3D,eAAe;gBACf;YACF;YAEA,2CAA2C;YAC3C,MAAM;oEAA4B,CAAC;oBACjC,EAAE,cAAc;oBAChB,kBAAkB;oBAClB,qBAAqB;gBACvB;;YAEA,iCAAiC;YACjC,MAAM;6DAAqB;oBACzB,eAAe;oBACf,qBAAqB;oBACrB,kBAAkB;gBACpB;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAC/C,OAAO,gBAAgB,CAAC,gBAAgB;YAExC,0BAA0B;YAC1B,IAAI,mBAAmB,WAAW;gBAChC,UAAU,aAAa,CAAC,QAAQ,CAAC,UAC9B,IAAI;8CAAC,CAAC;wBACL,QAAQ,GAAG,CAAC,8BAA8B;oBAC5C;6CACC,KAAK;8CAAC,CAAC;wBACN,QAAQ,KAAK,CAAC,uCAAuC;oBACvD;;YACJ;YAEA;0CAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;oBAClD,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;iCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,eAAe,MAAM;YAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;YAEnD,IAAI,YAAY,YAAY;gBAC1B,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,kBAAkB;YAClB,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,gBAAgB;QACpB,qBAAqB;QACrB,oCAAoC;QACpC,eAAe,OAAO,CAAC,yBAAyB;IAClD;IAEA,+CAA+C;IAC/C,IAAI,eAAe,CAAC,qBAAqB,eAAe,OAAO,CAAC,0BAA0B;QACxF,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;;;;;;;kCAE3C,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAIb,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;0BAI1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;GAhHwB;KAAA", "debugId": null}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { User, UserWithSession } from '@/types';\n\nexport function useAuth() {\n  const [user, setUser] = useState<UserWithSession | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check if session is expired\n  const isSessionExpired = (user: UserWithSession | null): boolean => {\n    if (!user || !user.sessionExpiry) return true;\n    return new Date() > new Date(user.sessionExpiry);\n  };\n\n  useEffect(() => {\n    // Check for stored user data on component mount\n    const storedUser = localStorage.getItem('astroconnect_user');\n    if (storedUser) {\n      try {\n        const parsedUser = JSON.parse(storedUser) as UserWithSession;\n\n        // Check if session is expired\n        if (isSessionExpired(parsedUser)) {\n          console.log('Session expired, removing stored user data');\n          localStorage.removeItem('astroconnect_user');\n          setUser(null);\n        } else {\n          setUser(parsedUser);\n        }\n      } catch (error) {\n        console.error('Error parsing stored user data:', error);\n        localStorage.removeItem('astroconnect_user');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  // Auto-logout when session expires\n  useEffect(() => {\n    if (!user || !user.sessionExpiry) return;\n\n    const checkSessionExpiry = () => {\n      if (isSessionExpired(user)) {\n        console.log('Session expired, logging out user');\n        logout();\n      }\n    };\n\n    // Check every minute\n    const interval = setInterval(checkSessionExpiry, 60000);\n\n    // Also set a timeout for the exact expiry time\n    const timeUntilExpiry = new Date(user.sessionExpiry).getTime() - Date.now();\n    const timeout = setTimeout(() => {\n      console.log('Session expired, logging out user');\n      logout();\n    }, timeUntilExpiry);\n\n    return () => {\n      clearInterval(interval);\n      clearTimeout(timeout);\n    };\n  }, [user]);\n\n  const login = async (token: string): Promise<{ success: boolean; error?: string }> => {\n    try {\n      const response = await fetch('/api/auth/qr', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ token }),\n      });\n\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const userWithSession = data.data as UserWithSession;\n\n        // Verify session is not expired\n        if (isSessionExpired(userWithSession)) {\n          return { success: false, error: 'Session expired during authentication' };\n        }\n\n        setUser(userWithSession);\n        localStorage.setItem('astroconnect_user', JSON.stringify(userWithSession));\n        console.log(`User logged in with session expiring at: ${userWithSession.sessionExpiry}`);\n        return { success: true };\n      } else {\n        return { success: false, error: data.error || 'Authentication failed' };\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      return { success: false, error: 'Network error occurred' };\n    }\n  };\n\n  const logout = () => {\n    console.log('User logged out');\n    setUser(null);\n    localStorage.removeItem('astroconnect_user');\n  };\n\n  const updateUser = (updatedUser: UserWithSession) => {\n    setUser(updatedUser);\n    localStorage.setItem('astroconnect_user', JSON.stringify(updatedUser));\n  };\n\n  const isAuthenticated = !!user && !isSessionExpired(user);\n\n  // Get session time remaining in minutes\n  const getSessionTimeRemaining = (): number => {\n    if (!user || !user.sessionExpiry) return 0;\n    const remaining = new Date(user.sessionExpiry).getTime() - Date.now();\n    return Math.max(0, Math.floor(remaining / (1000 * 60))); // Convert to minutes\n  };\n\n  return {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser,\n    getSessionTimeRemaining,\n    isSessionExpired: () => isSessionExpired(user)\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAKO,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,8BAA8B;IAC9B,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE,OAAO;QACzC,OAAO,IAAI,SAAS,IAAI,KAAK,KAAK,aAAa;IACjD;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,gDAAgD;YAChD,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,IAAI;oBACF,MAAM,aAAa,KAAK,KAAK,CAAC;oBAE9B,8BAA8B;oBAC9B,IAAI,iBAAiB,aAAa;wBAChC,QAAQ,GAAG,CAAC;wBACZ,aAAa,UAAU,CAAC;wBACxB,QAAQ;oBACV,OAAO;wBACL,QAAQ;oBACV;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,aAAa,UAAU,CAAC;gBAC1B;YACF;YACA,WAAW;QACb;4BAAG,EAAE;IAEL,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE;YAElC,MAAM;wDAAqB;oBACzB,IAAI,iBAAiB,OAAO;wBAC1B,QAAQ,GAAG,CAAC;wBACZ;oBACF;gBACF;;YAEA,qBAAqB;YACrB,MAAM,WAAW,YAAY,oBAAoB;YAEjD,+CAA+C;YAC/C,MAAM,kBAAkB,IAAI,KAAK,KAAK,aAAa,EAAE,OAAO,KAAK,KAAK,GAAG;YACzE,MAAM,UAAU;6CAAW;oBACzB,QAAQ,GAAG,CAAC;oBACZ;gBACF;4CAAG;YAEH;qCAAO;oBACL,cAAc;oBACd,aAAa;gBACf;;QACF;4BAAG;QAAC;KAAK;IAET,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;gBAC7B,MAAM,kBAAkB,KAAK,IAAI;gBAEjC,gCAAgC;gBAChC,IAAI,iBAAiB,kBAAkB;oBACrC,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAwC;gBAC1E;gBAEA,QAAQ;gBACR,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;gBACzD,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,gBAAgB,aAAa,EAAE;gBACvF,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK,IAAI;gBAAwB;YACxE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;IACF;IAEA,MAAM,SAAS;QACb,QAAQ,GAAG,CAAC;QACZ,QAAQ;QACR,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,aAAa,CAAC;QAClB,QAAQ;QACR,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D;IAEA,MAAM,kBAAkB,CAAC,CAAC,QAAQ,CAAC,iBAAiB;IAEpD,wCAAwC;IACxC,MAAM,0BAA0B;QAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE,OAAO;QACzC,MAAM,YAAY,IAAI,KAAK,KAAK,aAAa,EAAE,OAAO,KAAK,KAAK,GAAG;QACnE,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,qBAAqB;IAChF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA,kBAAkB,IAAM,iBAAiB;IAC3C;AACF;GA3HgB", "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/qr.ts"], "sourcesContent": ["import { v4 as uuidv4 } from 'uuid';\nimport QRCode from 'qrcode';\n\nexport function generateQRToken(): string {\n  return uuidv4();\n}\n\nexport function generateQRUrl(token: string, baseUrl: string = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'): string {\n  return `${baseUrl}/qr/${token}`;\n}\n\nexport async function generateQRCodeImage(token: string, baseUrl?: string): Promise<string> {\n  const url = generateQRUrl(token, baseUrl);\n  try {\n    const qrCodeDataUrl = await QRCode.toDataURL(url, {\n      width: 300,\n      margin: 2,\n      color: {\n        dark: '#000000',\n        light: '#FFFFFF'\n      }\n    });\n    return qrCodeDataUrl;\n  } catch (error) {\n    console.error('Error generating QR code:', error);\n    throw new Error('Failed to generate QR code');\n  }\n}\n\nexport function extractTokenFromUrl(url: string): string | null {\n  try {\n    const urlObj = new URL(url);\n    const pathParts = urlObj.pathname.split('/');\n    const qrIndex = pathParts.indexOf('qr');\n    \n    if (qrIndex !== -1 && qrIndex < pathParts.length - 1) {\n      return pathParts[qrIndex + 1];\n    }\n    \n    return null;\n  } catch (error) {\n    console.error('Error extracting token from URL:', error);\n    return null;\n  }\n}\n\nexport function isValidQRToken(token: string): boolean {\n  // UUID v4 regex pattern\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(token);\n}\n"], "names": [], "mappings": ";;;;;;;AAO+D;AAP/D;AACA;;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;AACd;AAEO,SAAS,cAAc,KAAa,EAAE,UAAkB,6DAAmC,uBAAuB;IACvH,OAAO,GAAG,QAAQ,IAAI,EAAE,OAAO;AACjC;AAEO,eAAe,oBAAoB,KAAa,EAAE,OAAgB;IACvE,MAAM,MAAM,cAAc,OAAO;IACjC,IAAI;QACF,MAAM,gBAAgB,MAAM,2IAAA,CAAA,UAAM,CAAC,SAAS,CAAC,KAAK;YAChD,OAAO;YACP,QAAQ;YACR,OAAO;gBACL,MAAM;gBACN,OAAO;YACT;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,SAAS,oBAAoB,GAAW;IAC7C,IAAI;QACF,MAAM,SAAS,IAAI,IAAI;QACvB,MAAM,YAAY,OAAO,QAAQ,CAAC,KAAK,CAAC;QACxC,MAAM,UAAU,UAAU,OAAO,CAAC;QAElC,IAAI,YAAY,CAAC,KAAK,UAAU,UAAU,MAAM,GAAG,GAAG;YACpD,OAAO,SAAS,CAAC,UAAU,EAAE;QAC/B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAEO,SAAS,eAAe,KAAa;IAC1C,wBAAwB;IACxB,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 1162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport QRScanner from '@/components/QRScanner';\nimport PWAInstaller from '@/components/PWAInstaller';\nimport { useAuth } from '@/hooks/useAuth';\nimport { extractTokenFromUrl } from '@/utils/qr';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, Star, Zap } from 'lucide-react';\n\nexport default function Home() {\n  const [showScanner, setShowScanner] = useState(false);\n  const [scanError, setScanError] = useState<string | null>(null);\n  const { login } = useAuth();\n  const router = useRouter();\n\n  const handleScanSuccess = async (decodedText: string) => {\n    console.log('QR Code scanned:', decodedText);\n\n    // Extract token from the scanned URL\n    const token = extractTokenFromUrl(decodedText);\n\n    if (!token) {\n      setScanError('Invalid QR code format');\n      return;\n    }\n\n    // Authenticate with the token\n    const result = await login(token);\n\n    if (result.success) {\n      router.push('/dashboard');\n    } else {\n      setScanError(result.error || 'Authentication failed');\n    }\n  };\n\n  const handleScanError = (error: string) => {\n    setScanError(error);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden\">\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-pulse opacity-70\"></div>\n        <div className=\"absolute top-1/3 right-1/3 w-1 h-1 bg-yellow-300 rounded-full animate-ping opacity-60\"></div>\n        <div className=\"absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-purple-300 rounded-full animate-pulse opacity-50\"></div>\n        <div className=\"absolute top-1/2 right-1/4 w-1 h-1 bg-blue-300 rounded-full animate-ping opacity-40\"></div>\n        <div className=\"absolute bottom-1/3 right-1/2 w-2 h-2 bg-indigo-300 rounded-full animate-pulse opacity-60\"></div>\n      </div>\n\n      <div className=\"relative z-10 flex flex-col items-center justify-center min-h-screen p-4\">\n        {!showScanner ? (\n          <div className=\"text-center max-w-4xl mx-auto\">\n            {/* Header */}\n            <div className=\"mb-8\">\n              <div className=\"flex items-center justify-center mb-4\">\n                <Sparkles className=\"w-8 h-8 text-yellow-400 mr-2\" />\n                <h1 className=\"text-4xl md:text-6xl font-bold text-white\">\n                  AstroConnect\n                </h1>\n                <Sparkles className=\"w-8 h-8 text-yellow-400 ml-2\" />\n              </div>\n              <p className=\"text-xl md:text-2xl text-gray-300 mb-2\">\n                Your Personal Horoscope & Daily Guide\n              </p>\n              <p className=\"text-gray-400\">\n                Discover your cosmic destiny with personalized astrology insights\n              </p>\n            </div>\n\n            {/* Features */}\n            <div className=\"grid md:grid-cols-3 gap-6 mb-12\">\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <Moon className=\"w-12 h-12 text-blue-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Daily Horoscopes</h3>\n                <p className=\"text-gray-300 text-sm\">\n                  Get personalized daily, weekly, and monthly predictions based on your zodiac sign\n                </p>\n              </div>\n\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <Star className=\"w-12 h-12 text-yellow-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Lucky Guidance</h3>\n                <p className=\"text-gray-300 text-sm\">\n                  Discover your lucky numbers, colors, and optimal times for important decisions\n                </p>\n              </div>\n\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <Zap className=\"w-12 h-12 text-purple-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">QR Access</h3>\n                <p className=\"text-gray-300 text-sm\">\n                  Instant access to your personalized dashboard with your unique QR code\n                </p>\n              </div>\n            </div>\n\n            {/* CTA Buttons */}\n            <div className=\"space-y-4\">\n              <button\n                onClick={() => setShowScanner(true)}\n                className=\"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-lg\"\n              >\n                Scan Your QR Code\n              </button>\n\n              <p className=\"text-gray-400 text-sm\">\n                Have a personalized QR card? Scan it to access your cosmic insights instantly\n              </p>\n            </div>\n\n            {scanError && (\n              <div className=\"mt-6 p-4 bg-red-500/20 border border-red-500/50 rounded-lg\">\n                <p className=\"text-red-300\">{scanError}</p>\n                <button\n                  onClick={() => setScanError(null)}\n                  className=\"text-red-200 hover:text-white underline mt-2\"\n                >\n                  Try Again\n                </button>\n              </div>\n            )}\n          </div>\n        ) : (\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 max-w-md w-full\">\n            <QRScanner\n              onScanSuccess={handleScanSuccess}\n              onScanError={handleScanError}\n            />\n\n            <button\n              onClick={() => {\n                setShowScanner(false);\n                setScanError(null);\n              }}\n              className=\"mt-4 w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors\"\n            >\n              Back to Home\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* PWA Installer */}\n      <PWAInstaller />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACxB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,OAAO;QAC/B,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,qCAAqC;QACrC,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE;QAElC,IAAI,CAAC,OAAO;YACV,aAAa;YACb;QACF;QAEA,8BAA8B;QAC9B,MAAM,SAAS,MAAM,MAAM;QAE3B,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,aAAa,OAAO,KAAK,IAAI;QAC/B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,aAAa;IACf;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACZ,CAAC,4BACA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAG1D,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,6LAAC;oCAAE,WAAU;8CAAyC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAM/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAOzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;8CAID,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;wBAKtC,2BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;8CAC7B,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CACX;;;;;;;;;;;;;;;;;yCAOP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,kIAAA,CAAA,UAAS;4BACR,eAAe;4BACf,aAAa;;;;;;sCAGf,6LAAC;4BACC,SAAS;gCACP,eAAe;gCACf,aAAa;4BACf;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAQP,6LAAC,qIAAA,CAAA,UAAY;;;;;;;;;;;AAGnB;GA3IwB;;QAGJ,0HAAA,CAAA,UAAO;QACV,qIAAA,CAAA,YAAS;;;KAJF", "debugId": null}}]}