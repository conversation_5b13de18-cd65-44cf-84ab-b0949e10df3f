{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/QRScanner.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { Html5QrcodeScanner, Html5QrcodeScannerConfig, Html5QrcodeResult, Html5Qrcode } from 'html5-qrcode';\nimport { QRScannerProps } from '@/types';\nimport { Camera, Upload, X } from 'lucide-react';\n\nexport default function QRScanner({ \n  onScanSuccess, \n  onScanError, \n  width = 300, \n  height = 300 \n}: QRScannerProps) {\n  const scannerRef = useRef<Html5QrcodeScanner | null>(null);\n  const [isScanning, setIsScanning] = useState(false);\n  const [scanMode, setScanMode] = useState<'camera' | 'file' | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const config: Html5QrcodeScannerConfig = {\n    fps: 10,\n    qrbox: { width: 250, height: 250 },\n    aspectRatio: 1.0,\n    disableFlip: false,\n    supportedScanTypes: [],\n  };\n\n  const handleScanSuccess = (decodedText: string, decodedResult: Html5QrcodeResult) => {\n    console.log('QR Code scanned:', decodedText);\n    onScanSuccess(decodedText);\n    stopScanning();\n  };\n\n  const handleScanError = (error: string) => {\n    // Only log actual errors, not the constant \"No QR code found\" messages\n    if (!error.includes('No QR code found')) {\n      console.error('QR scan error:', error);\n      onScanError?.(error);\n    }\n  };\n\n  const startCameraScanning = () => {\n    if (scannerRef.current) {\n      scannerRef.current.clear();\n    }\n\n    setScanMode('camera');\n    setIsScanning(true);\n\n    scannerRef.current = new Html5QrcodeScanner(\n      'qr-scanner-container',\n      config,\n      false\n    );\n\n    scannerRef.current.render(handleScanSuccess, handleScanError);\n  };\n\n  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    setScanMode('file');\n    setIsScanning(true);\n\n    try {\n      const html5QrCode = new Html5Qrcode('qr-file-scanner');\n\n      const decodedText = await html5QrCode.scanFile(file, true);\n      console.log('QR Code from file:', decodedText);\n      onScanSuccess(decodedText);\n\n      // Clear the file input\n      if (fileInputRef.current) {\n        fileInputRef.current.value = '';\n      }\n    } catch (error: any) {\n      console.error('File scan error:', error);\n      onScanError?.('Failed to scan QR code from image. Please ensure the image contains a valid QR code.');\n    } finally {\n      setIsScanning(false);\n      setScanMode(null);\n    }\n  };\n\n  const stopScanning = () => {\n    if (scannerRef.current) {\n      scannerRef.current.clear().catch(console.error);\n      scannerRef.current = null;\n    }\n    setIsScanning(false);\n    setScanMode(null);\n  };\n\n  useEffect(() => {\n    return () => {\n      if (scannerRef.current) {\n        scannerRef.current.clear().catch(console.error);\n      }\n    };\n  }, []);\n\n  if (!scanMode) {\n    return (\n      <div className=\"flex flex-col items-center space-y-4 p-6\">\n        <h3 className=\"text-lg font-semibold text-white mb-4\">Scan Your QR Code</h3>\n        \n        <div className=\"flex flex-col sm:flex-row gap-4 w-full max-w-md\">\n          <button\n            onClick={startCameraScanning}\n            className=\"flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors\"\n          >\n            <Camera size={20} />\n            Use Camera\n          </button>\n          \n          <button\n            onClick={() => fileInputRef.current?.click()}\n            className=\"flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors\"\n          >\n            <Upload size={20} />\n            Upload Image\n          </button>\n        </div>\n\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          accept=\"image/*\"\n          onChange={handleFileUpload}\n          className=\"hidden\"\n        />\n\n        <p className=\"text-gray-300 text-sm text-center mt-4\">\n          Point your camera at the QR code or upload an image containing the QR code\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex flex-col items-center space-y-4 p-6\">\n      <div className=\"flex items-center justify-between w-full max-w-md\">\n        <h3 className=\"text-lg font-semibold text-white\">\n          {scanMode === 'camera' ? 'Camera Scanner' : 'Processing Image...'}\n        </h3>\n        <button\n          onClick={stopScanning}\n          className=\"text-gray-400 hover:text-white transition-colors\"\n        >\n          <X size={24} />\n        </button>\n      </div>\n\n      {scanMode === 'camera' && (\n        <div \n          id=\"qr-scanner-container\" \n          className=\"w-full max-w-md\"\n          style={{ width, height }}\n        />\n      )}\n\n      {scanMode === 'file' && (\n        <div className=\"flex items-center justify-center w-64 h-64 border-2 border-dashed border-gray-400 rounded-lg\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2\"></div>\n            <p className=\"text-gray-300\">Scanning image...</p>\n          </div>\n        </div>\n      )}\n\n      <div id=\"qr-file-scanner\" className=\"hidden\"></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAEA;AAAA;AAAA;;;AALA;;;;AAOe,SAAS,UAAU,EAChC,aAAa,EACb,WAAW,EACX,QAAQ,GAAG,EACX,SAAS,GAAG,EACG;;IACf,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA6B;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IACnE,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,SAAmC;QACvC,KAAK;QACL,OAAO;YAAE,OAAO;YAAK,QAAQ;QAAI;QACjC,aAAa;QACb,aAAa;QACb,oBAAoB,EAAE;IACxB;IAEA,MAAM,oBAAoB,CAAC,aAAqB;QAC9C,QAAQ,GAAG,CAAC,oBAAoB;QAChC,cAAc;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,uEAAuE;QACvE,IAAI,CAAC,MAAM,QAAQ,CAAC,qBAAqB;YACvC,QAAQ,KAAK,CAAC,kBAAkB;YAChC,cAAc;QAChB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,WAAW,OAAO,EAAE;YACtB,WAAW,OAAO,CAAC,KAAK;QAC1B;QAEA,YAAY;QACZ,cAAc;QAEd,WAAW,OAAO,GAAG,IAAI,uKAAA,CAAA,qBAAkB,CACzC,wBACA,QACA;QAGF,WAAW,OAAO,CAAC,MAAM,CAAC,mBAAmB;IAC/C;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,YAAY;QACZ,cAAc;QAEd,IAAI;YACF,MAAM,cAAc,IAAI,4JAAA,CAAA,cAAW,CAAC;YAEpC,MAAM,cAAc,MAAM,YAAY,QAAQ,CAAC,MAAM;YACrD,QAAQ,GAAG,CAAC,sBAAsB;YAClC,cAAc;YAEd,uBAAuB;YACvB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,cAAc;QAChB,SAAU;YACR,cAAc;YACd,YAAY;QACd;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW,OAAO,EAAE;YACtB,WAAW,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,KAAK;YAC9C,WAAW,OAAO,GAAG;QACvB;QACA,cAAc;QACd,YAAY;IACd;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;uCAAO;oBACL,IAAI,WAAW,OAAO,EAAE;wBACtB,WAAW,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,KAAK;oBAChD;gBACF;;QACF;8BAAG,EAAE;IAEL,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAEtD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,yMAAA,CAAA,SAAM;oCAAC,MAAM;;;;;;gCAAM;;;;;;;sCAItB,6LAAC;4BACC,SAAS,IAAM,aAAa,OAAO,EAAE;4BACrC,WAAU;;8CAEV,6LAAC,yMAAA,CAAA,SAAM;oCAAC,MAAM;;;;;;gCAAM;;;;;;;;;;;;;8BAKxB,6LAAC;oBACC,KAAK;oBACL,MAAK;oBACL,QAAO;oBACP,UAAU;oBACV,WAAU;;;;;;8BAGZ,6LAAC;oBAAE,WAAU;8BAAyC;;;;;;;;;;;;IAK5D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,aAAa,WAAW,mBAAmB;;;;;;kCAE9C,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,MAAM;;;;;;;;;;;;;;;;;YAIZ,aAAa,0BACZ,6LAAC;gBACC,IAAG;gBACH,WAAU;gBACV,OAAO;oBAAE;oBAAO;gBAAO;;;;;;YAI1B,aAAa,wBACZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAKnC,6LAAC;gBAAI,IAAG;gBAAkB,WAAU;;;;;;;;;;;;AAG1C;GAtKwB;KAAA", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/PWAInstaller.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Download, X } from 'lucide-react';\n\ninterface BeforeInstallPromptEvent extends Event {\n  readonly platforms: string[];\n  readonly userChoice: Promise<{\n    outcome: 'accepted' | 'dismissed';\n    platform: string;\n  }>;\n  prompt(): Promise<void>;\n}\n\nexport default function PWAInstaller() {\n  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);\n  const [showInstallPrompt, setShowInstallPrompt] = useState(false);\n  const [isInstalled, setIsInstalled] = useState(false);\n\n  useEffect(() => {\n    // Check if app is already installed\n    if (window.matchMedia('(display-mode: standalone)').matches) {\n      setIsInstalled(true);\n      return;\n    }\n\n    // Listen for the beforeinstallprompt event\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault();\n      setDeferredPrompt(e as BeforeInstallPromptEvent);\n      setShowInstallPrompt(true);\n    };\n\n    // Listen for app installed event\n    const handleAppInstalled = () => {\n      setIsInstalled(true);\n      setShowInstallPrompt(false);\n      setDeferredPrompt(null);\n    };\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n    window.addEventListener('appinstalled', handleAppInstalled);\n\n    // Register service worker\n    if ('serviceWorker' in navigator) {\n      navigator.serviceWorker.register('/sw.js')\n        .then((registration) => {\n          console.log('Service Worker registered:', registration);\n        })\n        .catch((error) => {\n          console.error('Service Worker registration failed:', error);\n        });\n    }\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n      window.removeEventListener('appinstalled', handleAppInstalled);\n    };\n  }, []);\n\n  const handleInstallClick = async () => {\n    if (!deferredPrompt) return;\n\n    try {\n      await deferredPrompt.prompt();\n      const { outcome } = await deferredPrompt.userChoice;\n      \n      if (outcome === 'accepted') {\n        console.log('User accepted the install prompt');\n      } else {\n        console.log('User dismissed the install prompt');\n      }\n      \n      setDeferredPrompt(null);\n      setShowInstallPrompt(false);\n    } catch (error) {\n      console.error('Error during installation:', error);\n    }\n  };\n\n  const handleDismiss = () => {\n    setShowInstallPrompt(false);\n    // Don't show again for this session\n    sessionStorage.setItem('pwa-install-dismissed', 'true');\n  };\n\n  // Don't show if already installed or dismissed\n  if (isInstalled || !showInstallPrompt || sessionStorage.getItem('pwa-install-dismissed')) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4 shadow-lg z-50\">\n      <div className=\"flex items-start justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          <Download className=\"w-5 h-5 text-purple-400\" />\n          <h3 className=\"text-white font-semibold\">Install AstroConnect</h3>\n        </div>\n        <button\n          onClick={handleDismiss}\n          className=\"text-gray-400 hover:text-white transition-colors\"\n        >\n          <X size={20} />\n        </button>\n      </div>\n      \n      <p className=\"text-gray-300 text-sm mb-4\">\n        Install our app for quick access to your daily horoscope and cosmic insights!\n      </p>\n      \n      <div className=\"flex space-x-2\">\n        <button\n          onClick={handleInstallClick}\n          className=\"flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n        >\n          Install\n        </button>\n        <button\n          onClick={handleDismiss}\n          className=\"px-4 py-2 text-gray-300 hover:text-white text-sm transition-colors\"\n        >\n          Not now\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAce,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IACtF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,oCAAoC;YACpC,IAAI,OAAO,UAAU,CAAC,8BAA8B,OAAO,EAAE;gBAC3D,eAAe;gBACf;YACF;YAEA,2CAA2C;YAC3C,MAAM;oEAA4B,CAAC;oBACjC,EAAE,cAAc;oBAChB,kBAAkB;oBAClB,qBAAqB;gBACvB;;YAEA,iCAAiC;YACjC,MAAM;6DAAqB;oBACzB,eAAe;oBACf,qBAAqB;oBACrB,kBAAkB;gBACpB;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAC/C,OAAO,gBAAgB,CAAC,gBAAgB;YAExC,0BAA0B;YAC1B,IAAI,mBAAmB,WAAW;gBAChC,UAAU,aAAa,CAAC,QAAQ,CAAC,UAC9B,IAAI;8CAAC,CAAC;wBACL,QAAQ,GAAG,CAAC,8BAA8B;oBAC5C;6CACC,KAAK;8CAAC,CAAC;wBACN,QAAQ,KAAK,CAAC,uCAAuC;oBACvD;;YACJ;YAEA;0CAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;oBAClD,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;iCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,eAAe,MAAM;YAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;YAEnD,IAAI,YAAY,YAAY;gBAC1B,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,kBAAkB;YAClB,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,gBAAgB;QACpB,qBAAqB;QACrB,oCAAoC;QACpC,eAAe,OAAO,CAAC,yBAAyB;IAClD;IAEA,+CAA+C;IAC/C,IAAI,eAAe,CAAC,qBAAqB,eAAe,OAAO,CAAC,0BAA0B;QACxF,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;;;;;;;kCAE3C,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAIb,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;0BAI1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;GAhHwB;KAAA", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { User } from '@/types';\n\nexport function useAuth() {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check for stored user data on component mount\n    const storedUser = localStorage.getItem('astroconnect_user');\n    if (storedUser) {\n      try {\n        setUser(JSON.parse(storedUser));\n      } catch (error) {\n        console.error('Error parsing stored user data:', error);\n        localStorage.removeItem('astroconnect_user');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  const login = async (token: string): Promise<{ success: boolean; error?: string }> => {\n    try {\n      const response = await fetch('/api/auth/qr', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ token }),\n      });\n\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        setUser(data.data);\n        localStorage.setItem('astroconnect_user', JSON.stringify(data.data));\n        return { success: true };\n      } else {\n        return { success: false, error: data.error || 'Authentication failed' };\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      return { success: false, error: 'Network error occurred' };\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('astroconnect_user');\n  };\n\n  const updateUser = (updatedUser: User) => {\n    setUser(updatedUser);\n    localStorage.setItem('astroconnect_user', JSON.stringify(updatedUser));\n  };\n\n  const isAuthenticated = !!user;\n\n  return {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAKO,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,gDAAgD;YAChD,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,IAAI;oBACF,QAAQ,KAAK,KAAK,CAAC;gBACrB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,aAAa,UAAU,CAAC;gBAC1B;YACF;YACA,WAAW;QACb;4BAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;gBAC7B,QAAQ,KAAK,IAAI;gBACjB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC,KAAK,IAAI;gBAClE,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK,IAAI;gBAAwB;YACxE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;IACF;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,aAAa,CAAC;QAClB,QAAQ;QACR,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D;IAEA,MAAM,kBAAkB,CAAC,CAAC;IAE1B,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA/DgB", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/qr.ts"], "sourcesContent": ["import { v4 as uuidv4 } from 'uuid';\nimport QRCode from 'qrcode';\n\nexport function generateQRToken(): string {\n  return uuidv4();\n}\n\nexport function generateQRUrl(token: string, baseUrl: string = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'): string {\n  return `${baseUrl}/qr/${token}`;\n}\n\nexport async function generateQRCodeImage(token: string, baseUrl?: string): Promise<string> {\n  const url = generateQRUrl(token, baseUrl);\n  try {\n    const qrCodeDataUrl = await QRCode.toDataURL(url, {\n      width: 300,\n      margin: 2,\n      color: {\n        dark: '#000000',\n        light: '#FFFFFF'\n      }\n    });\n    return qrCodeDataUrl;\n  } catch (error) {\n    console.error('Error generating QR code:', error);\n    throw new Error('Failed to generate QR code');\n  }\n}\n\nexport function extractTokenFromUrl(url: string): string | null {\n  try {\n    const urlObj = new URL(url);\n    const pathParts = urlObj.pathname.split('/');\n    const qrIndex = pathParts.indexOf('qr');\n    \n    if (qrIndex !== -1 && qrIndex < pathParts.length - 1) {\n      return pathParts[qrIndex + 1];\n    }\n    \n    return null;\n  } catch (error) {\n    console.error('Error extracting token from URL:', error);\n    return null;\n  }\n}\n\nexport function isValidQRToken(token: string): boolean {\n  // UUID v4 regex pattern\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(token);\n}\n"], "names": [], "mappings": ";;;;;;;AAO+D;AAP/D;AACA;;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;AACd;AAEO,SAAS,cAAc,KAAa,EAAE,UAAkB,6DAAmC,uBAAuB;IACvH,OAAO,GAAG,QAAQ,IAAI,EAAE,OAAO;AACjC;AAEO,eAAe,oBAAoB,KAAa,EAAE,OAAgB;IACvE,MAAM,MAAM,cAAc,OAAO;IACjC,IAAI;QACF,MAAM,gBAAgB,MAAM,2IAAA,CAAA,UAAM,CAAC,SAAS,CAAC,KAAK;YAChD,OAAO;YACP,QAAQ;YACR,OAAO;gBACL,MAAM;gBACN,OAAO;YACT;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,SAAS,oBAAoB,GAAW;IAC7C,IAAI;QACF,MAAM,SAAS,IAAI,IAAI;QACvB,MAAM,YAAY,OAAO,QAAQ,CAAC,KAAK,CAAC;QACxC,MAAM,UAAU,UAAU,OAAO,CAAC;QAElC,IAAI,YAAY,CAAC,KAAK,UAAU,UAAU,MAAM,GAAG,GAAG;YACpD,OAAO,SAAS,CAAC,UAAU,EAAE;QAC/B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAEO,SAAS,eAAe,KAAa;IAC1C,wBAAwB;IACxB,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 633, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport QRScanner from '@/components/QRScanner';\nimport PWAInstaller from '@/components/PWAInstaller';\nimport { useAuth } from '@/hooks/useAuth';\nimport { extractTokenFromUrl } from '@/utils/qr';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, Star, Zap } from 'lucide-react';\n\nexport default function Home() {\n  const [showScanner, setShowScanner] = useState(false);\n  const [scanError, setScanError] = useState<string | null>(null);\n  const { login } = useAuth();\n  const router = useRouter();\n\n  const handleScanSuccess = async (decodedText: string) => {\n    console.log('QR Code scanned:', decodedText);\n\n    // Extract token from the scanned URL\n    const token = extractTokenFromUrl(decodedText);\n\n    if (!token) {\n      setScanError('Invalid QR code format');\n      return;\n    }\n\n    // Authenticate with the token\n    const result = await login(token);\n\n    if (result.success) {\n      router.push('/dashboard');\n    } else {\n      setScanError(result.error || 'Authentication failed');\n    }\n  };\n\n  const handleScanError = (error: string) => {\n    setScanError(error);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden\">\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-pulse opacity-70\"></div>\n        <div className=\"absolute top-1/3 right-1/3 w-1 h-1 bg-yellow-300 rounded-full animate-ping opacity-60\"></div>\n        <div className=\"absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-purple-300 rounded-full animate-pulse opacity-50\"></div>\n        <div className=\"absolute top-1/2 right-1/4 w-1 h-1 bg-blue-300 rounded-full animate-ping opacity-40\"></div>\n        <div className=\"absolute bottom-1/3 right-1/2 w-2 h-2 bg-indigo-300 rounded-full animate-pulse opacity-60\"></div>\n      </div>\n\n      <div className=\"relative z-10 flex flex-col items-center justify-center min-h-screen p-4\">\n        {!showScanner ? (\n          <div className=\"text-center max-w-4xl mx-auto\">\n            {/* Header */}\n            <div className=\"mb-8\">\n              <div className=\"flex items-center justify-center mb-4\">\n                <Sparkles className=\"w-8 h-8 text-yellow-400 mr-2\" />\n                <h1 className=\"text-4xl md:text-6xl font-bold text-white\">\n                  AstroConnect\n                </h1>\n                <Sparkles className=\"w-8 h-8 text-yellow-400 ml-2\" />\n              </div>\n              <p className=\"text-xl md:text-2xl text-gray-300 mb-2\">\n                Your Personal Horoscope & Daily Guide\n              </p>\n              <p className=\"text-gray-400\">\n                Discover your cosmic destiny with personalized astrology insights\n              </p>\n            </div>\n\n            {/* Features */}\n            <div className=\"grid md:grid-cols-3 gap-6 mb-12\">\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <Moon className=\"w-12 h-12 text-blue-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Daily Horoscopes</h3>\n                <p className=\"text-gray-300 text-sm\">\n                  Get personalized daily, weekly, and monthly predictions based on your zodiac sign\n                </p>\n              </div>\n\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <Star className=\"w-12 h-12 text-yellow-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Lucky Guidance</h3>\n                <p className=\"text-gray-300 text-sm\">\n                  Discover your lucky numbers, colors, and optimal times for important decisions\n                </p>\n              </div>\n\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <Zap className=\"w-12 h-12 text-purple-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">QR Access</h3>\n                <p className=\"text-gray-300 text-sm\">\n                  Instant access to your personalized dashboard with your unique QR code\n                </p>\n              </div>\n            </div>\n\n            {/* CTA Buttons */}\n            <div className=\"space-y-4\">\n              <button\n                onClick={() => setShowScanner(true)}\n                className=\"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-lg\"\n              >\n                Scan Your QR Code\n              </button>\n\n              <p className=\"text-gray-400 text-sm\">\n                Have a personalized QR card? Scan it to access your cosmic insights instantly\n              </p>\n            </div>\n\n            {scanError && (\n              <div className=\"mt-6 p-4 bg-red-500/20 border border-red-500/50 rounded-lg\">\n                <p className=\"text-red-300\">{scanError}</p>\n                <button\n                  onClick={() => setScanError(null)}\n                  className=\"text-red-200 hover:text-white underline mt-2\"\n                >\n                  Try Again\n                </button>\n              </div>\n            )}\n          </div>\n        ) : (\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 max-w-md w-full\">\n            <QRScanner\n              onScanSuccess={handleScanSuccess}\n              onScanError={handleScanError}\n            />\n\n            <button\n              onClick={() => {\n                setShowScanner(false);\n                setScanError(null);\n              }}\n              className=\"mt-4 w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors\"\n            >\n              Back to Home\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* PWA Installer */}\n      <PWAInstaller />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACxB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,OAAO;QAC/B,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,qCAAqC;QACrC,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE;QAElC,IAAI,CAAC,OAAO;YACV,aAAa;YACb;QACF;QAEA,8BAA8B;QAC9B,MAAM,SAAS,MAAM,MAAM;QAE3B,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,aAAa,OAAO,KAAK,IAAI;QAC/B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,aAAa;IACf;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACZ,CAAC,4BACA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAG1D,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,6LAAC;oCAAE,WAAU;8CAAyC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAM/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAOzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;8CAID,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;wBAKtC,2BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;8CAC7B,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CACX;;;;;;;;;;;;;;;;;yCAOP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,kIAAA,CAAA,UAAS;4BACR,eAAe;4BACf,aAAa;;;;;;sCAGf,6LAAC;4BACC,SAAS;gCACP,eAAe;gCACf,aAAa;4BACf;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAQP,6LAAC,qIAAA,CAAA,UAAY;;;;;;;;;;;AAGnB;GA3IwB;;QAGJ,0HAAA,CAAA,UAAO;QACV,qIAAA,CAAA,YAAS;;;KAJF", "debugId": null}}]}