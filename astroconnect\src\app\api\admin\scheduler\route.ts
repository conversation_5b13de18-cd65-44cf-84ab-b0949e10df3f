import { NextRequest, NextResponse } from 'next/server';
import { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';
import { dailyReadingsScheduler } from '@/lib/scheduler';
import { ApiResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const status = dailyReadingsScheduler.getStatus();

    return NextResponse.json<ApiResponse<any>>({
      success: true,
      data: {
        scheduler: status,
        serverTime: new Date().toISOString(),
        localTime: new Date().toLocaleString()
      },
      message: 'Scheduler status retrieved successfully'
    });

  } catch (error) {
    console.error('❌ Error getting scheduler status:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Failed to get scheduler status'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { action, date } = await request.json();

    if (action === 'generate') {
      console.log('🔄 Admin triggered manual reading generation');
      
      const targetDate = date || new Date().toISOString().split('T')[0];
      const readings = await dailyReadingsScheduler.generateNow(targetDate);

      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: {
          readings,
          count: readings.length,
          date: targetDate
        },
        message: `Successfully generated ${readings.length} daily readings for ${targetDate}`
      });
    }

    if (action === 'status') {
      const status = dailyReadingsScheduler.getStatus();
      
      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: { scheduler: status },
        message: 'Scheduler status retrieved successfully'
      });
    }

    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Invalid action. Use "generate" or "status"'
    }, { status: 400 });

  } catch (error) {
    console.error('❌ Error in scheduler action:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Failed to execute scheduler action'
    }, { status: 500 });
  }
}
