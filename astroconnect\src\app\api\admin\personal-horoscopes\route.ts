import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';
import { ApiResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { userId, title, content, language = 'en' } = await request.json();

    if (!userId || !title || !content) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'User ID, title, and content are required'
      }, { status: 400 });
    }

    console.log('📝 Creating personal horoscope for user:', userId);

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    // Create personal horoscope
    const horoscope = await prisma.personalHoroscope.create({
      data: {
        userId,
        title: title.trim(),
        content: content.trim(),
        language,
        isActive: true
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            zodiacSign: true
          }
        }
      }
    });

    console.log('✅ Personal horoscope created:', horoscope.id);

    return NextResponse.json<ApiResponse<any>>({
      success: true,
      data: { horoscope },
      message: 'Personal horoscope created successfully'
    });

  } catch (error) {
    console.error('❌ Error creating personal horoscope:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Failed to create personal horoscope'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    if (userId) {
      // Get horoscopes for specific user
      const horoscopes = await prisma.personalHoroscope.findMany({
        where: { userId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              zodiacSign: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: { horoscopes },
        message: 'Personal horoscopes retrieved successfully'
      });
    } else {
      // Get all horoscopes with pagination
      const horoscopes = await prisma.personalHoroscope.findMany({
        include: {
          user: {
            select: {
              id: true,
              name: true,
              zodiacSign: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip: offset,
        take: limit
      });

      const total = await prisma.personalHoroscope.count();

      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: {
          horoscopes,
          total,
          page,
          limit
        },
        message: 'Personal horoscopes retrieved successfully'
      });
    }

  } catch (error) {
    console.error('❌ Error fetching personal horoscopes:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Failed to fetch personal horoscopes'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { id, title, content, isActive } = await request.json();

    if (!id) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Horoscope ID is required'
      }, { status: 400 });
    }

    console.log('📝 Updating personal horoscope:', id);

    const horoscope = await prisma.personalHoroscope.update({
      where: { id },
      data: {
        ...(title && { title: title.trim() }),
        ...(content && { content: content.trim() }),
        ...(typeof isActive === 'boolean' && { isActive }),
        updatedAt: new Date()
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            zodiacSign: true
          }
        }
      }
    });

    console.log('✅ Personal horoscope updated:', horoscope.id);

    return NextResponse.json<ApiResponse<any>>({
      success: true,
      data: { horoscope },
      message: 'Personal horoscope updated successfully'
    });

  } catch (error) {
    console.error('❌ Error updating personal horoscope:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Failed to update personal horoscope'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Horoscope ID is required'
      }, { status: 400 });
    }

    console.log('🗑️ Deleting personal horoscope:', id);

    await prisma.personalHoroscope.delete({
      where: { id }
    });

    console.log('✅ Personal horoscope deleted:', id);

    return NextResponse.json<ApiResponse<any>>({
      success: true,
      data: { deleted: true },
      message: 'Personal horoscope deleted successfully'
    });

  } catch (error) {
    console.error('❌ Error deleting personal horoscope:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Failed to delete personal horoscope'
    }, { status: 500 });
  }
}
