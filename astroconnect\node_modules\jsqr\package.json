{"name": "jsqr", "version": "1.4.0", "description": "A pure javascript QR code reading library that takes in raw images and will locate, extract and parse any QR code found within.", "repository": "https://github.com/cozmo/jsQR", "main": "./dist/jsQR.js", "types": "./dist/index.d.ts", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "Apache-2.0", "devDependencies": {"@types/fs-extra": "^4.0.2", "@types/jest": "^20.0.8", "@types/node": "^8.0.27", "awesome-typescript-loader": "^3.2.3", "fs-extra": "^4.0.1", "jest": "^23.1.0", "rimraf": "^2.6.2", "ts-jest": "^23.0.4", "ts-node": "^3.3.0", "tslint": "^5.7.0", "typescript": "^2.5.2", "upng-js": "^1.0.1", "webpack": "^3.10.0"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "webpack", "watch": "webpack --watch", "test": "jest", "lint": "tslint --project .", "generate-test-data": "ts-node --project tests/ tests/generate-test-data.ts"}, "jest": {"testRegex": ".*test.ts", "moduleFileExtensions": ["ts", "js"], "transform": {"^.+\\.ts$": "<rootDir>/node_modules/ts-jest/preprocessor.js"}, "globals": {"ts-jest": {"tsConfig": {"noImplicitAny": false, "lib": ["es2015"], "types": ["jest", "node"]}}}}}