{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { User } from '@/types';\n\nexport function useAuth() {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check for stored user data on component mount\n    const storedUser = localStorage.getItem('astroconnect_user');\n    if (storedUser) {\n      try {\n        setUser(JSON.parse(storedUser));\n      } catch (error) {\n        console.error('Error parsing stored user data:', error);\n        localStorage.removeItem('astroconnect_user');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  const login = async (token: string): Promise<{ success: boolean; error?: string }> => {\n    try {\n      const response = await fetch('/api/auth/qr', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ token }),\n      });\n\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        setUser(data.data);\n        localStorage.setItem('astroconnect_user', JSON.stringify(data.data));\n        return { success: true };\n      } else {\n        return { success: false, error: data.error || 'Authentication failed' };\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      return { success: false, error: 'Network error occurred' };\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('astroconnect_user');\n  };\n\n  const updateUser = (updatedUser: User) => {\n    setUser(updatedUser);\n    localStorage.setItem('astroconnect_user', JSON.stringify(updatedUser));\n  };\n\n  const isAuthenticated = !!user;\n\n  return {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAKO,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,gDAAgD;YAChD,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,IAAI;oBACF,QAAQ,KAAK,KAAK,CAAC;gBACrB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,aAAa,UAAU,CAAC;gBAC1B;YACF;YACA,WAAW;QACb;4BAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;gBAC7B,QAAQ,KAAK,IAAI;gBACjB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC,KAAK,IAAI;gBAClE,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK,IAAI;gBAAwB;YACxE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;IACF;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,aAAa,CAAC;QAClB,QAAQ;QACR,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D;IAEA,MAAM,kBAAkB,CAAC,CAAC;IAE1B,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA/DgB", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/zodiac.ts"], "sourcesContent": ["import { ZodiacSign } from '@prisma/client';\n\nexport const ZODIAC_SIGNS: ZodiacSign[] = [\n  'aries', 'taurus', 'gemini', 'cancer',\n  'leo', 'virgo', 'libra', 'scorpio',\n  'sagittarius', 'capricorn', 'aquarius', 'pisces'\n];\n\nexport const ZODIAC_INFO = {\n  aries: { name: '<PERSON><PERSON>', symbol: '♈', dates: 'Mar 21 - Apr 19', element: 'Fire' },\n  taurus: { name: 'Taurus', symbol: '♉', dates: 'Apr 20 - May 20', element: 'Earth' },\n  gemini: { name: 'Gemini', symbol: '♊', dates: 'May 21 - Jun 20', element: 'Air' },\n  cancer: { name: 'Cancer', symbol: '♋', dates: 'Jun 21 - Jul 22', element: 'Water' },\n  leo: { name: '<PERSON>', symbol: '♌', dates: 'Jul 23 - Aug 22', element: 'Fire' },\n  virgo: { name: '<PERSON>ir<PERSON>', symbol: '♍', dates: 'Aug 23 - Sep 22', element: 'Earth' },\n  libra: { name: '<PERSON><PERSON>', symbol: '♎', dates: 'Sep 23 - Oct 22', element: 'Air' },\n  scorpio: { name: '<PERSON><PERSON><PERSON>', symbol: '♏', dates: 'Oct 23 - Nov 21', element: 'Water' },\n  sagittarius: { name: 'Sagittarius', symbol: '♐', dates: 'Nov 22 - Dec 21', element: 'Fire' },\n  capricorn: { name: 'Capricorn', symbol: '♑', dates: 'Dec 22 - Jan 19', element: 'Earth' },\n  aquarius: { name: 'Aquarius', symbol: '♒', dates: 'Jan 20 - Feb 18', element: 'Air' },\n  pisces: { name: 'Pisces', symbol: '♓', dates: 'Feb 19 - Mar 20', element: 'Water' }\n};\n\nexport function getZodiacFromDate(birthDate: string): ZodiacSign {\n  const date = new Date(birthDate);\n  const month = date.getMonth() + 1; // 1-12\n  const day = date.getDate();\n\n  if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) return 'aries';\n  if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) return 'taurus';\n  if ((month === 5 && day >= 21) || (month === 6 && day <= 20)) return 'gemini';\n  if ((month === 6 && day >= 21) || (month === 7 && day <= 22)) return 'cancer';\n  if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) return 'leo';\n  if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) return 'virgo';\n  if ((month === 9 && day >= 23) || (month === 10 && day <= 22)) return 'libra';\n  if ((month === 10 && day >= 23) || (month === 11 && day <= 21)) return 'scorpio';\n  if ((month === 11 && day >= 22) || (month === 12 && day <= 21)) return 'sagittarius';\n  if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) return 'capricorn';\n  if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) return 'aquarius';\n  return 'pisces';\n}\n\nexport function getZodiacColors(sign: ZodiacSign): string[] {\n  const colorMap = {\n    aries: ['#FF6B6B', '#FF4757'],\n    taurus: ['#2ECC71', '#27AE60'],\n    gemini: ['#F39C12', '#E67E22'],\n    cancer: ['#3498DB', '#2980B9'],\n    leo: ['#E74C3C', '#C0392B'],\n    virgo: ['#1ABC9C', '#16A085'],\n    libra: ['#9B59B6', '#8E44AD'],\n    scorpio: ['#34495E', '#2C3E50'],\n    sagittarius: ['#E67E22', '#D35400'],\n    capricorn: ['#95A5A6', '#7F8C8D'],\n    aquarius: ['#3498DB', '#2980B9'],\n    pisces: ['#1ABC9C', '#16A085']\n  };\n  return colorMap[sign];\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,eAA6B;IACxC;IAAS;IAAU;IAAU;IAC7B;IAAO;IAAS;IAAS;IACzB;IAAe;IAAa;IAAY;CACzC;AAEM,MAAM,cAAc;IACzB,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC/E,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAClF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IAChF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAClF,KAAK;QAAE,MAAM;QAAO,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC3E,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAChF,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IAC9E,SAAS;QAAE,MAAM;QAAW,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IACpF,aAAa;QAAE,MAAM;QAAe,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC3F,WAAW;QAAE,MAAM;QAAa,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IACxF,UAAU;QAAE,MAAM;QAAY,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IACpF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;AACpF;AAEO,SAAS,kBAAkB,SAAiB;IACjD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,QAAQ,KAAK,QAAQ,KAAK,GAAG,OAAO;IAC1C,MAAM,MAAM,KAAK,OAAO;IAExB,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACtE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACvE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACvE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACtE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,OAAO;AACT;AAEO,SAAS,gBAAgB,IAAgB;IAC9C,MAAM,WAAW;QACf,OAAO;YAAC;YAAW;SAAU;QAC7B,QAAQ;YAAC;YAAW;SAAU;QAC9B,QAAQ;YAAC;YAAW;SAAU;QAC9B,QAAQ;YAAC;YAAW;SAAU;QAC9B,KAAK;YAAC;YAAW;SAAU;QAC3B,OAAO;YAAC;YAAW;SAAU;QAC7B,OAAO;YAAC;YAAW;SAAU;QAC7B,SAAS;YAAC;YAAW;SAAU;QAC/B,aAAa;YAAC;YAAW;SAAU;QACnC,WAAW;YAAC;YAAW;SAAU;QACjC,UAAU;YAAC;YAAW;SAAU;QAChC,QAAQ;YAAC;YAAW;SAAU;IAChC;IACA,OAAO,QAAQ,CAAC,KAAK;AACvB", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\nimport { Loader2 } from 'lucide-react';\n\ninterface LoadingSpinnerProps {\n  message?: string;\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ \n  message = 'Loading...', \n  size = 'md',\n  className = '' \n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-6 h-6',\n    md: 'w-12 h-12',\n    lg: 'w-16 h-16'\n  };\n\n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <Loader2 className={`${sizeClasses[size]} text-white animate-spin mb-4`} />\n      {message && (\n        <p className=\"text-white text-center\">{message}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,eAAe,EACrC,UAAU,YAAY,EACtB,OAAO,IAAI,EACX,YAAY,EAAE,EACM;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACtE,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,6BAA6B,CAAC;;;;;;YACtE,yBACC,6LAAC;gBAAE,WAAU;0BAA0B;;;;;;;;;;;;AAI/C;KAnBwB", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, RefreshCw } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  title?: string;\n  message: string;\n  onRetry?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ \n  title = 'Something went wrong',\n  message,\n  onRetry,\n  className = ''\n}: ErrorMessageProps) {\n  return (\n    <div className={`text-center max-w-md mx-auto p-6 ${className}`}>\n      <AlertCircle className=\"w-16 h-16 text-red-400 mx-auto mb-4\" />\n      <h2 className=\"text-xl font-bold text-white mb-4\">{title}</h2>\n      <p className=\"text-gray-300 mb-6\">{message}</p>\n      {onRetry && (\n        <button\n          onClick={onRetry}\n          className=\"flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors mx-auto\"\n        >\n          <RefreshCw size={16} />\n          Try Again\n        </button>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAWe,SAAS,aAAa,EACnC,QAAQ,sBAAsB,EAC9B,OAAO,EACP,OAAO,EACP,YAAY,EAAE,EACI;IAClB,qBACE,6LAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;;0BAC7D,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,6LAAC;gBAAG,WAAU;0BAAqC;;;;;;0BACnD,6LAAC;gBAAE,WAAU;0BAAsB;;;;;;YAClC,yBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,6LAAC,mNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;oBAAM;;;;;;;;;;;;;AAMjC;KAtBwB", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/TranslatedText.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useLanguage } from '@/hooks/useLanguage';\nimport { Loader2 } from 'lucide-react';\n\ninterface TranslatedTextProps {\n  text: string;\n  className?: string;\n  fallback?: string;\n  showLoader?: boolean;\n}\n\nexport default function TranslatedText({ \n  text, \n  className = '', \n  fallback,\n  showLoader = true \n}: TranslatedTextProps) {\n  const { language, translate, isTranslating } = useLanguage();\n  const [translatedText, setTranslatedText] = useState<string>(text);\n  const [isLoading, setIsLoading] = useState(false);\n\n  useEffect(() => {\n    const translateContent = async () => {\n      if (language === 'en') {\n        setTranslatedText(text);\n        return;\n      }\n\n      setIsLoading(true);\n      try {\n        const translated = await translate(text);\n        setTranslatedText(translated);\n      } catch (error) {\n        console.error('Translation error:', error);\n        setTranslatedText(fallback || text);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    translateContent();\n  }, [text, language, translate, fallback]);\n\n  if (isLoading && showLoader) {\n    return (\n      <div className={`flex items-center gap-2 ${className}`}>\n        <Loader2 className=\"w-4 h-4 animate-spin\" />\n        <span className=\"opacity-70\">{fallback || text}</span>\n      </div>\n    );\n  }\n\n  return <span className={className}>{translatedText}</span>;\n}\n\n// Hook for batch translation\nexport function useTranslatedContent(content: Record<string, string>) {\n  const { language, translate } = useLanguage();\n  const [translatedContent, setTranslatedContent] = useState<Record<string, string>>(content);\n  const [isTranslating, setIsTranslating] = useState(false);\n\n  useEffect(() => {\n    const translateAll = async () => {\n      if (language === 'en') {\n        setTranslatedContent(content);\n        return;\n      }\n\n      setIsTranslating(true);\n      try {\n        const translations: Record<string, string> = {};\n        \n        for (const [key, text] of Object.entries(content)) {\n          translations[key] = await translate(text);\n        }\n        \n        setTranslatedContent(translations);\n      } catch (error) {\n        console.error('Batch translation error:', error);\n        setTranslatedContent(content);\n      } finally {\n        setIsTranslating(false);\n      }\n    };\n\n    translateAll();\n  }, [content, language, translate]);\n\n  return { translatedContent, isTranslating };\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAae,SAAS,eAAe,EACrC,IAAI,EACJ,YAAY,EAAE,EACd,QAAQ,EACR,aAAa,IAAI,EACG;;IACpB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;6DAAmB;oBACvB,IAAI,aAAa,MAAM;wBACrB,kBAAkB;wBAClB;oBACF;oBAEA,aAAa;oBACb,IAAI;wBACF,MAAM,aAAa,MAAM,UAAU;wBACnC,kBAAkB;oBACpB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,sBAAsB;wBACpC,kBAAkB,YAAY;oBAChC,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;mCAAG;QAAC;QAAM;QAAU;QAAW;KAAS;IAExC,IAAI,aAAa,YAAY;QAC3B,qBACE,6LAAC;YAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;8BACpD,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,6LAAC;oBAAK,WAAU;8BAAc,YAAY;;;;;;;;;;;;IAGhD;IAEA,qBAAO,6LAAC;QAAK,WAAW;kBAAY;;;;;;AACtC;GA1CwB;;QAMyB,+HAAA,CAAA,cAAW;;;KANpC;AA6CjB,SAAS,qBAAqB,OAA+B;;IAClE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAC1C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACnF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;+DAAe;oBACnB,IAAI,aAAa,MAAM;wBACrB,qBAAqB;wBACrB;oBACF;oBAEA,iBAAiB;oBACjB,IAAI;wBACF,MAAM,eAAuC,CAAC;wBAE9C,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,OAAO,OAAO,CAAC,SAAU;4BACjD,YAAY,CAAC,IAAI,GAAG,MAAM,UAAU;wBACtC;wBAEA,qBAAqB;oBACvB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,qBAAqB;oBACvB,SAAU;wBACR,iBAAiB;oBACnB;gBACF;;YAEA;QACF;yCAAG;QAAC;QAAS;QAAU;KAAU;IAEjC,OAAO;QAAE;QAAmB;IAAc;AAC5C;IAjCgB;;QACkB,+HAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useLanguage } from '@/hooks/useLanguage';\nimport { Globe, Check, Loader2 } from 'lucide-react';\nimport { LANGUAGE_NAMES } from '@/utils/translation';\n\ninterface LanguageSwitcherProps {\n  onLanguageChange?: (language: 'en' | 'si') => void;\n  className?: string;\n}\n\nexport default function LanguageSwitcher({ onLanguageChange, className = '' }: LanguageSwitcherProps) {\n  const { language, setLanguage, isTranslating } = useLanguage();\n  const [isOpen, setIsOpen] = useState(false);\n\n  const handleLanguageChange = async (newLanguage: 'en' | 'si') => {\n    if (newLanguage === language) return;\n    \n    setLanguage(newLanguage);\n    setIsOpen(false);\n    \n    if (onLanguageChange) {\n      onLanguageChange(newLanguage);\n    }\n  };\n\n  return (\n    <div className={`relative ${className}`}>\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 bg-white/10 hover:bg-white/20 text-white px-3 py-2 rounded-lg transition-colors\"\n        disabled={isTranslating}\n      >\n        {isTranslating ? (\n          <Loader2 size={16} className=\"animate-spin\" />\n        ) : (\n          <Globe size={16} />\n        )}\n        <span>{LANGUAGE_NAMES[language]}</span>\n        <svg \n          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} \n          fill=\"none\" \n          stroke=\"currentColor\" \n          viewBox=\"0 0 24 24\"\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n        </svg>\n      </button>\n\n      {isOpen && (\n        <div className=\"absolute top-full right-0 mt-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg shadow-lg z-50 min-w-[120px]\">\n          {Object.entries(LANGUAGE_NAMES).map(([code, name]) => (\n            <button\n              key={code}\n              onClick={() => handleLanguageChange(code as 'en' | 'si')}\n              className=\"flex items-center justify-between w-full px-4 py-2 text-white hover:bg-white/10 transition-colors first:rounded-t-lg last:rounded-b-lg\"\n            >\n              <span>{name}</span>\n              {language === code && <Check size={16} className=\"text-green-400\" />}\n            </button>\n          ))}\n        </div>\n      )}\n\n      {/* Overlay to close dropdown */}\n      {isOpen && (\n        <div \n          className=\"fixed inset-0 z-40\" \n          onClick={() => setIsOpen(false)}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;;;AALA;;;;;AAYe,SAAS,iBAAiB,EAAE,gBAAgB,EAAE,YAAY,EAAE,EAAyB;;IAClG,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAC3D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,uBAAuB,OAAO;QAClC,IAAI,gBAAgB,UAAU;QAE9B,YAAY;QACZ,UAAU;QAEV,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACrC,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,UAAU;;oBAET,8BACC,6LAAC,oNAAA,CAAA,UAAO;wBAAC,MAAM;wBAAI,WAAU;;;;;6CAE7B,6LAAC,uMAAA,CAAA,QAAK;wBAAC,MAAM;;;;;;kCAEf,6LAAC;kCAAM,8HAAA,CAAA,iBAAc,CAAC,SAAS;;;;;;kCAC/B,6LAAC;wBACC,WAAW,CAAC,6BAA6B,EAAE,SAAS,eAAe,IAAI;wBACvE,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAIxE,wBACC,6LAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,8HAAA,CAAA,iBAAc,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAC/C,6LAAC;wBAEC,SAAS,IAAM,qBAAqB;wBACpC,WAAU;;0CAEV,6LAAC;0CAAM;;;;;;4BACN,aAAa,sBAAQ,6LAAC,uMAAA,CAAA,QAAK;gCAAC,MAAM;gCAAI,WAAU;;;;;;;uBAL5C;;;;;;;;;;YAYZ,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;;;;;;;AAKnC;GA9DwB;;QAC2B,+HAAA,CAAA,cAAW;;;KADtC", "debugId": null}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/PWAInstaller.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Download, X } from 'lucide-react';\n\ninterface BeforeInstallPromptEvent extends Event {\n  readonly platforms: string[];\n  readonly userChoice: Promise<{\n    outcome: 'accepted' | 'dismissed';\n    platform: string;\n  }>;\n  prompt(): Promise<void>;\n}\n\nexport default function PWAInstaller() {\n  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);\n  const [showInstallPrompt, setShowInstallPrompt] = useState(false);\n  const [isInstalled, setIsInstalled] = useState(false);\n\n  useEffect(() => {\n    // Check if app is already installed\n    if (window.matchMedia('(display-mode: standalone)').matches) {\n      setIsInstalled(true);\n      return;\n    }\n\n    // Listen for the beforeinstallprompt event\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault();\n      setDeferredPrompt(e as BeforeInstallPromptEvent);\n      setShowInstallPrompt(true);\n    };\n\n    // Listen for app installed event\n    const handleAppInstalled = () => {\n      setIsInstalled(true);\n      setShowInstallPrompt(false);\n      setDeferredPrompt(null);\n    };\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n    window.addEventListener('appinstalled', handleAppInstalled);\n\n    // Register service worker\n    if ('serviceWorker' in navigator) {\n      navigator.serviceWorker.register('/sw.js')\n        .then((registration) => {\n          console.log('Service Worker registered:', registration);\n        })\n        .catch((error) => {\n          console.error('Service Worker registration failed:', error);\n        });\n    }\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n      window.removeEventListener('appinstalled', handleAppInstalled);\n    };\n  }, []);\n\n  const handleInstallClick = async () => {\n    if (!deferredPrompt) return;\n\n    try {\n      await deferredPrompt.prompt();\n      const { outcome } = await deferredPrompt.userChoice;\n      \n      if (outcome === 'accepted') {\n        console.log('User accepted the install prompt');\n      } else {\n        console.log('User dismissed the install prompt');\n      }\n      \n      setDeferredPrompt(null);\n      setShowInstallPrompt(false);\n    } catch (error) {\n      console.error('Error during installation:', error);\n    }\n  };\n\n  const handleDismiss = () => {\n    setShowInstallPrompt(false);\n    // Don't show again for this session\n    sessionStorage.setItem('pwa-install-dismissed', 'true');\n  };\n\n  // Don't show if already installed or dismissed\n  if (isInstalled || !showInstallPrompt || sessionStorage.getItem('pwa-install-dismissed')) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4 shadow-lg z-50\">\n      <div className=\"flex items-start justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          <Download className=\"w-5 h-5 text-purple-400\" />\n          <h3 className=\"text-white font-semibold\">Install AstroConnect</h3>\n        </div>\n        <button\n          onClick={handleDismiss}\n          className=\"text-gray-400 hover:text-white transition-colors\"\n        >\n          <X size={20} />\n        </button>\n      </div>\n      \n      <p className=\"text-gray-300 text-sm mb-4\">\n        Install our app for quick access to your daily horoscope and cosmic insights!\n      </p>\n      \n      <div className=\"flex space-x-2\">\n        <button\n          onClick={handleInstallClick}\n          className=\"flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n        >\n          Install\n        </button>\n        <button\n          onClick={handleDismiss}\n          className=\"px-4 py-2 text-gray-300 hover:text-white text-sm transition-colors\"\n        >\n          Not now\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAce,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IACtF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,oCAAoC;YACpC,IAAI,OAAO,UAAU,CAAC,8BAA8B,OAAO,EAAE;gBAC3D,eAAe;gBACf;YACF;YAEA,2CAA2C;YAC3C,MAAM;oEAA4B,CAAC;oBACjC,EAAE,cAAc;oBAChB,kBAAkB;oBAClB,qBAAqB;gBACvB;;YAEA,iCAAiC;YACjC,MAAM;6DAAqB;oBACzB,eAAe;oBACf,qBAAqB;oBACrB,kBAAkB;gBACpB;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAC/C,OAAO,gBAAgB,CAAC,gBAAgB;YAExC,0BAA0B;YAC1B,IAAI,mBAAmB,WAAW;gBAChC,UAAU,aAAa,CAAC,QAAQ,CAAC,UAC9B,IAAI;8CAAC,CAAC;wBACL,QAAQ,GAAG,CAAC,8BAA8B;oBAC5C;6CACC,KAAK;8CAAC,CAAC;wBACN,QAAQ,KAAK,CAAC,uCAAuC;oBACvD;;YACJ;YAEA;0CAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;oBAClD,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;iCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,eAAe,MAAM;YAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;YAEnD,IAAI,YAAY,YAAY;gBAC1B,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,kBAAkB;YAClB,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,gBAAgB;QACpB,qBAAqB;QACrB,oCAAoC;QACpC,eAAe,OAAO,CAAC,yBAAyB;IAClD;IAEA,+CAA+C;IAC/C,IAAI,eAAe,CAAC,qBAAqB,eAAe,OAAO,CAAC,0BAA0B;QACxF,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;;;;;;;kCAE3C,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAIb,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;0BAI1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;GAhHwB;KAAA", "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/MobileNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Menu, X, Home, Star, Calendar, Clock, Settings, BookOpen } from 'lucide-react';\nimport TranslatedText from './TranslatedText';\n\ninterface MobileNavigationProps {\n  activeTab: string;\n  onTabChange: (tab: string) => void;\n  className?: string;\n}\n\nexport default function MobileNavigation({ activeTab, onTabChange, className = '' }: MobileNavigationProps) {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const navigationItems = [\n    { id: 'horoscope', label: 'Horoscope', icon: BookOpen },\n    { id: 'guide', label: 'Daily Guide', icon: Clock }\n  ];\n\n  const handleTabChange = (tabId: string) => {\n    onTabChange(tabId);\n    setIsOpen(false);\n  };\n\n  return (\n    <>\n      {/* Mobile Menu Button */}\n      <div className={`md:hidden ${className}`}>\n        <button\n          onClick={() => setIsOpen(!isOpen)}\n          className=\"flex items-center justify-center w-10 h-10 bg-white/10 hover:bg-white/20 rounded-lg transition-colors\"\n        >\n          {isOpen ? <X size={20} className=\"text-white\" /> : <Menu size={20} className=\"text-white\" />}\n        </button>\n      </div>\n\n      {/* Mobile Menu Overlay */}\n      {isOpen && (\n        <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 md:hidden\">\n          <div className=\"absolute top-0 right-0 w-64 h-full bg-gradient-to-b from-purple-900 to-indigo-900 shadow-xl\">\n            <div className=\"p-4\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className=\"text-white font-semibold\">\n                  <TranslatedText text=\"Navigation\" />\n                </h2>\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className=\"text-gray-400 hover:text-white transition-colors\"\n                >\n                  <X size={20} />\n                </button>\n              </div>\n\n              <nav className=\"space-y-2\">\n                {navigationItems.map(({ id, label, icon: Icon }) => (\n                  <button\n                    key={id}\n                    onClick={() => handleTabChange(id)}\n                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${\n                      activeTab === id\n                        ? 'bg-white/20 text-white'\n                        : 'text-gray-300 hover:bg-white/10 hover:text-white'\n                    }`}\n                  >\n                    <Icon size={20} />\n                    <span><TranslatedText text={label} /></span>\n                  </button>\n                ))}\n              </nav>\n\n              <div className=\"mt-8 pt-6 border-t border-white/10\">\n                <button className=\"w-full flex items-center space-x-3 px-4 py-3 text-gray-300 hover:bg-white/10 hover:text-white rounded-lg transition-colors\">\n                  <Settings size={20} />\n                  <span><TranslatedText text=\"Settings\" /></span>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Bottom Navigation for Mobile */}\n      <div className=\"fixed bottom-0 left-0 right-0 bg-black/20 backdrop-blur-sm border-t border-white/10 md:hidden z-40\">\n        <div className=\"flex items-center justify-around py-2\">\n          {navigationItems.slice(0, 4).map(({ id, label, icon: Icon }) => (\n            <button\n              key={id}\n              onClick={() => handleTabChange(id)}\n              className={`flex flex-col items-center space-y-1 px-3 py-2 rounded-lg transition-colors ${\n                activeTab === id\n                  ? 'text-purple-400'\n                  : 'text-gray-400 hover:text-white'\n              }`}\n            >\n              <Icon size={20} />\n              <span className=\"text-xs\">\n                <TranslatedText text={label.split(' ')[0]} />\n              </span>\n            </button>\n          ))}\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAYe,SAAS,iBAAiB,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,EAAyB;;IACxG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,kBAAkB;QACtB;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,iNAAA,CAAA,WAAQ;QAAC;QACtD;YAAE,IAAI;YAAS,OAAO;YAAe,MAAM,uMAAA,CAAA,QAAK;QAAC;KAClD;IAED,MAAM,kBAAkB,CAAC;QACvB,YAAY;QACZ,UAAU;IACZ;IAEA,qBACE;;0BAEE,6LAAC;gBAAI,WAAW,CAAC,UAAU,EAAE,WAAW;0BACtC,cAAA,6LAAC;oBACC,SAAS,IAAM,UAAU,CAAC;oBAC1B,WAAU;8BAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;wBAAC,MAAM;wBAAI,WAAU;;;;;6CAAkB,6LAAC,qMAAA,CAAA,OAAI;wBAAC,MAAM;wBAAI,WAAU;;;;;;;;;;;;;;;;YAKhF,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC,uIAAA,CAAA,UAAc;4CAAC,MAAK;;;;;;;;;;;kDAEvB,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAIb,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,iBAC7C,6LAAC;wCAEC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,CAAC,0EAA0E,EACpF,cAAc,KACV,2BACA,oDACJ;;0DAEF,6LAAC;gDAAK,MAAM;;;;;;0DACZ,6LAAC;0DAAK,cAAA,6LAAC,uIAAA,CAAA,UAAc;oDAAC,MAAM;;;;;;;;;;;;uCATvB;;;;;;;;;;0CAcX,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;sDAChB,6LAAC;sDAAK,cAAA,6LAAC,uIAAA,CAAA,UAAc;gDAAC,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,iBACzD,6LAAC;4BAEC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,4EAA4E,EACtF,cAAc,KACV,oBACA,kCACJ;;8CAEF,6LAAC;oCAAK,MAAM;;;;;;8CACZ,6LAAC;oCAAK,WAAU;8CACd,cAAA,6LAAC,uIAAA,CAAA,UAAc;wCAAC,MAAM,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;2BAVtC;;;;;;;;;;;;;;;;;AAkBnB;GA7FwB;KAAA", "debugId": null}}, {"offset": {"line": 1138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/hooks/useAuth';\nimport { useLanguage } from '@/hooks/useLanguage';\nimport { DashboardData } from '@/types';\nimport { ZODIAC_INFO } from '@/utils/zodiac';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport ZodiacCard from '@/components/ZodiacCard';\nimport TranslatedText from '@/components/TranslatedText';\nimport LanguageSwitcher from '@/components/LanguageSwitcher';\nimport PWAInstaller from '@/components/PWAInstaller';\nimport MobileNavigation from '@/components/MobileNavigation';\nimport { Settings, Star, Calendar, Clock, Palette, Hash, BookOpen } from 'lucide-react';\n\nexport default function Dashboard() {\n  const { user, isAuthenticated, loading: authLoading } = useAuth();\n  const { language, setLanguage } = useLanguage();\n  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState<'horoscope' | 'guide'>('horoscope');\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!authLoading && !isAuthenticated) {\n      router.push('/');\n      return;\n    }\n\n    if (user) {\n      fetchDashboardData();\n    }\n  }, [user, isAuthenticated, authLoading, router, language]);\n\n  const fetchDashboardData = async () => {\n    if (!user) return;\n\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/dashboard?userId=${user.id}&language=${language}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setDashboardData(data.data);\n      } else {\n        setError(data.error || 'Failed to load dashboard data');\n      }\n    } catch (err) {\n      console.error('Dashboard fetch error:', err);\n      setError('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLanguageChange = async (newLanguage: 'en' | 'si') => {\n    // Update user preference in backend\n    if (user) {\n      try {\n        await fetch('/api/dashboard', {\n          method: 'POST',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({ userId: user.id, language: newLanguage })\n        });\n      } catch (error) {\n        console.error('Failed to update language preference:', error);\n      }\n    }\n  };\n\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\">\n        <LoadingSpinner message=\"Loading your cosmic insights...\" />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\">\n        <ErrorMessage\n          title=\"Error Loading Dashboard\"\n          message={error}\n          onRetry={fetchDashboardData}\n        />\n      </div>\n    );\n  }\n\n  if (!dashboardData || !user) {\n    return null;\n  }\n\n  const zodiacInfo = ZODIAC_INFO[user.zodiacSign];\n\n  // Fallback if zodiac info is not found\n  if (!zodiacInfo) {\n    console.error('Zodiac info not found for sign:', user.zodiacSign);\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\">\n        <ErrorMessage\n          title=\"Configuration Error\"\n          message=\"Unable to load zodiac information. Please contact support.\"\n          onRetry={fetchDashboardData}\n        />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\">\n      {/* Header */}\n      <header className=\"bg-black/20 backdrop-blur-sm border-b border-white/10\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"text-3xl\">{zodiacInfo.symbol}</div>\n              <div>\n                <h1 className=\"text-xl font-bold text-white\">Welcome, {user.name}</h1>\n                <p className=\"text-gray-300 text-sm\">{zodiacInfo.name} • {zodiacInfo.dates}</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <LanguageSwitcher onLanguageChange={handleLanguageChange} />\n\n              <button className=\"hidden md:block text-gray-300 hover:text-white transition-colors\">\n                <Settings size={20} />\n              </button>\n\n              <MobileNavigation\n                activeTab={activeTab}\n                onTabChange={setActiveTab}\n              />\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation Tabs */}\n      <nav className=\"bg-black/10 backdrop-blur-sm border-b border-white/10\">\n        <div className=\"max-w-6xl mx-auto px-4\">\n          <div className=\"flex space-x-8\">\n            {[\n              { id: 'horoscope', label: 'Horoscope', icon: BookOpen },\n              { id: 'guide', label: 'Daily Guide', icon: Clock }\n            ].map(({ id, label, icon: Icon }) => (\n              <button\n                key={id}\n                onClick={() => setActiveTab(id as any)}\n                className={`flex items-center space-x-2 py-4 px-2 border-b-2 transition-colors ${\n                  activeTab === id\n                    ? 'border-purple-400 text-white'\n                    : 'border-transparent text-gray-300 hover:text-white'\n                }`}\n              >\n                <Icon size={16} />\n                <span><TranslatedText text={label} /></span>\n              </button>\n            ))}\n          </div>\n        </div>\n      </nav>\n\n      {/* Content */}\n      <main className=\"max-w-6xl mx-auto px-4 py-8 pb-20 md:pb-8\">\n        {activeTab === 'horoscope' && (\n          <div className=\"space-y-6\">\n            {/* Today's Horoscope */}\n            {dashboardData.todayHoroscope && (\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <h2 className=\"text-2xl font-bold text-white mb-4 flex items-center\">\n                  <Star className=\"mr-2 text-yellow-400\" />\n                  <TranslatedText text=\"Today's Horoscope\" />\n                </h2>\n                <p className=\"text-gray-200 leading-relaxed text-lg\">\n                  <TranslatedText text={dashboardData.todayHoroscope.content} />\n                </p>\n              </div>\n            )}\n\n            {/* Weekly Horoscope */}\n            {dashboardData.weeklyHoroscope && (\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <h2 className=\"text-2xl font-bold text-white mb-4 flex items-center\">\n                  <Calendar className=\"mr-2 text-blue-400\" />\n                  <TranslatedText text=\"Weekly Horoscope\" />\n                </h2>\n                <p className=\"text-gray-200 leading-relaxed text-lg\">\n                  <TranslatedText text={dashboardData.weeklyHoroscope.content} />\n                </p>\n              </div>\n            )}\n\n            {/* Monthly Horoscope */}\n            {dashboardData.monthlyHoroscope && (\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <h2 className=\"text-2xl font-bold text-white mb-4 flex items-center\">\n                  <Calendar className=\"mr-2 text-purple-400\" />\n                  <TranslatedText text=\"Monthly Horoscope\" />\n                </h2>\n                <p className=\"text-gray-200 leading-relaxed text-lg\">\n                  <TranslatedText text={dashboardData.monthlyHoroscope.content} />\n                </p>\n              </div>\n            )}\n\n            {/* No Horoscope Content */}\n            {!dashboardData.todayHoroscope && !dashboardData.weeklyHoroscope && !dashboardData.monthlyHoroscope && (\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center\">\n                <BookOpen className=\"mx-auto mb-4 text-gray-400\" size={48} />\n                <h3 className=\"text-xl font-semibold text-white mb-2\">\n                  <TranslatedText text=\"No Horoscope Available\" />\n                </h3>\n                <p className=\"text-gray-300\">\n                  <TranslatedText text=\"Your personalized horoscope content will appear here once added by the admin.\" />\n                </p>\n              </div>\n            )}\n          </div>\n        )}\n\n        {activeTab === 'guide' && (\n          dashboardData.dailyGuide ? (\n          <div className=\"space-y-6\">\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n              <h2 className=\"text-2xl font-bold text-white mb-6 flex items-center\">\n                <Clock className=\"mr-2 text-green-400\" />\n                <TranslatedText text=\"Today's Guidance\" />\n              </h2>\n\n              <div className=\"grid md:grid-cols-2 gap-6 mb-6\">\n                <div className=\"bg-white/5 rounded-lg p-4\">\n                  <div className=\"flex items-center mb-2\">\n                    <Hash className=\"text-yellow-400 mr-2\" size={20} />\n                    <h3 className=\"text-white font-semibold\">\n                      <TranslatedText text=\"Lucky Number\" />\n                    </h3>\n                  </div>\n                  <p className=\"text-2xl font-bold text-yellow-400\">\n                    {dashboardData.dailyGuide.lucky_number}\n                  </p>\n                </div>\n\n                <div className=\"bg-white/5 rounded-lg p-4\">\n                  <div className=\"flex items-center mb-2\">\n                    <Palette className=\"text-pink-400 mr-2\" size={20} />\n                    <h3 className=\"text-white font-semibold\">\n                      <TranslatedText text=\"Lucky Color\" />\n                    </h3>\n                  </div>\n                  <p className=\"text-lg font-semibold text-pink-400\">\n                    <TranslatedText text={dashboardData.dailyGuide.lucky_color} />\n                  </p>\n                </div>\n\n                <div className=\"bg-white/5 rounded-lg p-4 md:col-span-2\">\n                  <div className=\"flex items-center mb-2\">\n                    <Clock className=\"text-blue-400 mr-2\" size={20} />\n                    <h3 className=\"text-white font-semibold\">\n                      <TranslatedText text=\"Lucky Time\" />\n                    </h3>\n                  </div>\n                  <p className=\"text-lg font-semibold text-blue-400\">\n                    <TranslatedText text={dashboardData.dailyGuide.lucky_time} />\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"bg-white/5 rounded-lg p-4\">\n                <h3 className=\"text-white font-semibold mb-3\">\n                  <TranslatedText text=\"Daily Advice\" />\n                </h3>\n                <p className=\"text-gray-200 leading-relaxed\">\n                  <TranslatedText text={dashboardData.dailyGuide.advice} />\n                </p>\n              </div>\n            </div>\n          </div>\n          ) : (\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center\">\n              <Clock className=\"mx-auto mb-4 text-gray-400\" size={48} />\n              <h3 className=\"text-xl font-semibold text-white mb-2\">\n                <TranslatedText text=\"No Daily Guide Available\" />\n              </h3>\n              <p className=\"text-gray-300\">\n                <TranslatedText text=\"Your personalized daily guide will appear here once added by the admin.\" />\n              </p>\n            </div>\n          )\n        )}\n      </main>\n\n      {/* PWA Installer */}\n      <PWAInstaller />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAfA;;;;;;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC9D,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;IAC5C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAClE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;gBACpC,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,MAAM;gBACR;YACF;QACF;8BAAG;QAAC;QAAM;QAAiB;QAAa;QAAQ;KAAS;IAEzD,MAAM,qBAAqB;QACzB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,UAAU;YACpF,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,KAAK,IAAI;YAC5B,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,oCAAoC;QACpC,IAAI,MAAM;YACR,IAAI;gBACF,MAAM,MAAM,kBAAkB;oBAC5B,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBAAE,QAAQ,KAAK,EAAE;wBAAE,UAAU;oBAAY;gBAChE;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;YACzD;QACF;IACF;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;gBAAC,SAAQ;;;;;;;;;;;IAG9B;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,qIAAA,CAAA,UAAY;gBACX,OAAM;gBACN,SAAS;gBACT,SAAS;;;;;;;;;;;IAIjB;IAEA,IAAI,CAAC,iBAAiB,CAAC,MAAM;QAC3B,OAAO;IACT;IAEA,MAAM,aAAa,yHAAA,CAAA,cAAW,CAAC,KAAK,UAAU,CAAC;IAE/C,uCAAuC;IACvC,IAAI,CAAC,YAAY;QACf,QAAQ,KAAK,CAAC,mCAAmC,KAAK,UAAU;QAChE,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,qIAAA,CAAA,UAAY;gBACX,OAAM;gBACN,SAAQ;gBACR,SAAS;;;;;;;;;;;IAIjB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAY,WAAW,MAAM;;;;;;kDAC5C,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;;oDAA+B;oDAAU,KAAK,IAAI;;;;;;;0DAChE,6LAAC;gDAAE,WAAU;;oDAAyB,WAAW,IAAI;oDAAC;oDAAI,WAAW,KAAK;;;;;;;;;;;;;;;;;;;0CAI9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yIAAA,CAAA,UAAgB;wCAAC,kBAAkB;;;;;;kDAEpC,6LAAC;wCAAO,WAAU;kDAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;;;;;;kDAGlB,6LAAC,yIAAA,CAAA,UAAgB;wCACf,WAAW;wCACX,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,IAAI;gCAAa,OAAO;gCAAa,MAAM,iNAAA,CAAA,WAAQ;4BAAC;4BACtD;gCAAE,IAAI;gCAAS,OAAO;gCAAe,MAAM,uMAAA,CAAA,QAAK;4BAAC;yBAClD,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,iBAC9B,6LAAC;gCAEC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,mEAAmE,EAC7E,cAAc,KACV,iCACA,qDACJ;;kDAEF,6LAAC;wCAAK,MAAM;;;;;;kDACZ,6LAAC;kDAAK,cAAA,6LAAC,uIAAA,CAAA,UAAc;4CAAC,MAAM;;;;;;;;;;;;+BATvB;;;;;;;;;;;;;;;;;;;;0BAiBf,6LAAC;gBAAK,WAAU;;oBACb,cAAc,6BACb,6LAAC;wBAAI,WAAU;;4BAEZ,cAAc,cAAc,kBAC3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC,uIAAA,CAAA,UAAc;gDAAC,MAAK;;;;;;;;;;;;kDAEvB,6LAAC;wCAAE,WAAU;kDACX,cAAA,6LAAC,uIAAA,CAAA,UAAc;4CAAC,MAAM,cAAc,cAAc,CAAC,OAAO;;;;;;;;;;;;;;;;;4BAM/D,cAAc,eAAe,kBAC5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC,uIAAA,CAAA,UAAc;gDAAC,MAAK;;;;;;;;;;;;kDAEvB,6LAAC;wCAAE,WAAU;kDACX,cAAA,6LAAC,uIAAA,CAAA,UAAc;4CAAC,MAAM,cAAc,eAAe,CAAC,OAAO;;;;;;;;;;;;;;;;;4BAMhE,cAAc,gBAAgB,kBAC7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC,uIAAA,CAAA,UAAc;gDAAC,MAAK;;;;;;;;;;;;kDAEvB,6LAAC;wCAAE,WAAU;kDACX,cAAA,6LAAC,uIAAA,CAAA,UAAc;4CAAC,MAAM,cAAc,gBAAgB,CAAC,OAAO;;;;;;;;;;;;;;;;;4BAMjE,CAAC,cAAc,cAAc,IAAI,CAAC,cAAc,eAAe,IAAI,CAAC,cAAc,gBAAgB,kBACjG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAA6B,MAAM;;;;;;kDACvD,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC,uIAAA,CAAA,UAAc;4CAAC,MAAK;;;;;;;;;;;kDAEvB,6LAAC;wCAAE,WAAU;kDACX,cAAA,6LAAC,uIAAA,CAAA,UAAc;4CAAC,MAAK;;;;;;;;;;;;;;;;;;;;;;;oBAO9B,cAAc,WAAW,CACxB,cAAc,UAAU,iBACxB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC,uIAAA,CAAA,UAAc;4CAAC,MAAK;;;;;;;;;;;;8CAGvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;4DAAuB,MAAM;;;;;;sEAC7C,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC,uIAAA,CAAA,UAAc;gEAAC,MAAK;;;;;;;;;;;;;;;;;8DAGzB,6LAAC;oDAAE,WAAU;8DACV,cAAc,UAAU,CAAC,YAAY;;;;;;;;;;;;sDAI1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAU;4DAAqB,MAAM;;;;;;sEAC9C,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC,uIAAA,CAAA,UAAc;gEAAC,MAAK;;;;;;;;;;;;;;;;;8DAGzB,6LAAC;oDAAE,WAAU;8DACX,cAAA,6LAAC,uIAAA,CAAA,UAAc;wDAAC,MAAM,cAAc,UAAU,CAAC,WAAW;;;;;;;;;;;;;;;;;sDAI9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;4DAAqB,MAAM;;;;;;sEAC5C,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC,uIAAA,CAAA,UAAc;gEAAC,MAAK;;;;;;;;;;;;;;;;;8DAGzB,6LAAC;oDAAE,WAAU;8DACX,cAAA,6LAAC,uIAAA,CAAA,UAAc;wDAAC,MAAM,cAAc,UAAU,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;8CAK/D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC,uIAAA,CAAA,UAAc;gDAAC,MAAK;;;;;;;;;;;sDAEvB,6LAAC;4CAAE,WAAU;sDACX,cAAA,6LAAC,uIAAA,CAAA,UAAc;gDAAC,MAAM,cAAc,UAAU,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAM3D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;gCAA6B,MAAM;;;;;;0CACpD,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC,uIAAA,CAAA,UAAc;oCAAC,MAAK;;;;;;;;;;;0CAEvB,6LAAC;gCAAE,WAAU;0CACX,cAAA,6LAAC,uIAAA,CAAA,UAAc;oCAAC,MAAK;;;;;;;;;;;;;;;;4BAI7B;;;;;;;0BAIF,6LAAC,qIAAA,CAAA,UAAY;;;;;;;;;;;AAGnB;GA5RwB;;QACkC,0HAAA,CAAA,UAAO;QAC7B,+HAAA,CAAA,cAAW;QAK9B,qIAAA,CAAA,YAAS;;;KAPF", "debugId": null}}]}