const fs = require('fs');
const path = require('path');

// Create icons directory if it doesn't exist
const iconsDir = path.join(__dirname, '../public/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Minimal 1x1 transparent PNG in base64
const minimalPNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';

// Create a simple colored PNG for icons (purple square)
const purplePNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

// Create placeholder PNG files for all required sizes
const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
sizes.forEach(size => {
  const pngPath = path.join(iconsDir, `icon-${size}x${size}.png`);
  fs.writeFileSync(pngPath, Buffer.from(purplePNG, 'base64'));
  console.log(`Created ${pngPath}`);
});

// Create shortcut icons
const shortcutIcons = ['shortcut-today.png', 'shortcut-guide.png'];
shortcutIcons.forEach(iconName => {
  const pngPath = path.join(iconsDir, iconName);
  fs.writeFileSync(pngPath, Buffer.from(purplePNG, 'base64'));
  console.log(`Created ${pngPath}`);
});

// Create screenshots directory and placeholder screenshots
const screenshotsDir = path.join(__dirname, '../public/screenshots');
if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

const screenshots = [
  'desktop-home.png',
  'mobile-dashboard.png'
];

screenshots.forEach(screenshot => {
  const screenshotPath = path.join(screenshotsDir, screenshot);
  fs.writeFileSync(screenshotPath, Buffer.from(purplePNG, 'base64'));
  console.log(`Created ${screenshotPath}`);
});

// Create favicon.ico (as a simple PNG for now)
const faviconPath = path.join(__dirname, '../public/favicon.ico');
fs.writeFileSync(faviconPath, Buffer.from(purplePNG, 'base64'));
console.log(`Created ${faviconPath}`);

console.log('PNG placeholder creation complete!');