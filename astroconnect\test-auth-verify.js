const { default: fetch } = require('node-fetch');

async function testAuthVerify() {
  try {
    console.log('Testing auth verification...');
    
    // First, login to get a token
    const loginResponse = await fetch('http://localhost:3000/api/admin/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      }),
    });
    
    const loginData = await loginResponse.json();
    
    if (!loginData.success) {
      console.log('❌ Login failed:', loginData.error);
      return;
    }
    
    console.log('✅ Login successful, got token');
    const token = loginData.data.token;
    
    // Now test the verification endpoint
    const verifyResponse = await fetch('http://localhost:3000/api/admin/auth/verify', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('Verify response status:', verifyResponse.status);
    
    const verifyData = await verifyResponse.json();
    console.log('Verify response data:', JSON.stringify(verifyData, null, 2));
    
    if (verifyData.success) {
      console.log('✅ Auth verification successful!');
      console.log('Admin data:', verifyData.data.admin);
    } else {
      console.log('❌ Auth verification failed:', verifyData.error);
    }
    
  } catch (error) {
    console.error('❌ Error testing auth verification:', error);
  }
}

testAuthVerify();
