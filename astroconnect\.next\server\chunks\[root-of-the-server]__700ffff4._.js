module.exports = {

"[project]/.next-internal/server/app/api/admin/scheduler/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateAdminToken": (()=>generateAdminToken),
    "generateSecurePassword": (()=>generateSecurePassword),
    "getAdminFromRequest": (()=>getAdminFromRequest),
    "hashPassword": (()=>hashPassword),
    "requireAdminAuth": (()=>requireAdminAuth),
    "verifyAdminToken": (()=>verifyAdminToken),
    "verifyPassword": (()=>verifyPassword)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-route] (ecmascript)");
;
;
const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';
async function hashPassword(password) {
    const saltRounds = 12;
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(password, saltRounds);
}
async function verifyPassword(password, hashedPassword) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(password, hashedPassword);
}
function generateAdminToken(payload) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].sign(payload, JWT_SECRET, {
        expiresIn: '24h',
        issuer: 'astroconnect-admin'
    });
}
function verifyAdminToken(token) {
    try {
        const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].verify(token, JWT_SECRET, {
            issuer: 'astroconnect-admin'
        });
        return decoded;
    } catch (error) {
        console.error('Token verification failed:', error);
        return null;
    }
}
function getAdminFromRequest(request) {
    try {
        // Check Authorization header
        const authHeader = request.headers.get('authorization');
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            return verifyAdminToken(token);
        }
        // Check cookie
        const tokenCookie = request.cookies.get('admin-token');
        if (tokenCookie) {
            return verifyAdminToken(tokenCookie.value);
        }
        return null;
    } catch (error) {
        console.error('Error extracting admin from request:', error);
        return null;
    }
}
function requireAdminAuth(admin) {
    return admin !== null && admin.role === 'admin';
}
function generateSecurePassword(length = 12) {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    for(let i = 0; i < length; i++){
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
}
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkDatabaseConnection": (()=>checkDatabaseConnection),
    "disconnectPrisma": (()=>disconnectPrisma),
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
async function disconnectPrisma() {
    await prisma.$disconnect();
}
async function checkDatabaseConnection() {
    try {
        await prisma.$queryRaw`SELECT 1`;
        return true;
    } catch (error) {
        console.error('Database connection failed:', error);
        return false;
    }
}
}}),
"[project]/src/lib/gemini.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateAllDailyReadings": (()=>generateAllDailyReadings),
    "generateDailyZodiacReading": (()=>generateDailyZodiacReading),
    "getZodiacInfo": (()=>getZodiacInfo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@google/generative-ai/dist/index.mjs [app-route] (ecmascript)");
;
const genAI = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$google$2f$generative$2d$ai$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GoogleGenerativeAI"](process.env.GEMINI_API_KEY || '');
async function generateDailyZodiacReading(zodiacSign, date) {
    try {
        const model = genAI.getGenerativeModel({
            model: 'gemini-pro'
        });
        const prompt = `
Generate a comprehensive daily horoscope reading for ${zodiacSign.toUpperCase()} for ${date}.

Please provide a detailed, personalized reading that includes:

1. GENERAL READING (2-3 sentences): Overall energy and theme for the day
2. LOVE & RELATIONSHIPS (2-3 sentences): Romance, partnerships, family relationships
3. CAREER & MONEY (2-3 sentences): Work, business, financial matters
4. HEALTH & WELLNESS (2-3 sentences): Physical health, mental wellbeing, energy levels
5. LUCKY NUMBER: A single number between 1-99
6. LUCKY COLOR: A specific color name
7. LUCKY TIME: A time range (e.g., "10:00 AM - 12:00 PM")
8. LUCKY GEM: A gemstone name
9. DAILY ADVICE (1-2 sentences): Practical guidance for the day
10. MOOD: One word describing the overall mood (e.g., "Optimistic", "Cautious", "Energetic")
11. COMPATIBILITY: List 2-3 zodiac signs that are most compatible today

Make the reading positive, insightful, and specific to ${zodiacSign}. Use astrological knowledge and current planetary influences.

Format your response as JSON with these exact keys:
{
  "generalReading": "",
  "loveReading": "",
  "careerReading": "",
  "healthReading": "",
  "luckyNumber": 0,
  "luckyColor": "",
  "luckyTime": "",
  "luckyGem": "",
  "advice": "",
  "mood": "",
  "compatibility": ""
}
`;
        const result = await model.generateContent(prompt);
        const response = await result.response;
        const text = response.text();
        // Extract JSON from the response
        const jsonMatch = text.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            throw new Error('Invalid response format from Gemini API');
        }
        const parsedData = JSON.parse(jsonMatch[0]);
        return {
            zodiacSign,
            date,
            generalReading: parsedData.generalReading || '',
            loveReading: parsedData.loveReading || '',
            careerReading: parsedData.careerReading || '',
            healthReading: parsedData.healthReading || '',
            luckyNumber: parseInt(parsedData.luckyNumber) || Math.floor(Math.random() * 99) + 1,
            luckyColor: parsedData.luckyColor || 'Blue',
            luckyTime: parsedData.luckyTime || '10:00 AM - 12:00 PM',
            luckyGem: parsedData.luckyGem || 'Amethyst',
            advice: parsedData.advice || '',
            mood: parsedData.mood || 'Positive',
            compatibility: parsedData.compatibility || 'Aries, Leo, Sagittarius'
        };
    } catch (error) {
        console.error('Error generating zodiac reading:', error);
        // Fallback data if API fails
        return {
            zodiacSign,
            date,
            generalReading: `Today brings positive energy for ${zodiacSign}. The stars align to support your endeavors and bring clarity to your path.`,
            loveReading: 'Love is in the air today. Open your heart to new possibilities and strengthen existing bonds with understanding and compassion.',
            careerReading: 'Professional opportunities may present themselves today. Stay focused on your goals and trust your instincts in decision-making.',
            healthReading: 'Your energy levels are balanced today. Take time for self-care and listen to what your body needs for optimal wellness.',
            luckyNumber: Math.floor(Math.random() * 99) + 1,
            luckyColor: 'Blue',
            luckyTime: '10:00 AM - 12:00 PM',
            luckyGem: 'Amethyst',
            advice: 'Trust your intuition today and embrace the opportunities that come your way.',
            mood: 'Optimistic',
            compatibility: 'Aries, Leo, Sagittarius'
        };
    }
}
async function generateAllDailyReadings(date) {
    const zodiacSigns = [
        'aries',
        'taurus',
        'gemini',
        'cancer',
        'leo',
        'virgo',
        'libra',
        'scorpio',
        'sagittarius',
        'capricorn',
        'aquarius',
        'pisces'
    ];
    const readings = [];
    // Generate readings for all zodiac signs
    for (const sign of zodiacSigns){
        try {
            const reading = await generateDailyZodiacReading(sign, date);
            readings.push(reading);
            // Add a small delay to avoid rate limiting
            await new Promise((resolve)=>setTimeout(resolve, 1000));
        } catch (error) {
            console.error(`Error generating reading for ${sign}:`, error);
        }
    }
    return readings;
}
function getZodiacInfo(sign) {
    const zodiacInfo = {
        aries: {
            name: 'Aries',
            symbol: '♈',
            element: 'Fire',
            dates: 'Mar 21 - Apr 19'
        },
        taurus: {
            name: 'Taurus',
            symbol: '♉',
            element: 'Earth',
            dates: 'Apr 20 - May 20'
        },
        gemini: {
            name: 'Gemini',
            symbol: '♊',
            element: 'Air',
            dates: 'May 21 - Jun 20'
        },
        cancer: {
            name: 'Cancer',
            symbol: '♋',
            element: 'Water',
            dates: 'Jun 21 - Jul 22'
        },
        leo: {
            name: 'Leo',
            symbol: '♌',
            element: 'Fire',
            dates: 'Jul 23 - Aug 22'
        },
        virgo: {
            name: 'Virgo',
            symbol: '♍',
            element: 'Earth',
            dates: 'Aug 23 - Sep 22'
        },
        libra: {
            name: 'Libra',
            symbol: '♎',
            element: 'Air',
            dates: 'Sep 23 - Oct 22'
        },
        scorpio: {
            name: 'Scorpio',
            symbol: '♏',
            element: 'Water',
            dates: 'Oct 23 - Nov 21'
        },
        sagittarius: {
            name: 'Sagittarius',
            symbol: '♐',
            element: 'Fire',
            dates: 'Nov 22 - Dec 21'
        },
        capricorn: {
            name: 'Capricorn',
            symbol: '♑',
            element: 'Earth',
            dates: 'Dec 22 - Jan 19'
        },
        aquarius: {
            name: 'Aquarius',
            symbol: '♒',
            element: 'Air',
            dates: 'Jan 20 - Feb 18'
        },
        pisces: {
            name: 'Pisces',
            symbol: '♓',
            element: 'Water',
            dates: 'Feb 19 - Mar 20'
        }
    };
    return zodiacInfo[sign];
}
}}),
"[project]/src/lib/scheduler.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "dailyReadingsScheduler": (()=>dailyReadingsScheduler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gemini$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/gemini.ts [app-route] (ecmascript)");
;
;
class DailyReadingsScheduler {
    intervalId = null;
    isRunning = false;
    lastGeneratedDate = null;
    constructor(){
        this.start();
    }
    start() {
        if (this.isRunning) {
            console.log('📅 Daily readings scheduler is already running');
            return;
        }
        console.log('🚀 Starting daily readings scheduler...');
        this.isRunning = true;
        // Check immediately on startup
        this.checkAndGenerateReadings();
        // Then check every hour
        this.intervalId = setInterval(()=>{
            this.checkAndGenerateReadings();
        }, 60 * 60 * 1000); // Check every hour
        console.log('✅ Daily readings scheduler started - checking every hour');
    }
    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        this.isRunning = false;
        console.log('🛑 Daily readings scheduler stopped');
    }
    async checkAndGenerateReadings() {
        try {
            const today = new Date().toISOString().split('T')[0];
            const currentHour = new Date().getHours();
            // Only generate readings between 12 AM and 6 AM (when most users are asleep)
            // and only once per day
            if (currentHour >= 6 && this.lastGeneratedDate === today) {
                return;
            }
            console.log(`🕐 Checking daily readings for ${today} at ${new Date().toLocaleTimeString()}`);
            // Check if readings already exist for today
            const existingReadings = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].dailyZodiacReading.findMany({
                where: {
                    date: new Date(today),
                    language: 'en'
                }
            });
            if (existingReadings.length === 12) {
                console.log(`✅ Daily readings already exist for ${today} (${existingReadings.length} readings)`);
                this.lastGeneratedDate = today;
                return;
            }
            console.log(`🤖 Generating daily zodiac readings for ${today}...`);
            console.log(`📊 Found ${existingReadings.length}/12 existing readings`);
            // Generate new readings using Gemini API
            const newReadings = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gemini$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateAllDailyReadings"])(today);
            console.log(`🎯 Generated ${newReadings.length} new readings from Gemini API`);
            // Save readings to database
            const savedReadings = [];
            for (const reading of newReadings){
                try {
                    const saved = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].dailyZodiacReading.upsert({
                        where: {
                            zodiacSign_date_language: {
                                zodiacSign: reading.zodiacSign,
                                date: new Date(today),
                                language: 'en'
                            }
                        },
                        update: {
                            generalReading: reading.generalReading,
                            loveReading: reading.loveReading,
                            careerReading: reading.careerReading,
                            healthReading: reading.healthReading,
                            luckyNumber: reading.luckyNumber,
                            luckyColor: reading.luckyColor,
                            luckyTime: reading.luckyTime,
                            luckyGem: reading.luckyGem,
                            advice: reading.advice,
                            mood: reading.mood,
                            compatibility: reading.compatibility,
                            updatedAt: new Date()
                        },
                        create: {
                            zodiacSign: reading.zodiacSign,
                            date: new Date(today),
                            generalReading: reading.generalReading,
                            loveReading: reading.loveReading,
                            careerReading: reading.careerReading,
                            healthReading: reading.healthReading,
                            luckyNumber: reading.luckyNumber,
                            luckyColor: reading.luckyColor,
                            luckyTime: reading.luckyTime,
                            luckyGem: reading.luckyGem,
                            advice: reading.advice,
                            mood: reading.mood,
                            compatibility: reading.compatibility,
                            language: 'en'
                        }
                    });
                    savedReadings.push(saved);
                    console.log(`✅ Saved reading for ${reading.zodiacSign}`);
                } catch (error) {
                    console.error(`❌ Error saving reading for ${reading.zodiacSign}:`, error);
                }
            }
            this.lastGeneratedDate = today;
            console.log(`🎉 Successfully generated and saved ${savedReadings.length} daily readings for ${today}`);
            // Clean up old readings (keep only last 30 days)
            await this.cleanupOldReadings();
        } catch (error) {
            console.error('❌ Error in daily readings scheduler:', error);
        }
    }
    async cleanupOldReadings() {
        try {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const deletedCount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].dailyZodiacReading.deleteMany({
                where: {
                    date: {
                        lt: thirtyDaysAgo
                    }
                }
            });
            if (deletedCount.count > 0) {
                console.log(`🧹 Cleaned up ${deletedCount.count} old daily readings`);
            }
        } catch (error) {
            console.error('❌ Error cleaning up old readings:', error);
        }
    }
    // Method to manually trigger reading generation (for testing)
    async generateNow(date) {
        const targetDate = date || new Date().toISOString().split('T')[0];
        console.log(`🔄 Manually generating readings for ${targetDate}...`);
        try {
            const readings = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gemini$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateAllDailyReadings"])(targetDate);
            const savedReadings = [];
            for (const reading of readings){
                const saved = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].dailyZodiacReading.upsert({
                    where: {
                        zodiacSign_date_language: {
                            zodiacSign: reading.zodiacSign,
                            date: new Date(targetDate),
                            language: 'en'
                        }
                    },
                    update: {
                        generalReading: reading.generalReading,
                        loveReading: reading.loveReading,
                        careerReading: reading.careerReading,
                        healthReading: reading.healthReading,
                        luckyNumber: reading.luckyNumber,
                        luckyColor: reading.luckyColor,
                        luckyTime: reading.luckyTime,
                        luckyGem: reading.luckyGem,
                        advice: reading.advice,
                        mood: reading.mood,
                        compatibility: reading.compatibility,
                        updatedAt: new Date()
                    },
                    create: {
                        zodiacSign: reading.zodiacSign,
                        date: new Date(targetDate),
                        generalReading: reading.generalReading,
                        loveReading: reading.loveReading,
                        careerReading: reading.careerReading,
                        healthReading: reading.healthReading,
                        luckyNumber: reading.luckyNumber,
                        luckyColor: reading.luckyColor,
                        luckyTime: reading.luckyTime,
                        luckyGem: reading.luckyGem,
                        advice: reading.advice,
                        mood: reading.mood,
                        compatibility: reading.compatibility,
                        language: 'en'
                    }
                });
                savedReadings.push(saved);
            }
            console.log(`✅ Manually generated ${savedReadings.length} readings for ${targetDate}`);
            return savedReadings;
        } catch (error) {
            console.error('❌ Error in manual generation:', error);
            throw error;
        }
    }
    getStatus() {
        return {
            isRunning: this.isRunning,
            lastGeneratedDate: this.lastGeneratedDate,
            nextCheck: this.intervalId ? 'Every hour' : 'Not scheduled'
        };
    }
}
const dailyReadingsScheduler = new DailyReadingsScheduler();
// Graceful shutdown
process.on('SIGTERM', ()=>{
    console.log('🔄 Received SIGTERM, stopping scheduler...');
    dailyReadingsScheduler.stop();
});
process.on('SIGINT', ()=>{
    console.log('🔄 Received SIGINT, stopping scheduler...');
    dailyReadingsScheduler.stop();
});
}}),
"[project]/src/app/api/admin/scheduler/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scheduler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/scheduler.ts [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        // Check admin authentication
        const admin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAdminFromRequest"])(request);
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["requireAdminAuth"])(admin)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Unauthorized - Admin access required'
            }, {
                status: 401
            });
        }
        const status = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scheduler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dailyReadingsScheduler"].getStatus();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                scheduler: status,
                serverTime: new Date().toISOString(),
                localTime: new Date().toLocaleString()
            },
            message: 'Scheduler status retrieved successfully'
        });
    } catch (error) {
        console.error('❌ Error getting scheduler status:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to get scheduler status'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        // Check admin authentication
        const admin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAdminFromRequest"])(request);
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["requireAdminAuth"])(admin)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Unauthorized - Admin access required'
            }, {
                status: 401
            });
        }
        const { action, date } = await request.json();
        if (action === 'generate') {
            console.log('🔄 Admin triggered manual reading generation');
            const targetDate = date || new Date().toISOString().split('T')[0];
            const readings = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scheduler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dailyReadingsScheduler"].generateNow(targetDate);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: {
                    readings,
                    count: readings.length,
                    date: targetDate
                },
                message: `Successfully generated ${readings.length} daily readings for ${targetDate}`
            });
        }
        if (action === 'status') {
            const status = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scheduler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["dailyReadingsScheduler"].getStatus();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: {
                    scheduler: status
                },
                message: 'Scheduler status retrieved successfully'
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Invalid action. Use "generate" or "status"'
        }, {
            status: 400
        });
    } catch (error) {
        console.error('❌ Error in scheduler action:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Failed to execute scheduler action'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__700ffff4._.js.map