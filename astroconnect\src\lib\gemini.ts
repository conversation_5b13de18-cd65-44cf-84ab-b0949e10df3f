import { GoogleGenerativeAI } from '@google/generative-ai';
import { ZodiacSign } from '@/types';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

export interface DailyZodiacData {
  zodiacSign: ZodiacSign;
  date: string;
  generalReading: string;
  loveReading: string;
  careerReading: string;
  healthReading: string;
  luckyNumber: number;
  luckyColor: string;
  luckyTime: string;
  luckyGem: string;
  advice: string;
  mood: string;
  compatibility: string;
}

export async function generateDailyZodiacReading(zodiacSign: ZodiacSign, date: string): Promise<DailyZodiacData> {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-pro' });

    const prompt = `
Generate a comprehensive daily horoscope reading for ${zodiacSign.toUpperCase()} for ${date}.

Please provide a detailed, personalized reading that includes:

1. GENERAL READING (2-3 sentences): Overall energy and theme for the day
2. LOVE & RELATIONSHIPS (2-3 sentences): Romance, partnerships, family relationships
3. CAREER & MONEY (2-3 sentences): Work, business, financial matters
4. HEALTH & WELLNESS (2-3 sentences): Physical health, mental wellbeing, energy levels
5. LUCKY NUMBER: A single number between 1-99
6. LUCKY COLOR: A specific color name
7. LUCKY TIME: A time range (e.g., "10:00 AM - 12:00 PM")
8. LUCKY GEM: A gemstone name
9. DAILY ADVICE (1-2 sentences): Practical guidance for the day
10. MOOD: One word describing the overall mood (e.g., "Optimistic", "Cautious", "Energetic")
11. COMPATIBILITY: List 2-3 zodiac signs that are most compatible today

Make the reading positive, insightful, and specific to ${zodiacSign}. Use astrological knowledge and current planetary influences.

Format your response as JSON with these exact keys:
{
  "generalReading": "",
  "loveReading": "",
  "careerReading": "",
  "healthReading": "",
  "luckyNumber": 0,
  "luckyColor": "",
  "luckyTime": "",
  "luckyGem": "",
  "advice": "",
  "mood": "",
  "compatibility": ""
}
`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Extract JSON from the response
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('Invalid response format from Gemini API');
    }

    const parsedData = JSON.parse(jsonMatch[0]);

    return {
      zodiacSign,
      date,
      generalReading: parsedData.generalReading || '',
      loveReading: parsedData.loveReading || '',
      careerReading: parsedData.careerReading || '',
      healthReading: parsedData.healthReading || '',
      luckyNumber: parseInt(parsedData.luckyNumber) || Math.floor(Math.random() * 99) + 1,
      luckyColor: parsedData.luckyColor || 'Blue',
      luckyTime: parsedData.luckyTime || '10:00 AM - 12:00 PM',
      luckyGem: parsedData.luckyGem || 'Amethyst',
      advice: parsedData.advice || '',
      mood: parsedData.mood || 'Positive',
      compatibility: parsedData.compatibility || 'Aries, Leo, Sagittarius'
    };

  } catch (error) {
    console.error('Error generating zodiac reading:', error);
    
    // Fallback data if API fails
    return {
      zodiacSign,
      date,
      generalReading: `Today brings positive energy for ${zodiacSign}. The stars align to support your endeavors and bring clarity to your path.`,
      loveReading: 'Love is in the air today. Open your heart to new possibilities and strengthen existing bonds with understanding and compassion.',
      careerReading: 'Professional opportunities may present themselves today. Stay focused on your goals and trust your instincts in decision-making.',
      healthReading: 'Your energy levels are balanced today. Take time for self-care and listen to what your body needs for optimal wellness.',
      luckyNumber: Math.floor(Math.random() * 99) + 1,
      luckyColor: 'Blue',
      luckyTime: '10:00 AM - 12:00 PM',
      luckyGem: 'Amethyst',
      advice: 'Trust your intuition today and embrace the opportunities that come your way.',
      mood: 'Optimistic',
      compatibility: 'Aries, Leo, Sagittarius'
    };
  }
}

export async function generateAllDailyReadings(date: string): Promise<DailyZodiacData[]> {
  const zodiacSigns: ZodiacSign[] = [
    'aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo',
    'libra', 'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'
  ];

  const readings: DailyZodiacData[] = [];

  // Generate readings for all zodiac signs
  for (const sign of zodiacSigns) {
    try {
      const reading = await generateDailyZodiacReading(sign, date);
      readings.push(reading);
      
      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Error generating reading for ${sign}:`, error);
    }
  }

  return readings;
}

export function getZodiacInfo(sign: ZodiacSign) {
  const zodiacInfo = {
    aries: { name: 'Aries', symbol: '♈', element: 'Fire', dates: 'Mar 21 - Apr 19' },
    taurus: { name: 'Taurus', symbol: '♉', element: 'Earth', dates: 'Apr 20 - May 20' },
    gemini: { name: 'Gemini', symbol: '♊', element: 'Air', dates: 'May 21 - Jun 20' },
    cancer: { name: 'Cancer', symbol: '♋', element: 'Water', dates: 'Jun 21 - Jul 22' },
    leo: { name: 'Leo', symbol: '♌', element: 'Fire', dates: 'Jul 23 - Aug 22' },
    virgo: { name: 'Virgo', symbol: '♍', element: 'Earth', dates: 'Aug 23 - Sep 22' },
    libra: { name: 'Libra', symbol: '♎', element: 'Air', dates: 'Sep 23 - Oct 22' },
    scorpio: { name: 'Scorpio', symbol: '♏', element: 'Water', dates: 'Oct 23 - Nov 21' },
    sagittarius: { name: 'Sagittarius', symbol: '♐', element: 'Fire', dates: 'Nov 22 - Dec 21' },
    capricorn: { name: 'Capricorn', symbol: '♑', element: 'Earth', dates: 'Dec 22 - Jan 19' },
    aquarius: { name: 'Aquarius', symbol: '♒', element: 'Air', dates: 'Jan 20 - Feb 18' },
    pisces: { name: 'Pisces', symbol: '♓', element: 'Water', dates: 'Feb 19 - Mar 20' }
  };

  return zodiacInfo[sign];
}
