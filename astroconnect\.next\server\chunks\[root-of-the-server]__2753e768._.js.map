{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Helper function to disconnect Prisma (useful for serverless)\nexport async function disconnectPrisma() {\n  await prisma.$disconnect();\n}\n\n// Helper function to check database connection\nexport async function checkDatabaseConnection() {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/qr.ts"], "sourcesContent": ["import { v4 as uuidv4 } from 'uuid';\nimport QRCode from 'qrcode';\n\nexport function generateQRToken(): string {\n  return uuidv4();\n}\n\nexport function generateQRUrl(token: string, baseUrl: string = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'): string {\n  return `${baseUrl}/qr/${token}`;\n}\n\nexport async function generateQRCodeImage(token: string, baseUrl?: string): Promise<string> {\n  const url = generateQRUrl(token, baseUrl);\n  try {\n    const qrCodeDataUrl = await QRCode.toDataURL(url, {\n      width: 300,\n      margin: 2,\n      color: {\n        dark: '#000000',\n        light: '#FFFFFF'\n      }\n    });\n    return qrCodeDataUrl;\n  } catch (error) {\n    console.error('Error generating QR code:', error);\n    throw new Error('Failed to generate QR code');\n  }\n}\n\nexport function extractTokenFromUrl(url: string): string | null {\n  try {\n    const urlObj = new URL(url);\n    const pathParts = urlObj.pathname.split('/');\n    const qrIndex = pathParts.indexOf('qr');\n    \n    if (qrIndex !== -1 && qrIndex < pathParts.length - 1) {\n      return pathParts[qrIndex + 1];\n    }\n    \n    return null;\n  } catch (error) {\n    console.error('Error extracting token from URL:', error);\n    return null;\n  }\n}\n\nexport function isValidQRToken(token: string): boolean {\n  // UUID v4 regex pattern\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(token);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;AACd;AAEO,SAAS,cAAc,KAAa,EAAE,UAAkB,6DAAmC,uBAAuB;IACvH,OAAO,GAAG,QAAQ,IAAI,EAAE,OAAO;AACjC;AAEO,eAAe,oBAAoB,KAAa,EAAE,OAAgB;IACvE,MAAM,MAAM,cAAc,OAAO;IACjC,IAAI;QACF,MAAM,gBAAgB,MAAM,wIAAA,CAAA,UAAM,CAAC,SAAS,CAAC,KAAK;YAChD,OAAO;YACP,QAAQ;YACR,OAAO;gBACL,MAAM;gBACN,OAAO;YACT;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,SAAS,oBAAoB,GAAW;IAC7C,IAAI;QACF,MAAM,SAAS,IAAI,IAAI;QACvB,MAAM,YAAY,OAAO,QAAQ,CAAC,KAAK,CAAC;QACxC,MAAM,UAAU,UAAU,OAAO,CAAC;QAElC,IAAI,YAAY,CAAC,KAAK,UAAU,UAAU,MAAM,GAAG,GAAG;YACpD,OAAO,SAAS,CAAC,UAAU,EAAE;QAC/B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAEO,SAAS,eAAe,KAAa;IAC1C,wBAAwB;IACxB,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/zodiac.ts"], "sourcesContent": ["import { ZodiacSign } from '@prisma/client';\n\nexport const ZODIAC_SIGNS: ZodiacSign[] = [\n  'aries', 'taurus', 'gemini', 'cancer',\n  'leo', 'virgo', 'libra', 'scorpio',\n  'sagittarius', 'capricorn', 'aquarius', 'pisces'\n];\n\nexport const ZODIAC_INFO = {\n  aries: { name: '<PERSON><PERSON>', symbol: '♈', dates: 'Mar 21 - Apr 19', element: 'Fire' },\n  taurus: { name: 'Taurus', symbol: '♉', dates: 'Apr 20 - May 20', element: 'Earth' },\n  gemini: { name: 'Gemini', symbol: '♊', dates: 'May 21 - Jun 20', element: 'Air' },\n  cancer: { name: 'Cancer', symbol: '♋', dates: 'Jun 21 - Jul 22', element: 'Water' },\n  leo: { name: '<PERSON>', symbol: '♌', dates: 'Jul 23 - Aug 22', element: 'Fire' },\n  virgo: { name: '<PERSON>ir<PERSON>', symbol: '♍', dates: 'Aug 23 - Sep 22', element: 'Earth' },\n  libra: { name: '<PERSON><PERSON>', symbol: '♎', dates: 'Sep 23 - Oct 22', element: 'Air' },\n  scorpio: { name: '<PERSON><PERSON><PERSON>', symbol: '♏', dates: 'Oct 23 - Nov 21', element: 'Water' },\n  sagittarius: { name: 'Sagittarius', symbol: '♐', dates: 'Nov 22 - Dec 21', element: 'Fire' },\n  capricorn: { name: 'Capricorn', symbol: '♑', dates: 'Dec 22 - Jan 19', element: 'Earth' },\n  aquarius: { name: 'Aquarius', symbol: '♒', dates: 'Jan 20 - Feb 18', element: 'Air' },\n  pisces: { name: 'Pisces', symbol: '♓', dates: 'Feb 19 - Mar 20', element: 'Water' }\n};\n\nexport function getZodiacFromDate(birthDate: string): ZodiacSign {\n  const date = new Date(birthDate);\n  const month = date.getMonth() + 1; // 1-12\n  const day = date.getDate();\n\n  if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) return 'aries';\n  if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) return 'taurus';\n  if ((month === 5 && day >= 21) || (month === 6 && day <= 20)) return 'gemini';\n  if ((month === 6 && day >= 21) || (month === 7 && day <= 22)) return 'cancer';\n  if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) return 'leo';\n  if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) return 'virgo';\n  if ((month === 9 && day >= 23) || (month === 10 && day <= 22)) return 'libra';\n  if ((month === 10 && day >= 23) || (month === 11 && day <= 21)) return 'scorpio';\n  if ((month === 11 && day >= 22) || (month === 12 && day <= 21)) return 'sagittarius';\n  if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) return 'capricorn';\n  if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) return 'aquarius';\n  return 'pisces';\n}\n\nexport function getZodiacColors(sign: ZodiacSign): string[] {\n  const colorMap = {\n    aries: ['#FF6B6B', '#FF4757'],\n    taurus: ['#2ECC71', '#27AE60'],\n    gemini: ['#F39C12', '#E67E22'],\n    cancer: ['#3498DB', '#2980B9'],\n    leo: ['#E74C3C', '#C0392B'],\n    virgo: ['#1ABC9C', '#16A085'],\n    libra: ['#9B59B6', '#8E44AD'],\n    scorpio: ['#34495E', '#2C3E50'],\n    sagittarius: ['#E67E22', '#D35400'],\n    capricorn: ['#95A5A6', '#7F8C8D'],\n    aquarius: ['#3498DB', '#2980B9'],\n    pisces: ['#1ABC9C', '#16A085']\n  };\n  return colorMap[sign];\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,eAA6B;IACxC;IAAS;IAAU;IAAU;IAC7B;IAAO;IAAS;IAAS;IACzB;IAAe;IAAa;IAAY;CACzC;AAEM,MAAM,cAAc;IACzB,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC/E,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAClF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IAChF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAClF,KAAK;QAAE,MAAM;QAAO,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC3E,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAChF,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IAC9E,SAAS;QAAE,MAAM;QAAW,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IACpF,aAAa;QAAE,MAAM;QAAe,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC3F,WAAW;QAAE,MAAM;QAAa,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IACxF,UAAU;QAAE,MAAM;QAAY,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IACpF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;AACpF;AAEO,SAAS,kBAAkB,SAAiB;IACjD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,QAAQ,KAAK,QAAQ,KAAK,GAAG,OAAO;IAC1C,MAAM,MAAM,KAAK,OAAO;IAExB,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACtE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACvE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACvE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACtE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,OAAO;AACT;AAEO,SAAS,gBAAgB,IAAgB;IAC9C,MAAM,WAAW;QACf,OAAO;YAAC;YAAW;SAAU;QAC7B,QAAQ;YAAC;YAAW;SAAU;QAC9B,QAAQ;YAAC;YAAW;SAAU;QAC9B,QAAQ;YAAC;YAAW;SAAU;QAC9B,KAAK;YAAC;YAAW;SAAU;QAC3B,OAAO;YAAC;YAAW;SAAU;QAC7B,OAAO;YAAC;YAAW;SAAU;QAC7B,SAAS;YAAC;YAAW;SAAU;QAC/B,aAAa;YAAC;YAAW;SAAU;QACnC,WAAW;YAAC;YAAW;SAAU;QACjC,UAAU;YAAC;YAAW;SAAU;QAChC,QAAQ;YAAC;YAAW;SAAU;IAChC;IACA,OAAO,QAAQ,CAAC,KAAK;AACvB", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\n\nexport interface AdminTokenPayload {\n  adminId: string;\n  email: string;\n  name: string;\n  role: string;\n}\n\n// Hash password\nexport async function hashPassword(password: string): Promise<string> {\n  const saltRounds = 12;\n  return bcrypt.hash(password, saltRounds);\n}\n\n// Verify password\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Generate JWT token for admin\nexport function generateAdminToken(payload: AdminTokenPayload): string {\n  return jwt.sign(payload, JWT_SECRET, {\n    expiresIn: '24h',\n    issuer: 'astroconnect-admin'\n  });\n}\n\n// Verify JWT token\nexport function verifyAdminToken(token: string): AdminTokenPayload | null {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET, {\n      issuer: 'astroconnect-admin'\n    }) as AdminTokenPayload;\n    return decoded;\n  } catch (error) {\n    console.error('Token verification failed:', error);\n    return null;\n  }\n}\n\n// Extract admin token from request\nexport function getAdminFromRequest(request: NextRequest): AdminTokenPayload | null {\n  try {\n    // Check Authorization header\n    const authHeader = request.headers.get('authorization');\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n      const token = authHeader.substring(7);\n      return verifyAdminToken(token);\n    }\n\n    // Check cookie\n    const tokenCookie = request.cookies.get('admin-token');\n    if (tokenCookie) {\n      return verifyAdminToken(tokenCookie.value);\n    }\n\n    return null;\n  } catch (error) {\n    console.error('Error extracting admin from request:', error);\n    return null;\n  }\n}\n\n// Validate admin permissions\nexport function requireAdminAuth(admin: AdminTokenPayload | null): boolean {\n  return admin !== null && admin.role === 'admin';\n}\n\n// Generate secure random password for demo\nexport function generateSecurePassword(length: number = 12): string {\n  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';\n  let password = '';\n  for (let i = 0; i < length; i++) {\n    password += charset.charAt(Math.floor(Math.random() * charset.length));\n  }\n  return password;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAUtC,eAAe,aAAa,QAAgB;IACjD,MAAM,aAAa;IACnB,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,mBAAmB,OAA0B;IAC3D,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QACnC,WAAW;QACX,QAAQ;IACV;AACF;AAGO,SAAS,iBAAiB,KAAa;IAC5C,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,YAAY;YAC5C,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,OAAoB;IACtD,IAAI;QACF,6BAA6B;QAC7B,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;YAClD,MAAM,QAAQ,WAAW,SAAS,CAAC;YACnC,OAAO,iBAAiB;QAC1B;QAEA,eAAe;QACf,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QACxC,IAAI,aAAa;YACf,OAAO,iBAAiB,YAAY,KAAK;QAC3C;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;IACT;AACF;AAGO,SAAS,iBAAiB,KAA+B;IAC9D,OAAO,UAAU,QAAQ,MAAM,IAAI,KAAK;AAC1C;AAGO,SAAS,uBAAuB,SAAiB,EAAE;IACxD,MAAM,UAAU;IAChB,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,YAAY,QAAQ,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM;IACtE;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/admin/users/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { prisma } from '@/lib/prisma';\nimport { generateQRCodeImage, generateQRToken } from '@/utils/qr';\nimport { getZodiacFromDate } from '@/utils/zodiac';\nimport { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';\nimport { ApiResponse, User, ZodiacSign, LanguageCode } from '@/types';\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Check admin authentication\n    const admin = getAdminFromRequest(request);\n    if (!requireAdminAuth(admin)) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Unauthorized - Admin access required'\n      }, { status: 401 });\n    }\n\n    const requestBody = await request.json();\n    console.log('Received user creation request:', requestBody);\n\n    const {\n      name,\n      email,\n      phoneNumber,\n      address,\n      birthDate,\n      birthTime,\n      zodiacSign,\n      languagePreference = 'en'\n    } = requestBody;\n\n    if (!name || !birthDate) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Name and birth date are required'\n      }, { status: 400 });\n    }\n\n    // Validate birth date format\n    const birthDateObj = new Date(birthDate);\n    if (isNaN(birthDateObj.getTime())) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Invalid birth date format'\n      }, { status: 400 });\n    }\n\n    // Use provided zodiac sign or calculate from birth date\n    const finalZodiacSign = zodiacSign || getZodiacFromDate(birthDate) as ZodiacSign;\n\n    // Validate zodiac sign\n    if (!finalZodiacSign || !['aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra', 'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'].includes(finalZodiacSign)) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Invalid zodiac sign'\n      }, { status: 400 });\n    }\n\n    const qrToken = generateQRToken();\n    console.log('Generated QR token:', qrToken);\n    console.log('Final zodiac sign:', finalZodiacSign);\n    console.log('Birth date object:', new Date(birthDate));\n\n    // Test database connection first\n    try {\n      await prisma.$connect();\n      console.log('Database connection successful');\n    } catch (dbError) {\n      console.error('Database connection failed:', dbError);\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: `Database connection failed: ${dbError instanceof Error ? dbError.message : 'Unknown error'}`\n      }, { status: 500 });\n    }\n\n    // Prepare user data with proper null handling\n    const userData = {\n      name: name.trim(),\n      email: email && email.trim() ? email.trim() : null,\n      phoneNumber: phoneNumber && phoneNumber.trim() ? phoneNumber.trim() : null,\n      address: address && address.trim() ? address.trim() : null,\n      birthDate: new Date(birthDate),\n      birthTime: birthTime && birthTime.trim() ? birthTime.trim() : null,\n      zodiacSign: finalZodiacSign,\n      languagePreference: languagePreference as LanguageCode,\n      qrToken\n    };\n\n    console.log('Creating user with data:', userData);\n\n    // Validate all required fields one more time\n    if (!userData.name || !userData.birthDate || !userData.zodiacSign || !userData.qrToken) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Missing required fields after validation'\n      }, { status: 400 });\n    }\n\n    // Create user in database\n    const user = await prisma.user.create({\n      data: userData\n    });\n\n    console.log('User created successfully:', user.id);\n\n    // Create QR code mapping\n    console.log('Creating QR code mapping for user:', user.id);\n    const qrMapping = await prisma.qrCodeMapping.create({\n      data: {\n        qrToken: qrToken,\n        userId: user.id\n      }\n    });\n    console.log('QR mapping created:', qrMapping.id);\n\n    // Generate QR code image\n    console.log('Generating QR code image...');\n    const qrCodeImage = await generateQRCodeImage(qrToken);\n    console.log('QR code image generated successfully');\n\n    return NextResponse.json<ApiResponse<{ user: User; qrCodeImage: string }>>({\n      success: true,\n      data: {\n        user: user as User,\n        qrCodeImage\n      },\n      message: 'User created successfully with QR code'\n    });\n\n  } catch (error) {\n    console.error('User creation error:', error);\n\n    // Provide more detailed error information\n    let errorMessage = 'Internal server error';\n    if (error instanceof Error) {\n      errorMessage = error.message;\n      console.error('Error details:', {\n        message: error.message,\n        stack: error.stack,\n        name: error.name\n      });\n    }\n\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: `User creation failed: ${errorMessage}`\n    }, { status: 500 });\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Check admin authentication\n    const admin = getAdminFromRequest(request);\n    if (!requireAdminAuth(admin)) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Unauthorized - Admin access required'\n      }, { status: 401 });\n    }\n\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '10');\n    const offset = (page - 1) * limit;\n    // Get users with pagination\n    const users = await prisma.user.findMany({\n      include: {\n        qrCodeMappings: {\n          select: {\n            scanCount: true,\n            lastScanned: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc'\n      },\n      skip: offset,\n      take: limit\n    });\n\n    // Get total count\n    const total = await prisma.user.count();\n\n    return NextResponse.json<ApiResponse<{ users: any[]; total: number; page: number; limit: number }>>({\n      success: true,\n      data: {\n        users,\n        total,\n        page,\n        limit\n      },\n      message: 'Users retrieved successfully'\n    });\n\n  } catch (error) {\n    console.error('Users fetch error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n\nexport async function DELETE(request: NextRequest) {\n  try {\n    // Check admin authentication\n    const admin = getAdminFromRequest(request);\n    if (!requireAdminAuth(admin)) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Unauthorized - Admin access required'\n      }, { status: 401 });\n    }\n\n    const { searchParams } = new URL(request.url);\n    const userId = searchParams.get('userId');\n\n    if (!userId) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'User ID is required'\n      }, { status: 400 });\n    }\n    await prisma.user.delete({\n      where: { id: userId }\n    });\n\n    return NextResponse.json<ApiResponse<{ deleted: boolean }>>({\n      success: true,\n      data: { deleted: true },\n      message: 'User deleted successfully'\n    });\n\n  } catch (error) {\n    console.error('User deletion error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,6BAA6B;QAC7B,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;QAClC,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,cAAc,MAAM,QAAQ,IAAI;QACtC,QAAQ,GAAG,CAAC,mCAAmC;QAE/C,MAAM,EACJ,IAAI,EACJ,KAAK,EACL,WAAW,EACX,OAAO,EACP,SAAS,EACT,SAAS,EACT,UAAU,EACV,qBAAqB,IAAI,EAC1B,GAAG;QAEJ,IAAI,CAAC,QAAQ,CAAC,WAAW;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,6BAA6B;QAC7B,MAAM,eAAe,IAAI,KAAK;QAC9B,IAAI,MAAM,aAAa,OAAO,KAAK;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,wDAAwD;QACxD,MAAM,kBAAkB,cAAc,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD,EAAE;QAExD,uBAAuB;QACvB,IAAI,CAAC,mBAAmB,CAAC;YAAC;YAAS;YAAU;YAAU;YAAU;YAAO;YAAS;YAAS;YAAW;YAAe;YAAa;YAAY;SAAS,CAAC,QAAQ,CAAC,kBAAkB;YAChL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,UAAU,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD;QAC9B,QAAQ,GAAG,CAAC,uBAAuB;QACnC,QAAQ,GAAG,CAAC,sBAAsB;QAClC,QAAQ,GAAG,CAAC,sBAAsB,IAAI,KAAK;QAE3C,iCAAiC;QACjC,IAAI;YACF,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ;YACrB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,SAAS;YAChB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO,CAAC,4BAA4B,EAAE,mBAAmB,QAAQ,QAAQ,OAAO,GAAG,iBAAiB;YACtG,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,8CAA8C;QAC9C,MAAM,WAAW;YACf,MAAM,KAAK,IAAI;YACf,OAAO,SAAS,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK;YAC9C,aAAa,eAAe,YAAY,IAAI,KAAK,YAAY,IAAI,KAAK;YACtE,SAAS,WAAW,QAAQ,IAAI,KAAK,QAAQ,IAAI,KAAK;YACtD,WAAW,IAAI,KAAK;YACpB,WAAW,aAAa,UAAU,IAAI,KAAK,UAAU,IAAI,KAAK;YAC9D,YAAY;YACZ,oBAAoB;YACpB;QACF;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QAExC,6CAA6C;QAC7C,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,SAAS,UAAU,IAAI,CAAC,SAAS,OAAO,EAAE;YACtF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,0BAA0B;QAC1B,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,8BAA8B,KAAK,EAAE;QAEjD,yBAAyB;QACzB,QAAQ,GAAG,CAAC,sCAAsC,KAAK,EAAE;QACzD,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YAClD,MAAM;gBACJ,SAAS;gBACT,QAAQ,KAAK,EAAE;YACjB;QACF;QACA,QAAQ,GAAG,CAAC,uBAAuB,UAAU,EAAE;QAE/C,yBAAyB;QACzB,QAAQ,GAAG,CAAC;QACZ,MAAM,cAAc,MAAM,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;QAC9C,QAAQ,GAAG,CAAC;QAEZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAmD;YACzE,SAAS;YACT,MAAM;gBACJ,MAAM;gBACN;YACF;YACA,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QAEtC,0CAA0C;QAC1C,IAAI,eAAe;QACnB,IAAI,iBAAiB,OAAO;YAC1B,eAAe,MAAM,OAAO;YAC5B,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAS,MAAM,OAAO;gBACtB,OAAO,MAAM,KAAK;gBAClB,MAAM,MAAM,IAAI;YAClB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO,CAAC,sBAAsB,EAAE,cAAc;QAChD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,6BAA6B;QAC7B,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;QAClC,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAC5B,4BAA4B;QAC5B,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC,SAAS;gBACP,gBAAgB;oBACd,QAAQ;wBACN,WAAW;wBACX,aAAa;oBACf;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;YACA,MAAM;YACN,MAAM;QACR;QAEA,kBAAkB;QAClB,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK;QAErC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAA4E;YAClG,SAAS;YACT,MAAM;gBACJ;gBACA;gBACA;gBACA;YACF;YACA,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,6BAA6B;QAC7B,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;QAClC,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QACA,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI;YAAO;QACtB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoC;YAC1D,SAAS;YACT,MAAM;gBAAE,SAAS;YAAK;YACtB,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}