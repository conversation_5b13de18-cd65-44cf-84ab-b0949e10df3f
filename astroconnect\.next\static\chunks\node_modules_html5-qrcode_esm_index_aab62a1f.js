(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/html5-qrcode/esm/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_html5-qrcode_esm_923cc3fe._.js",
  "static/chunks/node_modules_html5-qrcode_third_party_zxing-js_umd_df022588.js",
  "static/chunks/node_modules_html5-qrcode_esm_index_8b1d2e0b.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/html5-qrcode/esm/index.js [app-client] (ecmascript)");
    });
});
}}),
}]);