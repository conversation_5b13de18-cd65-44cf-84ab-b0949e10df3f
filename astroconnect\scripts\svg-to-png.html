<!DOCTYPE html>
<html>
<head>
    <title>SVG to PNG Converter</title>
</head>
<body>
    <h1>Converting SVG Icons to PNG...</h1>
    <div id="status"></div>
    <canvas id="canvas" style="display: none;"></canvas>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const status = document.getElementById('status');

        const icons = [
            { size: 144, name: 'icon-144x144' },
            { size: 192, name: 'icon-192x192' },
            { size: 512, name: 'icon-512x512' }
        ];

        async function convertSVGToPNG(svgContent, size, filename) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(svgBlob);

                img.onload = function() {
                    canvas.width = size;
                    canvas.height = size;
                    ctx.clearRect(0, 0, size, size);
                    ctx.drawImage(img, 0, 0, size, size);
                    
                    canvas.toBlob(function(blob) {
                        const link = document.createElement('a');
                        link.download = filename + '.png';
                        link.href = URL.createObjectURL(blob);
                        link.click();
                        URL.revokeObjectURL(url);
                        resolve();
                    }, 'image/png');
                };

                img.onerror = reject;
                img.src = url;
            });
        }

        const createSVGIcon = (size) => `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="url(#grad)"/>
  <circle cx="${size * 0.5}" cy="${size * 0.35}" r="${size * 0.15}" fill="white" opacity="0.9"/>
  <path d="M ${size * 0.25} ${size * 0.55} Q ${size * 0.5} ${size * 0.75} ${size * 0.75} ${size * 0.55}" 
        stroke="white" stroke-width="${size * 0.05}" fill="none" opacity="0.9"/>
  <text x="${size * 0.5}" y="${size * 0.9}" text-anchor="middle" fill="white" 
        font-family="Arial, sans-serif" font-size="${size * 0.08}" opacity="0.8">AC</text>
</svg>`;

        async function convertAllIcons() {
            status.innerHTML = 'Starting conversion...<br>';
            
            for (const icon of icons) {
                try {
                    const svgContent = createSVGIcon(icon.size);
                    await convertSVGToPNG(svgContent, icon.size, icon.name);
                    status.innerHTML += `✓ Converted ${icon.name}.png<br>`;
                } catch (error) {
                    status.innerHTML += `✗ Failed to convert ${icon.name}: ${error}<br>`;
                }
            }
            
            status.innerHTML += '<br><strong>Conversion complete! Check your downloads folder.</strong>';
        }

        // Start conversion when page loads
        window.onload = convertAllIcons;
    </script>
</body>
</html>
