{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Helper function to disconnect Prisma (useful for serverless)\nexport async function disconnectPrisma() {\n  await prisma.$disconnect();\n}\n\n// Helper function to check database connection\nexport async function checkDatabaseConnection() {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/test-db/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { prisma } from '@/lib/prisma';\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Test database connection\n    await prisma.$connect();\n    console.log('Database connection successful');\n\n    // Test a simple query\n    const result = await prisma.$queryRaw`SELECT 1 as test`;\n    console.log('Query result:', result);\n\n    // Test user table access\n    const userCount = await prisma.user.count();\n    console.log('User count:', userCount);\n\n    // Test admin table access\n    const adminCount = await prisma.admin.count();\n    console.log('Admin count:', adminCount);\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        connected: true,\n        userCount,\n        adminCount,\n        queryResult: result\n      },\n      message: 'Database connection test successful'\n    });\n\n  } catch (error) {\n    console.error('Database test error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n      details: error\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,2BAA2B;QAC3B,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ;QACrB,QAAQ,GAAG,CAAC;QAEZ,sBAAsB;QACtB,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QACvD,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,yBAAyB;QACzB,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK;QACzC,QAAQ,GAAG,CAAC,eAAe;QAE3B,0BAA0B;QAC1B,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK;QAC3C,QAAQ,GAAG,CAAC,gBAAgB;QAE5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,WAAW;gBACX;gBACA;gBACA,aAAa;YACf;YACA,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}