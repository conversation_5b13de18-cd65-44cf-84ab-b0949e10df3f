globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/admin/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/hooks/useLanguage.tsx <module evaluation>":{"id":"[project]/src/hooks/useLanguage.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/hooks/useLanguage.tsx":{"id":"[project]/src/hooks/useLanguage.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js"],"async":false},"[project]/src/app/admin/page.tsx <module evaluation>":{"id":"[project]/src/app/admin/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/src_e491e321._.js","/_next/static/chunks/node_modules_a605789b._.js","/_next/static/chunks/src_app_admin_page_tsx_306227ea._.js"],"async":false},"[project]/src/app/admin/page.tsx":{"id":"[project]/src/app/admin/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_7c8ddf44._.js","/_next/static/chunks/src_654026fd._.js","/_next/static/chunks/src_app_layout_tsx_007ca514._.js","/_next/static/chunks/src_e491e321._.js","/_next/static/chunks/node_modules_a605789b._.js","/_next/static/chunks/src_app_admin_page_tsx_306227ea._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_bfd274b3._.js","server/chunks/ssr/[root-of-the-server]__a93fb3a4._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_bfd274b3._.js","server/chunks/ssr/[root-of-the-server]__a93fb3a4._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_bfd274b3._.js","server/chunks/ssr/[root-of-the-server]__a93fb3a4._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_bfd274b3._.js","server/chunks/ssr/[root-of-the-server]__a93fb3a4._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_bfd274b3._.js","server/chunks/ssr/[root-of-the-server]__a93fb3a4._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_bfd274b3._.js","server/chunks/ssr/[root-of-the-server]__a93fb3a4._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_bfd274b3._.js","server/chunks/ssr/[root-of-the-server]__a93fb3a4._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_bfd274b3._.js","server/chunks/ssr/[root-of-the-server]__a93fb3a4._.js"],"async":false}},"[project]/src/hooks/useLanguage.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/hooks/useLanguage.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_bfd274b3._.js","server/chunks/ssr/[root-of-the-server]__a93fb3a4._.js"],"async":false}},"[project]/src/app/admin/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/admin/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_bfd274b3._.js","server/chunks/ssr/[root-of-the-server]__a93fb3a4._.js","server/chunks/ssr/src_904de78e._.js","server/chunks/ssr/node_modules_4c890c04._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/admin/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/admin/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/admin/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/admin/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/admin/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/admin/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/admin/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/admin/page.js"],"async":false}},"[project]/src/hooks/useLanguage.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/hooks/useLanguage.tsx (client reference/proxy)","name":"*","chunks":["server/app/admin/page.js"],"async":false}},"[project]/src/app/admin/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/admin/page.tsx (client reference/proxy)","name":"*","chunks":["server/app/admin/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/layout":[{"path":"static/chunks/[root-of-the-server]__8ebb6d4b._.css","inlined":false}],"[project]/src/app/admin/page":[{"path":"static/chunks/[root-of-the-server]__8ebb6d4b._.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/layout":["static/chunks/node_modules_7c8ddf44._.js","static/chunks/src_654026fd._.js","static/chunks/src_app_layout_tsx_007ca514._.js"],"[project]/src/app/admin/page":["static/chunks/node_modules_7c8ddf44._.js","static/chunks/src_654026fd._.js","static/chunks/src_app_layout_tsx_007ca514._.js","static/chunks/src_e491e321._.js","static/chunks/node_modules_a605789b._.js","static/chunks/src_app_admin_page_tsx_306227ea._.js"]}}
