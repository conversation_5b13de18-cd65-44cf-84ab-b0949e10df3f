'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { useLanguage } from '@/hooks/useLanguage';
import { DashboardData } from '@/types';
import { ZODIAC_INFO } from '@/utils/zodiac';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import ZodiacCard from '@/components/ZodiacCard';
import TranslatedText from '@/components/TranslatedText';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import PWAInstaller from '@/components/PWAInstaller';
import MobileNavigation from '@/components/MobileNavigation';
import { Settings, Star, Calendar, Clock, Palette, Hash, BookOpen } from 'lucide-react';

export default function Dashboard() {
  const { user, isAuthenticated, loading: authLoading, getSessionTimeRemaining, isSessionExpired } = useAuth();
  const { language, setLanguage } = useLanguage();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'horoscope' | 'guide'>('horoscope');
  const [sessionTimeRemaining, setSessionTimeRemaining] = useState<number>(0);
  const router = useRouter();

  // Update session time remaining every minute
  useEffect(() => {
    if (!isAuthenticated) return;

    const updateSessionTime = () => {
      const remaining = getSessionTimeRemaining();
      setSessionTimeRemaining(remaining);

      // If session expired, redirect to home
      if (remaining <= 0 || isSessionExpired()) {
        console.log('Session expired, redirecting to home');
        router.push('/');
        return;
      }
    };

    // Update immediately
    updateSessionTime();

    // Update every minute
    const interval = setInterval(updateSessionTime, 60000);

    return () => clearInterval(interval);
  }, [isAuthenticated, getSessionTimeRemaining, isSessionExpired, router]);

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/');
      return;
    }

    if (user) {
      fetchDashboardData();
    }
  }, [user, isAuthenticated, authLoading, router, language]);

  const fetchDashboardData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/dashboard?userId=${user.id}&language=${language}`);
      const data = await response.json();

      if (data.success) {
        setDashboardData(data.data);
      } else {
        setError(data.error || 'Failed to load dashboard data');
      }
    } catch (err) {
      console.error('Dashboard fetch error:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageChange = async (newLanguage: 'en' | 'si') => {
    // Update user preference in backend
    if (user) {
      try {
        await fetch('/api/dashboard', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ userId: user.id, language: newLanguage })
        });
      } catch (error) {
        console.error('Failed to update language preference:', error);
      }
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <LoadingSpinner message="Loading your cosmic insights..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <ErrorMessage
          title="Error Loading Dashboard"
          message={error}
          onRetry={fetchDashboardData}
        />
      </div>
    );
  }

  if (!dashboardData || !user) {
    return null;
  }

  const zodiacInfo = ZODIAC_INFO[user.zodiacSign];

  // Fallback if zodiac info is not found
  if (!zodiacInfo) {
    console.error('Zodiac info not found for sign:', user.zodiacSign);
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <ErrorMessage
          title="Configuration Error"
          message="Unable to load zodiac information. Please contact support."
          onRetry={fetchDashboardData}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Session Warning */}
      {sessionTimeRemaining <= 5 && sessionTimeRemaining > 0 && (
        <div className="bg-red-600/90 backdrop-blur-sm text-white px-4 py-2 text-center text-sm font-medium">
          ⚠️ Your session will expire in {sessionTimeRemaining} minute{sessionTimeRemaining !== 1 ? 's' : ''}. Please scan your QR code again to continue.
        </div>
      )}

      {/* Header */}
      <header className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-3xl">{zodiacInfo.symbol}</div>
              <div>
                <h1 className="text-xl font-bold text-white">Welcome, {user.name}</h1>
                <p className="text-gray-300 text-sm">{zodiacInfo.name} • {zodiacInfo.dates}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Session Timer */}
              <div className="hidden md:flex items-center space-x-2 text-sm">
                <Clock size={16} className="text-yellow-400" />
                <span className={`font-medium ${sessionTimeRemaining <= 5 ? 'text-red-400' : sessionTimeRemaining <= 10 ? 'text-yellow-400' : 'text-green-400'}`}>
                  {sessionTimeRemaining}m
                </span>
              </div>

              <LanguageSwitcher onLanguageChange={handleLanguageChange} />

              <button className="hidden md:block text-gray-300 hover:text-white transition-colors">
                <Settings size={20} />
              </button>

              <MobileNavigation
                activeTab={activeTab}
                onTabChange={setActiveTab}
              />
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-black/10 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex space-x-8">
            {[
              { id: 'horoscope', label: 'Horoscope', icon: BookOpen },
              { id: 'guide', label: 'Daily Guide', icon: Clock }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center space-x-2 py-4 px-2 border-b-2 transition-colors ${
                  activeTab === id
                    ? 'border-purple-400 text-white'
                    : 'border-transparent text-gray-300 hover:text-white'
                }`}
              >
                <Icon size={16} />
                <span><TranslatedText text={label} /></span>
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Content */}
      <main className="max-w-6xl mx-auto px-4 py-8 pb-20 md:pb-8">
        {activeTab === 'horoscope' && (
          <div className="space-y-6">
            {/* Today's Horoscope */}
            {dashboardData.todayHoroscope && (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
                  <Star className="mr-2 text-yellow-400" />
                  <TranslatedText text="Today's Horoscope" />
                </h2>
                <p className="text-gray-200 leading-relaxed text-lg">
                  <TranslatedText text={dashboardData.todayHoroscope.content} />
                </p>
              </div>
            )}

            {/* Weekly Horoscope */}
            {dashboardData.weeklyHoroscope && (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
                  <Calendar className="mr-2 text-blue-400" />
                  <TranslatedText text="Weekly Horoscope" />
                </h2>
                <p className="text-gray-200 leading-relaxed text-lg">
                  <TranslatedText text={dashboardData.weeklyHoroscope.content} />
                </p>
              </div>
            )}

            {/* Monthly Horoscope */}
            {dashboardData.monthlyHoroscope && (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
                  <Calendar className="mr-2 text-purple-400" />
                  <TranslatedText text="Monthly Horoscope" />
                </h2>
                <p className="text-gray-200 leading-relaxed text-lg">
                  <TranslatedText text={dashboardData.monthlyHoroscope.content} />
                </p>
              </div>
            )}

            {/* No Horoscope Content */}
            {!dashboardData.todayHoroscope && !dashboardData.weeklyHoroscope && !dashboardData.monthlyHoroscope && (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center">
                <BookOpen className="mx-auto mb-4 text-gray-400" size={48} />
                <h3 className="text-xl font-semibold text-white mb-2">
                  <TranslatedText text="No Horoscope Available" />
                </h3>
                <p className="text-gray-300">
                  <TranslatedText text="Your personalized horoscope content will appear here once added by the admin." />
                </p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'guide' && (
          dashboardData.dailyGuide ? (
          <div className="space-y-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
                <Clock className="mr-2 text-green-400" />
                <TranslatedText text="Today's Guidance" />
              </h2>

              <div className="grid md:grid-cols-2 gap-6 mb-6">
                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <Hash className="text-yellow-400 mr-2" size={20} />
                    <h3 className="text-white font-semibold">
                      <TranslatedText text="Lucky Number" />
                    </h3>
                  </div>
                  <p className="text-2xl font-bold text-yellow-400">
                    {dashboardData.dailyGuide.lucky_number}
                  </p>
                </div>

                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <Palette className="text-pink-400 mr-2" size={20} />
                    <h3 className="text-white font-semibold">
                      <TranslatedText text="Lucky Color" />
                    </h3>
                  </div>
                  <p className="text-lg font-semibold text-pink-400">
                    <TranslatedText text={dashboardData.dailyGuide.lucky_color} />
                  </p>
                </div>

                <div className="bg-white/5 rounded-lg p-4 md:col-span-2">
                  <div className="flex items-center mb-2">
                    <Clock className="text-blue-400 mr-2" size={20} />
                    <h3 className="text-white font-semibold">
                      <TranslatedText text="Lucky Time" />
                    </h3>
                  </div>
                  <p className="text-lg font-semibold text-blue-400">
                    <TranslatedText text={dashboardData.dailyGuide.lucky_time} />
                  </p>
                </div>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <h3 className="text-white font-semibold mb-3">
                  <TranslatedText text="Daily Advice" />
                </h3>
                <p className="text-gray-200 leading-relaxed">
                  <TranslatedText text={dashboardData.dailyGuide.advice} />
                </p>
              </div>
            </div>
          </div>
          ) : (
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center">
              <Clock className="mx-auto mb-4 text-gray-400" size={48} />
              <h3 className="text-xl font-semibold text-white mb-2">
                <TranslatedText text="No Daily Guide Available" />
              </h3>
              <p className="text-gray-300">
                <TranslatedText text="Your personalized daily guide will appear here once added by the admin." />
              </p>
            </div>
          )
        )}
      </main>

      {/* PWA Installer */}
      <PWAInstaller />
    </div>
  );
}
