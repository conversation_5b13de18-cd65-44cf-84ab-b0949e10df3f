{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\nimport { Loader2 } from 'lucide-react';\n\ninterface LoadingSpinnerProps {\n  message?: string;\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ \n  message = 'Loading...', \n  size = 'md',\n  className = '' \n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-6 h-6',\n    md: 'w-12 h-12',\n    lg: 'w-16 h-16'\n  };\n\n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <Loader2 className={`${sizeClasses[size]} text-white animate-spin mb-4`} />\n      {message && (\n        <p className=\"text-white text-center\">{message}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUe,SAAS,eAAe,EACrC,UAAU,YAAY,EACtB,OAAO,IAAI,EACX,YAAY,EAAE,EACM;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACtE,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,6BAA6B,CAAC;;;;;;YACtE,yBACC,8OAAC;gBAAE,WAAU;0BAA0B;;;;;;;;;;;;AAI/C", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, RefreshCw } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  title?: string;\n  message: string;\n  onRetry?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ \n  title = 'Something went wrong',\n  message,\n  onRetry,\n  className = ''\n}: ErrorMessageProps) {\n  return (\n    <div className={`text-center max-w-md mx-auto p-6 ${className}`}>\n      <AlertCircle className=\"w-16 h-16 text-red-400 mx-auto mb-4\" />\n      <h2 className=\"text-xl font-bold text-white mb-4\">{title}</h2>\n      <p className=\"text-gray-300 mb-6\">{message}</p>\n      {onRetry && (\n        <button\n          onClick={onRetry}\n          className=\"flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors mx-auto\"\n        >\n          <RefreshCw size={16} />\n          Try Again\n        </button>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAWe,SAAS,aAAa,EACnC,QAAQ,sBAAsB,EAC9B,OAAO,EACP,OAAO,EACP,YAAY,EAAE,EACI;IAClB,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;;0BAC7D,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,8OAAC;gBAAG,WAAU;0BAAqC;;;;;;0BACnD,8OAAC;gBAAE,WAAU;0BAAsB;;;;;;YAClC,yBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,8OAAC,gNAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;oBAAM;;;;;;;;;;;;;AAMjC", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/zodiac.ts"], "sourcesContent": ["import { ZodiacSign } from '@prisma/client';\n\nexport const ZODIAC_SIGNS: ZodiacSign[] = [\n  'aries', 'taurus', 'gemini', 'cancer',\n  'leo', 'virgo', 'libra', 'scorpio',\n  'sagittarius', 'capricorn', 'aquarius', 'pisces'\n];\n\nexport const ZODIAC_INFO = {\n  aries: { name: '<PERSON><PERSON>', symbol: '♈', dates: 'Mar 21 - Apr 19', element: 'Fire' },\n  taurus: { name: 'Taurus', symbol: '♉', dates: 'Apr 20 - May 20', element: 'Earth' },\n  gemini: { name: 'Gemini', symbol: '♊', dates: 'May 21 - Jun 20', element: 'Air' },\n  cancer: { name: 'Cancer', symbol: '♋', dates: 'Jun 21 - Jul 22', element: 'Water' },\n  leo: { name: '<PERSON>', symbol: '♌', dates: 'Jul 23 - Aug 22', element: 'Fire' },\n  virgo: { name: '<PERSON>ir<PERSON>', symbol: '♍', dates: 'Aug 23 - Sep 22', element: 'Earth' },\n  libra: { name: '<PERSON><PERSON>', symbol: '♎', dates: 'Sep 23 - Oct 22', element: 'Air' },\n  scorpio: { name: '<PERSON><PERSON><PERSON>', symbol: '♏', dates: 'Oct 23 - Nov 21', element: 'Water' },\n  sagittarius: { name: 'Sagittarius', symbol: '♐', dates: 'Nov 22 - Dec 21', element: 'Fire' },\n  capricorn: { name: 'Capricorn', symbol: '♑', dates: 'Dec 22 - Jan 19', element: 'Earth' },\n  aquarius: { name: 'Aquarius', symbol: '♒', dates: 'Jan 20 - Feb 18', element: 'Air' },\n  pisces: { name: 'Pisces', symbol: '♓', dates: 'Feb 19 - Mar 20', element: 'Water' }\n};\n\nexport function getZodiacFromDate(birthDate: string): ZodiacSign {\n  const date = new Date(birthDate);\n  const month = date.getMonth() + 1; // 1-12\n  const day = date.getDate();\n\n  if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) return 'aries';\n  if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) return 'taurus';\n  if ((month === 5 && day >= 21) || (month === 6 && day <= 20)) return 'gemini';\n  if ((month === 6 && day >= 21) || (month === 7 && day <= 22)) return 'cancer';\n  if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) return 'leo';\n  if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) return 'virgo';\n  if ((month === 9 && day >= 23) || (month === 10 && day <= 22)) return 'libra';\n  if ((month === 10 && day >= 23) || (month === 11 && day <= 21)) return 'scorpio';\n  if ((month === 11 && day >= 22) || (month === 12 && day <= 21)) return 'sagittarius';\n  if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) return 'capricorn';\n  if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) return 'aquarius';\n  return 'pisces';\n}\n\nexport function getZodiacColors(sign: ZodiacSign): string[] {\n  const colorMap = {\n    aries: ['#FF6B6B', '#FF4757'],\n    taurus: ['#2ECC71', '#27AE60'],\n    gemini: ['#F39C12', '#E67E22'],\n    cancer: ['#3498DB', '#2980B9'],\n    leo: ['#E74C3C', '#C0392B'],\n    virgo: ['#1ABC9C', '#16A085'],\n    libra: ['#9B59B6', '#8E44AD'],\n    scorpio: ['#34495E', '#2C3E50'],\n    sagittarius: ['#E67E22', '#D35400'],\n    capricorn: ['#95A5A6', '#7F8C8D'],\n    aquarius: ['#3498DB', '#2980B9'],\n    pisces: ['#1ABC9C', '#16A085']\n  };\n  return colorMap[sign];\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,eAA6B;IACxC;IAAS;IAAU;IAAU;IAC7B;IAAO;IAAS;IAAS;IACzB;IAAe;IAAa;IAAY;CACzC;AAEM,MAAM,cAAc;IACzB,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC/E,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAClF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IAChF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAClF,KAAK;QAAE,MAAM;QAAO,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC3E,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IAChF,OAAO;QAAE,MAAM;QAAS,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IAC9E,SAAS;QAAE,MAAM;QAAW,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IACpF,aAAa;QAAE,MAAM;QAAe,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAO;IAC3F,WAAW;QAAE,MAAM;QAAa,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;IACxF,UAAU;QAAE,MAAM;QAAY,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAM;IACpF,QAAQ;QAAE,MAAM;QAAU,QAAQ;QAAK,OAAO;QAAmB,SAAS;IAAQ;AACpF;AAEO,SAAS,kBAAkB,SAAiB;IACjD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,QAAQ,KAAK,QAAQ,KAAK,GAAG,OAAO;IAC1C,MAAM,MAAM,KAAK,OAAO;IAExB,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACtE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACvE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,MAAM,OAAO,IAAK,OAAO;IACvE,IAAI,AAAC,UAAU,MAAM,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACtE,IAAI,AAAC,UAAU,KAAK,OAAO,MAAQ,UAAU,KAAK,OAAO,IAAK,OAAO;IACrE,OAAO;AACT;AAEO,SAAS,gBAAgB,IAAgB;IAC9C,MAAM,WAAW;QACf,OAAO;YAAC;YAAW;SAAU;QAC7B,QAAQ;YAAC;YAAW;SAAU;QAC9B,QAAQ;YAAC;YAAW;SAAU;QAC9B,QAAQ;YAAC;YAAW;SAAU;QAC9B,KAAK;YAAC;YAAW;SAAU;QAC3B,OAAO;YAAC;YAAW;SAAU;QAC7B,OAAO;YAAC;YAAW;SAAU;QAC7B,SAAS;YAAC;YAAW;SAAU;QAC/B,aAAa;YAAC;YAAW;SAAU;QACnC,WAAW;YAAC;YAAW;SAAU;QACjC,UAAU;YAAC;YAAW;SAAU;QAChC,QAAQ;YAAC;YAAW;SAAU;IAChC;IACA,OAAO,QAAQ,CAAC,KAAK;AACvB", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/admin/UserManagement.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { User, ZodiacSign } from '@/types';\nimport { Search, Filter, Plus, Edit, Trash2, QrCode, Download, Link, Copy } from 'lucide-react';\nimport { ZODIAC_INFO, ZODIAC_SIGNS } from '@/utils/zodiac';\n\ninterface UserWithStats extends User {\n  scan_count: number;\n  last_scanned: string | null;\n}\n\nexport default function UserManagement() {\n  const [users, setUsers] = useState<UserWithStats[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showQRUrlModal, setShowQRUrlModal] = useState(false);\n  const [selectedUserForQR, setSelectedUserForQR] = useState<UserWithStats | null>(null);\n  const [selectedUserForEdit, setSelectedUserForEdit] = useState<UserWithStats | null>(null);\n  const [newUser, setNewUser] = useState({\n    name: '',\n    email: '',\n    phoneNumber: '',\n    address: '',\n    birthDate: '',\n    birthTime: '',\n    zodiacSign: '' as ZodiacSign | '',\n    languagePreference: 'en'\n  });\n\n  const [editUser, setEditUser] = useState({\n    name: '',\n    email: '',\n    phoneNumber: '',\n    address: '',\n    birthDate: '',\n    birthTime: '',\n    zodiacSign: '' as ZodiacSign | '',\n    languagePreference: 'en'\n  });\n\n  useEffect(() => {\n    fetchUsers();\n  }, []);\n\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch('/api/admin/users', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      const data = await response.json();\n      \n      if (data.success) {\n        setUsers(data.data.users);\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteUser = async (userId: string) => {\n    if (!confirm('Are you sure you want to delete this user?')) return;\n\n    try {\n      console.log('🗑️ Deleting user:', userId);\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch(`/api/admin/users?userId=${userId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      const data = await response.json();\n      console.log('Delete response:', data);\n\n      if (response.ok && data.success) {\n        console.log('✅ User deleted successfully');\n        setUsers(users.filter(user => user.id !== userId));\n        alert('User deleted successfully!');\n      } else {\n        console.error('❌ Delete failed:', data.error);\n        alert('Failed to delete user: ' + (data.error || 'Unknown error'));\n      }\n    } catch (error) {\n      console.error('❌ Error deleting user:', error);\n      alert('Error deleting user. Please try again.');\n    }\n  };\n\n  const handleGenerateQR = async (user: UserWithStats) => {\n    try {\n      console.log('📱 Generating QR code for user:', user.name);\n\n      // Check if user already has a QR token\n      if (user.qrToken) {\n        console.log('✅ User already has QR token, generating image...');\n        // Generate QR code image directly using existing token\n        const response = await fetch('/api/admin/qr-generate', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('admin-token')}`\n          },\n          body: JSON.stringify({ qrToken: user.qrToken, userName: user.name })\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          if (data.success && data.data.qrCodeImage) {\n            const link = document.createElement('a');\n            link.href = data.data.qrCodeImage;\n            link.download = `${user.name}-qr-code.png`;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            alert('QR code downloaded successfully!');\n            return;\n          }\n        }\n      }\n\n      console.log('⚠️ Creating new QR code for existing user...');\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch('/api/admin/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({\n          name: user.name,\n          email: user.email,\n          phoneNumber: user.phoneNumber,\n          address: user.address,\n          birthDate: user.birthDate,\n          birthTime: user.birthTime,\n          languagePreference: user.languagePreference\n        })\n      });\n\n      const data = await response.json();\n      console.log('QR generation response:', data);\n\n      if (data.success && data.data.qrCodeImage) {\n        console.log('✅ QR code generated successfully');\n        // Download QR code\n        const link = document.createElement('a');\n        link.href = data.data.qrCodeImage;\n        link.download = `${user.name}-qr-code.png`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        alert('QR code downloaded successfully!');\n      } else {\n        console.error('❌ QR generation failed:', data.error);\n        alert('Failed to generate QR code: ' + (data.error || 'Unknown error'));\n      }\n    } catch (error) {\n      console.error('❌ Error generating QR code:', error);\n      alert('Error generating QR code. Please try again.');\n    }\n  };\n\n  const handleCopyQRUrl = async (user: UserWithStats) => {\n    console.log('🔗 Copying QR URL for user:', user.name);\n\n    if (!user.qrToken) {\n      console.error('❌ User has no QR token');\n      alert('This user does not have a QR code yet. Please generate one first.');\n      return;\n    }\n\n    setSelectedUserForQR(user);\n    setShowQRUrlModal(true);\n  };\n\n  const handleEditUser = (user: UserWithStats) => {\n    console.log('✏️ Editing user:', user.name);\n    setSelectedUserForEdit(user);\n\n    // Pre-populate the edit form with current user data\n    setEditUser({\n      name: user.name || '',\n      email: user.email || '',\n      phoneNumber: user.phoneNumber || '',\n      address: user.address || '',\n      birthDate: user.birthDate ? new Date(user.birthDate).toISOString().split('T')[0] : '',\n      birthTime: user.birthTime || '',\n      zodiacSign: user.zodiacSign || '',\n      languagePreference: user.languagePreference || 'en'\n    });\n\n    setShowEditModal(true);\n  };\n\n  const copyUrlToClipboard = async (url: string) => {\n    try {\n      await navigator.clipboard.writeText(url);\n\n      // Show a temporary success message\n      const copyButton = document.querySelector('.copy-url-modal-btn');\n      if (copyButton) {\n        const originalText = copyButton.innerHTML;\n        copyButton.innerHTML = '✓ Copied!';\n        setTimeout(() => {\n          copyButton.innerHTML = originalText;\n        }, 2000);\n      }\n    } catch (error) {\n      console.error('Error copying QR URL:', error);\n      alert('Failed to copy URL to clipboard');\n    }\n  };\n\n  const handleBulkDelete = async () => {\n    if (selectedUsers.length === 0) return;\n\n    const confirmMessage = `Are you sure you want to delete ${selectedUsers.length} user${selectedUsers.length > 1 ? 's' : ''}?`;\n    if (!confirm(confirmMessage)) return;\n\n    try {\n      console.log('🗑️ Bulk deleting users:', selectedUsers);\n      const token = localStorage.getItem('admin-token');\n\n      // Delete users one by one (could be optimized with bulk API)\n      const deletePromises = selectedUsers.map(userId =>\n        fetch(`/api/admin/users?userId=${userId}`, {\n          method: 'DELETE',\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        })\n      );\n\n      const results = await Promise.all(deletePromises);\n      const successCount = results.filter(r => r.ok).length;\n\n      if (successCount === selectedUsers.length) {\n        console.log('✅ All users deleted successfully');\n        setUsers(users.filter(user => !selectedUsers.includes(user.id)));\n        setSelectedUsers([]);\n        alert(`Successfully deleted ${successCount} user${successCount > 1 ? 's' : ''}!`);\n      } else {\n        console.warn('⚠️ Some deletions failed');\n        alert(`Deleted ${successCount} out of ${selectedUsers.length} users. Please refresh to see current state.`);\n        fetchUsers(); // Refresh the list\n      }\n    } catch (error) {\n      console.error('❌ Bulk delete error:', error);\n      alert('Error during bulk delete. Please try again.');\n    }\n  };\n\n  const handleBulkExport = () => {\n    if (selectedUsers.length === 0) return;\n\n    console.log('📊 Exporting users:', selectedUsers);\n\n    const selectedUserData = users.filter(user => selectedUsers.includes(user.id));\n    const csvContent = [\n      // CSV Header\n      'Name,Email,Phone,Address,Birth Date,Birth Time,Zodiac Sign,Language,Scan Count,Last Scanned,QR Token',\n      // CSV Data\n      ...selectedUserData.map(user => [\n        user.name,\n        user.email || '',\n        user.phoneNumber || '',\n        user.address || '',\n        user.birthDate ? new Date(user.birthDate).toLocaleDateString() : '',\n        user.birthTime || '',\n        user.zodiacSign || '',\n        user.languagePreference || 'en',\n        user.scan_count || 0,\n        user.last_scanned ? new Date(user.last_scanned).toLocaleDateString() : 'Never',\n        user.qrToken || ''\n      ].map(field => `\"${String(field).replace(/\"/g, '\"\"')}\"`).join(','))\n    ].join('\\n');\n\n    // Create and download CSV file\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    link.href = URL.createObjectURL(blob);\n    link.download = `astroconnect-users-${new Date().toISOString().split('T')[0]}.csv`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    alert(`Exported ${selectedUsers.length} user${selectedUsers.length > 1 ? 's' : ''} to CSV file!`);\n  };\n\n  const handleUpdateUser = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!selectedUserForEdit) {\n      alert('No user selected for editing');\n      return;\n    }\n\n    try {\n      console.log('💾 Updating user:', selectedUserForEdit.id, editUser);\n\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch(`/api/admin/users/${selectedUserForEdit.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({\n          ...editUser,\n          zodiacSign: editUser.zodiacSign || undefined // Use selected zodiac sign or let server calculate from birthDate\n        })\n      });\n\n      const data = await response.json();\n      console.log('Update response:', data);\n\n      if (data.success) {\n        console.log('✅ User updated successfully');\n\n        // Update the user in the local state\n        setUsers(users.map(user =>\n          user.id === selectedUserForEdit.id\n            ? { ...user, ...data.data.user }\n            : user\n        ));\n\n        // Reset form and close modal\n        setEditUser({\n          name: '',\n          email: '',\n          phoneNumber: '',\n          address: '',\n          birthDate: '',\n          birthTime: '',\n          zodiacSign: '' as ZodiacSign | '',\n          languagePreference: 'en'\n        });\n        setShowEditModal(false);\n        setSelectedUserForEdit(null);\n\n        alert('User updated successfully!');\n      } else {\n        console.error('❌ Update failed:', data.error);\n        alert('Failed to update user: ' + (data.error || 'Unknown error'));\n      }\n    } catch (error) {\n      console.error('❌ Error updating user:', error);\n      alert('Error updating user. Please try again.');\n    }\n  };\n\n  const handleAddUser = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    try {\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch('/api/admin/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({\n          ...newUser,\n          zodiacSign: newUser.zodiacSign || undefined // Use selected zodiac sign or let server calculate from birthDate\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        // Add the new user to the list\n        setUsers([data.data.user, ...users]);\n\n        // Reset form and close modal\n        setNewUser({\n          name: '',\n          email: '',\n          phoneNumber: '',\n          address: '',\n          birthDate: '',\n          birthTime: '',\n          zodiacSign: '' as ZodiacSign | '',\n          languagePreference: 'en'\n        });\n        setShowAddModal(false);\n\n        // Download QR code automatically\n        if (data.data.qrCodeImage) {\n          const link = document.createElement('a');\n          link.href = data.data.qrCodeImage;\n          link.download = `${data.data.user.name}-qr-code.png`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n        }\n      } else {\n        alert('Error adding user: ' + data.error);\n      }\n    } catch (error) {\n      console.error('Error adding user:', error);\n      alert('Error adding user. Please try again.');\n    }\n  };\n\n  const filteredUsers = users.filter(user =>\n    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    user.email?.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-12\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-white\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header Actions */}\n      <div className=\"flex flex-col sm:flex-row gap-4\">\n        <div className=\"flex-1 relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" size={20} />\n          <input\n            type=\"text\"\n            placeholder=\"Search users...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500\"\n          />\n        </div>\n        <button\n          onClick={() => alert('Advanced filtering coming soon! Currently you can search by name or email.')}\n          className=\"flex items-center space-x-2 bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-colors\"\n        >\n          <Filter size={16} />\n          <span>Filter</span>\n        </button>\n        <button \n          onClick={() => setShowAddModal(true)}\n          className=\"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors\"\n        >\n          <Plus size={16} />\n          <span>Add User</span>\n        </button>\n      </div>\n\n      {/* Users Table */}\n      <div className=\"bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-white/5\">\n              <tr>\n                <th className=\"text-left py-3 px-4 text-gray-300 font-semibold\">\n                  <input\n                    type=\"checkbox\"\n                    className=\"rounded border-gray-300\"\n                    onChange={(e) => {\n                      if (e.target.checked) {\n                        setSelectedUsers(filteredUsers.map(u => u.id));\n                      } else {\n                        setSelectedUsers([]);\n                      }\n                    }}\n                  />\n                </th>\n                <th className=\"text-left py-3 px-4 text-gray-300 font-semibold\">Name</th>\n                <th className=\"text-left py-3 px-4 text-gray-300 font-semibold\">Email</th>\n                <th className=\"text-left py-3 px-4 text-gray-300 font-semibold\">Phone</th>\n                <th className=\"text-left py-3 px-4 text-gray-300 font-semibold\">Zodiac</th>\n                <th className=\"text-left py-3 px-4 text-gray-300 font-semibold\">Language</th>\n                <th className=\"text-left py-3 px-4 text-gray-300 font-semibold\">Scans</th>\n                <th className=\"text-left py-3 px-4 text-gray-300 font-semibold\">Last Active</th>\n                <th className=\"text-left py-3 px-4 text-gray-300 font-semibold\">Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {filteredUsers.map((user) => (\n                <tr key={user.id} className=\"border-t border-white/10 hover:bg-white/5\">\n                  <td className=\"py-3 px-4\">\n                    <input\n                      type=\"checkbox\"\n                      className=\"rounded border-gray-300\"\n                      checked={selectedUsers.includes(user.id)}\n                      onChange={(e) => {\n                        if (e.target.checked) {\n                          setSelectedUsers([...selectedUsers, user.id]);\n                        } else {\n                          setSelectedUsers(selectedUsers.filter(id => id !== user.id));\n                        }\n                      }}\n                    />\n                  </td>\n                  <td className=\"py-3 px-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"text-2xl\">{user.zodiacSign && ZODIAC_INFO[user.zodiacSign] ? ZODIAC_INFO[user.zodiacSign].symbol : '⭐'}</div>\n                      <div>\n                        <p className=\"text-white font-medium\">{user.name}</p>\n                        <p className=\"text-gray-400 text-sm\">{user.zodiacSign && ZODIAC_INFO[user.zodiacSign] ? ZODIAC_INFO[user.zodiacSign].name : 'Unknown'}</p>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"py-3 px-4 text-gray-300\">{user.email || 'N/A'}</td>\n                  <td className=\"py-3 px-4 text-gray-300\">{user.phoneNumber || 'N/A'}</td>\n                  <td className=\"py-3 px-4 text-gray-300\">{user.zodiacSign && ZODIAC_INFO[user.zodiacSign] ? ZODIAC_INFO[user.zodiacSign].name : 'Unknown'}</td>\n                  <td className=\"py-3 px-4\">\n                    <span className={`px-2 py-1 rounded-full text-xs ${\n                      user.languagePreference === 'en'\n                        ? 'bg-blue-500/20 text-blue-300'\n                        : 'bg-green-500/20 text-green-300'\n                    }`}>\n                      {user.languagePreference === 'en' ? 'English' : 'සිංහල'}\n                    </span>\n                  </td>\n                  <td className=\"py-3 px-4 text-gray-300\">{user.scan_count || 0}</td>\n                  <td className=\"py-3 px-4 text-gray-300\">\n                    {user.last_scanned \n                      ? new Date(user.last_scanned).toLocaleDateString()\n                      : 'Never'\n                    }\n                  </td>\n                  <td className=\"py-3 px-4\">\n                    <div className=\"flex items-center space-x-2\" data-user-id={user.id}>\n                      <button\n                        onClick={() => handleGenerateQR(user)}\n                        className=\"text-blue-400 hover:text-blue-300 hover:bg-blue-400/10 p-2 rounded transition-all duration-200 active:scale-95\"\n                        title=\"Generate QR Code\"\n                      >\n                        <QrCode size={16} />\n                      </button>\n                      <button\n                        onClick={() => handleCopyQRUrl(user)}\n                        className=\"text-green-400 hover:text-green-300 hover:bg-green-400/10 p-2 rounded transition-all duration-200 active:scale-95 copy-url-btn\"\n                        title=\"Copy QR URL\"\n                      >\n                        <Link size={16} />\n                      </button>\n                      <button\n                        onClick={() => handleEditUser(user)}\n                        className=\"text-purple-400 hover:text-purple-300 hover:bg-purple-400/10 p-2 rounded transition-all duration-200 active:scale-95\"\n                        title=\"Edit\"\n                      >\n                        <Edit size={16} />\n                      </button>\n                      <button\n                        onClick={() => handleDeleteUser(user.id)}\n                        className=\"text-red-400 hover:text-red-300 hover:bg-red-400/10 p-2 rounded transition-all duration-200 active:scale-95\"\n                        title=\"Delete\"\n                      >\n                        <Trash2 size={16} />\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Bulk Actions */}\n      {selectedUsers.length > 0 && (\n        <div className=\"bg-purple-600/20 border border-purple-500/50 rounded-lg p-4\">\n          <div className=\"flex items-center justify-between\">\n            <p className=\"text-white\">\n              {selectedUsers.length} user{selectedUsers.length > 1 ? 's' : ''} selected\n            </p>\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => handleBulkDelete()}\n                className=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors\"\n              >\n                Delete Selected\n              </button>\n              <button\n                onClick={() => handleBulkExport()}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors\"\n              >\n                Export Selected\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Pagination */}\n      <div className=\"flex items-center justify-between\">\n        <p className=\"text-gray-300 text-sm\">\n          Showing {filteredUsers.length} of {users.length} users\n        </p>\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={() => alert('Pagination coming soon!')}\n            className=\"bg-white/10 hover:bg-white/20 text-white px-3 py-1 rounded text-sm transition-colors\"\n          >\n            Previous\n          </button>\n          <button className=\"bg-purple-600 text-white px-3 py-1 rounded text-sm\">1</button>\n          <button\n            onClick={() => alert('Pagination coming soon!')}\n            className=\"bg-white/10 hover:bg-white/20 text-white px-3 py-1 rounded text-sm transition-colors\"\n          >\n            Next\n          </button>\n        </div>\n      </div>\n\n      {/* Add User Modal */}\n      {showAddModal && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n          <div className=\"bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\">\n            <h3 className=\"text-xl font-bold text-white mb-4\">Add New User</h3>\n\n            <form onSubmit={handleAddUser} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Name</label>\n                <input\n                  type=\"text\"\n                  value={newUser.name}\n                  onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Email (Optional)</label>\n                <input\n                  type=\"email\"\n                  value={newUser.email}\n                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Phone Number (Optional)</label>\n                <input\n                  type=\"tel\"\n                  value={newUser.phoneNumber}\n                  onChange={(e) => setNewUser({ ...newUser, phoneNumber: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  placeholder=\"+94 77 123 4567\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Address (Optional)</label>\n                <textarea\n                  value={newUser.address}\n                  onChange={(e) => setNewUser({ ...newUser, address: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  rows={2}\n                  placeholder=\"Enter full address\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Birth Date</label>\n                <input\n                  type=\"date\"\n                  value={newUser.birthDate}\n                  onChange={(e) => setNewUser({ ...newUser, birthDate: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Birth Time (Optional)</label>\n                <input\n                  type=\"time\"\n                  value={newUser.birthTime}\n                  onChange={(e) => setNewUser({ ...newUser, birthTime: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Zodiac Sign</label>\n                <select\n                  value={newUser.zodiacSign}\n                  onChange={(e) => setNewUser({ ...newUser, zodiacSign: e.target.value as ZodiacSign | '' })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  style={{ colorScheme: 'dark' }}\n                >\n                  <option value=\"\" className=\"bg-gray-800 text-white\">Auto-detect from birth date</option>\n                  {ZODIAC_SIGNS.map((sign) => (\n                    <option key={sign} value={sign} className=\"bg-gray-800 text-white\">\n                      {ZODIAC_INFO[sign].symbol} {ZODIAC_INFO[sign].name} ({ZODIAC_INFO[sign].dates})\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Language Preference</label>\n                <select\n                  value={newUser.languagePreference}\n                  onChange={(e) => setNewUser({ ...newUser, languagePreference: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  style={{ colorScheme: 'dark' }}\n                >\n                  <option value=\"en\" className=\"bg-gray-800 text-white\">English</option>\n                  <option value=\"si\" className=\"bg-gray-800 text-white\">Sinhala</option>\n                </select>\n              </div>\n\n              <div className=\"flex items-center space-x-3 pt-4\">\n                <button\n                  type=\"submit\"\n                  className=\"flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors\"\n                >\n                  Add User & Generate QR\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={() => {\n                    setShowAddModal(false);\n                    setNewUser({\n                      name: '',\n                      email: '',\n                      phoneNumber: '',\n                      address: '',\n                      birthDate: '',\n                      birthTime: '',\n                      zodiacSign: '' as ZodiacSign | '',\n                      languagePreference: 'en'\n                    });\n                  }}\n                  className=\"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors\"\n                >\n                  Cancel\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n\n      {/* Edit User Modal */}\n      {showEditModal && selectedUserForEdit && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n          <div className=\"bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\">\n            <div className=\"mb-4\">\n              <h3 className=\"text-xl font-bold text-white\">Edit User: {selectedUserForEdit.name}</h3>\n              <div className=\"text-sm text-gray-400 space-y-1\">\n                <p>User ID: {selectedUserForEdit.id}</p>\n                <p>Created: {new Date(selectedUserForEdit.createdAt).toLocaleDateString()}</p>\n                {selectedUserForEdit.updatedAt && (\n                  <p>Last Updated: {new Date(selectedUserForEdit.updatedAt).toLocaleDateString()}</p>\n                )}\n              </div>\n            </div>\n\n            <form onSubmit={handleUpdateUser} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Name</label>\n                <input\n                  type=\"text\"\n                  value={editUser.name}\n                  onChange={(e) => setEditUser({ ...editUser, name: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Email (Optional)</label>\n                <input\n                  type=\"email\"\n                  value={editUser.email}\n                  onChange={(e) => setEditUser({ ...editUser, email: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Phone Number (Optional)</label>\n                <input\n                  type=\"tel\"\n                  value={editUser.phoneNumber}\n                  onChange={(e) => setEditUser({ ...editUser, phoneNumber: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  placeholder=\"+94 77 123 4567\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Address (Optional)</label>\n                <textarea\n                  value={editUser.address}\n                  onChange={(e) => setEditUser({ ...editUser, address: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  rows={2}\n                  placeholder=\"Enter full address\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Birth Date</label>\n                <input\n                  type=\"date\"\n                  value={editUser.birthDate}\n                  onChange={(e) => setEditUser({ ...editUser, birthDate: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Birth Time (Optional)</label>\n                <input\n                  type=\"time\"\n                  value={editUser.birthTime}\n                  onChange={(e) => setEditUser({ ...editUser, birthTime: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">\n                  Zodiac Sign\n                  {selectedUserForEdit?.zodiacSign && (\n                    <span className=\"ml-2 text-xs text-gray-400\">\n                      (Current: {ZODIAC_INFO[selectedUserForEdit.zodiacSign]?.symbol} {ZODIAC_INFO[selectedUserForEdit.zodiacSign]?.name})\n                    </span>\n                  )}\n                </label>\n                <select\n                  value={editUser.zodiacSign}\n                  onChange={(e) => setEditUser({ ...editUser, zodiacSign: e.target.value as ZodiacSign | '' })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  style={{ colorScheme: 'dark' }}\n                >\n                  <option value=\"\" className=\"bg-gray-800 text-white\">Auto-detect from birth date</option>\n                  {ZODIAC_SIGNS.map((sign) => (\n                    <option key={sign} value={sign} className=\"bg-gray-800 text-white\">\n                      {ZODIAC_INFO[sign].symbol} {ZODIAC_INFO[sign].name} ({ZODIAC_INFO[sign].dates})\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Language Preference</label>\n                <select\n                  value={editUser.languagePreference}\n                  onChange={(e) => setEditUser({ ...editUser, languagePreference: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  style={{ colorScheme: 'dark' }}\n                >\n                  <option value=\"en\" className=\"bg-gray-800 text-white\">English</option>\n                  <option value=\"si\" className=\"bg-gray-800 text-white\">Sinhala</option>\n                </select>\n              </div>\n\n              <div className=\"flex items-center space-x-3 pt-4\">\n                <button\n                  type=\"submit\"\n                  className=\"flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors\"\n                >\n                  Update User\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={() => {\n                    setShowEditModal(false);\n                    setSelectedUserForEdit(null);\n                    setEditUser({\n                      name: '',\n                      email: '',\n                      phoneNumber: '',\n                      address: '',\n                      birthDate: '',\n                      birthTime: '',\n                      zodiacSign: '' as ZodiacSign | '',\n                      languagePreference: 'en'\n                    });\n                  }}\n                  className=\"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors\"\n                >\n                  Cancel\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n\n      {/* QR URL Modal */}\n      {showQRUrlModal && selectedUserForQR && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n          <div className=\"bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\">\n            <h3 className=\"text-xl font-bold text-white mb-4\">QR Code URL</h3>\n            <p className=\"text-gray-300 mb-4\">\n              Share this URL with <strong>{selectedUserForQR.name}</strong> to access their dashboard:\n            </p>\n\n            <div className=\"bg-gray-700 rounded-lg p-3 mb-4\">\n              <code className=\"text-green-400 text-sm break-all\">\n                {`${window.location.origin}/qr/${selectedUserForQR.qrToken}`}\n              </code>\n            </div>\n\n            <div className=\"flex gap-3\">\n              <button\n                onClick={() => copyUrlToClipboard(`${window.location.origin}/qr/${selectedUserForQR.qrToken}`)}\n                className=\"flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors copy-url-modal-btn flex items-center justify-center gap-2\"\n              >\n                <Copy size={16} />\n                Copy URL\n              </button>\n              <button\n                onClick={() => {\n                  setShowQRUrlModal(false);\n                  setSelectedUserForQR(null);\n                }}\n                className=\"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors\"\n              >\n                Close\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACjF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACrF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;QACT,WAAW;QACX,WAAW;QACX,YAAY;QACZ,oBAAoB;IACtB;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;QACT,WAAW;QACX,WAAW;QACX,YAAY;QACZ,oBAAoB;IACtB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS,KAAK,IAAI,CAAC,KAAK;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,QAAQ,+CAA+C;QAE5D,IAAI;YACF,QAAQ,GAAG,CAAC,sBAAsB;YAClC,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,wBAAwB,EAAE,QAAQ,EAAE;gBAChE,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,oBAAoB;YAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE;gBAC/B,QAAQ,GAAG,CAAC;gBACZ,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBAC1C,MAAM;YACR,OAAO;gBACL,QAAQ,KAAK,CAAC,oBAAoB,KAAK,KAAK;gBAC5C,MAAM,4BAA4B,CAAC,KAAK,KAAK,IAAI,eAAe;YAClE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC,KAAK,IAAI;YAExD,uCAAuC;YACvC,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,GAAG,CAAC;gBACZ,uDAAuD;gBACvD,MAAM,WAAW,MAAM,MAAM,0BAA0B;oBACrD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,gBAAgB;oBAClE;oBACA,MAAM,KAAK,SAAS,CAAC;wBAAE,SAAS,KAAK,OAAO;wBAAE,UAAU,KAAK,IAAI;oBAAC;gBACpE;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;wBACzC,MAAM,OAAO,SAAS,aAAa,CAAC;wBACpC,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,WAAW;wBACjC,KAAK,QAAQ,GAAG,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC;wBAC1C,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B,KAAK,KAAK;wBACV,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B,MAAM;wBACN;oBACF;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,aAAa,KAAK,WAAW;oBAC7B,SAAS,KAAK,OAAO;oBACrB,WAAW,KAAK,SAAS;oBACzB,WAAW,KAAK,SAAS;oBACzB,oBAAoB,KAAK,kBAAkB;gBAC7C;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;gBACzC,QAAQ,GAAG,CAAC;gBACZ,mBAAmB;gBACnB,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,WAAW;gBACjC,KAAK,QAAQ,GAAG,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC;gBAC1C,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,KAAK,KAAK;gBACV,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,MAAM;YACR,OAAO;gBACL,QAAQ,KAAK,CAAC,2BAA2B,KAAK,KAAK;gBACnD,MAAM,iCAAiC,CAAC,KAAK,KAAK,IAAI,eAAe;YACvE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,QAAQ,GAAG,CAAC,+BAA+B,KAAK,IAAI;QAEpD,IAAI,CAAC,KAAK,OAAO,EAAE;YACjB,QAAQ,KAAK,CAAC;YACd,MAAM;YACN;QACF;QAEA,qBAAqB;QACrB,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,QAAQ,GAAG,CAAC,oBAAoB,KAAK,IAAI;QACzC,uBAAuB;QAEvB,oDAAoD;QACpD,YAAY;YACV,MAAM,KAAK,IAAI,IAAI;YACnB,OAAO,KAAK,KAAK,IAAI;YACrB,aAAa,KAAK,WAAW,IAAI;YACjC,SAAS,KAAK,OAAO,IAAI;YACzB,WAAW,KAAK,SAAS,GAAG,IAAI,KAAK,KAAK,SAAS,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;YACnF,WAAW,KAAK,SAAS,IAAI;YAC7B,YAAY,KAAK,UAAU,IAAI;YAC/B,oBAAoB,KAAK,kBAAkB,IAAI;QACjD;QAEA,iBAAiB;IACnB;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YAEpC,mCAAmC;YACnC,MAAM,aAAa,SAAS,aAAa,CAAC;YAC1C,IAAI,YAAY;gBACd,MAAM,eAAe,WAAW,SAAS;gBACzC,WAAW,SAAS,GAAG;gBACvB,WAAW;oBACT,WAAW,SAAS,GAAG;gBACzB,GAAG;YACL;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,cAAc,MAAM,KAAK,GAAG;QAEhC,MAAM,iBAAiB,CAAC,gCAAgC,EAAE,cAAc,MAAM,CAAC,KAAK,EAAE,cAAc,MAAM,GAAG,IAAI,MAAM,GAAG,CAAC,CAAC;QAC5H,IAAI,CAAC,QAAQ,iBAAiB;QAE9B,IAAI;YACF,QAAQ,GAAG,CAAC,4BAA4B;YACxC,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,6DAA6D;YAC7D,MAAM,iBAAiB,cAAc,GAAG,CAAC,CAAA,SACvC,MAAM,CAAC,wBAAwB,EAAE,QAAQ,EAAE;oBACzC,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;oBACpC;gBACF;YAGF,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAClC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,EAAE,MAAM;YAErD,IAAI,iBAAiB,cAAc,MAAM,EAAE;gBACzC,QAAQ,GAAG,CAAC;gBACZ,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,cAAc,QAAQ,CAAC,KAAK,EAAE;gBAC7D,iBAAiB,EAAE;gBACnB,MAAM,CAAC,qBAAqB,EAAE,aAAa,KAAK,EAAE,eAAe,IAAI,MAAM,GAAG,CAAC,CAAC;YAClF,OAAO;gBACL,QAAQ,IAAI,CAAC;gBACb,MAAM,CAAC,QAAQ,EAAE,aAAa,QAAQ,EAAE,cAAc,MAAM,CAAC,4CAA4C,CAAC;gBAC1G,cAAc,mBAAmB;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,cAAc,MAAM,KAAK,GAAG;QAEhC,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,MAAM,mBAAmB,MAAM,MAAM,CAAC,CAAA,OAAQ,cAAc,QAAQ,CAAC,KAAK,EAAE;QAC5E,MAAM,aAAa;YACjB,aAAa;YACb;YACA,WAAW;eACR,iBAAiB,GAAG,CAAC,CAAA,OAAQ;oBAC9B,KAAK,IAAI;oBACT,KAAK,KAAK,IAAI;oBACd,KAAK,WAAW,IAAI;oBACpB,KAAK,OAAO,IAAI;oBAChB,KAAK,SAAS,GAAG,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,KAAK;oBACjE,KAAK,SAAS,IAAI;oBAClB,KAAK,UAAU,IAAI;oBACnB,KAAK,kBAAkB,IAAI;oBAC3B,KAAK,UAAU,IAAI;oBACnB,KAAK,YAAY,GAAG,IAAI,KAAK,KAAK,YAAY,EAAE,kBAAkB,KAAK;oBACvE,KAAK,OAAO,IAAI;iBACjB,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC,CAAC,EAAE,OAAO,OAAO,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;SAC/D,CAAC,IAAI,CAAC;QAEP,+BAA+B;QAC/B,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAA0B;QACtE,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG,IAAI,eAAe,CAAC;QAChC,KAAK,QAAQ,GAAG,CAAC,mBAAmB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QAClF,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,MAAM,CAAC,SAAS,EAAE,cAAc,MAAM,CAAC,KAAK,EAAE,cAAc,MAAM,GAAG,IAAI,MAAM,GAAG,aAAa,CAAC;IAClG;IAEA,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAEhB,IAAI,CAAC,qBAAqB;YACxB,MAAM;YACN;QACF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,qBAAqB,oBAAoB,EAAE,EAAE;YAEzD,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,oBAAoB,EAAE,EAAE,EAAE;gBACzE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,QAAQ;oBACX,YAAY,SAAS,UAAU,IAAI,UAAU,kEAAkE;gBACjH;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,oBAAoB;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,QAAQ,GAAG,CAAC;gBAEZ,qCAAqC;gBACrC,SAAS,MAAM,GAAG,CAAC,CAAA,OACjB,KAAK,EAAE,KAAK,oBAAoB,EAAE,GAC9B;wBAAE,GAAG,IAAI;wBAAE,GAAG,KAAK,IAAI,CAAC,IAAI;oBAAC,IAC7B;gBAGN,6BAA6B;gBAC7B,YAAY;oBACV,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,SAAS;oBACT,WAAW;oBACX,WAAW;oBACX,YAAY;oBACZ,oBAAoB;gBACtB;gBACA,iBAAiB;gBACjB,uBAAuB;gBAEvB,MAAM;YACR,OAAO;gBACL,QAAQ,KAAK,CAAC,oBAAoB,KAAK,KAAK;gBAC5C,MAAM,4BAA4B,CAAC,KAAK,KAAK,IAAI,eAAe;YAClE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,EAAE,cAAc;QAEhB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,OAAO;oBACV,YAAY,QAAQ,UAAU,IAAI,UAAU,kEAAkE;gBAChH;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,+BAA+B;gBAC/B,SAAS;oBAAC,KAAK,IAAI,CAAC,IAAI;uBAAK;iBAAM;gBAEnC,6BAA6B;gBAC7B,WAAW;oBACT,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,SAAS;oBACT,WAAW;oBACX,WAAW;oBACX,YAAY;oBACZ,oBAAoB;gBACtB;gBACA,gBAAgB;gBAEhB,iCAAiC;gBACjC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;oBACzB,MAAM,OAAO,SAAS,aAAa,CAAC;oBACpC,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,WAAW;oBACjC,KAAK,QAAQ,GAAG,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;oBACpD,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC1B,KAAK,KAAK;oBACV,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B;YACF,OAAO;gBACL,MAAM,wBAAwB,KAAK,KAAK;YAC1C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OACjC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,KAAK,EAAE,cAAc,SAAS,WAAW,WAAW;IAG3D,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;gCAAmE,MAAM;;;;;;0CAC3F,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;kCAGd,8OAAC;wBACC,SAAS,IAAM,MAAM;wBACrB,WAAU;;0CAEV,8OAAC,sMAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;0CACd,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC;wBACC,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;0CACZ,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAM,WAAU;;0CACf,8OAAC;gCAAM,WAAU;0CACf,cAAA,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,UAAU,CAAC;oDACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;wDACpB,iBAAiB,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;oDAC9C,OAAO;wDACL,iBAAiB,EAAE;oDACrB;gDACF;;;;;;;;;;;sDAGJ,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAChE,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;;;;;;;;;;;;0CAGpE,8OAAC;0CACE,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;wCAAiB,WAAU;;0DAC1B,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,cAAc,QAAQ,CAAC,KAAK,EAAE;oDACvC,UAAU,CAAC;wDACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;4DACpB,iBAAiB;mEAAI;gEAAe,KAAK,EAAE;6DAAC;wDAC9C,OAAO;4DACL,iBAAiB,cAAc,MAAM,CAAC,CAAA,KAAM,OAAO,KAAK,EAAE;wDAC5D;oDACF;;;;;;;;;;;0DAGJ,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAY,KAAK,UAAU,IAAI,sHAAA,CAAA,cAAW,CAAC,KAAK,UAAU,CAAC,GAAG,sHAAA,CAAA,cAAW,CAAC,KAAK,UAAU,CAAC,CAAC,MAAM,GAAG;;;;;;sEACnH,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAA0B,KAAK,IAAI;;;;;;8EAChD,8OAAC;oEAAE,WAAU;8EAAyB,KAAK,UAAU,IAAI,sHAAA,CAAA,cAAW,CAAC,KAAK,UAAU,CAAC,GAAG,sHAAA,CAAA,cAAW,CAAC,KAAK,UAAU,CAAC,CAAC,IAAI,GAAG;;;;;;;;;;;;;;;;;;;;;;;0DAIlI,8OAAC;gDAAG,WAAU;0DAA2B,KAAK,KAAK,IAAI;;;;;;0DACvD,8OAAC;gDAAG,WAAU;0DAA2B,KAAK,WAAW,IAAI;;;;;;0DAC7D,8OAAC;gDAAG,WAAU;0DAA2B,KAAK,UAAU,IAAI,sHAAA,CAAA,cAAW,CAAC,KAAK,UAAU,CAAC,GAAG,sHAAA,CAAA,cAAW,CAAC,KAAK,UAAU,CAAC,CAAC,IAAI,GAAG;;;;;;0DAC/H,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAK,WAAW,CAAC,+BAA+B,EAC/C,KAAK,kBAAkB,KAAK,OACxB,iCACA,kCACJ;8DACC,KAAK,kBAAkB,KAAK,OAAO,YAAY;;;;;;;;;;;0DAGpD,8OAAC;gDAAG,WAAU;0DAA2B,KAAK,UAAU,IAAI;;;;;;0DAC5D,8OAAC;gDAAG,WAAU;0DACX,KAAK,YAAY,GACd,IAAI,KAAK,KAAK,YAAY,EAAE,kBAAkB,KAC9C;;;;;;0DAGN,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAI,WAAU;oDAA8B,gBAAc,KAAK,EAAE;;sEAChE,8OAAC;4DACC,SAAS,IAAM,iBAAiB;4DAChC,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;gEAAC,MAAM;;;;;;;;;;;sEAEhB,8OAAC;4DACC,SAAS,IAAM,gBAAgB;4DAC/B,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,MAAM;;;;;;;;;;;sEAEd,8OAAC;4DACC,SAAS,IAAM,eAAe;4DAC9B,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;gEAAC,MAAM;;;;;;;;;;;sEAEd,8OAAC;4DACC,SAAS,IAAM,iBAAiB,KAAK,EAAE;4DACvC,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;gEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;uCAvEb,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;YAmFzB,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;gCACV,cAAc,MAAM;gCAAC;gCAAM,cAAc,MAAM,GAAG,IAAI,MAAM;gCAAG;;;;;;;sCAElE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM;oCACf,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM;oCACf,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;;4BAAwB;4BAC1B,cAAc,MAAM;4BAAC;4BAAK,MAAM,MAAM;4BAAC;;;;;;;kCAElD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,MAAM;gCACrB,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAO,WAAU;0CAAqD;;;;;;0CACvE,8OAAC;gCACC,SAAS,IAAM,MAAM;gCACrB,WAAU;0CACX;;;;;;;;;;;;;;;;;;YAOJ,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAElD,8OAAC;4BAAK,UAAU;4BAAe,WAAU;;8CACvC,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,QAAQ,IAAI;4CACnB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC/D,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,QAAQ,KAAK;4CACpB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAChE,WAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,QAAQ,WAAW;4CAC1B,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACtE,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,OAAO,QAAQ,OAAO;4CACtB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAClE,WAAU;4CACV,MAAM;4CACN,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,QAAQ,SAAS;4CACxB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACpE,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,QAAQ,SAAS;4CACxB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACpE,WAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,OAAO,QAAQ,UAAU;4CACzB,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;gDAAoB;4CACxF,WAAU;4CACV,OAAO;gDAAE,aAAa;4CAAO;;8DAE7B,8OAAC;oDAAO,OAAM;oDAAG,WAAU;8DAAyB;;;;;;gDACnD,sHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,qBACjB,8OAAC;wDAAkB,OAAO;wDAAM,WAAU;;4DACvC,sHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,MAAM;4DAAC;4DAAE,sHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,IAAI;4DAAC;4DAAG,sHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,KAAK;4DAAC;;uDADnE;;;;;;;;;;;;;;;;;8CAOnB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,OAAO,QAAQ,kBAAkB;4CACjC,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC7E,WAAU;4CACV,OAAO;gDAAE,aAAa;4CAAO;;8DAE7B,8OAAC;oDAAO,OAAM;oDAAK,WAAU;8DAAyB;;;;;;8DACtD,8OAAC;oDAAO,OAAM;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;8CAI1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,SAAS;gDACP,gBAAgB;gDAChB,WAAW;oDACT,MAAM;oDACN,OAAO;oDACP,aAAa;oDACb,SAAS;oDACT,WAAW;oDACX,WAAW;oDACX,YAAY;oDACZ,oBAAoB;gDACtB;4CACF;4CACA,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,iBAAiB,qCAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAA+B;wCAAY,oBAAoB,IAAI;;;;;;;8CACjF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAE;gDAAU,oBAAoB,EAAE;;;;;;;sDACnC,8OAAC;;gDAAE;gDAAU,IAAI,KAAK,oBAAoB,SAAS,EAAE,kBAAkB;;;;;;;wCACtE,oBAAoB,SAAS,kBAC5B,8OAAC;;gDAAE;gDAAe,IAAI,KAAK,oBAAoB,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;sCAKlF,8OAAC;4BAAK,UAAU;4BAAkB,WAAU;;8CAC1C,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACjE,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAClE,WAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACxE,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,OAAO,SAAS,OAAO;4CACvB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACpE,WAAU;4CACV,MAAM;4CACN,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACtE,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACtE,WAAU;;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;;gDAA+C;gDAE7D,qBAAqB,4BACpB,8OAAC;oDAAK,WAAU;;wDAA6B;wDAChC,sHAAA,CAAA,cAAW,CAAC,oBAAoB,UAAU,CAAC,EAAE;wDAAO;wDAAE,sHAAA,CAAA,cAAW,CAAC,oBAAoB,UAAU,CAAC,EAAE;wDAAK;;;;;;;;;;;;;sDAIzH,8OAAC;4CACC,OAAO,SAAS,UAAU;4CAC1B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;gDAAoB;4CAC1F,WAAU;4CACV,OAAO;gDAAE,aAAa;4CAAO;;8DAE7B,8OAAC;oDAAO,OAAM;oDAAG,WAAU;8DAAyB;;;;;;gDACnD,sHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,qBACjB,8OAAC;wDAAkB,OAAO;wDAAM,WAAU;;4DACvC,sHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,MAAM;4DAAC;4DAAE,sHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,IAAI;4DAAC;4DAAG,sHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,KAAK;4DAAC;;uDADnE;;;;;;;;;;;;;;;;;8CAOnB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,OAAO,SAAS,kBAAkB;4CAClC,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC/E,WAAU;4CACV,OAAO;gDAAE,aAAa;4CAAO;;8DAE7B,8OAAC;oDAAO,OAAM;oDAAK,WAAU;8DAAyB;;;;;;8DACtD,8OAAC;oDAAO,OAAM;oDAAK,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;8CAI1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,SAAS;gDACP,iBAAiB;gDACjB,uBAAuB;gDACvB,YAAY;oDACV,MAAM;oDACN,OAAO;oDACP,aAAa;oDACb,SAAS;oDACT,WAAW;oDACX,WAAW;oDACX,YAAY;oDACZ,oBAAoB;gDACtB;4CACF;4CACA,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,kBAAkB,mCACjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,8OAAC;4BAAE,WAAU;;gCAAqB;8CACZ,8OAAC;8CAAQ,kBAAkB,IAAI;;;;;;gCAAU;;;;;;;sCAG/D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CACb,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,kBAAkB,OAAO,EAAE;;;;;;;;;;;sCAIhE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,mBAAmB,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,kBAAkB,OAAO,EAAE;oCAC7F,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;wCAAM;;;;;;;8CAGpB,8OAAC;oCACC,SAAS;wCACP,kBAAkB;wCAClB,qBAAqB;oCACvB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 2137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/admin/AddHoroscopeModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ZodiacSign, HoroscopeType, LanguageCode } from '@/types';\nimport { X } from 'lucide-react';\nimport { ZODIAC_SIGNS, ZODIAC_INFO } from '@/utils/zodiac';\n\ninterface AddHoroscopeModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onAdd: (horoscope: any) => void;\n}\n\nexport default function AddHoroscopeModal({ isOpen, onClose, onAdd }: AddHoroscopeModalProps) {\n  const [formData, setFormData] = useState({\n    zodiacSign: '' as ZodiacSign | '',\n    type: '' as HoroscopeType | '',\n    content: '',\n    date: new Date().toISOString().split('T')[0],\n    language: 'en' as LanguageCode\n  });\n  const [loading, setLoading] = useState(false);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.zodiacSign || !formData.type || !formData.content) {\n      alert('Please fill in all required fields');\n      return;\n    }\n\n    setLoading(true);\n    \n    try {\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch('/api/admin/horoscopes', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify(formData)\n      });\n\n      const data = await response.json();\n      \n      if (data.success) {\n        onAdd(data.data);\n        setFormData({\n          zodiacSign: '' as ZodiacSign | '',\n          type: '' as HoroscopeType | '',\n          content: '',\n          date: new Date().toISOString().split('T')[0],\n          language: 'en' as LanguageCode\n        });\n        onClose();\n      } else {\n        alert('Error adding horoscope: ' + data.error);\n      }\n    } catch (error) {\n      console.error('Error adding horoscope:', error);\n      alert('Error adding horoscope. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n      <div className=\"bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-xl font-bold text-white\">Add New Horoscope</h3>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-white\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-1\">Zodiac Sign *</label>\n            <select\n              value={formData.zodiacSign}\n              onChange={(e) => setFormData({ ...formData, zodiacSign: e.target.value as ZodiacSign })}\n              className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              style={{ colorScheme: 'dark' }}\n              required\n            >\n              <option value=\"\" className=\"bg-gray-800 text-white\">Select zodiac sign</option>\n              {ZODIAC_SIGNS.map((sign) => (\n                <option key={sign} value={sign} className=\"bg-gray-800 text-white\">\n                  {ZODIAC_INFO[sign].symbol} {ZODIAC_INFO[sign].name}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-1\">Type *</label>\n            <select\n              value={formData.type}\n              onChange={(e) => setFormData({ ...formData, type: e.target.value as HoroscopeType })}\n              className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              style={{ colorScheme: 'dark' }}\n              required\n            >\n              <option value=\"\" className=\"bg-gray-800 text-white\">Select type</option>\n              <option value=\"daily\" className=\"bg-gray-800 text-white\">Daily</option>\n              <option value=\"weekly\" className=\"bg-gray-800 text-white\">Weekly</option>\n              <option value=\"monthly\" className=\"bg-gray-800 text-white\">Monthly</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-1\">Date *</label>\n            <input\n              type=\"date\"\n              value={formData.date}\n              onChange={(e) => setFormData({ ...formData, date: e.target.value })}\n              className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              required\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-1\">Language</label>\n            <select\n              value={formData.language}\n              onChange={(e) => setFormData({ ...formData, language: e.target.value as LanguageCode })}\n              className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              style={{ colorScheme: 'dark' }}\n            >\n              <option value=\"en\" className=\"bg-gray-800 text-white\">English</option>\n              <option value=\"si\" className=\"bg-gray-800 text-white\">සිංහල</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-300 mb-1\">Content *</label>\n            <textarea\n              value={formData.content}\n              onChange={(e) => setFormData({ ...formData, content: e.target.value })}\n              rows={6}\n              className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none\"\n              placeholder=\"Enter horoscope content...\"\n              required\n            />\n          </div>\n\n          <div className=\"flex items-center space-x-3 pt-4\">\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 text-white py-2 px-4 rounded-lg transition-colors\"\n            >\n              {loading ? 'Adding...' : 'Add Horoscope'}\n            </button>\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors\"\n            >\n              Cancel\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAae,SAAS,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAA0B;IAC1F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,YAAY;QACZ,MAAM;QACN,SAAS;QACT,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,UAAU;IACZ;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,UAAU,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,OAAO,EAAE;YAC/D,MAAM;YACN;QACF;QAEA,WAAW;QAEX,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,KAAK,IAAI;gBACf,YAAY;oBACV,YAAY;oBACZ,MAAM;oBACN,SAAS;oBACT,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC5C,UAAU;gBACZ;gBACA;YACF,OAAO;gBACL,MAAM,6BAA6B,KAAK,KAAK;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA+B;;;;;;sCAC7C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIb,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAChE,8OAAC;oCACC,OAAO,SAAS,UAAU;oCAC1B,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wCAAe;oCACrF,WAAU;oCACV,OAAO;wCAAE,aAAa;oCAAO;oCAC7B,QAAQ;;sDAER,8OAAC;4CAAO,OAAM;4CAAG,WAAU;sDAAyB;;;;;;wCACnD,sHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,qBACjB,8OAAC;gDAAkB,OAAO;gDAAM,WAAU;;oDACvC,sHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,MAAM;oDAAC;oDAAE,sHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,IAAI;;+CADvC;;;;;;;;;;;;;;;;;sCAOnB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAChE,8OAAC;oCACC,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAkB;oCAClF,WAAU;oCACV,OAAO;wCAAE,aAAa;oCAAO;oCAC7B,QAAQ;;sDAER,8OAAC;4CAAO,OAAM;4CAAG,WAAU;sDAAyB;;;;;;sDACpD,8OAAC;4CAAO,OAAM;4CAAQ,WAAU;sDAAyB;;;;;;sDACzD,8OAAC;4CAAO,OAAM;4CAAS,WAAU;sDAAyB;;;;;;sDAC1D,8OAAC;4CAAO,OAAM;4CAAU,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAI/D,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAChE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACjE,WAAU;oCACV,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAChE,8OAAC;oCACC,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAiB;oCACrF,WAAU;oCACV,OAAO;wCAAE,aAAa;oCAAO;;sDAE7B,8OAAC;4CAAO,OAAM;4CAAK,WAAU;sDAAyB;;;;;;sDACtD,8OAAC;4CAAO,OAAM;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAI1D,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAChE,8OAAC;oCACC,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACpE,MAAM;oCACN,WAAU;oCACV,aAAY;oCACZ,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,cAAc;;;;;;8CAE3B,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 2529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/admin/ContentManagement.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Horoscope, ZodiacSign } from '@/types';\nimport { Plus, Edit, Trash2, Calendar, Globe, Users } from 'lucide-react';\nimport { ZODIAC_SIGNS, ZODIAC_INFO } from '@/utils/zodiac';\nimport AddHoroscopeModal from './AddHoroscopeModal';\n\nexport default function ContentManagement() {\n  const [horoscopes, setHoroscopes] = useState<Horoscope[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedZodiac, setSelectedZodiac] = useState<ZodiacSign | 'all'>('all');\n  const [selectedType, setSelectedType] = useState<'daily' | 'weekly' | 'monthly' | 'all'>('all');\n  const [selectedLanguage, setSelectedLanguage] = useState<'en' | 'si' | 'all'>('all');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [userCounts, setUserCounts] = useState<Record<ZodiacSign, number>>({} as Record<ZodiacSign, number>);\n\n  useEffect(() => {\n    fetchHoroscopes();\n    fetchUserCounts();\n  }, [selectedZodiac, selectedType, selectedLanguage]);\n\n  const fetchHoroscopes = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      if (selectedZodiac !== 'all') params.append('zodiacSign', selectedZodiac);\n      if (selectedType !== 'all') params.append('type', selectedType);\n      if (selectedLanguage !== 'all') params.append('language', selectedLanguage);\n\n      const response = await fetch(`/api/admin/horoscopes?${params}`);\n      const data = await response.json();\n      \n      if (data.success) {\n        setHoroscopes(data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching horoscopes:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchUserCounts = async () => {\n    try {\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch('/api/admin/users/counts', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setUserCounts(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Error fetching user counts:', error);\n    }\n  };\n\n  const handleAddHoroscope = (newHoroscope: Horoscope) => {\n    setHoroscopes([newHoroscope, ...horoscopes]);\n    fetchUserCounts(); // Refresh user counts\n  };\n\n  const handleDeleteHoroscope = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this horoscope?')) return;\n\n    try {\n      const response = await fetch(`/api/admin/horoscopes?id=${id}`, {\n        method: 'DELETE'\n      });\n\n      if (response.ok) {\n        setHoroscopes(horoscopes.filter(h => h.id !== id));\n      }\n    } catch (error) {\n      console.error('Error deleting horoscope:', error);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-12\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-white\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Filters */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div>\n          <label className=\"block text-gray-300 text-sm mb-2\">Zodiac Sign</label>\n          <select\n            value={selectedZodiac}\n            onChange={(e) => setSelectedZodiac(e.target.value as ZodiacSign | 'all')}\n            className=\"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n            style={{ colorScheme: 'dark' }}\n          >\n            <option value=\"all\" className=\"bg-gray-800 text-white\">All Signs</option>\n            {ZODIAC_SIGNS.map(sign => (\n              <option key={sign} value={sign} className=\"bg-gray-800 text-white\">\n                {ZODIAC_INFO[sign].symbol} {ZODIAC_INFO[sign].name}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        <div>\n          <label className=\"block text-gray-300 text-sm mb-2\">Type</label>\n          <select\n            value={selectedType}\n            onChange={(e) => setSelectedType(e.target.value as any)}\n            className=\"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n            style={{ colorScheme: 'dark' }}\n          >\n            <option value=\"all\" className=\"bg-gray-800 text-white\">All Types</option>\n            <option value=\"daily\" className=\"bg-gray-800 text-white\">Daily</option>\n            <option value=\"weekly\" className=\"bg-gray-800 text-white\">Weekly</option>\n            <option value=\"monthly\" className=\"bg-gray-800 text-white\">Monthly</option>\n          </select>\n        </div>\n\n        <div>\n          <label className=\"block text-gray-300 text-sm mb-2\">Language</label>\n          <select\n            value={selectedLanguage}\n            onChange={(e) => setSelectedLanguage(e.target.value as any)}\n            className=\"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n            style={{ colorScheme: 'dark' }}\n          >\n            <option value=\"all\" className=\"bg-gray-800 text-white\">All Languages</option>\n            <option value=\"en\" className=\"bg-gray-800 text-white\">English</option>\n            <option value=\"si\" className=\"bg-gray-800 text-white\">සිංහල</option>\n          </select>\n        </div>\n\n        <div className=\"flex items-end\">\n          <button \n            onClick={() => setShowAddModal(true)}\n            className=\"w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center justify-center space-x-2 transition-colors\"\n          >\n            <Plus size={16} />\n            <span>Add Horoscope</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Content Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n        {horoscopes.map((horoscope) => (\n          <div key={horoscope.id} className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n            <div className=\"flex items-start justify-between mb-4\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"text-2xl\">{horoscope.zodiacSign && ZODIAC_INFO[horoscope.zodiacSign] ? ZODIAC_INFO[horoscope.zodiacSign].symbol : '⭐'}</div>\n                <div>\n                  <h3 className=\"text-white font-semibold\">\n                    {horoscope.zodiacSign && ZODIAC_INFO[horoscope.zodiacSign] ? ZODIAC_INFO[horoscope.zodiacSign].name : 'Unknown'}\n                  </h3>\n                  <div className=\"flex items-center space-x-2 text-sm\">\n                    <span className={`px-2 py-1 rounded-full text-xs ${\n                      horoscope.type === 'daily' ? 'bg-green-500/20 text-green-300' :\n                      horoscope.type === 'weekly' ? 'bg-blue-500/20 text-blue-300' :\n                      'bg-purple-500/20 text-purple-300'\n                    }`}>\n                      {horoscope.type}\n                    </span>\n                    <span className={`px-2 py-1 rounded-full text-xs ${\n                      horoscope.language === 'en' \n                        ? 'bg-blue-500/20 text-blue-300' \n                        : 'bg-green-500/20 text-green-300'\n                    }`}>\n                      {horoscope.language === 'en' ? 'EN' : 'SI'}\n                    </span>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-2\">\n                <button className=\"text-purple-400 hover:text-purple-300 p-1\" title=\"Edit\">\n                  <Edit size={16} />\n                </button>\n                <button \n                  onClick={() => handleDeleteHoroscope(horoscope.id)}\n                  className=\"text-red-400 hover:text-red-300 p-1\"\n                  title=\"Delete\"\n                >\n                  <Trash2 size={16} />\n                </button>\n              </div>\n            </div>\n\n            <p className=\"text-gray-300 text-sm mb-4 line-clamp-3\">\n              {horoscope.content}\n            </p>\n\n            <div className=\"flex items-center justify-between text-xs text-gray-400\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-1\">\n                  <Calendar size={12} />\n                  <span>{new Date(horoscope.date).toLocaleDateString()}</span>\n                </div>\n                <div className=\"flex items-center space-x-1\">\n                  <Users size={12} />\n                  <span>{userCounts[horoscope.zodiacSign] || 0} users</span>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-1\">\n                <Globe size={12} />\n                <span>{horoscope.language === 'en' ? 'English' : 'සිංහල'}</span>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {horoscopes.length === 0 && (\n        <div className=\"text-center py-12\">\n          <div className=\"text-gray-400 mb-4\">📝</div>\n          <h3 className=\"text-white font-semibold mb-2\">No horoscopes found</h3>\n          <p className=\"text-gray-400 mb-4\">\n            No horoscopes match your current filters. Try adjusting your search criteria.\n          </p>\n          <button \n            onClick={() => setShowAddModal(true)}\n            className=\"bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors\"\n          >\n            Add First Horoscope\n          </button>\n        </div>\n      )}\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n        <div className=\"bg-white/5 rounded-lg p-4 text-center\">\n          <p className=\"text-2xl font-bold text-white\">{horoscopes.filter(h => h.type === 'daily').length}</p>\n          <p className=\"text-gray-400 text-sm\">Daily</p>\n        </div>\n        <div className=\"bg-white/5 rounded-lg p-4 text-center\">\n          <p className=\"text-2xl font-bold text-white\">{horoscopes.filter(h => h.type === 'weekly').length}</p>\n          <p className=\"text-gray-400 text-sm\">Weekly</p>\n        </div>\n        <div className=\"bg-white/5 rounded-lg p-4 text-center\">\n          <p className=\"text-2xl font-bold text-white\">{horoscopes.filter(h => h.type === 'monthly').length}</p>\n          <p className=\"text-gray-400 text-sm\">Monthly</p>\n        </div>\n        <div className=\"bg-white/5 rounded-lg p-4 text-center\">\n          <p className=\"text-2xl font-bold text-white\">{horoscopes.filter(h => h.language === 'si').length}</p>\n          <p className=\"text-gray-400 text-sm\">Sinhala</p>\n        </div>\n      </div>\n\n      {/* Add Horoscope Modal */}\n      <AddHoroscopeModal\n        isOpen={showAddModal}\n        onClose={() => setShowAddModal(false)}\n        onAdd={handleAddHoroscope}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0C;IACzF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAE1E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG;QAAC;QAAgB;QAAc;KAAiB;IAEnD,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI;YACnB,IAAI,mBAAmB,OAAO,OAAO,MAAM,CAAC,cAAc;YAC1D,IAAI,iBAAiB,OAAO,OAAO,MAAM,CAAC,QAAQ;YAClD,IAAI,qBAAqB,OAAO,OAAO,MAAM,CAAC,YAAY;YAE1D,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,QAAQ;YAC9D,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,cAAc,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc,KAAK,IAAI;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,cAAc;YAAC;eAAiB;SAAW;QAC3C,mBAAmB,sBAAsB;IAC3C;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI,CAAC,QAAQ,oDAAoD;QAEjE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,yBAAyB,EAAE,IAAI,EAAE;gBAC7D,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,cAAc,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAChD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAmC;;;;;;0CACpD,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;gCACV,OAAO;oCAAE,aAAa;gCAAO;;kDAE7B,8OAAC;wCAAO,OAAM;wCAAM,WAAU;kDAAyB;;;;;;oCACtD,sHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAA,qBAChB,8OAAC;4CAAkB,OAAO;4CAAM,WAAU;;gDACvC,sHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,MAAM;gDAAC;gDAAE,sHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,IAAI;;2CADvC;;;;;;;;;;;;;;;;;kCAOnB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAmC;;;;;;0CACpD,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;gCACV,OAAO;oCAAE,aAAa;gCAAO;;kDAE7B,8OAAC;wCAAO,OAAM;wCAAM,WAAU;kDAAyB;;;;;;kDACvD,8OAAC;wCAAO,OAAM;wCAAQ,WAAU;kDAAyB;;;;;;kDACzD,8OAAC;wCAAO,OAAM;wCAAS,WAAU;kDAAyB;;;;;;kDAC1D,8OAAC;wCAAO,OAAM;wCAAU,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAI/D,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAmC;;;;;;0CACpD,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAU;gCACV,OAAO;oCAAE,aAAa;gCAAO;;kDAE7B,8OAAC;wCAAO,OAAM;wCAAM,WAAU;kDAAyB;;;;;;kDACvD,8OAAC;wCAAO,OAAM;wCAAK,WAAU;kDAAyB;;;;;;kDACtD,8OAAC;wCAAO,OAAM;wCAAK,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAI1D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;8CACZ,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,0BACf,8OAAC;wBAAuB,WAAU;;0CAChC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAY,UAAU,UAAU,IAAI,sHAAA,CAAA,cAAW,CAAC,UAAU,UAAU,CAAC,GAAG,sHAAA,CAAA,cAAW,CAAC,UAAU,UAAU,CAAC,CAAC,MAAM,GAAG;;;;;;0DAClI,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,UAAU,UAAU,IAAI,sHAAA,CAAA,cAAW,CAAC,UAAU,UAAU,CAAC,GAAG,sHAAA,CAAA,cAAW,CAAC,UAAU,UAAU,CAAC,CAAC,IAAI,GAAG;;;;;;kEAExG,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,+BAA+B,EAC/C,UAAU,IAAI,KAAK,UAAU,mCAC7B,UAAU,IAAI,KAAK,WAAW,iCAC9B,oCACA;0EACC,UAAU,IAAI;;;;;;0EAEjB,8OAAC;gEAAK,WAAW,CAAC,+BAA+B,EAC/C,UAAU,QAAQ,KAAK,OACnB,iCACA,kCACJ;0EACC,UAAU,QAAQ,KAAK,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;kDAM9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAO,WAAU;gDAA4C,OAAM;0DAClE,cAAA,8OAAC,2MAAA,CAAA,OAAI;oDAAC,MAAM;;;;;;;;;;;0DAEd,8OAAC;gDACC,SAAS,IAAM,sBAAsB,UAAU,EAAE;gDACjD,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;0CAKpB,8OAAC;gCAAE,WAAU;0CACV,UAAU,OAAO;;;;;;0CAGpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;kEAChB,8OAAC;kEAAM,IAAI,KAAK,UAAU,IAAI,EAAE,kBAAkB;;;;;;;;;;;;0DAEpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,MAAM;;;;;;kEACb,8OAAC;;4DAAM,UAAU,CAAC,UAAU,UAAU,CAAC,IAAI;4DAAE;;;;;;;;;;;;;;;;;;;kDAGjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;0DACb,8OAAC;0DAAM,UAAU,QAAQ,KAAK,OAAO,YAAY;;;;;;;;;;;;;;;;;;;uBA1D7C,UAAU,EAAE;;;;;;;;;;YAiEzB,WAAW,MAAM,KAAK,mBACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAqB;;;;;;kCACpC,8OAAC;wBAAG,WAAU;kCAAgC;;;;;;kCAC9C,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBACC,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;kCACX;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAiC,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,MAAM;;;;;;0CAC/F,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAiC,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAU,MAAM;;;;;;0CAChG,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAiC,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM;;;;;;0CACjG,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAiC,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,MAAM,MAAM;;;;;;0CAChG,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAKzC,8OAAC,gJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,gBAAgB;gBAC/B,OAAO;;;;;;;;;;;;AAIf", "debugId": null}}, {"offset": {"line": 3248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/admin/PersonalHoroscopeManagement.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { User } from '@/types';\nimport { Search, Plus, Edit, Trash2, Star, Calendar, User as UserIcon } from 'lucide-react';\n\ninterface PersonalHoroscope {\n  id: string;\n  userId: string;\n  title: string;\n  content: string;\n  isActive: boolean;\n  language: string;\n  createdAt: string;\n  updatedAt: string;\n  user: {\n    id: string;\n    name: string;\n    zodiacSign: string;\n  };\n}\n\nexport default function PersonalHoroscopeManagement() {\n  const [horoscopes, setHoroscopes] = useState<PersonalHoroscope[]>([]);\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showAddModal, setShowAddModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [selectedHoroscope, setSelectedHoroscope] = useState<PersonalHoroscope | null>(null);\n  const [newHoroscope, setNewHoroscope] = useState({\n    userId: '',\n    title: '',\n    content: '',\n    language: 'en'\n  });\n\n  useEffect(() => {\n    fetchHoroscopes();\n    fetchUsers();\n  }, []);\n\n  const fetchHoroscopes = async () => {\n    try {\n      setLoading(true);\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch('/api/admin/personal-horoscopes', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      const data = await response.json();\n      \n      if (data.success) {\n        setHoroscopes(data.data.horoscopes);\n      }\n    } catch (error) {\n      console.error('Error fetching horoscopes:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchUsers = async () => {\n    try {\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch('/api/admin/users', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      const data = await response.json();\n      \n      if (data.success) {\n        setUsers(data.data.users);\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error);\n    }\n  };\n\n  const handleAddHoroscope = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    try {\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch('/api/admin/personal-horoscopes', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify(newHoroscope)\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setHoroscopes([data.data.horoscope, ...horoscopes]);\n        setNewHoroscope({\n          userId: '',\n          title: '',\n          content: '',\n          language: 'en'\n        });\n        setShowAddModal(false);\n        alert('Personal horoscope created successfully!');\n      } else {\n        alert('Error creating horoscope: ' + data.error);\n      }\n    } catch (error) {\n      console.error('Error creating horoscope:', error);\n      alert('Error creating horoscope. Please try again.');\n    }\n  };\n\n  const handleEditHoroscope = (horoscope: PersonalHoroscope) => {\n    setSelectedHoroscope(horoscope);\n    setNewHoroscope({\n      userId: horoscope.userId,\n      title: horoscope.title,\n      content: horoscope.content,\n      language: horoscope.language\n    });\n    setShowEditModal(true);\n  };\n\n  const handleUpdateHoroscope = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!selectedHoroscope) return;\n\n    try {\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch('/api/admin/personal-horoscopes', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({\n          id: selectedHoroscope.id,\n          title: newHoroscope.title,\n          content: newHoroscope.content,\n          isActive: selectedHoroscope.isActive\n        })\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setHoroscopes(horoscopes.map(h => \n          h.id === selectedHoroscope.id ? data.data.horoscope : h\n        ));\n        setShowEditModal(false);\n        setSelectedHoroscope(null);\n        alert('Personal horoscope updated successfully!');\n      } else {\n        alert('Error updating horoscope: ' + data.error);\n      }\n    } catch (error) {\n      console.error('Error updating horoscope:', error);\n      alert('Error updating horoscope. Please try again.');\n    }\n  };\n\n  const handleDeleteHoroscope = async (id: string) => {\n    if (!confirm('Are you sure you want to delete this horoscope?')) return;\n\n    try {\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch(`/api/admin/personal-horoscopes?id=${id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setHoroscopes(horoscopes.filter(h => h.id !== id));\n        alert('Personal horoscope deleted successfully!');\n      } else {\n        alert('Error deleting horoscope: ' + data.error);\n      }\n    } catch (error) {\n      console.error('Error deleting horoscope:', error);\n      alert('Error deleting horoscope. Please try again.');\n    }\n  };\n\n  const filteredHoroscopes = horoscopes.filter(horoscope =>\n    horoscope.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    horoscope.user.name.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-12\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-white\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header Actions */}\n      <div className=\"flex flex-col sm:flex-row gap-4\">\n        <div className=\"flex-1 relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" size={20} />\n          <input\n            type=\"text\"\n            placeholder=\"Search horoscopes...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500\"\n          />\n        </div>\n        <button \n          onClick={() => setShowAddModal(true)}\n          className=\"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors\"\n        >\n          <Plus size={16} />\n          <span>Add Personal Horoscope</span>\n        </button>\n      </div>\n\n      {/* Horoscopes List */}\n      <div className=\"space-y-4\">\n        {filteredHoroscopes.length > 0 ? (\n          filteredHoroscopes.map((horoscope) => (\n            <div key={horoscope.id} className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-3 mb-2\">\n                    <Star className=\"text-yellow-400\" size={20} />\n                    <h3 className=\"text-lg font-bold text-white\">{horoscope.title}</h3>\n                    <span className={`px-2 py-1 rounded-full text-xs ${\n                      horoscope.isActive \n                        ? 'bg-green-500/20 text-green-300' \n                        : 'bg-red-500/20 text-red-300'\n                    }`}>\n                      {horoscope.isActive ? 'Active' : 'Inactive'}\n                    </span>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-4 text-sm text-gray-300 mb-3\">\n                    <span className=\"flex items-center\">\n                      <UserIcon size={16} className=\"mr-1\" />\n                      {horoscope.user.name}\n                    </span>\n                    <span className=\"flex items-center\">\n                      <Calendar size={16} className=\"mr-1\" />\n                      {new Date(horoscope.createdAt).toLocaleDateString()}\n                    </span>\n                  </div>\n                  \n                  <p className=\"text-gray-200 leading-relaxed line-clamp-3\">\n                    {horoscope.content}\n                  </p>\n                </div>\n                \n                <div className=\"flex items-center space-x-2 ml-4\">\n                  <button\n                    onClick={() => handleEditHoroscope(horoscope)}\n                    className=\"text-purple-400 hover:text-purple-300 hover:bg-purple-400/10 p-2 rounded transition-all duration-200\"\n                    title=\"Edit\"\n                  >\n                    <Edit size={16} />\n                  </button>\n                  <button\n                    onClick={() => handleDeleteHoroscope(horoscope.id)}\n                    className=\"text-red-400 hover:text-red-300 hover:bg-red-400/10 p-2 rounded transition-all duration-200\"\n                    title=\"Delete\"\n                  >\n                    <Trash2 size={16} />\n                  </button>\n                </div>\n              </div>\n            </div>\n          ))\n        ) : (\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center\">\n            <Star className=\"mx-auto mb-4 text-gray-400\" size={48} />\n            <h3 className=\"text-xl font-semibold text-white mb-2\">No Personal Horoscopes</h3>\n            <p className=\"text-gray-300\">Create personalized horoscope content for your users.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Add/Edit Modal */}\n      {(showAddModal || showEditModal) && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n          <div className=\"bg-gray-800 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto\">\n            <h3 className=\"text-xl font-bold text-white mb-4\">\n              {showEditModal ? 'Edit Personal Horoscope' : 'Add Personal Horoscope'}\n            </h3>\n\n            <form onSubmit={showEditModal ? handleUpdateHoroscope : handleAddHoroscope} className=\"space-y-4\">\n              {!showEditModal && (\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-1\">Select User</label>\n                  <select\n                    value={newHoroscope.userId}\n                    onChange={(e) => setNewHoroscope({ ...newHoroscope, userId: e.target.value })}\n                    className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                    required\n                  >\n                    <option value=\"\">Choose a user...</option>\n                    {users.map((user) => (\n                      <option key={user.id} value={user.id}>\n                        {user.name} ({user.zodiacSign})\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              )}\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Title</label>\n                <input\n                  type=\"text\"\n                  value={newHoroscope.title}\n                  onChange={(e) => setNewHoroscope({ ...newHoroscope, title: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  placeholder=\"e.g., Your Weekly Cosmic Guidance\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-1\">Content</label>\n                <textarea\n                  value={newHoroscope.content}\n                  onChange={(e) => setNewHoroscope({ ...newHoroscope, content: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n                  rows={8}\n                  placeholder=\"Write the personalized horoscope content...\"\n                  required\n                />\n              </div>\n\n              <div className=\"flex items-center space-x-3 pt-4\">\n                <button\n                  type=\"submit\"\n                  className=\"flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors\"\n                >\n                  {showEditModal ? 'Update Horoscope' : 'Create Horoscope'}\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={() => {\n                    setShowAddModal(false);\n                    setShowEditModal(false);\n                    setSelectedHoroscope(null);\n                    setNewHoroscope({\n                      userId: '',\n                      title: '',\n                      content: '',\n                      language: 'en'\n                    });\n                  }}\n                  className=\"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors\"\n                >\n                  Cancel\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAsBe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IACpE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IACrF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,QAAQ;QACR,OAAO;QACP,SAAS;QACT,UAAU;IACZ;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,kCAAkC;gBAC7D,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,cAAc,KAAK,IAAI,CAAC,UAAU;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS,KAAK,IAAI,CAAC,KAAK;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,EAAE,cAAc;QAEhB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,kCAAkC;gBAC7D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,cAAc;oBAAC,KAAK,IAAI,CAAC,SAAS;uBAAK;iBAAW;gBAClD,gBAAgB;oBACd,QAAQ;oBACR,OAAO;oBACP,SAAS;oBACT,UAAU;gBACZ;gBACA,gBAAgB;gBAChB,MAAM;YACR,OAAO;gBACL,MAAM,+BAA+B,KAAK,KAAK;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,qBAAqB;QACrB,gBAAgB;YACd,QAAQ,UAAU,MAAM;YACxB,OAAO,UAAU,KAAK;YACtB,SAAS,UAAU,OAAO;YAC1B,UAAU,UAAU,QAAQ;QAC9B;QACA,iBAAiB;IACnB;IAEA,MAAM,wBAAwB,OAAO;QACnC,EAAE,cAAc;QAEhB,IAAI,CAAC,mBAAmB;QAExB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,kCAAkC;gBAC7D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,IAAI,kBAAkB,EAAE;oBACxB,OAAO,aAAa,KAAK;oBACzB,SAAS,aAAa,OAAO;oBAC7B,UAAU,kBAAkB,QAAQ;gBACtC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,cAAc,WAAW,GAAG,CAAC,CAAA,IAC3B,EAAE,EAAE,KAAK,kBAAkB,EAAE,GAAG,KAAK,IAAI,CAAC,SAAS,GAAG;gBAExD,iBAAiB;gBACjB,qBAAqB;gBACrB,MAAM;YACR,OAAO;gBACL,MAAM,+BAA+B,KAAK,KAAK;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI,CAAC,QAAQ,oDAAoD;QAEjE,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,kCAAkC,EAAE,IAAI,EAAE;gBACtE,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,cAAc,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9C,MAAM;YACR,OAAO;gBACL,MAAM,+BAA+B,KAAK,KAAK;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,YAC3C,UAAU,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,UAAU,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGnE,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;gCAAmE,MAAM;;;;;;0CAC3F,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;kCAGd,8OAAC;wBACC,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;0CACZ,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKV,8OAAC;gBAAI,WAAU;0BACZ,mBAAmB,MAAM,GAAG,IAC3B,mBAAmB,GAAG,CAAC,CAAC,0BACtB,8OAAC;wBAAuB,WAAU;kCAChC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;oDAAkB,MAAM;;;;;;8DACxC,8OAAC;oDAAG,WAAU;8DAAgC,UAAU,KAAK;;;;;;8DAC7D,8OAAC;oDAAK,WAAW,CAAC,+BAA+B,EAC/C,UAAU,QAAQ,GACd,mCACA,8BACJ;8DACC,UAAU,QAAQ,GAAG,WAAW;;;;;;;;;;;;sDAIrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;sEACd,8OAAC,kMAAA,CAAA,OAAQ;4DAAC,MAAM;4DAAI,WAAU;;;;;;wDAC7B,UAAU,IAAI,CAAC,IAAI;;;;;;;8DAEtB,8OAAC;oDAAK,WAAU;;sEACd,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,MAAM;4DAAI,WAAU;;;;;;wDAC7B,IAAI,KAAK,UAAU,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;sDAIrD,8OAAC;4CAAE,WAAU;sDACV,UAAU,OAAO;;;;;;;;;;;;8CAItB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,oBAAoB;4CACnC,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;sDAEd,8OAAC;4CACC,SAAS,IAAM,sBAAsB,UAAU,EAAE;4CACjD,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;uBA5CZ,UAAU,EAAE;;;;8CAmDxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;4BAA6B,MAAM;;;;;;sCACnD,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;YAMlC,CAAC,gBAAgB,aAAa,mBAC7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,gBAAgB,4BAA4B;;;;;;sCAG/C,8OAAC;4BAAK,UAAU,gBAAgB,wBAAwB;4BAAoB,WAAU;;gCACnF,CAAC,+BACA,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,OAAO,aAAa,MAAM;4CAC1B,UAAU,CAAC,IAAM,gBAAgB;oDAAE,GAAG,YAAY;oDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC3E,WAAU;4CACV,QAAQ;;8DAER,8OAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wDAAqB,OAAO,KAAK,EAAE;;4DACjC,KAAK,IAAI;4DAAC;4DAAG,KAAK,UAAU;4DAAC;;uDADnB,KAAK,EAAE;;;;;;;;;;;;;;;;;8CAQ5B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,MAAK;4CACL,OAAO,aAAa,KAAK;4CACzB,UAAU,CAAC,IAAM,gBAAgB;oDAAE,GAAG,YAAY;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC1E,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,OAAO,aAAa,OAAO;4CAC3B,UAAU,CAAC,IAAM,gBAAgB;oDAAE,GAAG,YAAY;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC5E,WAAU;4CACV,MAAM;4CACN,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;sDAET,gBAAgB,qBAAqB;;;;;;sDAExC,8OAAC;4CACC,MAAK;4CACL,SAAS;gDACP,gBAAgB;gDAChB,iBAAiB;gDACjB,qBAAqB;gDACrB,gBAAgB;oDACd,QAAQ;oDACR,OAAO;oDACP,SAAS;oDACT,UAAU;gDACZ;4CACF;4CACA,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 3886, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/admin/Analytics.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { BarChart3, Users, TrendingUp, Calendar, Globe, Clock, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';\n\ninterface AnalyticsData {\n  totalUsers: number;\n  activeUsers: number;\n  totalScans: number;\n  totalHoroscopes: number;\n  userGrowth: number;\n  scanGrowth: number;\n  topZodiacSigns: Array<{\n    sign: string;\n    count: number;\n    percentage: number;\n  }>;\n  languageDistribution: Array<{\n    language: string;\n    count: number;\n    percentage: number;\n  }>;\n}\n\nexport default function Analytics() {\n  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');\n  const [schedulerStatus, setSchedulerStatus] = useState<any>(null);\n  const [generatingReadings, setGeneratingReadings] = useState(false);\n\n  useEffect(() => {\n    fetchAnalytics();\n    fetchSchedulerStatus();\n  }, [timeRange]);\n\n  const fetchAnalytics = async () => {\n    try {\n      setLoading(true);\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch(`/api/admin/analytics?range=${timeRange}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setAnalytics(data.data);\n      } else {\n        console.error('Failed to load analytics');\n      }\n    } catch (error) {\n      console.error('Error fetching analytics:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchSchedulerStatus = async () => {\n    try {\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch('/api/admin/scheduler', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setSchedulerStatus(data.data);\n      }\n    } catch (error) {\n      console.error('Failed to load scheduler status:', error);\n    }\n  };\n\n  const generateDailyReadings = async () => {\n    try {\n      setGeneratingReadings(true);\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch('/api/admin/scheduler', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify({ action: 'generate' })\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        alert(`Successfully generated ${data.data.count} daily readings!`);\n        fetchSchedulerStatus(); // Refresh status\n      } else {\n        alert('Failed to generate readings: ' + data.error);\n      }\n    } catch (error) {\n      console.error('Error generating readings:', error);\n      alert('Error generating readings. Please try again.');\n    } finally {\n      setGeneratingReadings(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-white/10 rounded w-48 mb-6\"></div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            {[1, 2, 3, 4].map((i) => (\n              <div key={i} className=\"bg-white/10 rounded-lg p-6 h-32\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h1 className=\"text-2xl font-bold text-white\">Analytics Dashboard</h1>\n        <div className=\"flex items-center space-x-2\">\n          <select\n            value={timeRange}\n            onChange={(e) => setTimeRange(e.target.value as '7d' | '30d' | '90d')}\n            className=\"bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500\"\n          >\n            <option value=\"7d\">Last 7 days</option>\n            <option value=\"30d\">Last 30 days</option>\n            <option value=\"90d\">Last 90 days</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-gray-300 text-sm\">Total Users</p>\n              <p className=\"text-3xl font-bold text-white\">{analytics?.totalUsers || 0}</p>\n              <p className=\"text-green-400 text-sm\">+{analytics?.userGrowth || 0}% growth</p>\n            </div>\n            <Users className=\"w-8 h-8 text-blue-400\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-gray-300 text-sm\">Active Users</p>\n              <p className=\"text-3xl font-bold text-white\">{analytics?.activeUsers || 0}</p>\n              <p className=\"text-gray-400 text-sm\">Users with scans</p>\n            </div>\n            <TrendingUp className=\"w-8 h-8 text-green-400\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-gray-300 text-sm\">Total Scans</p>\n              <p className=\"text-3xl font-bold text-white\">{analytics?.totalScans || 0}</p>\n              <p className=\"text-green-400 text-sm\">+{analytics?.scanGrowth || 0}% growth</p>\n            </div>\n            <BarChart3 className=\"w-8 h-8 text-yellow-400\" />\n          </div>\n        </div>\n\n        <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-gray-300 text-sm\">Content Items</p>\n              <p className=\"text-3xl font-bold text-white\">{analytics?.totalHoroscopes || 0}</p>\n              <p className=\"text-gray-400 text-sm\">Horoscopes & guides</p>\n            </div>\n            <Calendar className=\"w-8 h-8 text-purple-400\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Daily Readings Scheduler Status */}\n      {schedulerStatus && (\n        <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-white flex items-center\">\n              <Clock className=\"mr-2 text-blue-400\" />\n              Daily Readings Scheduler\n            </h3>\n            <button\n              onClick={fetchSchedulerStatus}\n              className=\"text-gray-400 hover:text-white transition-colors\"\n              title=\"Refresh Status\"\n            >\n              <RefreshCw size={16} />\n            </button>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n            <div className=\"bg-white/5 rounded-lg p-4\">\n              <div className=\"flex items-center mb-2\">\n                {schedulerStatus.scheduler.isRunning ? (\n                  <CheckCircle className=\"text-green-400 mr-2\" size={20} />\n                ) : (\n                  <AlertCircle className=\"text-red-400 mr-2\" size={20} />\n                )}\n                <h4 className=\"text-white font-semibold\">Status</h4>\n              </div>\n              <p className={`text-sm ${schedulerStatus.scheduler.isRunning ? 'text-green-300' : 'text-red-300'}`}>\n                {schedulerStatus.scheduler.isRunning ? 'Running' : 'Stopped'}\n              </p>\n            </div>\n\n            <div className=\"bg-white/5 rounded-lg p-4\">\n              <div className=\"flex items-center mb-2\">\n                <Calendar className=\"text-blue-400 mr-2\" size={20} />\n                <h4 className=\"text-white font-semibold\">Last Generated</h4>\n              </div>\n              <p className=\"text-sm text-gray-300\">\n                {schedulerStatus.scheduler.lastGeneratedDate || 'Never'}\n              </p>\n            </div>\n\n            <div className=\"bg-white/5 rounded-lg p-4\">\n              <div className=\"flex items-center mb-2\">\n                <Clock className=\"text-purple-400 mr-2\" size={20} />\n                <h4 className=\"text-white font-semibold\">Next Check</h4>\n              </div>\n              <p className=\"text-sm text-gray-300\">\n                {schedulerStatus.scheduler.nextCheck}\n              </p>\n            </div>\n          </div>\n\n          <div className=\"flex items-center justify-between\">\n            <div className=\"text-sm text-gray-300\">\n              <p>Server Time: {new Date(schedulerStatus.serverTime).toLocaleString()}</p>\n            </div>\n            <button\n              onClick={generateDailyReadings}\n              disabled={generatingReadings}\n              className=\"bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors\"\n            >\n              {generatingReadings ? (\n                <>\n                  <RefreshCw className=\"animate-spin\" size={16} />\n                  <span>Generating...</span>\n                </>\n              ) : (\n                <>\n                  <RefreshCw size={16} />\n                  <span>Generate Now</span>\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Charts and Detailed Analytics */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Top Zodiac Signs */}\n        <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n          <h3 className=\"text-lg font-semibold text-white mb-4\">Popular Zodiac Signs</h3>\n          <div className=\"space-y-3\">\n            {analytics?.topZodiacSigns?.map((sign, index) => (\n              <div key={sign.sign} className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"text-2xl\">{getZodiacSymbol(sign.sign)}</span>\n                  <span className=\"text-white\">{sign.sign}</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-24 bg-gray-700 rounded-full h-2\">\n                    <div \n                      className=\"bg-purple-500 h-2 rounded-full\" \n                      style={{ width: `${sign.percentage}%` }}\n                    ></div>\n                  </div>\n                  <span className=\"text-gray-300 text-sm w-12\">{sign.count}</span>\n                </div>\n              </div>\n            )) || (\n              <p className=\"text-gray-400 text-center py-4\">No data available</p>\n            )}\n          </div>\n        </div>\n\n        {/* Language Distribution */}\n        <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n          <h3 className=\"text-lg font-semibold text-white mb-4\">Language Preferences</h3>\n          <div className=\"space-y-3\">\n            {analytics?.languageDistribution?.map((lang) => (\n              <div key={lang.language} className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-3\">\n                  <Globe className=\"w-5 h-5 text-blue-400\" />\n                  <span className=\"text-white\">{lang.language === 'en' ? 'English' : 'Sinhala'}</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-24 bg-gray-700 rounded-full h-2\">\n                    <div \n                      className=\"bg-blue-500 h-2 rounded-full\" \n                      style={{ width: `${lang.percentage}%` }}\n                    ></div>\n                  </div>\n                  <span className=\"text-gray-300 text-sm w-12\">{lang.count}</span>\n                </div>\n              </div>\n            )) || (\n              <p className=\"text-gray-400 text-center py-4\">No data available</p>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Coming Soon Features */}\n      <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center\">\n        <h3 className=\"text-xl font-bold text-white mb-4\">Advanced Analytics</h3>\n        <p className=\"text-gray-300 mb-6\">\n          More detailed charts, user behavior analysis, and engagement metrics coming soon.\n        </p>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-400\">\n          <div>📊 User Engagement Trends</div>\n          <div>📈 Content Performance</div>\n          <div>🎯 Conversion Analytics</div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction getZodiacSymbol(sign: string): string {\n  const symbols: { [key: string]: string } = {\n    'Aries': '♈',\n    'Taurus': '♉',\n    'Gemini': '♊',\n    'Cancer': '♋',\n    'Leo': '♌',\n    'Virgo': '♍',\n    'Libra': '♎',\n    'Scorpio': '♏',\n    'Sagittarius': '♐',\n    'Capricorn': '♑',\n    'Aquarius': '♒',\n    'Pisces': '♓'\n  };\n  return symbols[sign] || '⭐';\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAwBe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,CAAC,2BAA2B,EAAE,WAAW,EAAE;gBACtE,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,aAAa,KAAK,IAAI;YACxB,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,mBAAmB,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI;YACF,sBAAsB;YACtB,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ;gBAAW;YAC5C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,CAAC,uBAAuB,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;gBACjE,wBAAwB,iBAAiB;YAC3C,OAAO;gBACL,MAAM,kCAAkC,KAAK,KAAK;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,8OAAC;gCAAY,WAAU;+BAAb;;;;;;;;;;;;;;;;;;;;;IAMtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAgC;;;;;;kCAC9C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4BAC5C,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;8CAAK;;;;;;8CACnB,8OAAC;oCAAO,OAAM;8CAAM;;;;;;8CACpB,8OAAC;oCAAO,OAAM;8CAAM;;;;;;;;;;;;;;;;;;;;;;;0BAM1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,8OAAC;4CAAE,WAAU;sDAAiC,WAAW,cAAc;;;;;;sDACvE,8OAAC;4CAAE,WAAU;;gDAAyB;gDAAE,WAAW,cAAc;gDAAE;;;;;;;;;;;;;8CAErE,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,8OAAC;4CAAE,WAAU;sDAAiC,WAAW,eAAe;;;;;;sDACxE,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAI1B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,8OAAC;4CAAE,WAAU;sDAAiC,WAAW,cAAc;;;;;;sDACvE,8OAAC;4CAAE,WAAU;;gDAAyB;gDAAE,WAAW,cAAc;gDAAE;;;;;;;;;;;;;8CAErE,8OAAC,kNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAIzB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,8OAAC;4CAAE,WAAU;sDAAiC,WAAW,mBAAmB;;;;;;sDAC5E,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAEvC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAMzB,iCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAuB;;;;;;;0CAG1C,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,MAAM;;;;;;;;;;;;;;;;;kCAIrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ,gBAAgB,SAAS,CAAC,SAAS,iBAClC,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;gDAAsB,MAAM;;;;;qEAEnD,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;gDAAoB,MAAM;;;;;;0DAEnD,8OAAC;gDAAG,WAAU;0DAA2B;;;;;;;;;;;;kDAE3C,8OAAC;wCAAE,WAAW,CAAC,QAAQ,EAAE,gBAAgB,SAAS,CAAC,SAAS,GAAG,mBAAmB,gBAAgB;kDAC/F,gBAAgB,SAAS,CAAC,SAAS,GAAG,YAAY;;;;;;;;;;;;0CAIvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAAqB,MAAM;;;;;;0DAC/C,8OAAC;gDAAG,WAAU;0DAA2B;;;;;;;;;;;;kDAE3C,8OAAC;wCAAE,WAAU;kDACV,gBAAgB,SAAS,CAAC,iBAAiB,IAAI;;;;;;;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAuB,MAAM;;;;;;0DAC9C,8OAAC;gDAAG,WAAU;0DAA2B;;;;;;;;;;;;kDAE3C,8OAAC;wCAAE,WAAU;kDACV,gBAAgB,SAAS,CAAC,SAAS;;;;;;;;;;;;;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;;wCAAE;wCAAc,IAAI,KAAK,gBAAgB,UAAU,EAAE,cAAc;;;;;;;;;;;;0CAEtE,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,mCACC;;sDACE,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;4CAAe,MAAM;;;;;;sDAC1C,8OAAC;sDAAK;;;;;;;iEAGR;;sDACE,8OAAC,gNAAA,CAAA,YAAS;4CAAC,MAAM;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;0BASlB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,gBAAgB,IAAI,CAAC,MAAM,sBACrC,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAY,gBAAgB,KAAK,IAAI;;;;;;kEACrD,8OAAC;wDAAK,WAAU;kEAAc,KAAK,IAAI;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC;4DAAC;;;;;;;;;;;kEAG1C,8OAAC;wDAAK,WAAU;kEAA8B,KAAK,KAAK;;;;;;;;;;;;;uCAZlD,KAAK,IAAI;;;;+DAgBnB,8OAAC;oCAAE,WAAU;8CAAiC;;;;;;;;;;;;;;;;;kCAMpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,sBAAsB,IAAI,CAAC,qBACrC,8OAAC;wCAAwB,WAAU;;0DACjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAc,KAAK,QAAQ,KAAK,OAAO,YAAY;;;;;;;;;;;;0DAErE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC;4DAAC;;;;;;;;;;;kEAG1C,8OAAC;wDAAK,WAAU;kEAA8B,KAAK,KAAK;;;;;;;;;;;;;uCAZlD,KAAK,QAAQ;;;;+DAgBvB,8OAAC;oCAAE,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;;;;;;0BAOtD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAI;;;;;;0CACL,8OAAC;0CAAI;;;;;;0CACL,8OAAC;0CAAI;;;;;;;;;;;;;;;;;;;;;;;;AAKf;AAEA,SAAS,gBAAgB,IAAY;IACnC,MAAM,UAAqC;QACzC,SAAS;QACT,UAAU;QACV,UAAU;QACV,UAAU;QACV,OAAO;QACP,SAAS;QACT,SAAS;QACT,WAAW;QACX,eAAe;QACf,aAAa;QACb,YAAY;QACZ,UAAU;IACZ;IACA,OAAO,OAAO,CAAC,KAAK,IAAI;AAC1B", "debugId": null}}, {"offset": {"line": 4879, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Users, FileText, BarChart3, Globe, Plus, Search, Filter, LogOut, Shield } from 'lucide-react';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport UserManagement from '@/components/admin/UserManagement';\nimport ContentManagement from '@/components/admin/ContentManagement';\nimport PersonalHoroscopeManagement from '@/components/admin/PersonalHoroscopeManagement';\nimport Analytics from '@/components/admin/Analytics';\n\ninterface AdminStats {\n  totalUsers: number;\n  totalHoroscopes: number;\n  totalScans: number;\n  activeUsers: number;\n}\n\ninterface AdminUser {\n  id: string;\n  email: string;\n  name: string;\n  role: string;\n}\n\ninterface ActivityItem {\n  id: string;\n  action: string;\n  user: string;\n  time: string;\n  type: 'user_registered' | 'qr_scanned' | 'horoscope_updated' | 'content_added';\n}\n\nexport default function AdminDashboard() {\n  const [admin, setAdmin] = useState<AdminUser | null>(null);\n  const [stats, setStats] = useState<AdminStats | null>(null);\n  const [activities, setActivities] = useState<ActivityItem[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'content' | 'horoscopes' | 'analytics'>('overview');\n  const router = useRouter();\n\n  useEffect(() => {\n    checkAdminAuth();\n  }, []);\n\n  const checkAdminAuth = async () => {\n    try {\n      const token = localStorage.getItem('admin-token');\n      if (!token) {\n        router.push('/admin/login');\n        return;\n      }\n\n      const response = await fetch('/api/admin/auth/verify', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setAdmin(data.data.admin);\n        await fetchAdminStats();\n        await fetchActivities();\n      } else {\n        localStorage.removeItem('admin-token');\n        localStorage.removeItem('admin-user');\n        router.push('/admin/login');\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      router.push('/admin/login');\n    }\n  };\n\n  const fetchAdminStats = async () => {\n    try {\n      setLoading(true);\n      const token = localStorage.getItem('admin-token');\n\n      const response = await fetch('/api/admin/stats', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setStats(data.data);\n      } else {\n        setError('Failed to load admin statistics');\n      }\n    } catch (err) {\n      console.error('Error fetching admin stats:', err);\n      setError('Failed to load admin statistics');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchActivities = async () => {\n    try {\n      const token = localStorage.getItem('admin-token');\n      const response = await fetch('/api/admin/activity', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setActivities(data.data);\n      } else {\n        console.error('Failed to load recent activities');\n      }\n    } catch (err) {\n      console.error('Error fetching activities:', err);\n    }\n  };\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/admin/auth/logout', { method: 'POST' });\n      localStorage.removeItem('admin-token');\n      localStorage.removeItem('admin-user');\n      router.push('/admin/login');\n    } catch (error) {\n      console.error('Logout error:', error);\n      // Force logout even if API call fails\n      localStorage.removeItem('admin-token');\n      localStorage.removeItem('admin-user');\n      router.push('/admin/login');\n    }\n  };\n\n  if (loading || !admin) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\">\n        <LoadingSpinner message=\"Loading admin dashboard...\" />\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center\">\n        <ErrorMessage\n          title=\"Admin Dashboard Error\"\n          message={error}\n          onRetry={fetchAdminStats}\n        />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900\">\n      {/* Header */}\n      <header className=\"bg-black/20 backdrop-blur-sm border-b border-white/10\">\n        <div className=\"max-w-7xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-white\">AstroConnect Admin</h1>\n              <p className=\"text-gray-300\">Manage users, content, and analytics</p>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              {admin && (\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"text-right\">\n                    <p className=\"text-white font-medium\">{admin.name}</p>\n                    <p className=\"text-gray-300 text-sm\">{admin.email}</p>\n                  </div>\n                  <div className=\"w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center\">\n                    <Shield className=\"w-4 h-4 text-white\" />\n                  </div>\n                </div>\n              )}\n              <button\n                onClick={handleLogout}\n                className=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors\"\n              >\n                <LogOut size={16} />\n                <span>Logout</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation */}\n      <nav className=\"bg-black/10 backdrop-blur-sm border-b border-white/10\">\n        <div className=\"max-w-7xl mx-auto px-4\">\n          <div className=\"flex space-x-8\">\n            {[\n              { id: 'overview', label: 'Overview', icon: BarChart3 },\n              { id: 'users', label: 'Users', icon: Users },\n              { id: 'content', label: 'Content', icon: FileText },\n              { id: 'horoscopes', label: 'Personal Horoscopes', icon: Globe },\n              { id: 'analytics', label: 'Analytics', icon: BarChart3 }\n            ].map(({ id, label, icon: Icon }) => (\n              <button\n                key={id}\n                onClick={() => setActiveTab(id as any)}\n                className={`flex items-center space-x-2 py-4 px-2 border-b-2 transition-colors ${\n                  activeTab === id\n                    ? 'border-purple-400 text-white'\n                    : 'border-transparent text-gray-300 hover:text-white'\n                }`}\n              >\n                <Icon size={16} />\n                <span>{label}</span>\n              </button>\n            ))}\n          </div>\n        </div>\n      </nav>\n\n      {/* Content */}\n      <main className=\"max-w-7xl mx-auto px-4 py-8\">\n        {activeTab === 'overview' && stats && (\n          <div className=\"space-y-8\">\n            {/* Stats Grid */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-gray-300 text-sm\">Total Users</p>\n                    <p className=\"text-3xl font-bold text-white\">{stats.totalUsers.toLocaleString()}</p>\n                  </div>\n                  <Users className=\"w-8 h-8 text-blue-400\" />\n                </div>\n              </div>\n\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-gray-300 text-sm\">Active Users</p>\n                    <p className=\"text-3xl font-bold text-white\">{stats.activeUsers.toLocaleString()}</p>\n                  </div>\n                  <Users className=\"w-8 h-8 text-green-400\" />\n                </div>\n              </div>\n\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-gray-300 text-sm\">Total Horoscopes</p>\n                    <p className=\"text-3xl font-bold text-white\">{stats.totalHoroscopes.toLocaleString()}</p>\n                  </div>\n                  <FileText className=\"w-8 h-8 text-purple-400\" />\n                </div>\n              </div>\n\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-gray-300 text-sm\">QR Scans</p>\n                    <p className=\"text-3xl font-bold text-white\">{stats.totalScans.toLocaleString()}</p>\n                  </div>\n                  <BarChart3 className=\"w-8 h-8 text-yellow-400\" />\n                </div>\n              </div>\n            </div>\n\n            {/* Recent Activity */}\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n              <h2 className=\"text-xl font-bold text-white mb-4\">Recent Activity</h2>\n              <div className=\"space-y-4\">\n                {activities.length > 0 ? (\n                  activities.map((activity) => (\n                    <div key={activity.id} className=\"flex items-center justify-between py-2 border-b border-white/10 last:border-b-0\">\n                      <div>\n                        <p className=\"text-white\">{activity.action}</p>\n                        <p className=\"text-gray-400 text-sm\">{activity.user}</p>\n                      </div>\n                      <p className=\"text-gray-400 text-sm\">{activity.time}</p>\n                    </div>\n                  ))\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <p className=\"text-gray-400\">No recent activity</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'users' && <UserManagement />}\n\n        {activeTab === 'content' && <ContentManagement />}\n\n        {activeTab === 'horoscopes' && <PersonalHoroscopeManagement />}\n\n        {activeTab === 'analytics' && <Analytics />}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAkCe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiE;IAC1G,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,IAAI,CAAC,KAAK;gBACxB,MAAM;gBACN,MAAM;YACR,OAAO;gBACL,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,QAAQ,aAAa,OAAO,CAAC;YAEnC,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,IAAI;YACpB,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,cAAc,KAAK,IAAI;YACzB,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,0BAA0B;gBAAE,QAAQ;YAAO;YACvD,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,sCAAsC;YACtC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,IAAI,WAAW,CAAC,OAAO;QACrB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;gBAAC,SAAQ;;;;;;;;;;;IAG9B;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;gBACX,OAAM;gBACN,SAAS;gBACT,SAAS;;;;;;;;;;;IAIjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAG/B,8OAAC;gCAAI,WAAU;;oCACZ,uBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAA0B,MAAM,IAAI;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAyB,MAAM,KAAK;;;;;;;;;;;;0DAEnD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIxB,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;;0DACd,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,IAAI;gCAAY,OAAO;gCAAY,MAAM,kNAAA,CAAA,YAAS;4BAAC;4BACrD;gCAAE,IAAI;gCAAS,OAAO;gCAAS,MAAM,oMAAA,CAAA,QAAK;4BAAC;4BAC3C;gCAAE,IAAI;gCAAW,OAAO;gCAAW,MAAM,8MAAA,CAAA,WAAQ;4BAAC;4BAClD;gCAAE,IAAI;gCAAc,OAAO;gCAAuB,MAAM,oMAAA,CAAA,QAAK;4BAAC;4BAC9D;gCAAE,IAAI;gCAAa,OAAO;gCAAa,MAAM,kNAAA,CAAA,YAAS;4BAAC;yBACxD,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,iBAC9B,8OAAC;gCAEC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,mEAAmE,EAC7E,cAAc,KACV,iCACA,qDACJ;;kDAEF,8OAAC;wCAAK,MAAM;;;;;;kDACZ,8OAAC;kDAAM;;;;;;;+BATF;;;;;;;;;;;;;;;;;;;;0BAiBf,8OAAC;gBAAK,WAAU;;oBACb,cAAc,cAAc,uBAC3B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAAiC,MAAM,UAAU,CAAC,cAAc;;;;;;;;;;;;8DAE/E,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIrB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAAiC,MAAM,WAAW,CAAC,cAAc;;;;;;;;;;;;8DAEhF,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIrB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAAiC,MAAM,eAAe,CAAC,cAAc;;;;;;;;;;;;8DAEpF,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIxB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAAiC,MAAM,UAAU,CAAC,cAAc;;;;;;;;;;;;8DAE/E,8OAAC,kNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,8OAAC;wCAAI,WAAU;kDACZ,WAAW,MAAM,GAAG,IACnB,WAAW,GAAG,CAAC,CAAC,yBACd,8OAAC;gDAAsB,WAAU;;kEAC/B,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAc,SAAS,MAAM;;;;;;0EAC1C,8OAAC;gEAAE,WAAU;0EAAyB,SAAS,IAAI;;;;;;;;;;;;kEAErD,8OAAC;wDAAE,WAAU;kEAAyB,SAAS,IAAI;;;;;;;+CAL3C,SAAS,EAAE;;;;sEASvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQxC,cAAc,yBAAW,8OAAC,6IAAA,CAAA,UAAc;;;;;oBAExC,cAAc,2BAAa,8OAAC,gJAAA,CAAA,UAAiB;;;;;oBAE7C,cAAc,8BAAgB,8OAAC,0JAAA,CAAA,UAA2B;;;;;oBAE1D,cAAc,6BAAe,8OAAC,wIAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;AAIhD", "debugId": null}}]}