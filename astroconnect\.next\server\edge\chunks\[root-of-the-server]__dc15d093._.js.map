{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n// Rate limiting store (in production, use Redis or similar)\nconst rateLimitStore = new Map<string, { count: number; resetTime: number }>();\n\n// Rate limiting configuration\nconst RATE_LIMITS = {\n  '/api/auth/qr': { requests: 10, windowMs: 60000 }, // 10 requests per minute\n  '/api/translate': { requests: 50, windowMs: 60000 }, // 50 requests per minute\n  '/api/dashboard': { requests: 100, windowMs: 60000 }, // 100 requests per minute\n  '/api/admin': { requests: 20, windowMs: 60000 }, // 20 requests per minute for admin\n};\n\nfunction getClientIP(request: NextRequest): string {\n  const forwarded = request.headers.get('x-forwarded-for');\n  const realIP = request.headers.get('x-real-ip');\n  \n  if (forwarded) {\n    return forwarded.split(',')[0].trim();\n  }\n  \n  if (realIP) {\n    return realIP;\n  }\n  \n  return 'unknown';\n}\n\nfunction isRateLimited(ip: string, endpoint: string): boolean {\n  const config = RATE_LIMITS[endpoint as keyof typeof RATE_LIMITS];\n  if (!config) return false;\n\n  const key = `${ip}:${endpoint}`;\n  const now = Date.now();\n  const record = rateLimitStore.get(key);\n\n  if (!record || now > record.resetTime) {\n    // Reset or create new record\n    rateLimitStore.set(key, {\n      count: 1,\n      resetTime: now + config.windowMs\n    });\n    return false;\n  }\n\n  if (record.count >= config.requests) {\n    return true;\n  }\n\n  record.count++;\n  return false;\n}\n\nfunction validateInput(request: NextRequest): boolean {\n  const contentType = request.headers.get('content-type');\n  const method = request.method;\n\n  // Only validate content-type for POST/PUT/PATCH requests with body\n  if (method !== 'GET' && method !== 'HEAD' && contentType) {\n    const allowedTypes = [\n      'application/json',\n      'multipart/form-data',\n      'application/x-www-form-urlencoded',\n      'text/plain'\n    ];\n\n    if (!allowedTypes.some(type => contentType.includes(type))) {\n      return false;\n    }\n  }\n\n  // Check for suspicious headers (only check for really dangerous ones)\n  const suspiciousHeaders = ['x-original-url', 'x-rewrite-url'];\n  for (const header of suspiciousHeaders) {\n    if (request.headers.get(header)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction addSecurityHeaders(response: NextResponse): NextResponse {\n  // Security headers\n  response.headers.set('X-Content-Type-Options', 'nosniff');\n  response.headers.set('X-Frame-Options', 'DENY');\n  response.headers.set('X-XSS-Protection', '1; mode=block');\n  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');\n  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');\n  \n  // Content Security Policy\n  const csp = [\n    \"default-src 'self'\",\n    \"script-src 'self' 'unsafe-inline' 'unsafe-eval'\",\n    \"style-src 'self' 'unsafe-inline'\",\n    \"img-src 'self' data: https:\",\n    \"font-src 'self'\",\n    \"connect-src 'self' https://generativelanguage.googleapis.com\",\n    \"media-src 'self'\",\n    \"object-src 'none'\",\n    \"base-uri 'self'\",\n    \"form-action 'self'\",\n    \"frame-ancestors 'none'\",\n    \"upgrade-insecure-requests\"\n  ].join('; ');\n  \n  response.headers.set('Content-Security-Policy', csp);\n\n  return response;\n}\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n  const clientIP = getClientIP(request);\n\n  // Skip middleware for static files and Next.js internals\n  if (\n    pathname.startsWith('/_next') ||\n    pathname.startsWith('/static') ||\n    pathname.includes('.') ||\n    pathname === '/favicon.ico'\n  ) {\n    return NextResponse.next();\n  }\n\n  // Input validation\n  if (!validateInput(request)) {\n    return new NextResponse('Bad Request', { status: 400 });\n  }\n\n  // Rate limiting for API routes\n  if (pathname.startsWith('/api/')) {\n    const endpoint = Object.keys(RATE_LIMITS).find(key => pathname.startsWith(key));\n    \n    if (endpoint && isRateLimited(clientIP, endpoint)) {\n      return new NextResponse('Too Many Requests', { \n        status: 429,\n        headers: {\n          'Retry-After': '60'\n        }\n      });\n    }\n  }\n\n  // Admin route protection - Skip for now, let client-side handle auth\n  // The admin authentication is handled client-side with localStorage\n  // and server-side API verification. Middleware should not block admin pages\n  // as it doesn't have access to localStorage tokens.\n\n  // Create response and add security headers\n  const response = NextResponse.next();\n  return addSecurityHeaders(response);\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEA,4DAA4D;AAC5D,MAAM,iBAAiB,IAAI;AAE3B,8BAA8B;AAC9B,MAAM,cAAc;IAClB,gBAAgB;QAAE,UAAU;QAAI,UAAU;IAAM;IAChD,kBAAkB;QAAE,UAAU;QAAI,UAAU;IAAM;IAClD,kBAAkB;QAAE,UAAU;QAAK,UAAU;IAAM;IACnD,cAAc;QAAE,UAAU;QAAI,UAAU;IAAM;AAChD;AAEA,SAAS,YAAY,OAAoB;IACvC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEnC,IAAI,WAAW;QACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,IAAI,QAAQ;QACV,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,cAAc,EAAU,EAAE,QAAgB;IACjD,MAAM,SAAS,WAAW,CAAC,SAAqC;IAChE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,MAAM,GAAG,GAAG,CAAC,EAAE,UAAU;IAC/B,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,SAAS,eAAe,GAAG,CAAC;IAElC,IAAI,CAAC,UAAU,MAAM,OAAO,SAAS,EAAE;QACrC,6BAA6B;QAC7B,eAAe,GAAG,CAAC,KAAK;YACtB,OAAO;YACP,WAAW,MAAM,OAAO,QAAQ;QAClC;QACA,OAAO;IACT;IAEA,IAAI,OAAO,KAAK,IAAI,OAAO,QAAQ,EAAE;QACnC,OAAO;IACT;IAEA,OAAO,KAAK;IACZ,OAAO;AACT;AAEA,SAAS,cAAc,OAAoB;IACzC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;IACxC,MAAM,SAAS,QAAQ,MAAM;IAE7B,mEAAmE;IACnE,IAAI,WAAW,SAAS,WAAW,UAAU,aAAa;QACxD,MAAM,eAAe;YACnB;YACA;YACA;YACA;SACD;QAED,IAAI,CAAC,aAAa,IAAI,CAAC,CAAA,OAAQ,YAAY,QAAQ,CAAC,QAAQ;YAC1D,OAAO;QACT;IACF;IAEA,sEAAsE;IACtE,MAAM,oBAAoB;QAAC;QAAkB;KAAgB;IAC7D,KAAK,MAAM,UAAU,kBAAmB;QACtC,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC,SAAS;YAC/B,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAAS,mBAAmB,QAAsB;IAChD,mBAAmB;IACnB,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,oBAAoB;IACzC,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,sBAAsB;IAE3C,0BAA0B;IAC1B,MAAM,MAAM;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD,CAAC,IAAI,CAAC;IAEP,SAAS,OAAO,CAAC,GAAG,CAAC,2BAA2B;IAEhD,OAAO;AACT;AAEO,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IACpC,MAAM,WAAW,YAAY;IAE7B,yDAAyD;IACzD,IACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,cACpB,SAAS,QAAQ,CAAC,QAClB,aAAa,gBACb;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,mBAAmB;IACnB,IAAI,CAAC,cAAc,UAAU;QAC3B,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,eAAe;YAAE,QAAQ;QAAI;IACvD;IAEA,+BAA+B;IAC/B,IAAI,SAAS,UAAU,CAAC,UAAU;QAChC,MAAM,WAAW,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,CAAA,MAAO,SAAS,UAAU,CAAC;QAE1E,IAAI,YAAY,cAAc,UAAU,WAAW;YACjD,OAAO,IAAI,6LAAA,CAAA,eAAY,CAAC,qBAAqB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,eAAe;gBACjB;YACF;QACF;IACF;IAEA,qEAAqE;IACrE,oEAAoE;IACpE,4EAA4E;IAC5E,oDAAoD;IAEpD,2CAA2C;IAC3C,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI;IAClC,OAAO,mBAAmB;AAC5B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;KAKC,GACD;KACD;AACH"}}]}