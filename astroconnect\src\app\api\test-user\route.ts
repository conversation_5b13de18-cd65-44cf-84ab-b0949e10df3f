import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { generateQRToken } from '@/utils/qr';
import { getZodiacFromDate } from '@/utils/zodiac';
import { ZodiacSign, LanguageCode } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const { name, birthDate } = await request.json();
    
    console.log('Test user creation request:', { name, birthDate });

    if (!name || !birthDate) {
      return NextResponse.json({
        success: false,
        error: 'Name and birth date are required'
      }, { status: 400 });
    }

    // Calculate zodiac sign
    const zodiacSign = getZodiacFromDate(birthDate) as ZodiacSign;
    const qrToken = generateQRToken();

    console.log('Calculated zodiac sign:', zodiacSign);
    console.log('Generated QR token:', qrToken);

    // Create minimal user
    const user = await prisma.user.create({
      data: {
        name: name.trim(),
        birthDate: new Date(birthDate),
        zodiacSign,
        languagePreference: 'en' as LanguageCode,
        qrToken
      }
    });

    console.log('User created:', user.id);

    // Create QR code mapping
    const qrMapping = await prisma.qrCodeMapping.create({
      data: {
        qrToken: qrToken,
        userId: user.id
      }
    });

    console.log('QR mapping created:', qrMapping.id);

    return NextResponse.json({
      success: true,
      data: { user },
      message: 'Test user created successfully'
    });

  } catch (error) {
    console.error('Test user creation error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error
    }, { status: 500 });
  }
}
