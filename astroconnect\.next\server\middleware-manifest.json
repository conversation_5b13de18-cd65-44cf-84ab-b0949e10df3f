{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "b618H/y1SLZywAkLdpd8YhNKp6xbjpdDK+HBLp+oGsE=", "__NEXT_PREVIEW_MODE_ID": "dbfd28129fdafc33ea332598e451c86d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "829281255b8ce2bbe01ca5c5fc7d594ae698911482e5f86abb0c733c472e6264", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5402c954b921d6f2a2a08aa200a74d984319a0d70cd2fc7312f9eb2f919fa697"}}}, "sortedMiddleware": ["/"], "functions": {}}