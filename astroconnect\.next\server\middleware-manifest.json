{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tmfP7t62FS42Ol92K1wxSwcOnzOY86FgwY6pHAG2qo8=", "__NEXT_PREVIEW_MODE_ID": "0e11b68fb407f15f050364dc69c32152", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7abd9538efc7f177df1095b3c66a26245b59e425c9ec7805e760948d6391f767", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "161db217f04c9564b102136d0a77dcae5308d57e93e324246349f317fde0d6e9"}}}, "sortedMiddleware": ["/"], "functions": {}}