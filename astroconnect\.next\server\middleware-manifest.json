{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "b618H/y1SLZywAkLdpd8YhNKp6xbjpdDK+HBLp+oGsE=", "__NEXT_PREVIEW_MODE_ID": "a7b2859198664e24d09f592804734b8f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a6d183e2dac7702190c3ec9193ca805b2fc8d613d3e7d20167b8a24c7da87936", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c404515d5366ad0422c923e59a78312a4997d357447bbeda9bb8c8403e7e5d8b"}}}, "sortedMiddleware": ["/"], "functions": {}}