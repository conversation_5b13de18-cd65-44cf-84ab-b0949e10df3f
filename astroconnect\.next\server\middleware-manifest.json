{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "b618H/y1SLZywAkLdpd8YhNKp6xbjpdDK+HBLp+oGsE=", "__NEXT_PREVIEW_MODE_ID": "97d7f585675f0141408bb0d41cf46e59", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bdca6132e92f5fc8f3e85e95dd5c4bb290eea7fa385d001a2e0865f3395f8d0b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7ebd48a8a4b8366573a6eead0a5e36a329f2aa165ca3e166d497968e922aa748"}}}, "sortedMiddleware": ["/"], "functions": {}}