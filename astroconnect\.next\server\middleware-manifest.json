{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "b618H/y1SLZywAkLdpd8YhNKp6xbjpdDK+HBLp+oGsE=", "__NEXT_PREVIEW_MODE_ID": "8b6ae4a669e1bf00c30eff49b010f3ab", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f9128164d457e6808fa64f11968b53f1c0e76b3ba7781498e50ca99468233da0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3ce18874e3eab111b9489fad5be460780735e6c5d19fe6ed7781d442fe5021c6"}}}, "sortedMiddleware": ["/"], "functions": {}}