{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "b618H/y1SLZywAkLdpd8YhNKp6xbjpdDK+HBLp+oGsE=", "__NEXT_PREVIEW_MODE_ID": "6821420d9b9efb003cbcef055a556ea7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b09820f0226fe2e343f123adb6ac585313c347ba2f5d2f2199cd96dc0e9edc41", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0275c0e99a19b2c2203ac4e526d5f173a8e572639bff1552388211f26789c3a7"}}}, "sortedMiddleware": ["/"], "functions": {}}