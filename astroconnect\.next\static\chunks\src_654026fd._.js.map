{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Helper function to disconnect Prisma (useful for serverless)\nexport async function disconnectPrisma() {\n  await prisma.$disconnect();\n}\n\n// Helper function to check database connection\nexport async function checkDatabaseConnection() {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAQI;AARJ;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,yJAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/translation.ts"], "sourcesContent": ["import { prisma } from '@/lib/prisma';\n\nexport async function translateText(\n  text: string, \n  targetLanguage: 'en' | 'si',\n  sourceLanguage: 'en' | 'si' = 'en'\n): Promise<string> {\n  // If source and target are the same, return original text\n  if (sourceLanguage === targetLanguage) {\n    return text;\n  }\n\n  // Check cache first\n  const cachedTranslation = await getCachedTranslation(text, sourceLanguage, targetLanguage);\n  if (cachedTranslation) {\n    return cachedTranslation;\n  }\n\n  try {\n    // Call Gemini API for translation\n    const translatedText = await callGeminiTranslation(text, sourceLanguage, targetLanguage);\n    \n    // Cache the translation\n    await cacheTranslation(text, translatedText, sourceLanguage, targetLanguage);\n    \n    return translatedText;\n  } catch (error) {\n    console.error('Translation error:', error);\n    // Return original text if translation fails\n    return text;\n  }\n}\n\nasync function getCachedTranslation(\n  originalText: string,\n  sourceLanguage: 'en' | 'si',\n  targetLanguage: 'en' | 'si'\n): Promise<string | null> {\n  try {\n    const cached = await prisma.translationCache.findFirst({\n      where: {\n        originalText,\n        sourceLanguage,\n        targetLanguage\n      }\n    });\n\n    return cached?.translatedText || null;\n  } catch (error) {\n    console.error('Error fetching cached translation:', error);\n    return null;\n  }\n}\n\nasync function cacheTranslation(\n  originalText: string,\n  translatedText: string,\n  sourceLanguage: 'en' | 'si',\n  targetLanguage: 'en' | 'si'\n): Promise<void> {\n  try {\n    await prisma.translationCache.create({\n      data: {\n        originalText,\n        translatedText,\n        sourceLanguage,\n        targetLanguage\n      }\n    });\n  } catch (error) {\n    console.error('Error caching translation:', error);\n  }\n}\n\nasync function callGeminiTranslation(\n  text: string,\n  sourceLanguage: 'en' | 'si',\n  targetLanguage: 'en' | 'si'\n): Promise<string> {\n  const response = await fetch('/api/translate', {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify({\n      text,\n      sourceLanguage,\n      targetLanguage\n    })\n  });\n\n  if (!response.ok) {\n    throw new Error('Translation API request failed');\n  }\n\n  const data = await response.json();\n  \n  if (!data.success) {\n    throw new Error(data.error || 'Translation failed');\n  }\n\n  return data.translatedText;\n}\n\nexport const LANGUAGE_NAMES = {\n  en: 'English',\n  si: 'සිංහල'\n} as const;\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe,cACpB,IAAY,EACZ,cAA2B,EAC3B,iBAA8B,IAAI;IAElC,0DAA0D;IAC1D,IAAI,mBAAmB,gBAAgB;QACrC,OAAO;IACT;IAEA,oBAAoB;IACpB,MAAM,oBAAoB,MAAM,qBAAqB,MAAM,gBAAgB;IAC3E,IAAI,mBAAmB;QACrB,OAAO;IACT;IAEA,IAAI;QACF,kCAAkC;QAClC,MAAM,iBAAiB,MAAM,sBAAsB,MAAM,gBAAgB;QAEzE,wBAAwB;QACxB,MAAM,iBAAiB,MAAM,gBAAgB,gBAAgB;QAE7D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,4CAA4C;QAC5C,OAAO;IACT;AACF;AAEA,eAAe,qBACb,YAAoB,EACpB,cAA2B,EAC3B,cAA2B;IAE3B,IAAI;QACF,MAAM,SAAS,MAAM,uHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;YACrD,OAAO;gBACL;gBACA;gBACA;YACF;QACF;QAEA,OAAO,QAAQ,kBAAkB;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;IACT;AACF;AAEA,eAAe,iBACb,YAAoB,EACpB,cAAsB,EACtB,cAA2B,EAC3B,cAA2B;IAE3B,IAAI;QACF,MAAM,uHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;YACnC,MAAM;gBACJ;gBACA;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;IAC9C;AACF;AAEA,eAAe,sBACb,IAAY,EACZ,cAA2B,EAC3B,cAA2B;IAE3B,MAAM,WAAW,MAAM,MAAM,kBAAkB;QAC7C,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB;YACA;YACA;QACF;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;QACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;IAChC;IAEA,OAAO,KAAK,cAAc;AAC5B;AAEO,MAAM,iBAAiB;IAC5B,IAAI;IACJ,IAAI;AACN", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/hooks/useLanguage.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useCallback } from 'react';\nimport { LanguageContextType } from '@/types';\nimport { translateText } from '@/utils/translation';\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined);\n\nexport function LanguageProvider({ children }: { children: React.ReactNode }) {\n  const [language, setLanguage] = useState<'en' | 'si'>('en');\n  const [isTranslating, setIsTranslating] = useState(false);\n\n  const translate = useCallback(async (text: string): Promise<string> => {\n    if (language === 'en') {\n      return text; // No translation needed for English\n    }\n\n    setIsTranslating(true);\n    try {\n      const translatedText = await translateText(text, language, 'en');\n      return translatedText;\n    } catch (error) {\n      console.error('Translation error:', error);\n      return text; // Return original text if translation fails\n    } finally {\n      setIsTranslating(false);\n    }\n  }, [language]);\n\n  const value: LanguageContextType = {\n    language,\n    setLanguage,\n    translate,\n    isTranslating\n  };\n\n  return (\n    <LanguageContext.Provider value={value}>\n      {children}\n    </LanguageContext.Provider>\n  );\n}\n\nexport function useLanguage() {\n  const context = useContext(LanguageContext);\n  if (context === undefined) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n}\n\n// Hook for translating text with caching\nexport function useTranslation() {\n  const { language, translate, isTranslating } = useLanguage();\n  const [translationCache, setTranslationCache] = useState<Map<string, string>>(new Map());\n\n  const t = useCallback(async (text: string): Promise<string> => {\n    if (language === 'en') {\n      return text;\n    }\n\n    const cacheKey = `${text}_${language}`;\n    if (translationCache.has(cacheKey)) {\n      return translationCache.get(cacheKey)!;\n    }\n\n    const translatedText = await translate(text);\n    setTranslationCache(prev => new Map(prev).set(cacheKey, translatedText));\n    return translatedText;\n  }, [language, translate, translationCache]);\n\n  return { t, isTranslating, language };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;;;AAJA;;;AAMA,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,SAAS,iBAAiB,EAAE,QAAQ,EAAiC;;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO;YACnC,IAAI,aAAa,MAAM;gBACrB,OAAO,MAAM,oCAAoC;YACnD;YAEA,iBAAiB;YACjB,IAAI;gBACF,MAAM,iBAAiB,MAAM,CAAA,GAAA,8HAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,UAAU;gBAC3D,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,OAAO,MAAM,4CAA4C;YAC3D,SAAU;gBACR,iBAAiB;YACnB;QACF;kDAAG;QAAC;KAAS;IAEb,MAAM,QAA6B;QACjC;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;GAjCgB;KAAA;AAmCT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,IAAI;IAElF,MAAM,IAAI,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yCAAE,OAAO;YAC3B,IAAI,aAAa,MAAM;gBACrB,OAAO;YACT;YAEA,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,UAAU;YACtC,IAAI,iBAAiB,GAAG,CAAC,WAAW;gBAClC,OAAO,iBAAiB,GAAG,CAAC;YAC9B;YAEA,MAAM,iBAAiB,MAAM,UAAU;YACvC;iDAAoB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC,UAAU;;YACxD,OAAO;QACT;wCAAG;QAAC;QAAU;QAAW;KAAiB;IAE1C,OAAO;QAAE;QAAG;QAAe;IAAS;AACtC;IApBgB;;QACiC", "debugId": null}}]}