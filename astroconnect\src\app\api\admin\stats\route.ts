import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';
import { ApiResponse } from '@/types';

interface AdminStats {
  totalUsers: number;
  totalHoroscopes: number;
  totalScans: number;
  activeUsers: number;
  recentUsers: number;
  totalDailyGuides: number;
}

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const admin = getAdminFromRequest(request);
    if (!requireAdminAuth(admin)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 401 });
    }

    // Get total users count
    const totalUsers = await prisma.user.count();

    // Get total horoscopes count
    const totalHoroscopes = await prisma.horoscope.count();

    // Get total daily zodiac readings count
    const totalDailyGuides = await prisma.dailyZodiacReading.count();

    // Get total scans (sum of all scan counts)
    const scanStats = await prisma.qrCodeMapping.aggregate({
      _sum: {
        scanCount: true
      }
    });
    const totalScans = scanStats._sum.scanCount || 0;

    // Get active users (users who have scanned their QR code at least once)
    const activeUsers = await prisma.qrCodeMapping.count({
      where: {
        scanCount: {
          gt: 0
        }
      }
    });

    // Get recent users (users created in the last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: thirtyDaysAgo
        }
      }
    });

    const stats: AdminStats = {
      totalUsers,
      totalHoroscopes,
      totalScans,
      activeUsers,
      recentUsers,
      totalDailyGuides
    };

    return NextResponse.json<ApiResponse<AdminStats>>({
      success: true,
      data: stats,
      message: 'Admin statistics retrieved successfully'
    });

  } catch (error) {
    console.error('Admin stats error:', error);
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
