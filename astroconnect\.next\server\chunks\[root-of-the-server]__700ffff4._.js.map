{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\n\nexport interface AdminTokenPayload {\n  adminId: string;\n  email: string;\n  name: string;\n  role: string;\n}\n\n// Hash password\nexport async function hashPassword(password: string): Promise<string> {\n  const saltRounds = 12;\n  return bcrypt.hash(password, saltRounds);\n}\n\n// Verify password\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword);\n}\n\n// Generate JWT token for admin\nexport function generateAdminToken(payload: AdminTokenPayload): string {\n  return jwt.sign(payload, JWT_SECRET, {\n    expiresIn: '24h',\n    issuer: 'astroconnect-admin'\n  });\n}\n\n// Verify JWT token\nexport function verifyAdminToken(token: string): AdminTokenPayload | null {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET, {\n      issuer: 'astroconnect-admin'\n    }) as AdminTokenPayload;\n    return decoded;\n  } catch (error) {\n    console.error('Token verification failed:', error);\n    return null;\n  }\n}\n\n// Extract admin token from request\nexport function getAdminFromRequest(request: NextRequest): AdminTokenPayload | null {\n  try {\n    // Check Authorization header\n    const authHeader = request.headers.get('authorization');\n    if (authHeader && authHeader.startsWith('Bearer ')) {\n      const token = authHeader.substring(7);\n      return verifyAdminToken(token);\n    }\n\n    // Check cookie\n    const tokenCookie = request.cookies.get('admin-token');\n    if (tokenCookie) {\n      return verifyAdminToken(tokenCookie.value);\n    }\n\n    return null;\n  } catch (error) {\n    console.error('Error extracting admin from request:', error);\n    return null;\n  }\n}\n\n// Validate admin permissions\nexport function requireAdminAuth(admin: AdminTokenPayload | null): boolean {\n  return admin !== null && admin.role === 'admin';\n}\n\n// Generate secure random password for demo\nexport function generateSecurePassword(length: number = 12): string {\n  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';\n  let password = '';\n  for (let i = 0; i < length; i++) {\n    password += charset.charAt(Math.floor(Math.random() * charset.length));\n  }\n  return password;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAUtC,eAAe,aAAa,QAAgB;IACjD,MAAM,aAAa;IACnB,OAAO,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,mBAAmB,OAA0B;IAC3D,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QACnC,WAAW;QACX,QAAQ;IACV;AACF;AAGO,SAAS,iBAAiB,KAAa;IAC5C,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,YAAY;YAC5C,QAAQ;QACV;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,OAAoB;IACtD,IAAI;QACF,6BAA6B;QAC7B,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;YAClD,MAAM,QAAQ,WAAW,SAAS,CAAC;YACnC,OAAO,iBAAiB;QAC1B;QAEA,eAAe;QACf,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QACxC,IAAI,aAAa;YACf,OAAO,iBAAiB,YAAY,KAAK;QAC3C;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;IACT;AACF;AAGO,SAAS,iBAAiB,KAA+B;IAC9D,OAAO,UAAU,QAAQ,MAAM,IAAI,KAAK;AAC1C;AAGO,SAAS,uBAAuB,SAAiB,EAAE;IACxD,MAAM,UAAU;IAChB,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,YAAY,QAAQ,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM;IACtE;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Helper function to disconnect Prisma (useful for serverless)\nexport async function disconnectPrisma() {\n  await prisma.$disconnect();\n}\n\n// Helper function to check database connection\nexport async function checkDatabaseConnection() {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/gemini.ts"], "sourcesContent": ["import { GoogleGenerativeAI } from '@google/generative-ai';\nimport { ZodiacSign } from '@/types';\n\nconst genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');\n\nexport interface DailyZodiacData {\n  zodiacSign: ZodiacSign;\n  date: string;\n  generalReading: string;\n  loveReading: string;\n  careerReading: string;\n  healthReading: string;\n  luckyNumber: number;\n  luckyColor: string;\n  luckyTime: string;\n  luckyGem: string;\n  advice: string;\n  mood: string;\n  compatibility: string;\n}\n\nexport async function generateDailyZodiacReading(zodiacSign: ZodiacSign, date: string): Promise<DailyZodiacData> {\n  try {\n    const model = genAI.getGenerativeModel({ model: 'gemini-pro' });\n\n    const prompt = `\nGenerate a comprehensive daily horoscope reading for ${zodiacSign.toUpperCase()} for ${date}.\n\nPlease provide a detailed, personalized reading that includes:\n\n1. GENERAL READING (2-3 sentences): Overall energy and theme for the day\n2. LOVE & RELATIONSHIPS (2-3 sentences): Romance, partnerships, family relationships\n3. CAREER & MONEY (2-3 sentences): Work, business, financial matters\n4. HEALTH & WELLNESS (2-3 sentences): Physical health, mental wellbeing, energy levels\n5. LUCKY NUMBER: A single number between 1-99\n6. LUCKY COLOR: A specific color name\n7. LUCKY TIME: A time range (e.g., \"10:00 AM - 12:00 PM\")\n8. LUCKY GEM: A gemstone name\n9. DAILY ADVICE (1-2 sentences): Practical guidance for the day\n10. MOOD: One word describing the overall mood (e.g., \"Optimistic\", \"Cautious\", \"Energetic\")\n11. COMPATIBILITY: List 2-3 zodiac signs that are most compatible today\n\nMake the reading positive, insightful, and specific to ${zodiacSign}. Use astrological knowledge and current planetary influences.\n\nFormat your response as JSON with these exact keys:\n{\n  \"generalReading\": \"\",\n  \"loveReading\": \"\",\n  \"careerReading\": \"\",\n  \"healthReading\": \"\",\n  \"luckyNumber\": 0,\n  \"luckyColor\": \"\",\n  \"luckyTime\": \"\",\n  \"luckyGem\": \"\",\n  \"advice\": \"\",\n  \"mood\": \"\",\n  \"compatibility\": \"\"\n}\n`;\n\n    const result = await model.generateContent(prompt);\n    const response = await result.response;\n    const text = response.text();\n\n    // Extract JSON from the response\n    const jsonMatch = text.match(/\\{[\\s\\S]*\\}/);\n    if (!jsonMatch) {\n      throw new Error('Invalid response format from Gemini API');\n    }\n\n    const parsedData = JSON.parse(jsonMatch[0]);\n\n    return {\n      zodiacSign,\n      date,\n      generalReading: parsedData.generalReading || '',\n      loveReading: parsedData.loveReading || '',\n      careerReading: parsedData.careerReading || '',\n      healthReading: parsedData.healthReading || '',\n      luckyNumber: parseInt(parsedData.luckyNumber) || Math.floor(Math.random() * 99) + 1,\n      luckyColor: parsedData.luckyColor || 'Blue',\n      luckyTime: parsedData.luckyTime || '10:00 AM - 12:00 PM',\n      luckyGem: parsedData.luckyGem || 'Amethyst',\n      advice: parsedData.advice || '',\n      mood: parsedData.mood || 'Positive',\n      compatibility: parsedData.compatibility || 'Aries, Leo, Sagittarius'\n    };\n\n  } catch (error) {\n    console.error('Error generating zodiac reading:', error);\n    \n    // Fallback data if API fails\n    return {\n      zodiacSign,\n      date,\n      generalReading: `Today brings positive energy for ${zodiacSign}. The stars align to support your endeavors and bring clarity to your path.`,\n      loveReading: 'Love is in the air today. Open your heart to new possibilities and strengthen existing bonds with understanding and compassion.',\n      careerReading: 'Professional opportunities may present themselves today. Stay focused on your goals and trust your instincts in decision-making.',\n      healthReading: 'Your energy levels are balanced today. Take time for self-care and listen to what your body needs for optimal wellness.',\n      luckyNumber: Math.floor(Math.random() * 99) + 1,\n      luckyColor: 'Blue',\n      luckyTime: '10:00 AM - 12:00 PM',\n      luckyGem: 'Amethyst',\n      advice: 'Trust your intuition today and embrace the opportunities that come your way.',\n      mood: 'Optimistic',\n      compatibility: 'Aries, Leo, Sagittarius'\n    };\n  }\n}\n\nexport async function generateAllDailyReadings(date: string): Promise<DailyZodiacData[]> {\n  const zodiacSigns: ZodiacSign[] = [\n    'aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo',\n    'libra', 'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'\n  ];\n\n  const readings: DailyZodiacData[] = [];\n\n  // Generate readings for all zodiac signs\n  for (const sign of zodiacSigns) {\n    try {\n      const reading = await generateDailyZodiacReading(sign, date);\n      readings.push(reading);\n      \n      // Add a small delay to avoid rate limiting\n      await new Promise(resolve => setTimeout(resolve, 1000));\n    } catch (error) {\n      console.error(`Error generating reading for ${sign}:`, error);\n    }\n  }\n\n  return readings;\n}\n\nexport function getZodiacInfo(sign: ZodiacSign) {\n  const zodiacInfo = {\n    aries: { name: 'Aries', symbol: '♈', element: 'Fire', dates: 'Mar 21 - Apr 19' },\n    taurus: { name: 'Taurus', symbol: '♉', element: 'Earth', dates: 'Apr 20 - May 20' },\n    gemini: { name: 'Gemini', symbol: '♊', element: 'Air', dates: 'May 21 - Jun 20' },\n    cancer: { name: 'Cancer', symbol: '♋', element: 'Water', dates: 'Jun 21 - Jul 22' },\n    leo: { name: 'Leo', symbol: '♌', element: 'Fire', dates: 'Jul 23 - Aug 22' },\n    virgo: { name: 'Virgo', symbol: '♍', element: 'Earth', dates: 'Aug 23 - Sep 22' },\n    libra: { name: 'Libra', symbol: '♎', element: 'Air', dates: 'Sep 23 - Oct 22' },\n    scorpio: { name: 'Scorpio', symbol: '♏', element: 'Water', dates: 'Oct 23 - Nov 21' },\n    sagittarius: { name: 'Sagittarius', symbol: '♐', element: 'Fire', dates: 'Nov 22 - Dec 21' },\n    capricorn: { name: 'Capricorn', symbol: '♑', element: 'Earth', dates: 'Dec 22 - Jan 19' },\n    aquarius: { name: 'Aquarius', symbol: '♒', element: 'Air', dates: 'Jan 20 - Feb 18' },\n    pisces: { name: 'Pisces', symbol: '♓', element: 'Water', dates: 'Feb 19 - Mar 20' }\n  };\n\n  return zodiacInfo[sign];\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAGA,MAAM,QAAQ,IAAI,gKAAA,CAAA,qBAAkB,CAAC,QAAQ,GAAG,CAAC,cAAc,IAAI;AAkB5D,eAAe,2BAA2B,UAAsB,EAAE,IAAY;IACnF,IAAI;QACF,MAAM,QAAQ,MAAM,kBAAkB,CAAC;YAAE,OAAO;QAAa;QAE7D,MAAM,SAAS,CAAC;qDACiC,EAAE,WAAW,WAAW,GAAG,KAAK,EAAE,KAAK;;;;;;;;;;;;;;;;uDAgBrC,EAAE,WAAW;;;;;;;;;;;;;;;;AAgBpE,CAAC;QAEG,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;QAC3C,MAAM,WAAW,MAAM,OAAO,QAAQ;QACtC,MAAM,OAAO,SAAS,IAAI;QAE1B,iCAAiC;QACjC,MAAM,YAAY,KAAK,KAAK,CAAC;QAC7B,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,aAAa,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;QAE1C,OAAO;YACL;YACA;YACA,gBAAgB,WAAW,cAAc,IAAI;YAC7C,aAAa,WAAW,WAAW,IAAI;YACvC,eAAe,WAAW,aAAa,IAAI;YAC3C,eAAe,WAAW,aAAa,IAAI;YAC3C,aAAa,SAAS,WAAW,WAAW,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;YAClF,YAAY,WAAW,UAAU,IAAI;YACrC,WAAW,WAAW,SAAS,IAAI;YACnC,UAAU,WAAW,QAAQ,IAAI;YACjC,QAAQ,WAAW,MAAM,IAAI;YAC7B,MAAM,WAAW,IAAI,IAAI;YACzB,eAAe,WAAW,aAAa,IAAI;QAC7C;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAElD,6BAA6B;QAC7B,OAAO;YACL;YACA;YACA,gBAAgB,CAAC,iCAAiC,EAAE,WAAW,2EAA2E,CAAC;YAC3I,aAAa;YACb,eAAe;YACf,eAAe;YACf,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;YAC9C,YAAY;YACZ,WAAW;YACX,UAAU;YACV,QAAQ;YACR,MAAM;YACN,eAAe;QACjB;IACF;AACF;AAEO,eAAe,yBAAyB,IAAY;IACzD,MAAM,cAA4B;QAChC;QAAS;QAAU;QAAU;QAAU;QAAO;QAC9C;QAAS;QAAW;QAAe;QAAa;QAAY;KAC7D;IAED,MAAM,WAA8B,EAAE;IAEtC,yCAAyC;IACzC,KAAK,MAAM,QAAQ,YAAa;QAC9B,IAAI;YACF,MAAM,UAAU,MAAM,2BAA2B,MAAM;YACvD,SAAS,IAAI,CAAC;YAEd,2CAA2C;YAC3C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC,EAAE;QACzD;IACF;IAEA,OAAO;AACT;AAEO,SAAS,cAAc,IAAgB;IAC5C,MAAM,aAAa;QACjB,OAAO;YAAE,MAAM;YAAS,QAAQ;YAAK,SAAS;YAAQ,OAAO;QAAkB;QAC/E,QAAQ;YAAE,MAAM;YAAU,QAAQ;YAAK,SAAS;YAAS,OAAO;QAAkB;QAClF,QAAQ;YAAE,MAAM;YAAU,QAAQ;YAAK,SAAS;YAAO,OAAO;QAAkB;QAChF,QAAQ;YAAE,MAAM;YAAU,QAAQ;YAAK,SAAS;YAAS,OAAO;QAAkB;QAClF,KAAK;YAAE,MAAM;YAAO,QAAQ;YAAK,SAAS;YAAQ,OAAO;QAAkB;QAC3E,OAAO;YAAE,MAAM;YAAS,QAAQ;YAAK,SAAS;YAAS,OAAO;QAAkB;QAChF,OAAO;YAAE,MAAM;YAAS,QAAQ;YAAK,SAAS;YAAO,OAAO;QAAkB;QAC9E,SAAS;YAAE,MAAM;YAAW,QAAQ;YAAK,SAAS;YAAS,OAAO;QAAkB;QACpF,aAAa;YAAE,MAAM;YAAe,QAAQ;YAAK,SAAS;YAAQ,OAAO;QAAkB;QAC3F,WAAW;YAAE,MAAM;YAAa,QAAQ;YAAK,SAAS;YAAS,OAAO;QAAkB;QACxF,UAAU;YAAE,MAAM;YAAY,QAAQ;YAAK,SAAS;YAAO,OAAO;QAAkB;QACpF,QAAQ;YAAE,MAAM;YAAU,QAAQ;YAAK,SAAS;YAAS,OAAO;QAAkB;IACpF;IAEA,OAAO,UAAU,CAAC,KAAK;AACzB", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/scheduler.ts"], "sourcesContent": ["import { prisma } from '@/lib/prisma';\nimport { generateAllDailyReadings } from '@/lib/gemini';\n\nclass DailyReadingsScheduler {\n  private intervalId: NodeJS.Timeout | null = null;\n  private isRunning = false;\n  private lastGeneratedDate: string | null = null;\n\n  constructor() {\n    this.start();\n  }\n\n  start() {\n    if (this.isRunning) {\n      console.log('📅 Daily readings scheduler is already running');\n      return;\n    }\n\n    console.log('🚀 Starting daily readings scheduler...');\n    this.isRunning = true;\n\n    // Check immediately on startup\n    this.checkAndGenerateReadings();\n\n    // Then check every hour\n    this.intervalId = setInterval(() => {\n      this.checkAndGenerateReadings();\n    }, 60 * 60 * 1000); // Check every hour\n\n    console.log('✅ Daily readings scheduler started - checking every hour');\n  }\n\n  stop() {\n    if (this.intervalId) {\n      clearInterval(this.intervalId);\n      this.intervalId = null;\n    }\n    this.isRunning = false;\n    console.log('🛑 Daily readings scheduler stopped');\n  }\n\n  private async checkAndGenerateReadings() {\n    try {\n      const today = new Date().toISOString().split('T')[0];\n      const currentHour = new Date().getHours();\n\n      // Only generate readings between 12 AM and 6 AM (when most users are asleep)\n      // and only once per day\n      if (currentHour >= 6 && this.lastGeneratedDate === today) {\n        return;\n      }\n\n      console.log(`🕐 Checking daily readings for ${today} at ${new Date().toLocaleTimeString()}`);\n\n      // Check if readings already exist for today\n      const existingReadings = await prisma.dailyZodiacReading.findMany({\n        where: {\n          date: new Date(today),\n          language: 'en'\n        }\n      });\n\n      if (existingReadings.length === 12) {\n        console.log(`✅ Daily readings already exist for ${today} (${existingReadings.length} readings)`);\n        this.lastGeneratedDate = today;\n        return;\n      }\n\n      console.log(`🤖 Generating daily zodiac readings for ${today}...`);\n      console.log(`📊 Found ${existingReadings.length}/12 existing readings`);\n\n      // Generate new readings using Gemini API\n      const newReadings = await generateAllDailyReadings(today);\n      console.log(`🎯 Generated ${newReadings.length} new readings from Gemini API`);\n\n      // Save readings to database\n      const savedReadings = [];\n      for (const reading of newReadings) {\n        try {\n          const saved = await prisma.dailyZodiacReading.upsert({\n            where: {\n              zodiacSign_date_language: {\n                zodiacSign: reading.zodiacSign,\n                date: new Date(today),\n                language: 'en'\n              }\n            },\n            update: {\n              generalReading: reading.generalReading,\n              loveReading: reading.loveReading,\n              careerReading: reading.careerReading,\n              healthReading: reading.healthReading,\n              luckyNumber: reading.luckyNumber,\n              luckyColor: reading.luckyColor,\n              luckyTime: reading.luckyTime,\n              luckyGem: reading.luckyGem,\n              advice: reading.advice,\n              mood: reading.mood,\n              compatibility: reading.compatibility,\n              updatedAt: new Date()\n            },\n            create: {\n              zodiacSign: reading.zodiacSign,\n              date: new Date(today),\n              generalReading: reading.generalReading,\n              loveReading: reading.loveReading,\n              careerReading: reading.careerReading,\n              healthReading: reading.healthReading,\n              luckyNumber: reading.luckyNumber,\n              luckyColor: reading.luckyColor,\n              luckyTime: reading.luckyTime,\n              luckyGem: reading.luckyGem,\n              advice: reading.advice,\n              mood: reading.mood,\n              compatibility: reading.compatibility,\n              language: 'en'\n            }\n          });\n          savedReadings.push(saved);\n          console.log(`✅ Saved reading for ${reading.zodiacSign}`);\n        } catch (error) {\n          console.error(`❌ Error saving reading for ${reading.zodiacSign}:`, error);\n        }\n      }\n\n      this.lastGeneratedDate = today;\n      console.log(`🎉 Successfully generated and saved ${savedReadings.length} daily readings for ${today}`);\n\n      // Clean up old readings (keep only last 30 days)\n      await this.cleanupOldReadings();\n\n    } catch (error) {\n      console.error('❌ Error in daily readings scheduler:', error);\n    }\n  }\n\n  private async cleanupOldReadings() {\n    try {\n      const thirtyDaysAgo = new Date();\n      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n\n      const deletedCount = await prisma.dailyZodiacReading.deleteMany({\n        where: {\n          date: {\n            lt: thirtyDaysAgo\n          }\n        }\n      });\n\n      if (deletedCount.count > 0) {\n        console.log(`🧹 Cleaned up ${deletedCount.count} old daily readings`);\n      }\n    } catch (error) {\n      console.error('❌ Error cleaning up old readings:', error);\n    }\n  }\n\n  // Method to manually trigger reading generation (for testing)\n  async generateNow(date?: string) {\n    const targetDate = date || new Date().toISOString().split('T')[0];\n    console.log(`🔄 Manually generating readings for ${targetDate}...`);\n    \n    try {\n      const readings = await generateAllDailyReadings(targetDate);\n      \n      const savedReadings = [];\n      for (const reading of readings) {\n        const saved = await prisma.dailyZodiacReading.upsert({\n          where: {\n            zodiacSign_date_language: {\n              zodiacSign: reading.zodiacSign,\n              date: new Date(targetDate),\n              language: 'en'\n            }\n          },\n          update: {\n            generalReading: reading.generalReading,\n            loveReading: reading.loveReading,\n            careerReading: reading.careerReading,\n            healthReading: reading.healthReading,\n            luckyNumber: reading.luckyNumber,\n            luckyColor: reading.luckyColor,\n            luckyTime: reading.luckyTime,\n            luckyGem: reading.luckyGem,\n            advice: reading.advice,\n            mood: reading.mood,\n            compatibility: reading.compatibility,\n            updatedAt: new Date()\n          },\n          create: {\n            zodiacSign: reading.zodiacSign,\n            date: new Date(targetDate),\n            generalReading: reading.generalReading,\n            loveReading: reading.loveReading,\n            careerReading: reading.careerReading,\n            healthReading: reading.healthReading,\n            luckyNumber: reading.luckyNumber,\n            luckyColor: reading.luckyColor,\n            luckyTime: reading.luckyTime,\n            luckyGem: reading.luckyGem,\n            advice: reading.advice,\n            mood: reading.mood,\n            compatibility: reading.compatibility,\n            language: 'en'\n          }\n        });\n        savedReadings.push(saved);\n      }\n\n      console.log(`✅ Manually generated ${savedReadings.length} readings for ${targetDate}`);\n      return savedReadings;\n    } catch (error) {\n      console.error('❌ Error in manual generation:', error);\n      throw error;\n    }\n  }\n\n  getStatus() {\n    return {\n      isRunning: this.isRunning,\n      lastGeneratedDate: this.lastGeneratedDate,\n      nextCheck: this.intervalId ? 'Every hour' : 'Not scheduled'\n    };\n  }\n}\n\n// Create singleton instance\nexport const dailyReadingsScheduler = new DailyReadingsScheduler();\n\n// Graceful shutdown\nprocess.on('SIGTERM', () => {\n  console.log('🔄 Received SIGTERM, stopping scheduler...');\n  dailyReadingsScheduler.stop();\n});\n\nprocess.on('SIGINT', () => {\n  console.log('🔄 Received SIGINT, stopping scheduler...');\n  dailyReadingsScheduler.stop();\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM;IACI,aAAoC,KAAK;IACzC,YAAY,MAAM;IAClB,oBAAmC,KAAK;IAEhD,aAAc;QACZ,IAAI,CAAC,KAAK;IACZ;IAEA,QAAQ;QACN,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,SAAS,GAAG;QAEjB,+BAA+B;QAC/B,IAAI,CAAC,wBAAwB;QAE7B,wBAAwB;QACxB,IAAI,CAAC,UAAU,GAAG,YAAY;YAC5B,IAAI,CAAC,wBAAwB;QAC/B,GAAG,KAAK,KAAK,OAAO,mBAAmB;QAEvC,QAAQ,GAAG,CAAC;IACd;IAEA,OAAO;QACL,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,cAAc,IAAI,CAAC,UAAU;YAC7B,IAAI,CAAC,UAAU,GAAG;QACpB;QACA,IAAI,CAAC,SAAS,GAAG;QACjB,QAAQ,GAAG,CAAC;IACd;IAEA,MAAc,2BAA2B;QACvC,IAAI;YACF,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACpD,MAAM,cAAc,IAAI,OAAO,QAAQ;YAEvC,6EAA6E;YAC7E,wBAAwB;YACxB,IAAI,eAAe,KAAK,IAAI,CAAC,iBAAiB,KAAK,OAAO;gBACxD;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,MAAM,IAAI,EAAE,IAAI,OAAO,kBAAkB,IAAI;YAE3F,4CAA4C;YAC5C,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;gBAChE,OAAO;oBACL,MAAM,IAAI,KAAK;oBACf,UAAU;gBACZ;YACF;YAEA,IAAI,iBAAiB,MAAM,KAAK,IAAI;gBAClC,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,MAAM,EAAE,EAAE,iBAAiB,MAAM,CAAC,UAAU,CAAC;gBAC/F,IAAI,CAAC,iBAAiB,GAAG;gBACzB;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,MAAM,GAAG,CAAC;YACjE,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,iBAAiB,MAAM,CAAC,qBAAqB,CAAC;YAEtE,yCAAyC;YACzC,MAAM,cAAc,MAAM,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD,EAAE;YACnD,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,MAAM,CAAC,6BAA6B,CAAC;YAE7E,4BAA4B;YAC5B,MAAM,gBAAgB,EAAE;YACxB,KAAK,MAAM,WAAW,YAAa;gBACjC,IAAI;oBACF,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;wBACnD,OAAO;4BACL,0BAA0B;gCACxB,YAAY,QAAQ,UAAU;gCAC9B,MAAM,IAAI,KAAK;gCACf,UAAU;4BACZ;wBACF;wBACA,QAAQ;4BACN,gBAAgB,QAAQ,cAAc;4BACtC,aAAa,QAAQ,WAAW;4BAChC,eAAe,QAAQ,aAAa;4BACpC,eAAe,QAAQ,aAAa;4BACpC,aAAa,QAAQ,WAAW;4BAChC,YAAY,QAAQ,UAAU;4BAC9B,WAAW,QAAQ,SAAS;4BAC5B,UAAU,QAAQ,QAAQ;4BAC1B,QAAQ,QAAQ,MAAM;4BACtB,MAAM,QAAQ,IAAI;4BAClB,eAAe,QAAQ,aAAa;4BACpC,WAAW,IAAI;wBACjB;wBACA,QAAQ;4BACN,YAAY,QAAQ,UAAU;4BAC9B,MAAM,IAAI,KAAK;4BACf,gBAAgB,QAAQ,cAAc;4BACtC,aAAa,QAAQ,WAAW;4BAChC,eAAe,QAAQ,aAAa;4BACpC,eAAe,QAAQ,aAAa;4BACpC,aAAa,QAAQ,WAAW;4BAChC,YAAY,QAAQ,UAAU;4BAC9B,WAAW,QAAQ,SAAS;4BAC5B,UAAU,QAAQ,QAAQ;4BAC1B,QAAQ,QAAQ,MAAM;4BACtB,MAAM,QAAQ,IAAI;4BAClB,eAAe,QAAQ,aAAa;4BACpC,UAAU;wBACZ;oBACF;oBACA,cAAc,IAAI,CAAC;oBACnB,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ,UAAU,EAAE;gBACzD,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,QAAQ,UAAU,CAAC,CAAC,CAAC,EAAE;gBACrE;YACF;YAEA,IAAI,CAAC,iBAAiB,GAAG;YACzB,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,cAAc,MAAM,CAAC,oBAAoB,EAAE,OAAO;YAErG,iDAAiD;YACjD,MAAM,IAAI,CAAC,kBAAkB;QAE/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD;IACF;IAEA,MAAc,qBAAqB;QACjC,IAAI;YACF,MAAM,gBAAgB,IAAI;YAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK;YAEhD,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC;gBAC9D,OAAO;oBACL,MAAM;wBACJ,IAAI;oBACN;gBACF;YACF;YAEA,IAAI,aAAa,KAAK,GAAG,GAAG;gBAC1B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,aAAa,KAAK,CAAC,mBAAmB,CAAC;YACtE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA,8DAA8D;IAC9D,MAAM,YAAY,IAAa,EAAE;QAC/B,MAAM,aAAa,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACjE,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,WAAW,GAAG,CAAC;QAElE,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD,EAAE;YAEhD,MAAM,gBAAgB,EAAE;YACxB,KAAK,MAAM,WAAW,SAAU;gBAC9B,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;oBACnD,OAAO;wBACL,0BAA0B;4BACxB,YAAY,QAAQ,UAAU;4BAC9B,MAAM,IAAI,KAAK;4BACf,UAAU;wBACZ;oBACF;oBACA,QAAQ;wBACN,gBAAgB,QAAQ,cAAc;wBACtC,aAAa,QAAQ,WAAW;wBAChC,eAAe,QAAQ,aAAa;wBACpC,eAAe,QAAQ,aAAa;wBACpC,aAAa,QAAQ,WAAW;wBAChC,YAAY,QAAQ,UAAU;wBAC9B,WAAW,QAAQ,SAAS;wBAC5B,UAAU,QAAQ,QAAQ;wBAC1B,QAAQ,QAAQ,MAAM;wBACtB,MAAM,QAAQ,IAAI;wBAClB,eAAe,QAAQ,aAAa;wBACpC,WAAW,IAAI;oBACjB;oBACA,QAAQ;wBACN,YAAY,QAAQ,UAAU;wBAC9B,MAAM,IAAI,KAAK;wBACf,gBAAgB,QAAQ,cAAc;wBACtC,aAAa,QAAQ,WAAW;wBAChC,eAAe,QAAQ,aAAa;wBACpC,eAAe,QAAQ,aAAa;wBACpC,aAAa,QAAQ,WAAW;wBAChC,YAAY,QAAQ,UAAU;wBAC9B,WAAW,QAAQ,SAAS;wBAC5B,UAAU,QAAQ,QAAQ;wBAC1B,QAAQ,QAAQ,MAAM;wBACtB,MAAM,QAAQ,IAAI;wBAClB,eAAe,QAAQ,aAAa;wBACpC,UAAU;oBACZ;gBACF;gBACA,cAAc,IAAI,CAAC;YACrB;YAEA,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,cAAc,MAAM,CAAC,cAAc,EAAE,YAAY;YACrF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,YAAY;QACV,OAAO;YACL,WAAW,IAAI,CAAC,SAAS;YACzB,mBAAmB,IAAI,CAAC,iBAAiB;YACzC,WAAW,IAAI,CAAC,UAAU,GAAG,eAAe;QAC9C;IACF;AACF;AAGO,MAAM,yBAAyB,IAAI;AAE1C,oBAAoB;AACpB,QAAQ,EAAE,CAAC,WAAW;IACpB,QAAQ,GAAG,CAAC;IACZ,uBAAuB,IAAI;AAC7B;AAEA,QAAQ,EAAE,CAAC,UAAU;IACnB,QAAQ,GAAG,CAAC;IACZ,uBAAuB,IAAI;AAC7B", "debugId": null}}, {"offset": {"line": 624, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/admin/scheduler/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getAdminFromRequest, requireAdminAuth } from '@/lib/auth';\nimport { dailyReadingsScheduler } from '@/lib/scheduler';\nimport { ApiResponse } from '@/types';\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Check admin authentication\n    const admin = getAdminFromRequest(request);\n    if (!requireAdminAuth(admin)) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Unauthorized - Admin access required'\n      }, { status: 401 });\n    }\n\n    const status = dailyReadingsScheduler.getStatus();\n\n    return NextResponse.json<ApiResponse<any>>({\n      success: true,\n      data: {\n        scheduler: status,\n        serverTime: new Date().toISOString(),\n        localTime: new Date().toLocaleString()\n      },\n      message: 'Scheduler status retrieved successfully'\n    });\n\n  } catch (error) {\n    console.error('❌ Error getting scheduler status:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Failed to get scheduler status'\n    }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Check admin authentication\n    const admin = getAdminFromRequest(request);\n    if (!requireAdminAuth(admin)) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'Unauthorized - Admin access required'\n      }, { status: 401 });\n    }\n\n    const { action, date } = await request.json();\n\n    if (action === 'generate') {\n      console.log('🔄 Admin triggered manual reading generation');\n      \n      const targetDate = date || new Date().toISOString().split('T')[0];\n      const readings = await dailyReadingsScheduler.generateNow(targetDate);\n\n      return NextResponse.json<ApiResponse<any>>({\n        success: true,\n        data: {\n          readings,\n          count: readings.length,\n          date: targetDate\n        },\n        message: `Successfully generated ${readings.length} daily readings for ${targetDate}`\n      });\n    }\n\n    if (action === 'status') {\n      const status = dailyReadingsScheduler.getStatus();\n      \n      return NextResponse.json<ApiResponse<any>>({\n        success: true,\n        data: { scheduler: status },\n        message: 'Scheduler status retrieved successfully'\n      });\n    }\n\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Invalid action. Use \"generate\" or \"status\"'\n    }, { status: 400 });\n\n  } catch (error) {\n    console.error('❌ Error in scheduler action:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Failed to execute scheduler action'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,6BAA6B;QAC7B,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;QAClC,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,SAAS,yHAAA,CAAA,yBAAsB,CAAC,SAAS;QAE/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAmB;YACzC,SAAS;YACT,MAAM;gBACJ,WAAW;gBACX,YAAY,IAAI,OAAO,WAAW;gBAClC,WAAW,IAAI,OAAO,cAAc;YACtC;YACA,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,6BAA6B;QAC7B,MAAM,QAAQ,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;QAClC,IAAI,CAAC,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE3C,IAAI,WAAW,YAAY;YACzB,QAAQ,GAAG,CAAC;YAEZ,MAAM,aAAa,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACjE,MAAM,WAAW,MAAM,yHAAA,CAAA,yBAAsB,CAAC,WAAW,CAAC;YAE1D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAmB;gBACzC,SAAS;gBACT,MAAM;oBACJ;oBACA,OAAO,SAAS,MAAM;oBACtB,MAAM;gBACR;gBACA,SAAS,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,oBAAoB,EAAE,YAAY;YACvF;QACF;QAEA,IAAI,WAAW,UAAU;YACvB,MAAM,SAAS,yHAAA,CAAA,yBAAsB,CAAC,SAAS;YAE/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAmB;gBACzC,SAAS;gBACT,MAAM;oBAAE,WAAW;gBAAO;gBAC1B,SAAS;YACX;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}