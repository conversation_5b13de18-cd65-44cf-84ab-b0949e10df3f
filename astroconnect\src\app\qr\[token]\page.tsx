'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { User } from '@/types';
import { Loader2, AlertCircle } from 'lucide-react';

interface QRAuthPageProps {
  params: Promise<{
    token: string;
  }>;
}

export default function QRAuthPage({ params }: QRAuthPageProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();

  useEffect(() => {
    const getToken = async () => {
      const resolvedParams = await params;
      setToken(resolvedParams.token);
    };
    getToken();
  }, [params]);

  useEffect(() => {
    if (!token) return;

    const authenticateWithQR = async () => {
      try {
        const response = await fetch('/api/auth/qr', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token }),
        });

        const data = await response.json();

        if (data.success && data.data) {
          setUser(data.data);
          
          // Store user data in localStorage for the session
          localStorage.setItem('astroconnect_user', JSON.stringify(data.data));
          
          // Redirect to dashboard after a brief delay
          setTimeout(() => {
            router.push('/dashboard');
          }, 2000);
        } else {
          setError(data.error || 'Invalid QR code');
        }
      } catch (err) {
        console.error('QR authentication error:', err);
        setError('Failed to authenticate. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    if (token) {
      authenticateWithQR();
    } else {
      setError('No QR token provided');
      setLoading(false);
    }
  }, [token, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-12 h-12 text-white animate-spin mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-2">Authenticating...</h2>
          <p className="text-gray-300">Please wait while we verify your QR code</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-4">Authentication Failed</h2>
          <p className="text-gray-300 mb-6">{error}</p>
          <button
            onClick={() => router.push('/')}
            className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Return to Home
          </button>
        </div>
      </div>
    );
  }

  if (user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">Welcome, {user.name}!</h2>
          <p className="text-gray-300 mb-4">Authentication successful</p>
          <p className="text-sm text-gray-400">Redirecting to your dashboard...</p>
        </div>
      </div>
    );
  }

  return null;
}
