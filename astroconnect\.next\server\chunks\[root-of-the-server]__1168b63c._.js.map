{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n\n// Helper function to disconnect Prisma (useful for serverless)\nexport async function disconnectPrisma() {\n  await prisma.$disconnect();\n}\n\n// Helper function to check database connection\nexport async function checkDatabaseConnection() {\n  try {\n    await prisma.$queryRaw`SELECT 1`;\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG;AAG7D,eAAe;IACpB,MAAM,OAAO,WAAW;AAC1B;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,OAAO,SAAS,CAAC,QAAQ,CAAC;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/api/dashboard/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { prisma } from '@/lib/prisma';\nimport { ApiResponse, DashboardData, User, Horoscope, DailyGuide, LanguageCode } from '@/types';\n\nexport async function GET(request: NextRequest) {\n  const { searchParams } = new URL(request.url);\n  const userId = searchParams.get('userId');\n  const language = searchParams.get('language') as 'en' | 'si' || 'en';\n\n  if (!userId) {\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'User ID is required'\n    }, { status: 400 });\n  }\n\n  try {\n    // Get user data\n    const user = await prisma.user.findUnique({\n      where: { id: userId }\n    });\n\n    if (!user) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'User not found'\n      }, { status: 404 });\n    }\n\n    const today = new Date();\n    const zodiacSign = user.zodiacSign;\n    const userLanguage = (language as LanguageCode) || user.languagePreference || 'en';\n\n    // Get recent horoscopes for user's zodiac sign (last 7 days)\n    const sevenDaysAgo = new Date();\n    sevenDaysAgo.setDate(today.getDate() - 7);\n\n    const horoscopes = await prisma.horoscope.findMany({\n      where: {\n        zodiacSign: zodiacSign,\n        date: {\n          gte: sevenDaysAgo,\n          lte: today\n        },\n        language: userLanguage\n      },\n      orderBy: {\n        date: 'desc'\n      }\n    });\n\n    // Get daily guide for user's zodiac sign (today or most recent)\n    const dailyGuide = await prisma.dailyGuide.findFirst({\n      where: {\n        zodiacSign: zodiacSign,\n        date: {\n          gte: sevenDaysAgo,\n          lte: today\n        },\n        language: userLanguage\n      },\n      orderBy: {\n        date: 'desc'\n      }\n    });\n\n    // Organize horoscopes by type (get the most recent of each type)\n    const todayHoroscope = horoscopes?.find(h => h.type === 'daily') || null;\n    const weeklyHoroscope = horoscopes?.find(h => h.type === 'weekly') || null;\n    const monthlyHoroscope = horoscopes?.find(h => h.type === 'monthly') || null;\n\n    const dashboardData: DashboardData = {\n      user: user as User,\n      todayHoroscope: todayHoroscope as Horoscope,\n      weeklyHoroscope: weeklyHoroscope as Horoscope,\n      monthlyHoroscope: monthlyHoroscope as Horoscope,\n      dailyGuide: dailyGuide as DailyGuide\n    };\n\n    return NextResponse.json<ApiResponse<DashboardData>>({\n      success: true,\n      data: dashboardData,\n      message: 'Dashboard data retrieved successfully'\n    });\n\n  } catch (error) {\n    console.error('Dashboard API error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { userId, language } = await request.json();\n\n    if (!userId) {\n      return NextResponse.json<ApiResponse<null>>({\n        success: false,\n        error: 'User ID is required'\n      }, { status: 400 });\n    }\n\n    // Update user's language preference\n    await prisma.user.update({\n      where: { id: userId },\n      data: { languagePreference: language as LanguageCode }\n    });\n\n    return NextResponse.json<ApiResponse<{ updated: boolean }>>({\n      success: true,\n      data: { updated: true },\n      message: 'Language preference updated successfully'\n    });\n\n  } catch (error) {\n    console.error('Language update error:', error);\n    return NextResponse.json<ApiResponse<null>>({\n      success: false,\n      error: 'Internal server error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;IAChC,MAAM,WAAW,aAAa,GAAG,CAAC,eAA8B;IAEhE,IAAI,CAAC,QAAQ;QACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;IAEA,IAAI;QACF,gBAAgB;QAChB,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI;YAAO;QACtB;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,QAAQ,IAAI;QAClB,MAAM,aAAa,KAAK,UAAU;QAClC,MAAM,eAAe,AAAC,YAA6B,KAAK,kBAAkB,IAAI;QAE9E,6DAA6D;QAC7D,MAAM,eAAe,IAAI;QACzB,aAAa,OAAO,CAAC,MAAM,OAAO,KAAK;QAEvC,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACjD,OAAO;gBACL,YAAY;gBACZ,MAAM;oBACJ,KAAK;oBACL,KAAK;gBACP;gBACA,UAAU;YACZ;YACA,SAAS;gBACP,MAAM;YACR;QACF;QAEA,gEAAgE;QAChE,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,SAAS,CAAC;YACnD,OAAO;gBACL,YAAY;gBACZ,MAAM;oBACJ,KAAK;oBACL,KAAK;gBACP;gBACA,UAAU;YACZ;YACA,SAAS;gBACP,MAAM;YACR;QACF;QAEA,iEAAiE;QACjE,MAAM,iBAAiB,YAAY,KAAK,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY;QACpE,MAAM,kBAAkB,YAAY,KAAK,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa;QACtE,MAAM,mBAAmB,YAAY,KAAK,CAAA,IAAK,EAAE,IAAI,KAAK,cAAc;QAExE,MAAM,gBAA+B;YACnC,MAAM;YACN,gBAAgB;YAChB,iBAAiB;YACjB,kBAAkB;YAClB,YAAY;QACd;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAA6B;YACnD,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE/C,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;gBAC1C,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,oCAAoC;QACpC,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACvB,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBAAE,oBAAoB;YAAyB;QACvD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoC;YAC1D,SAAS;YACT,MAAM;gBAAE,SAAS;YAAK;YACtB,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAoB;YAC1C,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}