/*
  Warnings:

  - You are about to drop the `daily_guides` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE "daily_guides";

-- CreateTable
CREATE TABLE "daily_zodiac_readings" (
    "id" TEXT NOT NULL,
    "zodiac_sign" "ZodiacSign" NOT NULL,
    "date" DATE NOT NULL,
    "general_reading" TEXT NOT NULL,
    "love_reading" TEXT NOT NULL,
    "career_reading" TEXT NOT NULL,
    "health_reading" TEXT NOT NULL,
    "lucky_number" INTEGER NOT NULL,
    "lucky_color" TEXT NOT NULL,
    "lucky_time" TEXT NOT NULL,
    "lucky_gem" TEXT NOT NULL,
    "advice" TEXT NOT NULL,
    "mood" TEXT NOT NULL,
    "compatibility" TEXT NOT NULL,
    "language" "LanguageCode" NOT NULL DEFAULT 'en',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "daily_zodiac_readings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "personal_horoscopes" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "language" "LanguageCode" NOT NULL DEFAULT 'en',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "personal_horoscopes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "daily_zodiac_readings_zodiac_sign_date_language_key" ON "daily_zodiac_readings"("zodiac_sign", "date", "language");

-- AddForeignKey
ALTER TABLE "personal_horoscopes" ADD CONSTRAINT "personal_horoscopes_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
