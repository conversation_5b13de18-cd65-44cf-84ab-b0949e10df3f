'use client';

import { useState } from 'react';
import QRScanner from '@/components/QRScanner';

export default function TestQRPage() {
  const [scannedResult, setScannedResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleScanSuccess = (result: string) => {
    console.log('✅ QR Scan Success:', result);
    setScannedResult(result);
    setError(null);
  };

  const handleScanError = (error: string) => {
    console.error('❌ QR Scan Error:', error);
    setError(error);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-white text-center mb-8">
          QR Scanner Test
        </h1>

        {/* Results Display */}
        {scannedResult && (
          <div className="mb-6 p-4 bg-green-500/20 border border-green-500/50 rounded-lg">
            <h3 className="text-green-300 font-semibold mb-2">✅ Scan Successful!</h3>
            <p className="text-green-100 break-all">{scannedResult}</p>
            <button
              onClick={() => setScannedResult(null)}
              className="mt-2 px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded transition-colors"
            >
              Clear Result
            </button>
          </div>
        )}

        {error && (
          <div className="mb-6 p-4 bg-red-500/20 border border-red-500/50 rounded-lg">
            <h3 className="text-red-300 font-semibold mb-2">❌ Error</h3>
            <p className="text-red-100">{error}</p>
            <button
              onClick={() => setError(null)}
              className="mt-2 px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors"
            >
              Clear Error
            </button>
          </div>
        )}

        {/* QR Scanner Component */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
          <QRScanner
            onScanSuccess={handleScanSuccess}
            onScanError={handleScanError}
            width={400}
            height={400}
          />
        </div>

        {/* Instructions */}
        <div className="mt-8 p-4 bg-blue-500/20 border border-blue-500/50 rounded-lg">
          <h3 className="text-blue-300 font-semibold mb-2">📋 Test Instructions</h3>
          <ul className="text-blue-100 text-sm space-y-1">
            <li>• Click "Use Camera" to test camera scanning</li>
            <li>• Click "Upload Image" to test file scanning</li>
            <li>• Generate a test QR code at: qr-code-generator.com</li>
            <li>• Try scanning text like "Hello World" or a URL</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
