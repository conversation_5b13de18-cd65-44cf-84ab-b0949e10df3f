{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/QRScannerModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef } from 'react';\nimport { Camera, Upload, X } from 'lucide-react';\nimport { useRouter } from 'next/navigation';\n\ninterface QRScannerModalProps {\n  onScanSuccess: (data: string) => void;\n  onScanError?: (error: string) => void;\n  onClose: () => void;\n}\n\nexport default function QRScannerModal({ onScanSuccess, onScanError, onClose }: QRScannerModalProps) {\n  const router = useRouter();\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleCameraClick = () => {\n    // Store the callback in sessionStorage so the scan page can use it\n    sessionStorage.setItem('qr-scan-callback', 'true');\n    router.push('/scan');\n  };\n\n  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    try {\n      // Import Html5Qrcode dynamically to avoid SSR issues\n      const { Html5Qrcode } = await import('html5-qrcode');\n      \n      // Create a temporary element for file scanning\n      const tempDiv = document.createElement('div');\n      tempDiv.id = 'temp-qr-reader';\n      tempDiv.style.display = 'none';\n      document.body.appendChild(tempDiv);\n      \n      const html5QrCode = new Html5Qrcode('temp-qr-reader');\n      const result = await html5QrCode.scanFile(file, true);\n      \n      // Clean up\n      document.body.removeChild(tempDiv);\n      \n      console.log('✅ QR Code from file:', result);\n      onScanSuccess(result);\n      onClose();\n    } catch (error: any) {\n      console.error('❌ File scan failed:', error);\n      onScanError?.('Could not read QR code from image. Please try a clearer image.');\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-gray-800 rounded-lg p-6 w-full max-w-md\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-xl font-bold text-white\">Scan Your QR Code</h2>\n          <button\n            onClick={onClose}\n            className=\"p-2 hover:bg-gray-700 rounded-lg transition-colors\"\n          >\n            <X className=\"text-gray-400\" size={20} />\n          </button>\n        </div>\n\n        {/* Scan Options */}\n        <div className=\"space-y-4\">\n          {/* Camera Option */}\n          <button\n            onClick={handleCameraClick}\n            className=\"w-full p-4 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center justify-center gap-3\"\n          >\n            <Camera size={24} />\n            <span className=\"font-medium\">Use Camera</span>\n          </button>\n\n          {/* File Upload Option */}\n          <div>\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={handleFileUpload}\n              className=\"hidden\"\n            />\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              className=\"w-full p-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center justify-center gap-3\"\n            >\n              <Upload size={24} />\n              <span className=\"font-medium\">Upload Image</span>\n            </button>\n          </div>\n        </div>\n\n        {/* Instructions */}\n        <div className=\"mt-6 p-4 bg-gray-700 rounded-lg\">\n          <h3 className=\"font-medium text-white mb-2\">Instructions</h3>\n          <ul className=\"text-sm text-gray-300 space-y-1\">\n            <li>• Point your camera at the QR code or upload an image</li>\n            <li>• Make sure the QR code is well-lit and in focus</li>\n            <li>• You'll be automatically redirected after scanning</li>\n          </ul>\n        </div>\n\n        {/* Troubleshooting */}\n        <div className=\"mt-4 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg\">\n          <h4 className=\"font-medium text-blue-300 mb-2\">Having trouble?</h4>\n          <ul className=\"text-sm text-blue-200 space-y-1\">\n            <li>• Make sure you allow camera access when prompted</li>\n            <li>• Try refreshing the page if camera doesn't work</li>\n            <li>• Use the upload option if camera access fails</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAYe,SAAS,eAAe,EAAE,aAAa,EAAE,WAAW,EAAE,OAAO,EAAuB;IACjG,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,oBAAoB;QACxB,mEAAmE;QACnE,eAAe,OAAO,CAAC,oBAAoB;QAC3C,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,qDAAqD;YACrD,MAAM,EAAE,WAAW,EAAE,GAAG;YAExB,+CAA+C;YAC/C,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,EAAE,GAAG;YACb,QAAQ,KAAK,CAAC,OAAO,GAAG;YACxB,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,MAAM,cAAc,IAAI,YAAY;YACpC,MAAM,SAAS,MAAM,YAAY,QAAQ,CAAC,MAAM;YAEhD,WAAW;YACX,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,QAAQ,GAAG,CAAC,wBAAwB;YACpC,cAAc;YACd;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,cAAc;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA+B;;;;;;sCAC7C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;gCAAgB,MAAM;;;;;;;;;;;;;;;;;8BAKvC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,sMAAA,CAAA,SAAM;oCAAC,MAAM;;;;;;8CACd,8OAAC;oCAAK,WAAU;8CAAc;;;;;;;;;;;;sCAIhC,8OAAC;;8CACC,8OAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAO;oCACP,UAAU;oCACV,WAAU;;;;;;8CAEZ,8OAAC;oCACC,SAAS,IAAM,aAAa,OAAO,EAAE;oCACrC,WAAU;;sDAEV,8OAAC,sMAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;sDACd,8OAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;;;;;;;;;;;;;8BAMpC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8B;;;;;;sCAC5C,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;8BAKR,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiC;;;;;;sCAC/C,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/components/PWAInstaller.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Download, X } from 'lucide-react';\n\ninterface BeforeInstallPromptEvent extends Event {\n  readonly platforms: string[];\n  readonly userChoice: Promise<{\n    outcome: 'accepted' | 'dismissed';\n    platform: string;\n  }>;\n  prompt(): Promise<void>;\n}\n\nexport default function PWAInstaller() {\n  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);\n  const [showInstallPrompt, setShowInstallPrompt] = useState(false);\n  const [isInstalled, setIsInstalled] = useState(false);\n\n  useEffect(() => {\n    // Check if app is already installed\n    if (window.matchMedia('(display-mode: standalone)').matches) {\n      setIsInstalled(true);\n      return;\n    }\n\n    // Listen for the beforeinstallprompt event\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault();\n      setDeferredPrompt(e as BeforeInstallPromptEvent);\n      setShowInstallPrompt(true);\n    };\n\n    // Listen for app installed event\n    const handleAppInstalled = () => {\n      setIsInstalled(true);\n      setShowInstallPrompt(false);\n      setDeferredPrompt(null);\n    };\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n    window.addEventListener('appinstalled', handleAppInstalled);\n\n    // Register service worker\n    if ('serviceWorker' in navigator) {\n      navigator.serviceWorker.register('/sw.js')\n        .then((registration) => {\n          console.log('Service Worker registered:', registration);\n        })\n        .catch((error) => {\n          console.error('Service Worker registration failed:', error);\n        });\n    }\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n      window.removeEventListener('appinstalled', handleAppInstalled);\n    };\n  }, []);\n\n  const handleInstallClick = async () => {\n    if (!deferredPrompt) return;\n\n    try {\n      await deferredPrompt.prompt();\n      const { outcome } = await deferredPrompt.userChoice;\n      \n      if (outcome === 'accepted') {\n        console.log('User accepted the install prompt');\n      } else {\n        console.log('User dismissed the install prompt');\n      }\n      \n      setDeferredPrompt(null);\n      setShowInstallPrompt(false);\n    } catch (error) {\n      console.error('Error during installation:', error);\n    }\n  };\n\n  const handleDismiss = () => {\n    setShowInstallPrompt(false);\n    // Don't show again for this session\n    sessionStorage.setItem('pwa-install-dismissed', 'true');\n  };\n\n  // Don't show if already installed or dismissed\n  if (isInstalled || !showInstallPrompt || sessionStorage.getItem('pwa-install-dismissed')) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg p-4 shadow-lg z-50\">\n      <div className=\"flex items-start justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          <Download className=\"w-5 h-5 text-purple-400\" />\n          <h3 className=\"text-white font-semibold\">Install AstroConnect</h3>\n        </div>\n        <button\n          onClick={handleDismiss}\n          className=\"text-gray-400 hover:text-white transition-colors\"\n        >\n          <X size={20} />\n        </button>\n      </div>\n      \n      <p className=\"text-gray-300 text-sm mb-4\">\n        Install our app for quick access to your daily horoscope and cosmic insights!\n      </p>\n      \n      <div className=\"flex space-x-2\">\n        <button\n          onClick={handleInstallClick}\n          className=\"flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\"\n        >\n          Install\n        </button>\n        <button\n          onClick={handleDismiss}\n          className=\"px-4 py-2 text-gray-300 hover:text-white text-sm transition-colors\"\n        >\n          Not now\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAce,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IACtF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oCAAoC;QACpC,IAAI,OAAO,UAAU,CAAC,8BAA8B,OAAO,EAAE;YAC3D,eAAe;YACf;QACF;QAEA,2CAA2C;QAC3C,MAAM,4BAA4B,CAAC;YACjC,EAAE,cAAc;YAChB,kBAAkB;YAClB,qBAAqB;QACvB;QAEA,iCAAiC;QACjC,MAAM,qBAAqB;YACzB,eAAe;YACf,qBAAqB;YACrB,kBAAkB;QACpB;QAEA,OAAO,gBAAgB,CAAC,uBAAuB;QAC/C,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,0BAA0B;QAC1B,IAAI,mBAAmB,WAAW;YAChC,UAAU,aAAa,CAAC,QAAQ,CAAC,UAC9B,IAAI,CAAC,CAAC;gBACL,QAAQ,GAAG,CAAC,8BAA8B;YAC5C,GACC,KAAK,CAAC,CAAC;gBACN,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACJ;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,uBAAuB;YAClD,OAAO,mBAAmB,CAAC,gBAAgB;QAC7C;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,eAAe,MAAM;YAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;YAEnD,IAAI,YAAY,YAAY;gBAC1B,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,kBAAkB;YAClB,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,gBAAgB;QACpB,qBAAqB;QACrB,oCAAoC;QACpC,eAAe,OAAO,CAAC,yBAAyB;IAClD;IAEA,+CAA+C;IAC/C,IAAI,eAAe,CAAC,qBAAqB,eAAe,OAAO,CAAC,0BAA0B;QACxF,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;;;;;;;kCAE3C,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAIb,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;0BAI1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { User, UserWithSession } from '@/types';\n\nexport function useAuth() {\n  const [user, setUser] = useState<UserWithSession | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check if session is expired\n  const isSessionExpired = (user: UserWithSession | null): boolean => {\n    if (!user || !user.sessionExpiry) return true;\n    return new Date() > new Date(user.sessionExpiry);\n  };\n\n  useEffect(() => {\n    // Check for stored user data on component mount\n    const storedUser = localStorage.getItem('astroconnect_user');\n    if (storedUser) {\n      try {\n        const parsedUser = JSON.parse(storedUser) as UserWithSession;\n\n        // Check if session is expired\n        if (isSessionExpired(parsedUser)) {\n          console.log('Session expired, removing stored user data');\n          localStorage.removeItem('astroconnect_user');\n          setUser(null);\n        } else {\n          setUser(parsedUser);\n        }\n      } catch (error) {\n        console.error('Error parsing stored user data:', error);\n        localStorage.removeItem('astroconnect_user');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  // Auto-logout when session expires\n  useEffect(() => {\n    if (!user || !user.sessionExpiry) return;\n\n    const checkSessionExpiry = () => {\n      if (isSessionExpired(user)) {\n        console.log('Session expired, logging out user');\n        logout();\n      }\n    };\n\n    // Check every minute\n    const interval = setInterval(checkSessionExpiry, 60000);\n\n    // Also set a timeout for the exact expiry time\n    const timeUntilExpiry = new Date(user.sessionExpiry).getTime() - Date.now();\n    const timeout = setTimeout(() => {\n      console.log('Session expired, logging out user');\n      logout();\n    }, timeUntilExpiry);\n\n    return () => {\n      clearInterval(interval);\n      clearTimeout(timeout);\n    };\n  }, [user]);\n\n  const login = async (token: string): Promise<{ success: boolean; error?: string }> => {\n    try {\n      const response = await fetch('/api/auth/qr', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ token }),\n      });\n\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const userWithSession = data.data as UserWithSession;\n\n        // Verify session is not expired\n        if (isSessionExpired(userWithSession)) {\n          return { success: false, error: 'Session expired during authentication' };\n        }\n\n        setUser(userWithSession);\n        localStorage.setItem('astroconnect_user', JSON.stringify(userWithSession));\n        console.log(`User logged in with session expiring at: ${userWithSession.sessionExpiry}`);\n        return { success: true };\n      } else {\n        return { success: false, error: data.error || 'Authentication failed' };\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      return { success: false, error: 'Network error occurred' };\n    }\n  };\n\n  const logout = () => {\n    console.log('User logged out');\n    setUser(null);\n    localStorage.removeItem('astroconnect_user');\n  };\n\n  const updateUser = (updatedUser: UserWithSession) => {\n    setUser(updatedUser);\n    localStorage.setItem('astroconnect_user', JSON.stringify(updatedUser));\n  };\n\n  const isAuthenticated = !!user && !isSessionExpired(user);\n\n  // Get session time remaining in minutes\n  const getSessionTimeRemaining = (): number => {\n    if (!user || !user.sessionExpiry) return 0;\n    const remaining = new Date(user.sessionExpiry).getTime() - Date.now();\n    return Math.max(0, Math.floor(remaining / (1000 * 60))); // Convert to minutes\n  };\n\n  return {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser,\n    getSessionTimeRemaining,\n    isSessionExpired: () => isSessionExpired(user)\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAKO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,8BAA8B;IAC9B,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE,OAAO;QACzC,OAAO,IAAI,SAAS,IAAI,KAAK,KAAK,aAAa;IACjD;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gDAAgD;QAChD,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,YAAY;YACd,IAAI;gBACF,MAAM,aAAa,KAAK,KAAK,CAAC;gBAE9B,8BAA8B;gBAC9B,IAAI,iBAAiB,aAAa;oBAChC,QAAQ,GAAG,CAAC;oBACZ,aAAa,UAAU,CAAC;oBACxB,QAAQ;gBACV,OAAO;oBACL,QAAQ;gBACV;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,aAAa,UAAU,CAAC;YAC1B;QACF;QACA,WAAW;IACb,GAAG,EAAE;IAEL,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE;QAElC,MAAM,qBAAqB;YACzB,IAAI,iBAAiB,OAAO;gBAC1B,QAAQ,GAAG,CAAC;gBACZ;YACF;QACF;QAEA,qBAAqB;QACrB,MAAM,WAAW,YAAY,oBAAoB;QAEjD,+CAA+C;QAC/C,MAAM,kBAAkB,IAAI,KAAK,KAAK,aAAa,EAAE,OAAO,KAAK,KAAK,GAAG;QACzE,MAAM,UAAU,WAAW;YACzB,QAAQ,GAAG,CAAC;YACZ;QACF,GAAG;QAEH,OAAO;YACL,cAAc;YACd,aAAa;QACf;IACF,GAAG;QAAC;KAAK;IAET,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;gBAC7B,MAAM,kBAAkB,KAAK,IAAI;gBAEjC,gCAAgC;gBAChC,IAAI,iBAAiB,kBAAkB;oBACrC,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAwC;gBAC1E;gBAEA,QAAQ;gBACR,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;gBACzD,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,gBAAgB,aAAa,EAAE;gBACvF,OAAO;oBAAE,SAAS;gBAAK;YACzB,OAAO;gBACL,OAAO;oBAAE,SAAS;oBAAO,OAAO,KAAK,KAAK,IAAI;gBAAwB;YACxE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;IACF;IAEA,MAAM,SAAS;QACb,QAAQ,GAAG,CAAC;QACZ,QAAQ;QACR,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,aAAa,CAAC;QAClB,QAAQ;QACR,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D;IAEA,MAAM,kBAAkB,CAAC,CAAC,QAAQ,CAAC,iBAAiB;IAEpD,wCAAwC;IACxC,MAAM,0BAA0B;QAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE,OAAO;QACzC,MAAM,YAAY,IAAI,KAAK,KAAK,aAAa,EAAE,OAAO,KAAK,KAAK,GAAG;QACnE,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,qBAAqB;IAChF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA,kBAAkB,IAAM,iBAAiB;IAC3C;AACF", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/utils/qr.ts"], "sourcesContent": ["import { v4 as uuidv4 } from 'uuid';\nimport QRCode from 'qrcode';\n\nexport function generateQRToken(): string {\n  return uuidv4();\n}\n\nexport function generateQRUrl(token: string, baseUrl: string = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'): string {\n  return `${baseUrl}/qr/${token}`;\n}\n\nexport async function generateQRCodeImage(token: string, baseUrl?: string): Promise<string> {\n  const url = generateQRUrl(token, baseUrl);\n  try {\n    const qrCodeDataUrl = await QRCode.toDataURL(url, {\n      width: 300,\n      margin: 2,\n      color: {\n        dark: '#000000',\n        light: '#FFFFFF'\n      }\n    });\n    return qrCodeDataUrl;\n  } catch (error) {\n    console.error('Error generating QR code:', error);\n    throw new Error('Failed to generate QR code');\n  }\n}\n\nexport function extractTokenFromUrl(url: string): string | null {\n  try {\n    const urlObj = new URL(url);\n    const pathParts = urlObj.pathname.split('/');\n    const qrIndex = pathParts.indexOf('qr');\n    \n    if (qrIndex !== -1 && qrIndex < pathParts.length - 1) {\n      return pathParts[qrIndex + 1];\n    }\n    \n    return null;\n  } catch (error) {\n    console.error('Error extracting token from URL:', error);\n    return null;\n  }\n}\n\nexport function isValidQRToken(token: string): boolean {\n  // UUID v4 regex pattern\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(token);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;AACd;AAEO,SAAS,cAAc,KAAa,EAAE,UAAkB,6DAAmC,uBAAuB;IACvH,OAAO,GAAG,QAAQ,IAAI,EAAE,OAAO;AACjC;AAEO,eAAe,oBAAoB,KAAa,EAAE,OAAgB;IACvE,MAAM,MAAM,cAAc,OAAO;IACjC,IAAI;QACF,MAAM,gBAAgB,MAAM,sIAAA,CAAA,UAAM,CAAC,SAAS,CAAC,KAAK;YAChD,OAAO;YACP,QAAQ;YACR,OAAO;gBACL,MAAM;gBACN,OAAO;YACT;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,SAAS,oBAAoB,GAAW;IAC7C,IAAI;QACF,MAAM,SAAS,IAAI,IAAI;QACvB,MAAM,YAAY,OAAO,QAAQ,CAAC,KAAK,CAAC;QACxC,MAAM,UAAU,UAAU,OAAO,CAAC;QAElC,IAAI,YAAY,CAAC,KAAK,UAAU,UAAU,MAAM,GAAG,GAAG;YACpD,OAAO,SAAS,CAAC,UAAU,EAAE;QAC/B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAEO,SAAS,eAAe,KAAa;IAC1C,wBAAwB;IACxB,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/digital_hadahana/astroconnect/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport QRScannerModal from '@/components/QRScannerModal';\nimport PWAInstaller from '@/components/PWAInstaller';\nimport { useAuth } from '@/hooks/useAuth';\nimport { extractTokenFromUrl } from '@/utils/qr';\nimport { Sparkles, Moon, Star, Zap } from 'lucide-react';\n\nexport default function Home() {\n  const [showScanner, setShowScanner] = useState(false);\n  const [scanError, setScanError] = useState<string | null>(null);\n  const { login } = useAuth();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n\n  // Handle QR code from URL parameter (when returning from scan page)\n  useEffect(() => {\n    const qrParam = searchParams.get('qr');\n    if (qrParam) {\n      handleScanSuccess(qrParam);\n      // Clean up URL\n      router.replace('/');\n    }\n  }, [searchParams]);\n\n  const handleScanSuccess = async (decodedText: string) => {\n    console.log('QR Code scanned:', decodedText);\n\n    // Extract token from the scanned URL\n    const token = extractTokenFromUrl(decodedText);\n\n    if (!token) {\n      setScanError('Invalid QR code format');\n      return;\n    }\n\n    // Authenticate with the token\n    const result = await login(token);\n\n    if (result.success) {\n      router.push('/dashboard');\n    } else {\n      setScanError(result.error || 'Authentication failed');\n    }\n  };\n\n  const handleScanError = (error: string) => {\n    setScanError(error);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden\">\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-pulse opacity-70\"></div>\n        <div className=\"absolute top-1/3 right-1/3 w-1 h-1 bg-yellow-300 rounded-full animate-ping opacity-60\"></div>\n        <div className=\"absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-purple-300 rounded-full animate-pulse opacity-50\"></div>\n        <div className=\"absolute top-1/2 right-1/4 w-1 h-1 bg-blue-300 rounded-full animate-ping opacity-40\"></div>\n        <div className=\"absolute bottom-1/3 right-1/2 w-2 h-2 bg-indigo-300 rounded-full animate-pulse opacity-60\"></div>\n      </div>\n\n      <div className=\"relative z-10 flex flex-col items-center justify-center min-h-screen p-4\">\n        {!showScanner ? (\n          <div className=\"text-center max-w-4xl mx-auto\">\n            {/* Header */}\n            <div className=\"mb-8\">\n              <div className=\"flex items-center justify-center mb-4\">\n                <Sparkles className=\"w-8 h-8 text-yellow-400 mr-2\" />\n                <h1 className=\"text-4xl md:text-6xl font-bold text-white\">\n                  AstroConnect\n                </h1>\n                <Sparkles className=\"w-8 h-8 text-yellow-400 ml-2\" />\n              </div>\n              <p className=\"text-xl md:text-2xl text-gray-300 mb-2\">\n                Your Personal Horoscope & Daily Guide\n              </p>\n              <p className=\"text-gray-400\">\n                Discover your cosmic destiny with personalized astrology insights\n              </p>\n            </div>\n\n            {/* Features */}\n            <div className=\"grid md:grid-cols-3 gap-6 mb-12\">\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <Moon className=\"w-12 h-12 text-blue-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Daily Horoscopes</h3>\n                <p className=\"text-gray-300 text-sm\">\n                  Get personalized daily, weekly, and monthly predictions based on your zodiac sign\n                </p>\n              </div>\n\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <Star className=\"w-12 h-12 text-yellow-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">Lucky Guidance</h3>\n                <p className=\"text-gray-300 text-sm\">\n                  Discover your lucky numbers, colors, and optimal times for important decisions\n                </p>\n              </div>\n\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20\">\n                <Zap className=\"w-12 h-12 text-purple-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-semibold text-white mb-2\">QR Access</h3>\n                <p className=\"text-gray-300 text-sm\">\n                  Instant access to your personalized dashboard with your unique QR code\n                </p>\n              </div>\n            </div>\n\n            {/* CTA Buttons */}\n            <div className=\"space-y-4\">\n              <button\n                onClick={() => setShowScanner(true)}\n                className=\"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-lg\"\n              >\n                Scan Your QR Code\n              </button>\n\n              <p className=\"text-gray-400 text-sm\">\n                Have a personalized QR card? Scan it to access your cosmic insights instantly\n              </p>\n            </div>\n\n            {scanError && (\n              <div className=\"mt-6 p-4 bg-red-500/20 border border-red-500/50 rounded-lg\">\n                <p className=\"text-red-300\">{scanError}</p>\n                <button\n                  onClick={() => setScanError(null)}\n                  className=\"text-red-200 hover:text-white underline mt-2\"\n                >\n                  Try Again\n                </button>\n              </div>\n            )}\n          </div>\n        ) : null}\n\n        {showScanner && (\n          <QRScannerModal\n            onScanSuccess={handleScanSuccess}\n            onScanError={handleScanError}\n            onClose={() => {\n              setShowScanner(false);\n              setScanError(null);\n            }}\n          />\n        )}\n      </div>\n\n      {/* PWA Installer */}\n      <PWAInstaller />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACxB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,oEAAoE;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,IAAI,SAAS;YACX,kBAAkB;YAClB,eAAe;YACf,OAAO,OAAO,CAAC;QACjB;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,oBAAoB,OAAO;QAC/B,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,qCAAqC;QACrC,MAAM,QAAQ,CAAA,GAAA,kHAAA,CAAA,sBAAmB,AAAD,EAAE;QAElC,IAAI,CAAC,OAAO;YACV,aAAa;YACb;QACF;QAEA,8BAA8B;QAC9B,MAAM,SAAS,MAAM,MAAM;QAE3B,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,IAAI,CAAC;QACd,OAAO;YACL,aAAa,OAAO,KAAK,IAAI;QAC/B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,4BACA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAG,WAAU;0DAA4C;;;;;;0DAG1D,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,8OAAC;wCAAE,WAAU;kDAAyC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAM/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAKvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAOzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDACX;;;;;;kDAID,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;4BAKtC,2BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAC7B,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDACX;;;;;;;;;;;;;;;;;+BAML;oBAEH,6BACC,8OAAC,oIAAA,CAAA,UAAc;wBACb,eAAe;wBACf,aAAa;wBACb,SAAS;4BACP,eAAe;4BACf,aAAa;wBACf;;;;;;;;;;;;0BAMN,8OAAC,kIAAA,CAAA,UAAY;;;;;;;;;;;AAGnB", "debugId": null}}]}