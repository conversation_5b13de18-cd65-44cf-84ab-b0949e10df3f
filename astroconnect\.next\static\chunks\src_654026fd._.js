(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/prisma.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkDatabaseConnection": (()=>checkDatabaseConnection),
    "disconnectPrisma": (()=>disconnectPrisma),
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$prisma$2f$client$2f$index$2d$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@prisma/client/index-browser.js [app-client] (ecmascript)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$prisma$2f$client$2f$index$2d$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
async function disconnectPrisma() {
    await prisma.$disconnect();
}
async function checkDatabaseConnection() {
    try {
        await prisma.$queryRaw`SELECT 1`;
        return true;
    } catch (error) {
        console.error('Database connection failed:', error);
        return false;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/translation.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LANGUAGE_NAMES": (()=>LANGUAGE_NAMES),
    "translateText": (()=>translateText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-client] (ecmascript)");
;
async function translateText(text, targetLanguage, sourceLanguage = 'en') {
    // If source and target are the same, return original text
    if (sourceLanguage === targetLanguage) {
        return text;
    }
    // Check cache first
    const cachedTranslation = await getCachedTranslation(text, sourceLanguage, targetLanguage);
    if (cachedTranslation) {
        return cachedTranslation;
    }
    try {
        // Call Gemini API for translation
        const translatedText = await callGeminiTranslation(text, sourceLanguage, targetLanguage);
        // Cache the translation
        await cacheTranslation(text, translatedText, sourceLanguage, targetLanguage);
        return translatedText;
    } catch (error) {
        console.error('Translation error:', error);
        // Return original text if translation fails
        return text;
    }
}
async function getCachedTranslation(originalText, sourceLanguage, targetLanguage) {
    try {
        const cached = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prisma"].translationCache.findFirst({
            where: {
                originalText,
                sourceLanguage,
                targetLanguage
            }
        });
        return cached?.translatedText || null;
    } catch (error) {
        console.error('Error fetching cached translation:', error);
        return null;
    }
}
async function cacheTranslation(originalText, translatedText, sourceLanguage, targetLanguage) {
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prisma"].translationCache.create({
            data: {
                originalText,
                translatedText,
                sourceLanguage,
                targetLanguage
            }
        });
    } catch (error) {
        console.error('Error caching translation:', error);
    }
}
async function callGeminiTranslation(text, sourceLanguage, targetLanguage) {
    const response = await fetch('/api/translate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            text,
            sourceLanguage,
            targetLanguage
        })
    });
    if (!response.ok) {
        throw new Error('Translation API request failed');
    }
    const data = await response.json();
    if (!data.success) {
        throw new Error(data.error || 'Translation failed');
    }
    return data.translatedText;
}
const LANGUAGE_NAMES = {
    en: 'English',
    si: 'සිංහල'
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useLanguage.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LanguageProvider": (()=>LanguageProvider),
    "useLanguage": (()=>useLanguage),
    "useTranslation": (()=>useTranslation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$translation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/translation.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
'use client';
;
;
const LanguageContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function LanguageProvider({ children }) {
    _s();
    const [language, setLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('en');
    const [isTranslating, setIsTranslating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const translate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "LanguageProvider.useCallback[translate]": async (text)=>{
            if (language === 'en') {
                return text; // No translation needed for English
            }
            setIsTranslating(true);
            try {
                const translatedText = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$translation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["translateText"])(text, language, 'en');
                return translatedText;
            } catch (error) {
                console.error('Translation error:', error);
                return text; // Return original text if translation fails
            } finally{
                setIsTranslating(false);
            }
        }
    }["LanguageProvider.useCallback[translate]"], [
        language
    ]);
    const value = {
        language,
        setLanguage,
        translate,
        isTranslating
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LanguageContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/hooks/useLanguage.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
}
_s(LanguageProvider, "6QIwINq+ORVuxAWjV1vOnhUuMos=");
_c = LanguageProvider;
function useLanguage() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(LanguageContext);
    if (context === undefined) {
        throw new Error('useLanguage must be used within a LanguageProvider');
    }
    return context;
}
_s1(useLanguage, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function useTranslation() {
    _s2();
    const { language, translate, isTranslating } = useLanguage();
    const [translationCache, setTranslationCache] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Map());
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTranslation.useCallback[t]": async (text)=>{
            if (language === 'en') {
                return text;
            }
            const cacheKey = `${text}_${language}`;
            if (translationCache.has(cacheKey)) {
                return translationCache.get(cacheKey);
            }
            const translatedText = await translate(text);
            setTranslationCache({
                "useTranslation.useCallback[t]": (prev)=>new Map(prev).set(cacheKey, translatedText)
            }["useTranslation.useCallback[t]"]);
            return translatedText;
        }
    }["useTranslation.useCallback[t]"], [
        language,
        translate,
        translationCache
    ]);
    return {
        t,
        isTranslating,
        language
    };
}
_s2(useTranslation, "8fMwHsNBqN4vGt0rqBdIdSOlZTU=", false, function() {
    return [
        useLanguage
    ];
});
var _c;
__turbopack_context__.k.register(_c, "LanguageProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_654026fd._.js.map