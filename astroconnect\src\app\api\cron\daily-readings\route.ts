import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { generateAllDailyReadings } from '@/lib/gemini';

export async function GET(request: NextRequest) {
  try {
    // This endpoint can be called by a cron service to generate daily readings
    const today = new Date().toISOString().split('T')[0];
    
    console.log('🕐 Cron job: Generating daily zodiac readings for:', today);

    // Check if readings already exist for today
    const existingReadings = await prisma.dailyZodiacReading.findMany({
      where: {
        date: new Date(today),
        language: 'en'
      }
    });

    if (existingReadings.length === 12) {
      console.log('✅ Cron: Readings already exist for today');
      return NextResponse.json({
        success: true,
        message: 'Daily readings already exist for today',
        date: today,
        count: existingReadings.length
      });
    }

    // Generate new readings
    console.log('🤖 Cron: Generating new readings with Gemini API...');
    const newReadings = await generateAllDailyReadings(today);

    // Save readings to database
    const savedReadings = [];
    for (const reading of newReadings) {
      try {
        const saved = await prisma.dailyZodiacReading.upsert({
          where: {
            zodiacSign_date_language: {
              zodiacSign: reading.zodiacSign,
              date: new Date(today),
              language: 'en'
            }
          },
          update: {
            generalReading: reading.generalReading,
            loveReading: reading.loveReading,
            careerReading: reading.careerReading,
            healthReading: reading.healthReading,
            luckyNumber: reading.luckyNumber,
            luckyColor: reading.luckyColor,
            luckyTime: reading.luckyTime,
            luckyGem: reading.luckyGem,
            advice: reading.advice,
            mood: reading.mood,
            compatibility: reading.compatibility,
            updatedAt: new Date()
          },
          create: {
            zodiacSign: reading.zodiacSign,
            date: new Date(today),
            generalReading: reading.generalReading,
            loveReading: reading.loveReading,
            careerReading: reading.careerReading,
            healthReading: reading.healthReading,
            luckyNumber: reading.luckyNumber,
            luckyColor: reading.luckyColor,
            luckyTime: reading.luckyTime,
            luckyGem: reading.luckyGem,
            advice: reading.advice,
            mood: reading.mood,
            compatibility: reading.compatibility,
            language: 'en'
          }
        });
        savedReadings.push(saved);
        console.log(`✅ Cron: Saved reading for ${reading.zodiacSign}`);
      } catch (error) {
        console.error(`❌ Cron: Error saving reading for ${reading.zodiacSign}:`, error);
      }
    }

    console.log(`🎉 Cron: Successfully generated ${savedReadings.length} daily readings`);

    return NextResponse.json({
      success: true,
      message: `Generated ${savedReadings.length} daily zodiac readings`,
      date: today,
      count: savedReadings.length
    });

  } catch (error) {
    console.error('❌ Cron: Error generating daily readings:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to generate daily readings',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
