'use client';

import { useState, useEffect } from 'react';
import { User } from '@/types';
import { Search, Filter, Plus, Edit, Trash2, QrCode, Download } from 'lucide-react';
import { ZODIAC_INFO } from '@/utils/zodiac';

interface UserWithStats extends User {
  scan_count: number;
  last_scanned: string | null;
}

export default function UserManagement() {
  const [users, setUsers] = useState<UserWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    phoneNumber: '',
    address: '',
    birthDate: '',
    birthTime: '',
    languagePreference: 'en'
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/users', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const data = await response.json();
      
      if (data.success) {
        setUsers(data.data.users);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return;

    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/users?userId=${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        setUsers(users.filter(user => user.id !== userId));
      }
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  };

  const handleGenerateQR = async (user: UserWithStats) => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          name: user.name,
          email: user.email,
          phoneNumber: user.phoneNumber,
          address: user.address,
          birthDate: user.birthDate,
          birthTime: user.birthTime,
          languagePreference: user.languagePreference
        })
      });

      const data = await response.json();
      
      if (data.success) {
        // Download QR code
        const link = document.createElement('a');
        link.href = data.data.qrCodeImage;
        link.download = `${user.name}-qr-code.png`;
        link.click();
      }
    } catch (error) {
      console.error('Error generating QR code:', error);
    }
  };

  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          ...newUser,
          zodiacSign: undefined // Let the server calculate this from birthDate
        })
      });

      const data = await response.json();

      if (data.success) {
        // Add the new user to the list
        setUsers([data.data.user, ...users]);

        // Reset form and close modal
        setNewUser({
          name: '',
          email: '',
          phoneNumber: '',
          address: '',
          birthDate: '',
          birthTime: '',
          languagePreference: 'en'
        });
        setShowAddModal(false);

        // Download QR code automatically
        if (data.data.qrCodeImage) {
          const link = document.createElement('a');
          link.href = data.data.qrCodeImage;
          link.download = `${data.data.user.name}-qr-code.png`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      } else {
        alert('Error adding user: ' + data.error);
      }
    } catch (error) {
      console.error('Error adding user:', error);
      alert('Error adding user. Please try again.');
    }
  };

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>
        <button className="flex items-center space-x-2 bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-colors">
          <Filter size={16} />
          <span>Filter</span>
        </button>
        <button 
          onClick={() => setShowAddModal(true)}
          className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
        >
          <Plus size={16} />
          <span>Add User</span>
        </button>
      </div>

      {/* Users Table */}
      <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/5">
              <tr>
                <th className="text-left py-3 px-4 text-gray-300 font-semibold">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedUsers(filteredUsers.map(u => u.id));
                      } else {
                        setSelectedUsers([]);
                      }
                    }}
                  />
                </th>
                <th className="text-left py-3 px-4 text-gray-300 font-semibold">Name</th>
                <th className="text-left py-3 px-4 text-gray-300 font-semibold">Email</th>
                <th className="text-left py-3 px-4 text-gray-300 font-semibold">Phone</th>
                <th className="text-left py-3 px-4 text-gray-300 font-semibold">Zodiac</th>
                <th className="text-left py-3 px-4 text-gray-300 font-semibold">Language</th>
                <th className="text-left py-3 px-4 text-gray-300 font-semibold">Scans</th>
                <th className="text-left py-3 px-4 text-gray-300 font-semibold">Last Active</th>
                <th className="text-left py-3 px-4 text-gray-300 font-semibold">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.map((user) => (
                <tr key={user.id} className="border-t border-white/10 hover:bg-white/5">
                  <td className="py-3 px-4">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300"
                      checked={selectedUsers.includes(user.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedUsers([...selectedUsers, user.id]);
                        } else {
                          setSelectedUsers(selectedUsers.filter(id => id !== user.id));
                        }
                      }}
                    />
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center space-x-3">
                      <div className="text-2xl">{user.zodiacSign && ZODIAC_INFO[user.zodiacSign] ? ZODIAC_INFO[user.zodiacSign].symbol : '⭐'}</div>
                      <div>
                        <p className="text-white font-medium">{user.name}</p>
                        <p className="text-gray-400 text-sm">{user.zodiacSign && ZODIAC_INFO[user.zodiacSign] ? ZODIAC_INFO[user.zodiacSign].name : 'Unknown'}</p>
                      </div>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-gray-300">{user.email || 'N/A'}</td>
                  <td className="py-3 px-4 text-gray-300">{user.phoneNumber || 'N/A'}</td>
                  <td className="py-3 px-4 text-gray-300">{user.zodiacSign && ZODIAC_INFO[user.zodiacSign] ? ZODIAC_INFO[user.zodiacSign].name : 'Unknown'}</td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      user.languagePreference === 'en'
                        ? 'bg-blue-500/20 text-blue-300'
                        : 'bg-green-500/20 text-green-300'
                    }`}>
                      {user.languagePreference === 'en' ? 'English' : 'සිංහල'}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-gray-300">{user.scan_count || 0}</td>
                  <td className="py-3 px-4 text-gray-300">
                    {user.last_scanned 
                      ? new Date(user.last_scanned).toLocaleDateString()
                      : 'Never'
                    }
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center space-x-2">
                      <button 
                        onClick={() => handleGenerateQR(user)}
                        className="text-blue-400 hover:text-blue-300 p-1"
                        title="Generate QR Code"
                      >
                        <QrCode size={16} />
                      </button>
                      <button className="text-purple-400 hover:text-purple-300 p-1" title="Edit">
                        <Edit size={16} />
                      </button>
                      <button 
                        onClick={() => handleDeleteUser(user.id)}
                        className="text-red-400 hover:text-red-300 p-1"
                        title="Delete"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedUsers.length > 0 && (
        <div className="bg-purple-600/20 border border-purple-500/50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-white">
              {selectedUsers.length} user{selectedUsers.length > 1 ? 's' : ''} selected
            </p>
            <div className="flex items-center space-x-2">
              <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                Delete Selected
              </button>
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                Export Selected
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <p className="text-gray-300 text-sm">
          Showing {filteredUsers.length} of {users.length} users
        </p>
        <div className="flex items-center space-x-2">
          <button className="bg-white/10 hover:bg-white/20 text-white px-3 py-1 rounded text-sm transition-colors">
            Previous
          </button>
          <button className="bg-purple-600 text-white px-3 py-1 rounded text-sm">1</button>
          <button className="bg-white/10 hover:bg-white/20 text-white px-3 py-1 rounded text-sm transition-colors">
            Next
          </button>
        </div>
      </div>

      {/* Add User Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-white mb-4">Add New User</h3>

            <form onSubmit={handleAddUser} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Name</label>
                <input
                  type="text"
                  value={newUser.name}
                  onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Email (Optional)</label>
                <input
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Phone Number (Optional)</label>
                <input
                  type="tel"
                  value={newUser.phoneNumber}
                  onChange={(e) => setNewUser({ ...newUser, phoneNumber: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="+94 77 123 4567"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Address (Optional)</label>
                <textarea
                  value={newUser.address}
                  onChange={(e) => setNewUser({ ...newUser, address: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  rows={2}
                  placeholder="Enter full address"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Birth Date</label>
                <input
                  type="date"
                  value={newUser.birthDate}
                  onChange={(e) => setNewUser({ ...newUser, birthDate: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Birth Time (Optional)</label>
                <input
                  type="time"
                  value={newUser.birthTime}
                  onChange={(e) => setNewUser({ ...newUser, birthTime: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Language Preference</label>
                <select
                  value={newUser.languagePreference}
                  onChange={(e) => setNewUser({ ...newUser, languagePreference: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="en">English</option>
                  <option value="si">Sinhala</option>
                </select>
              </div>

              <div className="flex items-center space-x-3 pt-4">
                <button
                  type="submit"
                  className="flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  Add User & Generate QR
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowAddModal(false);
                    setNewUser({
                      name: '',
                      email: '',
                      phoneNumber: '',
                      address: '',
                      birthDate: '',
                      birthTime: '',
                      languagePreference: 'en'
                    });
                  }}
                  className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
